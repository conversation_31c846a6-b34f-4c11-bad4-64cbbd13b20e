{"name": "myapp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:prod": "tsc && vite build --mode prod", "build:pre": "tsc && vite build --mode pre", "build:test": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "scp": "ssh -p 18722 admin@************* rm -rf /data/react_gaomei_manage && scp -r -P 18722 dist admin@*************:/data/react_gaomei_manage"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@tinymce/tinymce-react": "^5.0.0", "antd": "^5.25.2", "axios": "^1.3.6", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.7", "echarts": "^5.4.3", "hash-wasm": "^4.9.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "react-to-print": "^3.0.2", "react-use": "^17.4.0", "react-zoom-pan-pinch": "^3.7.0", "zustand": "^5.0.6"}, "devDependencies": {"@swc/core": "^1.3.102", "@types/node": "^22.15.21", "@types/react": "^18.0.28", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react-swc": "^3.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.62.1", "typescript": "^5.0.2", "unocss": "^0.62.4", "vite": "^4.3.2"}}