import { createContext, use<PERSON>ontext, ReactNode } from "react";

export function createAutoContext<T>() {
  const Context = createContext<T | undefined>(undefined);

  function useCtx() {
    const ctx = useContext(Context);
    if (ctx === undefined) {
      throw new Error("Context not found. Make sure to wrap in the Provider.");
    }
    return ctx;
  }

  function Provider(props: { value: T; children: ReactNode }) {
    return <Context.Provider value={props.value}>{props.children}</Context.Provider>;
  }

  return [useCtx, Provider] as const;
}
