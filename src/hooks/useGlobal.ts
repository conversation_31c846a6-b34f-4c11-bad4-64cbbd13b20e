// import UserApi from "@/services/UserApi";
// import { createGlobalState, useLocalStorage, useMount } from "react-use";

// const useGlobalState = createGlobalState({
//   user: {} as { [key: string]: any },
//   menu: [] as any[],
// });

// const useWeApp = () => {
//   const [state, setState] = useGlobalState();

//   const fetchUser = async () => {
//     const user = await UserApi.getUserInfo();
//     setState((prev) => ({ ...prev, user }));
//   };

//   useMount(() => {});
// };

// export default useWeApp();

// // export default useGlobal;
