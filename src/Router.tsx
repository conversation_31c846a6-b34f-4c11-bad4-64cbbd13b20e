import { Router<PERSON><PERSON><PERSON>, createBrowserRouter, Navigate } from "react-router-dom";
import LoginPage from "./pages/others/Login";
// import AdminLayout from "./components/AdminLayout";
import DemoPage from "./pages/others/Demo";
import LoadShop from "./pages/others/LoadShop";
import UploadPage from "./pages/others/Upload";
import AdminLayoutV2 from "./components/AdminLayoutV2";

const pages = import.meta.glob(["./pages/admin/**/*.page.tsx"], { import: "default", eager: true });

const NotFound = () => <div style={{ padding: "10px 0" }}>Not Found</div>;

const autoRoute: any[] = [];

Object.keys(pages).forEach((page) => {
  const path = page.replace(/(^.\/pages\/admin\/)|(\/index\.page\.tsx$)|(\.page\.tsx$)/g, "");
  const Comp: any = pages[page];

  autoRoute.push({ path, element: <Comp /> });
});

const router = createBrowserRouter([
  { path: "*", element: <Navigate to="/admin" replace /> },
  { path: "/login", element: <LoginPage /> },
  { path: "/loadShop", element: <LoadShop /> },
  { path: "/upload", element: <UploadPage /> },
  { path: "/admin", element: <AdminLayoutV2 /> },
  {
    path: "/admin/:module",
    element: <AdminLayoutV2 />,
    children: [{ path: "*", element: <NotFound /> }, { path: "demo", element: <DemoPage /> }, ...autoRoute],
  },
]);

(window as any)._router = router;

const Router = () => <RouterProvider router={router} />;

export default Router;
