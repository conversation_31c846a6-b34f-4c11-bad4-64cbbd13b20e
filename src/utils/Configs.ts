const mode = import.meta.env.MODE;

export default Object.assign(
  {
    IMAGE_HOLDER: "https://guanjia.x9w.com/file-dev/file/100/2023/0822/17/1143594144278683649_size_701.png",
  },

  mode === "prod"
    ? {
        apiHost: "https://vip.gaomei168.com/shaping-manage-release",
        uploadHost: "https://vip.gaomei168.com/file-oss/upload",
      }
    : mode === "pre"
    ? {
        apiHost: "http://dev.gaomei168.com:9202",
        uploadHost: "https://vip.gaomei168.com/file-oss/upload",
      }
    : {
        // apiHost: "http://dev.gaomei168.com:9000/service-shaping-manage",
        apiHost: "https://vip.gaomei168.com/shaping-manage-dev",
        //apiHost: "http://dev.gaomei168.com/service-shaping-manage",
        //apiHost: "http://127.0.0.1:9102",
        uploadHost: "https://vip.gaomei168.com/file-oss/upload",
      }
);
