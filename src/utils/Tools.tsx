import { message, Tag } from "antd";
import axios from "axios";
import dayjs from "dayjs";
import { useCallback, useEffect, useRef } from "react";
import Configs from "./Configs";

export const sleep = (ms: number) => {
  return new Promise((res) => setTimeout(res, ms));
};

export const str2arr = (s = "") => {
  return (s || "").split(",").filter((n) => n);
};

export const arr2str = (arr: any[]) => {
  return (arr || []).join(",");
};

export const formatDate = (date: any, format = "YYYY-MM-DD HH:mm:ss") => {
  if (!date) return "";
  return dayjs(date).format(format) ?? "";
};

export const formatDateRange = (arr: any[] = [], format = "YYYY-MM-DD") => {
  return { start: formatDate(arr[0], format), end: formatDate(arr[1], format) };
};

export const renderTag = (types: any[], id: any) => {
  const crt = types.find((n) => n.id == id);

  if (!crt) return null;
  return <Tag color={crt?.color}>{crt?.name}</Tag>;
};

export const getAuth = () => {
  let auth: any = {};
  try {
    auth = JSON.parse(localStorage.getItem("auth") || "{}");
  } catch (e) {
    auth = {};
  }
  return auth;
};

//  prettier-ignore
export const DatePresetRanges: any[] = [
  { label: "今天", value: [dayjs().startOf("day"), dayjs().endOf("day")] },
  { label: "昨天", value: [dayjs().subtract(1, "day").startOf("day"), dayjs().subtract(1, "day").endOf("day")] },
  { label: "本周", value: [dayjs().startOf("week"), dayjs().endOf("week")] },
  { label: "上周", value: [dayjs().startOf("week").subtract(1, "week"), dayjs().endOf("week").subtract(1, "week")] },
  { label: "本月", value: [dayjs().startOf("month"), dayjs().endOf("month")] },
  { label: "上月", value: [dayjs().subtract(1, "month").startOf("month"), dayjs().subtract(1, "month").endOf("month")] },
  { label: "今年", value: [dayjs().startOf("year"), dayjs().endOf("year")] },
  { label: "去年", value: [dayjs().subtract(1, "year").startOf("year"), dayjs().subtract(1, "year").endOf("year")] },
  { label: "最近10天", value: [dayjs().subtract(10, "day").startOf("day"), dayjs().endOf("day")] },
  { label: "最近30天", value: [dayjs().subtract(30, "day").startOf("day"), dayjs().endOf("day")] },
  { label: "最近90天", value: [dayjs().subtract(90, "day").startOf("day"), dayjs().endOf("day")] },
  { label: "未来10天", value: [dayjs().endOf("day"), dayjs().add(10, "day").endOf("day")] },
  { label: "未来30天", value: [dayjs().endOf("day"), dayjs().add(30, "day").endOf("day")] },
  { label: "未来90天", value: [dayjs().endOf("day"), dayjs().add(90, "day").endOf("day")] },
];

export const useDebounceFn = (fn: any, wait = 300, options?: { leading: boolean; trailing: boolean }) => {
  const fnRef = useRef(fn);
  const timeoutRef = useRef<any>();
  const { leading = false, trailing = true } = options || {};

  useEffect(() => {
    fnRef.current = fn;
  }, [fn]);

  const debouncedFn = useCallback(
    (...args: any[]) => {
      clearTimeout(timeoutRef.current);

      if (leading && !timeoutRef.current) {
        fnRef.current(...args);
      }

      timeoutRef.current = setTimeout(() => {
        if (trailing) {
          fnRef.current(...args);
        }
        timeoutRef.current = undefined;
      }, wait);
    },
    [wait, leading, trailing]
  );

  return debouncedFn;
};

export const downloadExcel = (url: string, params?: any) => {
  axios
    .get(url, {
      baseURL: Configs.apiHost,
      params: {
        token: localStorage.getItem("token") || "",
        ...params,
      },
    })
    .then((response) => {
      if (response.data.code === 0) {
        const fileUrl = response.data?.data?.url;
        if (!fileUrl) {
          message.error("无数据可导出");
          return;
        }
        window.open(fileUrl, "_blank");
      } else {
        console.error("Failed to get file URL:", response.data.message);
      }
    })
    .catch((error) => {
      console.error("Error downloading Excel:", error);
    });
};
