import { message } from "antd";
import axios, { CanceledError } from "axios";
import { AxiosRequestConfig } from "axios";
import Configs from "./Configs";
import { getAuth } from "./Tools";
import dayjs from "dayjs";

const api = axios.create({
  baseURL: Configs.apiHost,
  timeout: 20 * 1000,
});

let refreshIng = false;

const refreshToken = async () => {
  const auth = getAuth();

  // if (!refreshIng && dayjs().isBefore(auth.expireTime) && dayjs(auth.create).add(30, "minute").isBefore(dayjs())) {
  if (!refreshIng && dayjs().isBefore(auth.expireTime) && dayjs(auth.create).add(30, "minute").isBefore(dayjs())) {
    refreshIng = true;
    const res = await axios({
      method: "post",
      url: "/api/token/refresh_token",
      params: { token: auth.token },
      data: { refreshToken: auth.refreshToken },
      baseURL: Configs.apiHost,
    });

    refreshIng = false;

    if (res.data.code === 0) {
      const data = res.data.data;
      data.create = dayjs().format("YYYY-MM-DD HH:mm:ss");
      localStorage.setItem("auth", JSON.stringify(data));
      localStorage.setItem("token", data.token);
    }
  }
};

api.interceptors.request.use((req) => {
  const token = getAuth().token || "";
  req.params = { token, ...req.params };
  //req.headers['X-Tenant-ID'] = "1000000000";
  refreshToken();
  return req;
});

api.interceptors.response.use(
  (res) => {
    const code = res.data.code;
    if (code === 0) return res.data.data;

    const msg = res?.data?.message || "发生错误，请重试";

    if (res.data.code === 100) {
      const path = location.pathname + location.search;
      if (!/^\/login/.test(path)) {
        const from = encodeURIComponent(path);
        (window as any)?._router.navigate(`/login?from=${from}`, { replace: false });
        // message.error("登录过期，请重新登录");
      }
    } else {
      message.error(msg);
    }

    throw new Error(msg);
  },
  (err) => {
    if (err instanceof CanceledError) {
      // ignore
    } else {
      const msg = err?.response?.data?.error || err?.message || "发生错误，请重试";
      message.error(msg);
    }

    throw err;
  }
);

export const Api = (config: AxiosRequestConfig): Promise<any> => api(config);

export const makeApi: {
  (method: string, url: string, suffix?: false): (config?: AxiosRequestConfig) => Promise<any>;
  (method: string, url: string, suffix: true): (id?: string, config?: AxiosRequestConfig) => Promise<any>;
} = (method, url, suffix) => {
  if (suffix) return (id, config) => api({ method, url: url + "/" + id, ...config }) as any;
  return ((config: any) => api({ method, url, ...config })) as any;
};

export default api;
