import { ConfigProvider, App as AntdApp } from "antd";
import zhCN from "antd/locale/zh_CN";
import dayjs from "dayjs";
import Router from "./Router";

import "dayjs/locale/zh-cn";
import "antd/dist/reset.css";

dayjs.locale("zh-cn");

const theme = {
  token: {
    colorPrimary: "#c48053",
  },
};

const App = () => {
  return (
    <div>
      <ConfigProvider locale={zhCN} theme={theme}>
        <AntdApp>
          <Router />
        </AntdApp>
      </ConfigProvider>
    </div>
  );
};

export default App;
