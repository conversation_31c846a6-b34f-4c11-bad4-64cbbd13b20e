import { makeApi } from "@/utils/Api";

const UserApi = {
  // 用户登录
  login: makeApi("post", "/api/user/login"),

  // 图片验证码
  getCaptcha: makeApi("get", "/api/picKaptcha/getKaptcha"),

  // 用户菜单
  getNavMenu: makeApi("get", "/api/user/user_menu_list"),
  getNavMenu2: makeApi("get", "/api/user/user_menu_tree"),

  // 用户信息
  getUserInfo: makeApi("get", "/api/user/query"),

  // 修改密码
  putUserPwd: makeApi("post", "/api/user/modify_pass"),

  // 字典
  getDictByCode: makeApi("get", "/api/sysDictionarydata/query_dictionarydata_list"),

  // 省市区
  getAreaCode: makeApi("get", "/api/sysArea/query_by_type"),

  // 菜单管理
  getAppMenu: makeApi("get", "/api/sysMenu"),
  addAppMenu: makeApi("post", "/api/sysMenu"),
  putAppMenu: makeApi("put", "/api/sysMenu"),
  delAppMenu: makeApi("delete", "/api/sysMenu"),
  moveAppMenuModule: makeApi("post", "/api/sysMenu/move_module"),
  getAppMenuInfo: makeApi("get", "/api/sysMenu", true),

  // 角色权限
  getAppRoleList: makeApi("get", "/api/sysEnterprisetype"),
  getAppRole: makeApi("get", "/api/sysEnterprisetype", true),
  addAppRole: makeApi("post", "/api/sysEnterprisetype"),
  putAppRole: makeApi("put", "/api/sysEnterprisetype"),
  delAppRole: makeApi("delete", "/api/sysEnterprisetype"),
  getAppRoleMenu: makeApi("get", "/api/sysEnterprisetypeMenuR/enterprisetype_menu_list"),
  putAppRoleMenu: makeApi("post", "/api/sysEnterprisetypeMenuR/modify_enterprisetype_menu"),

  // 根据角色获取用户列表
  getRoleUserList: makeApi("get", "/api/sysUserRoleR/role_user_list"),
  // 保存 角色关联用户
  putRoleUser: makeApi("post", "/api/sysUserRoleR/save_role_user"),

  // 公司管理
  getCompanyList: makeApi("get", "/api/sysOrganize/company"),
  getCompany: makeApi("get", "/api/sysOrganize/company", true),
  addCompany: makeApi("post", "/api/sysOrganize/company"),
  putCompany: makeApi("put", "/api/sysOrganize/company"),
  delCompany: makeApi("delete", "/api/sysOrganize/company"),
  putCompanyPass: makeApi("post", "/api/sysCompany/init_password"),
  selectCompanyList: makeApi("get", "/api/sysCompany/select_company_list"),

  // 可用公司类型
  getCompanyType: makeApi("get", "/api/sysOrganize/query_manage_enterprisetype_list"),

  // 部门管理
  getDepartmentList: makeApi("get", "/api/sysOrganize/department"),
  getDepartment: makeApi("get", "/api/sysOrganize/department", true),
  addDepartment: makeApi("post", "/api/sysOrganize/department"),
  putDepartment: makeApi("put", "/api/sysOrganize/department"),
  delDepartment: makeApi("delete", "/api/sysOrganize/department"),

  // 角色管理
  getRoleList: makeApi("get", "/api/sysRole"),
  getRole: makeApi("get", "/api/sysRole", true),
  addRole: makeApi("post", "/api/sysRole"),
  putRole: makeApi("put", "/api/sysRole"),
  delRole: makeApi("delete", "/api/sysRole"),
  getRoleMenu: makeApi("get", "/api/sysRoleMenuR/role_menu_list"),
  getRoleScope: makeApi("get", "/api/sysRole/role_scope_list"),
  setRoleAuth: makeApi("post", `/api/sysRoleMenuR/modify_role_menu`),

  // 用户管理
  getUserList: makeApi("get", "/api/sysUser"),
  getUser: makeApi("get", "/api/sysUser", true),
  addUser: makeApi("post", "/api/sysUser"),
  putUser: makeApi("put", "/api/sysUser"),
  delUser: makeApi("delete", "/api/sysUser"),
  initUserPwd: makeApi("post", "/api/sysUser/init_password"),
  selectUserList: makeApi("get", "/api/sysUser/select_user_list"),

  getUserOrgsList: () => makeApi("get", "/api/sysOrganize/department")({ params: { state: 1, pageSize: 99999 } }),
  getUserRoles: makeApi("get", "/api/sysUserRoleR/user_role_list"),

  //所有用户
  getAllUserList: makeApi("get", "/api/sysUser/all_user_list"),

  // 日志管理
  getLoginLogList: makeApi("get", "/api/sysLoginlog"),
  getActionsLogList: makeApi("get", "/api/sysOperlog"),

  // 字典管理
  getDictType: makeApi("get", `/api/sysDictionarytype`),
  addDictType: makeApi("post", `/api/sysDictionarytype`),
  putDictType: makeApi("put", `/api/sysDictionarytype`),
  delDictType: makeApi("delete", `/api/sysDictionarytype`),

  getDictData: makeApi("get", `/api/sysDictionarydata`),
  addDictData: makeApi("post", `/api/sysDictionarydata`),
  putDictData: makeApi("put", `/api/sysDictionarydata`),
  delDictData: makeApi("delete", `/api/sysDictionarydata`),
  defaultDictData: makeApi("post", `/api/sysDictionarydata/set_default`),

  getDictDataByCode: makeApi("get", `/api/sysDictionarydata/query_dictionarydata_list`),
};

export default UserApi;
