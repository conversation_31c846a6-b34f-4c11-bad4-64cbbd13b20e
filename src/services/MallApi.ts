import { makeApi } from "@/utils/Api";

const MallApi = {
  // 门店管理
  getShopList: makeApi("get", "/api/mallShop"),
  getShop: makeApi("get", `/api/mallShop`, true),
  addShop: makeApi("post", `/api/mallShop`),
  putShop: makeApi("put", `/api/mallShop`),
  delShop: makeApi("delete", `/api/mallShop`),
  defaultServiceOrderShop: makeApi("post", `/api/mallShop/service_order_set_default`),
  defaultDeliveryOrderShop: makeApi("post", `/api/mallShop/delivery_order_set_default`),

  // 系统项目分类
  getSysProductTypeList: makeApi("get", `/api/mallProductCategory/system`),
  getSysProductType: makeApi("get", `/api/mallProductCategory/system`, true),
  addSysProductType: makeApi("post", `/api/mallProductCategory/system`),
  putSysProductType: makeApi("put", `/api/mallProductCategory/system`),
  delSysProductType: makeApi("delete", `/api/mallProductCategory/system`),

  // 门店项目分类
  getShopProductTypeList: makeApi("get", `/api/mallProductCategory/shop`),
  getShopProductType: makeApi("get", `/api/mallProductCategory/shop`, true),
  addShopProductType: makeApi("post", `/api/mallProductCategory/shop`),
  putShopProductType: makeApi("put", `/api/mallProductCategory/shop`),
  delShopProductType: makeApi("delete", `/api/mallProductCategory/shop`),
  getShopProductTypeForSelect: makeApi("get", `/api/mallProductCategory/select_shop_product_category_list`),

  //系统优惠券
  getCouponsList: makeApi("get", "/api/mallCoupon/system"),
  getCoupons: makeApi("get", "/api/mallCoupon/system", true),
  addCoupons: makeApi("post", "/api/mallCoupon/system"),
  putCoupons: makeApi("put", "/api/mallCoupon/system"),
  delCoupons: makeApi("delete", "/api/mallCoupon/system"),

  //资质
  getQualificationsList: makeApi("get", "/api/platformQualifications"),
  addQualifications: makeApi("post", "/api/platformQualifications"),
  putQualifications: makeApi("put", "/api/platformQualifications"),
  delQualifications: makeApi("delete", "/api/platformQualifications"),

  // 门店优惠券
  getShopCouponList: makeApi("get", `/api/mallCoupon/shop`),
  getShopCoupon: makeApi("get", `/api/mallCoupon/shop`, true),
  addShopCoupon: makeApi("post", `/api/mallCoupon/shop`),
  putShopCoupon: makeApi("put", `/api/mallCoupon/shop`),
  delShopCoupon: makeApi("delete", `/api/mallCoupon/shop`),

  // 会员管理
  getMemberList: makeApi("get", `/api/mallShopVipUser`),
  getMember: makeApi("get", `/api/mallShopVipUser`, true),
  addMember: makeApi("post", `/api/mallShopVipUser`),
  putMember: makeApi("put", `/api/mallShopVipUser`),
  putMemberLv: makeApi("put", `/api/mallShopVipUser/modify_level`),
  delMember: makeApi("delete", `/api/mallShopVipUser`),
  getMemberTotal: makeApi("get", `/api/mallShopVipUser/query_money_total`),
  randomMemberNo: makeApi("get", `/api/mallShopVipUser/rand_card_no`),
  getMemberForSelect: makeApi("get", `/api/mallShopVipUser/search_a`),
  modifyVipContent: makeApi("put", `/api/mallShopVipUser/modify_content`),
  vipAccountCombo: makeApi("get", `/api/mallShopVipUser/account_combo`),
  vipAccountRecovery: makeApi("get", `/api/mallShopVipUser/account_recovery`),
  getMemberChild: makeApi("get", `/api/mallShopVipUser/query_child_list`),

  // 会员等级
  getMLevel: makeApi("get", `/api/mallShopVipLevel`),
  addMLevel: makeApi("post", `/api/mallShopVipLevel`),
  putMLevel: makeApi("put", `/api/mallShopVipLevel`),
  delMLevel: makeApi("delete", `/api/mallShopVipLevel`),
  expMLevel: makeApi("get", `/api/mallShopVipLevel/exportExcel`),
  getMLevelForSelect: makeApi("get", `/api/mallShopVipLevel/select_shop_vip_level_list`),
  gutMLevelRule: makeApi("get", `/api/mallShopVipLevel/query_rule`),
  putMLevelRule: makeApi("post", `/api/mallShopVipLevel/save_rule`),
  defaultMLevel: makeApi("post", `/api/mallShopVipLevel/set_default`),

  // 用户优惠券
  getUserCoupon: makeApi("get", `/api/appUserCoupon`),
  addUserCoupon: makeApi("post", `/api/appUserCoupon`),
  delUserCoupon: makeApi("delete", `/api/appUserCoupon`),

  // 用户 - 钱包记录
  getWalletLog: makeApi("get", `/api/appUserBalanceLog`),
  getWalletType: makeApi("get", `/api/appUserBalanceLog/query_money_type_list`),
  incWallet: makeApi("put", `/api/mallShopVipUser/balance_recharge`),
  decWallet: makeApi("put", `/api/mallShopVipUser/balance_reduce`),
  incIntegral: makeApi("put", `/api/mallShopVipUser/integral_increase`),
  decIntegral: makeApi("put", `/api/mallShopVipUser/integral_reduce`),

  // 用户 - 访问记录
  getUserAccessLog: makeApi("get", `/api/appUserAccessLog`),
  addUserAccessLog: makeApi("post", `/api/appUserAccessLog`),
  putUserAccessLog: makeApi("put", `/api/appUserAccessLog`),
  putUserAccessLog2: makeApi("post", `/api/appUserAccessLog/batch_modify_pre_employee`),
  putUserAccessLogPlan: makeApi("put", `/api/appUserAccessLog/modify_plan`),
  delUserAccessLog: makeApi("delete", `/api/appUserAccessLog`),
  cancelUserAccessLog: makeApi("put", `/api/appUserAccessLog/cancel_access`),
  completeUserAccessLog: makeApi("put", `/api/appUserAccessLog/complete_access`),
  addUserAccessLogPlan: makeApi("post", `/api/appUserAccessLog/add_plan`),

  // 订单相关
  getOrderListNew: makeApi("get", "/api/mallOrderInfo"), //获取订单列表
  getOrderDetailNew: makeApi("get", `/api/mallOrderInfo`, true),
  getOrderCouponNew: makeApi("post", `/api/mallOrderInfo/check_preferential`),
  getOrderTotalNew: makeApi("post", `/api/mallOrderInfo/check_order`),
  createOrderNew: makeApi("post", "/api/mallOrderInfo/create_order"),
  payOrderFreeNew: makeApi("post", `/api/mallOrderInfo/zeropay`),
  payOrderNew: makeApi("post", `/api/mallOrderInfo/balance_pay`),
  payOrderOfflineNew: makeApi("post", `/api/mallOrderInfo/cash_pay`),
  useOrderNew: makeApi("post", `/api/mallOrderInfo/use_order`),
  delOrderNew: makeApi("post", `/api/mallOrderInfo/delete_order`),
  closeOrderNew: makeApi("post", `/api/mallOrderInfo/close_order`),
  orderRefundNew: makeApi("post", "/api/mallOrderInfo/agree_refund"), //订单同意退款
  refundOrderNew: makeApi("post", `/api/mallOrderInfo/apply_refund`),
  takeOrderNew: makeApi("post", `/api/mallOrderInfo/take_order`),
  sendOrderNew: makeApi("post", `/api/mallOrderInfo/send_order`),
  pickUpOrderNew: makeApi("post", `/api/mallOrderInfo/pickup_order`),
  getOrderTotal: makeApi("get", `/api/mallOrderInfo/query_money_total`),
  getOrderRefundTotal: makeApi("get", `/api/mallOrderInfo/query_refund_money_total`),
  modifyOrderEvaluationShow: makeApi("post", "/api/mallOrderInfo/modify_evaluation_show_state"), //修改订单评论显示状态
  toggleOrderCostType: makeApi("post", `/api/mallOrderInfo/modify_bill_consume_type`),
  getFxLog: makeApi("get", `/api/mallCommissionLog`),

  // 订单物资
  getOrderListMatterNew: makeApi("get", `/api/mallOrderGoods`),
  getOrderListMatterByProject: makeApi("get", `/api/mallOrderGoods/query_goods_by_project`),

  //订单项目管理
  getproject: makeApi("get", "/api/mallOrderProject/query_user_project_list"),
  selectproject: makeApi("get", "/api/mallOrderProject/select_user_project_list"),
  reduceProject: makeApi("post", "/api/mallOrderProject/project_reduce"),

  // 预约记录
  getReservation: makeApi("get", `/api/appUserReservation`),
  getReservationInfo: makeApi("get", `/api/appUserReservation`, true),
  addReservation: makeApi("post", `/api/appUserReservation`),
  addReservationGroup: makeApi("post", `/api/appUserReservation/batch_add`),
  putReservation: makeApi("put", `/api/appUserReservation`),
  delReservation: makeApi("delete", `/api/appUserReservation`),
  getReservationStat: makeApi("get", `/api/appUserReservation/search_count`),
  daoDianReservation: makeApi("put", `/api/appUserReservation/modify_state`),
  relateReservation: makeApi("post", `/api/appUserReservation/relate`),
  confirmReservation: makeApi("put", `/api/appUserReservation/confirm`),

  // 广告
  getAds: makeApi("get", `/api/mallIndexAdv`),
  addAds: makeApi("post", `/api/mallIndexAdv`),
  putAds: makeApi("put", `/api/mallIndexAdv`),
  delAds: makeApi("delete", `/api/mallIndexAdv`),

  // 治疗记录
  getServiceOrder: makeApi("get", `/api/mallServiceOrder`),
  addServiceOrder: makeApi("post", `/api/mallServiceOrder/create`),
  addServiceOrderV2: makeApi("post", `/api/mallServiceOrder/create_new`),
  putServiceOrder: makeApi("put", `/api/mallServiceOrder`),
  delServiceOrder: makeApi("post", `/api/mallServiceOrder/delete_order`),
  delServiceOrderv2: makeApi("post", `/api/mallServiceOrder/delete_order_new`),
  putServiceOrderFinish: makeApi("post", `/api/mallServiceOrder/finish_service`),
  putServiceOrderStart: makeApi("put", `/api/mallServiceOrder/start_service`),
  putServiceOrderModifyContent: makeApi("post", `/api/mallServiceOrder/modify_service_content`),
  addServiceOrderRemark: makeApi("post", `/api/mallServiceOrder/service_remark`),
  getServiceOrderForSelect: makeApi("get", `/api/mallServiceOrder/select_service_order_list`),
  getServiceOrderMatter: makeApi("get", `/api/mallServiceOrderGoods`),
  getServiceOrderMatterByService: makeApi("get", `/api/mallServiceOrderGoods/query_goods_by_service`),
  getServiceOrderHuakou: makeApi("put", `/api/mallServiceOrder/deduction_service`),
  getServiceOrderEvaluation: makeApi("get", `/api/mallServiceOrder/query_evaluation_list`),
  getServiceOrderEvaluationTotal: makeApi("get", `/api/mallServiceOrder/query_evaluation_total`),
  getServiceOrderRule: makeApi("get", `/api/mallServiceOrder/query_rule`),
  putServiceOrderRule: makeApi("post", `/api/mallServiceOrder/save_rule`),

  // 门店员工
  getShopStaff: makeApi("get", `/api/mallShopEmployee`),
  addShopStaff: makeApi("post", `/api/mallShopEmployee`),
  delShopStaff: makeApi("delete", `/api/mallShopEmployee`),
  putShopStaff: makeApi("put", `/api/mallShopEmployee`),
  getShopStaffForSelect: makeApi("get", `/api/mallShopEmployee/select_employee_list`),
  getShopUserSelect: makeApi("get", `/api/mallShopEmployee/select_user_list`),

  // 门店投资人
  getShopInvestor: makeApi("get", `/api/mallShopInvestor`),
  addShopInvestor: makeApi("post", `/api/mallShopInvestor`),
  putShopInvestor: makeApi("put", `/api/mallShopInvestor`),
  delShopInvestor: makeApi("delete", `/api/mallShopInvestor`),

  // 选择门店
  getShopListForManage: makeApi("get", "/api/mallShop/query_manage_shop_list"),
  getShopListForSelect: makeApi("get", "/api/mallShop/select_shop_list"),
  setCurrentShop: makeApi("post", `/api/mallShop/modify_current_shop`),

  // app用户
  getAppUser: makeApi("get", `/api/appUser`),
  putAppUser: makeApi("put", `/api/appUser`),
  getNewAppUserList: makeApi("get", `/api/appUser/query_online_new_user`),

  // 分诊用户
  getAppUserTriage: makeApi("get", `/api/appUserTriage`),
  delAppUserTriage: makeApi("delete", `/api/appUserTriage`),
  addAppUserTriage: makeApi("post", `/api/appUserTriage`),
  putAppUserTriage: makeApi("put", `/api/appUserTriage`),
  putAppUserTriageState: makeApi("put", `/api/appUserTriage/modify_state`),
  putAppUserTriageStateZixun: makeApi("put", `/api/appUserTriage/modify_consult_state`),
  putAppUserTriageStateMianzhen: makeApi("put", `/api/appUserTriage/modify_diagnosis_state`),
  cancelAppUserTriage: makeApi("put", `/api/appUserTriage/revoke`),
  getAppUserTriageStat: makeApi("get", `/api/appUserTriage/search_count`),

  // 咨询列表
  getConsultLog: makeApi("get", `/api/appUserConsultLog`),
  getConsultLogItem: makeApi("get", `/api/appUserConsultLog`, true),
  addConsultLog: makeApi("post", `/api/appUserConsultLog`),
  putConsultLog: makeApi("put", `/api/appUserConsultLog`),
  delConsultLog: makeApi("delete", `/api/appUserConsultLog`),

  // 治疗照
  getHealPhoto: makeApi("get", `/api/appUserServicePhoto`),
  addHealPhoto: makeApi("post", `/api/appUserServicePhoto`),
  putHealPhoto: makeApi("put", `/api/appUserServicePhoto`),
  delHealPhoto: makeApi("delete", `/api/appUserServicePhoto`),

  // 物资 - 分类管理
  getGoodsType: makeApi("get", `/api/mallGoodsCategory`),
  addGoodsType: makeApi("post", `/api/mallGoodsCategory`),
  putGoodsType: makeApi("put", `/api/mallGoodsCategory`),
  delGoodsType: makeApi("delete", `/api/mallGoodsCategory`),
  getGoodsTypeForSelect: makeApi("get", `/api/mallGoodsCategory/select_goods_category`),

  // 物资 - 管理
  getGoods: makeApi("get", `/api/mallGoods`),
  addGoods: makeApi("post", `/api/mallGoods`),
  putGoods: makeApi("put", `/api/mallGoods`),
  delGoods: makeApi("delete", `/api/mallGoods`),
  getGoodsForPicker: makeApi("get", `/api/mallGoods/select_goods_list`),

  // 物资 - 库存
  getGoodsStock: makeApi("get", `/api/mallGoodsInventory`),
  putGoodsInOut: makeApi("post", `/api/mallGoodsBill`),
  getGoodsStockLogDetail: makeApi("get", `/api/mallGoodsBillDetail`),
  getGoodsStockLogs: makeApi("get", `/api/mallGoodsBill`),

  // 卡类型
  getCardType: makeApi("get", `/api/mallCardType`),
  getCardTypeDetail: makeApi("get", `/api/mallCardType`, true),
  addCardType: makeApi("post", `/api/mallCardType`),
  putCardType: makeApi("put", `/api/mallCardType`),
  delCardType: makeApi("delete", `/api/mallCardType`),

  // 卡操作单
  getCardBill: makeApi("get", `/api/mallCardBill`),
  getCardBillDetail: makeApi("get", `/api/mallCardBillDetail`),

  // 卡号管理
  getCardNo: makeApi("get", `/api/mallCardNo`),
  addCardNo: makeApi("post", `/api/mallCardNo/add`),
  delCardNo: makeApi("post", `/api/mallCardNo/abolish`),
  outCardNo: makeApi("post", `/api/mallCardNo/out_bound`),
  useCardNo: makeApi("post", `/api/mallCardNo/use_card`),
  getCardNoDetail: makeApi("get", `/api/mallCardNo/query_one`),
  getCardNoSelf: makeApi("get", `/api/mallCardNo/query_my_shop`),

  // 提现管理
  getWithdrawList: makeApi("get", `/api/accountWithdraw`),
  getWithdrawTotal: makeApi("get", `/api/accountWithdraw/query_money_total`),
  agreeWithdraw: makeApi("put", `/api/accountWithdraw/agree`),
  refuseWithdraw: makeApi("put", `/api/accountWithdraw/refuse`),

  getWithdrawRule: makeApi("get", `/api/accountWithdraw/query_rule`),
  putWithdrawRule: makeApi("post", `/api/accountWithdraw/save_rule`),

  // 会员备注列表
  getNote: makeApi("get", `/api/appUserNote`),
  getNoteItem: makeApi("get", `/api/appUserNote`, true),
  addNote: makeApi("post", `/api/appUserNote`),
  putNote: makeApi("put", `/api/appUserNote`),
  delNote: makeApi("delete", `/api/appUserNote`),

  // 记账本
  getCashBook: makeApi("get", `/api/platformCashbook`),
  getCashBookItem: makeApi("get", `/api/platformCashbook`, true),
  addCashBook: makeApi("post", `/api/platformCashbook`),
  putCashBook: makeApi("put", `/api/platformCashbook`),
  delCashBook: makeApi("delete", `/api/platformCashbook`),
  getCashBookStat: makeApi("get", `/api/platformCashbook/query_total`),
  getCashBookUserSelect: makeApi("get", `/api/platformCashbook/select_user_list`),

  // 海报管理
  getPoster: makeApi("get", `/api/appPosters`),
  addPoster: makeApi("post", `/api/appPosters`),
  putPoster: makeApi("put", `/api/appPosters`),
  delPoster: makeApi("delete", `/api/appPosters`),

  // 选择集卡
  getCollectCard: makeApi("get", `/api/mallCollectCardType`),

  // 选择回访模板
  getAccessTempList: makeApi("get", `/api/appUserAccessTemplate/select_access_template`),
  getAccessTempInfo: makeApi("get", `/api/appUserAccessTemplate`, true),
  getAccessTempDef: makeApi("get", `/api/appUserAccessTemplate/get_template_by_product`),
  getAccessTempPlan: makeApi("get", `/api/appUserAccessTemplate/get_plan_by_template`),

  // 打印
  printOrder: makeApi("post", `/api/mallOrderInfo/pre_print`),
};

export default MallApi;
