import { DownloadOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, ButtonProps, DatePicker, Form, FormItemProps, Input, InputProps, Select, SelectProps } from "antd";
import { RangePickerProps } from "antd/es/date-picker";

export const AddBtn = (props: ButtonProps) => {
  return <Button icon={<PlusOutlined />} type="primary" children={"新增"} {...props} />;
};

export const ExportBtn = (props: ButtonProps) => {
  return <Button icon={<DownloadOutlined />} type="primary" children={"导出"} {...props} />;
};

export const SearchItemInput = (props: {
  label?: FormItemProps["label"];
  name?: FormItemProps["name"];
  initialValue?: FormItemProps["initialValue"];
  placeholder?: string;

  itemProps?: FormItemProps;
  fieldProps?: InputProps;
}) => {
  return (
    <Form.Item label={props.label} name={props.name} initialValue={props.initialValue} {...props.itemProps}>
      <Input placeholder={props.placeholder} {...props.fieldProps} />
    </Form.Item>
  );
};

export const SearchItemSelect = (props: {
  label?: FormItemProps["label"];
  name?: FormItemProps["name"];
  initialValue?: FormItemProps["initialValue"];
  placeholder?: string;
  options?: SelectProps["options"] | any[];

  itemProps?: FormItemProps;
  fieldProps?: SelectProps;
}) => {
  return (
    <Form.Item label={props.label} name={props.name} initialValue={props.initialValue} {...props.itemProps}>
      <Select placeholder={props.placeholder} options={props.options} {...props.fieldProps} />
    </Form.Item>
  );
};

export const SearchItemDateRange = (props: {
  label?: FormItemProps["label"];
  name?: FormItemProps["name"];
  initialValue?: FormItemProps["initialValue"];

  itemProps?: FormItemProps;
  fieldProps?: RangePickerProps;
}) => {
  return (
    <Form.Item label={props.label} name={props.name} initialValue={props.initialValue ?? []} {...props.itemProps}>
      <DatePicker.RangePicker {...props.fieldProps} />
    </Form.Item>
  );
};
