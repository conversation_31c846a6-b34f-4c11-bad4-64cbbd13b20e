import { <PERSON><PERSON>, Card, Col, Form, FormInstance, Row, Space, Table, TableColumnType, TableProps } from "antd";
import { forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import { useMount, useSetState, useUpdateEffect } from "react-use";
import { AddBtn, ExportBtn, SearchItemInput, SearchItemSelect, SearchItemDateRange } from "./Extra";
import api from "@/utils/Api";
import { formatDateRange, useDebounceFn } from "@/utils/Tools";
import { ClearOutlined, DownOutlined, SearchOutlined, UpOutlined } from "@ant-design/icons";
import Style from "./index.module.scss";

interface WeTableProps {
  request: (params: any) => Promise<any>;
  columns?: (TableColumnType<any> & { hide?: boolean })[];
  params?: any;
  paramsFormat?: (params: any) => any;
  search?: any[];
  searchNum?: number;
  size?: number;
  title?: any;
  extra?: any;
  tableProps?: TableProps<any>;
  autoLoad?: boolean;
  export?: string;
  defaultSort?: { type: any; key: any };
  className?: string;
}

export interface WeTableRef {
  form: FormInstance;
  getList: (page: number) => any;
  reload: Function;
  reset: Function;
  getParams: Function;
}

const WeTableFunc = forwardRef<WeTableRef, WeTableProps>((props, ref) => {
  const [form] = Form.useForm();
  const times = useRef<number>(1);
  const [state, setState] = useSetState({
    list: [] as any[],
    page: 1,
    size: 20,
    total: 0,
    loading: false,

    sortType: props.defaultSort?.type as any,
    sortKey: props.defaultSort?.key as any,

    params: {} as any,

    fold: true,
  });

  useEffect(() => {
    if ((props.autoLoad && times.current > 1) || (!props.autoLoad && times.current > 2)) {
      fetchList();
    }

    times.current = times.current + 1;
  }, [state.params, state.page, state.size, state.sortKey, state.sortType]);

  useUpdateEffect(() => {
    handleReset();
  }, [JSON.stringify(props.params)]);

  useMount(async () => {
    const params = await getFormParams();
    setState({ params });
  });

  const getFormParams = async () => {
    const data = await form.validateFields();
    Object.keys(data)
      .filter((key) => /^[A-Z]/.test(key))
      .forEach((key) => {
        const value = data[key] || [];
        const range = formatDateRange(value);
        data["start" + key] = range.start;
        data["end" + key] = range.end;
        delete data[key];
      });

    return data;
  };

  const getFullParams = () => {
    return props.paramsFormat!({
      sortType: state.sortType,
      sortKey: state.sortKey,
      pageNum: state.page,
      pageSize: props.size ?? state.size,
      ...props.params,
      ...state.params,
    });
  };

  const fetchList = async () => {
    setState({ loading: true });
    const params = getFullParams();
    const res = await props.request(params).finally(() => setState({ loading: false }));
    setState({ list: res?.list || [], total: res?.total });
  };

  const handleReset = async () => {
    form.resetFields();
    handleSubmit();
  };

  const handleSubmit = async () => {
    const params = await getFormParams();
    setState({ params, page: 1 });
  };

  useImperativeHandle(ref, () => {
    return {
      form,
      getList: fetchList,
      reload: (page: number) => setState({ page: page ?? state.page, params: { ...state.params } }),
      reset: () => handleReset(),
      getParams: getFullParams,
    };
  });

  const serach: any[] = props.search?.filter((n) => !!n) ?? [];
  const searchNum = props.searchNum ?? 3;

  const __hanleValueUpdate__ = useDebounceFn(async () => {
    const params = await getFormParams();
    setState({ params, page: 1 });
  }, 500);

  return (
    <div className={props.className}>
      {!!serach?.length && (
        <Card
          size="small"
          style={{ marginBottom: 10 }}
          styles={{ body: { paddingBottom: 0 } }}
          className={Style.search}
        >
          <Form form={form} onValuesChange={__hanleValueUpdate__}>
            <Row gutter={10}>
              <Col flex="1">
                <Row gutter={10}>
                  {serach
                    .filter((_, idx) => !(state.fold && idx >= searchNum))
                    .map((item, idx) => (
                      <Col span={24 / searchNum} key={idx}>
                        {item}
                      </Col>
                    ))}
                </Row>
              </Col>
              <Col>
                <div>
                  <Space>
                    {serach.length > searchNum && (
                      <Button
                        type="dashed"
                        icon={state.fold ? <DownOutlined /> : <UpOutlined />}
                        onClick={() => setState({ fold: !state.fold })}
                      />
                    )}
                    <Button type="primary" icon={<SearchOutlined />} onClick={handleSubmit}>
                      搜索
                    </Button>
                    <Button type="default" icon={<ClearOutlined />} onClick={handleReset} />
                  </Space>
                </div>
              </Col>
            </Row>
          </Form>
        </Card>
      )}

      <Card
        // bodyStyle={{ padding: 0 }}
        styles={{ body: { padding: 0 } }}
        extra={props.extra}
        title={
          (props.title || props.export) && (
            <Space>
              {props.title}
              {!!props.export && (
                <>
                  <ExportBtn
                    onClick={() => {
                      const link = document.createElement("a");
                      link.href = api.getUri({
                        method: "get",
                        url: props.export,
                        params: {
                          token: localStorage.getItem("token") || "",
                          ...getFullParams(),
                        },
                      });
                      link.setAttribute("download", "");
                      link.click();
                    }}
                  />
                </>
              )}
            </Space>
          )
        }
      >
        <Table
          rowKey="id"
          loading={{ spinning: state.loading, delay: 300 }}
          dataSource={state.list}
          columns={props.columns?.filter((n) => !n.hide)}
          pagination={{
            style: { paddingRight: 10 },
            current: state.page,
            pageSize: props.size ?? state.size,
            total: state.total,
            showSizeChanger: !props.size,
            showTotal: (total) => <div>共 {total} 条数据</div>,
            pageSizeOptions: [5, 10, 20, 50, 100],
          }}
          onChange={(pag, _, sort: any) => {
            const page = pag.current;
            const size = pag.pageSize;
            const sortKey = sort.field;
            const sortType = sort.order;

            setState({ sortKey, sortType, page, size });
          }}
          {...props.tableProps}
        />
      </Card>
    </div>
  );
});

WeTableFunc.defaultProps = {
  search: [],
  columns: [],
  autoLoad: true,
  paramsFormat: (p) => p,
};

const WeTable = Object.assign(WeTableFunc, {
  AddBtn,
  ExportBtn,
  SearchItemInput,
  SearchItemSelect,
  SearchItemDateRange,
});

export default WeTable;
