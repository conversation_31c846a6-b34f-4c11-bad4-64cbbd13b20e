import UserApi from "@/services/UserApi";
import { Cascader, CascaderProps } from "antd";
import { useMount, useSetState } from "react-use";

const HangyePicker = (props: CascaderProps) => {
  const [state, setState] = useSetState({
    hangye: [] as any[],
  });

  const fetchHangye = async () => {
    const params = { typeEnCode: "IndustryType" };
    const res: any[] = (await UserApi.getDictByCode({ params })) || [];

    const arr: any[] = [];
    const map: any = {};

    res.forEach((item) => {
      const rect = { ...item };
      map[item.id] = rect;
      const parent = map[item.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(rect);
      } else {
        arr.push(rect);
      }
    });

    setState({ hangye: arr });
  };

  useMount(() => {
    fetchHangye();
  });

  return (
    <Cascader
      placeholder="请选择行业"
      options={state.hangye}
      fieldNames={{ label: "name", value: "id" }}
      changeOnSelect
      {...(props as any)}
    />
  );
};

export default HangyePicker;
