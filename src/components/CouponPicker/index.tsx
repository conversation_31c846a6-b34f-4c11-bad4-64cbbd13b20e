import MallApi from "@/services/MallApi";
import WeModal from "../WeModal/WeModal";
import WeTable from "../WeTable";
import dayjs from "dayjs";
import { Form, Input, Radio } from "antd";
import { useState } from "react";

export const CouponType = [
  { id: 10, name: "满减券" },
  { id: 20, name: "折扣券" },
];

export default function CouponPicker(props: {
  params?: any;
  children?: any;
  title?: any;
  max: number;
  onOk?: (list: any[]) => any;
}) {
  const [state, setState] = useState({
    list: [] as any[],
  });

  const max = props.max ?? 0;

  return (
    <WeModal
      trigger={props.children}
      title={props.title ?? "选择优惠券"}
      width={900}
      onOpen={() => setState({ list: [] })}
      onOk={async () => props.onOk?.(state.list)}
    >
      <WeTable
        size={10}
        params={props.params}
        tableProps={{
          size: "small",
          rowSelection: {
            type: max == 1 ? "radio" : "checkbox",
            selectedRowKeys: state.list.map((n) => n.id),
            preserveSelectedRowKeys: true,
            onChange: (_, r) => {
              let list = r;

              if (max > 1) {
                list = list.slice(0, max);
              }

              if (max == 1) {
                list = r;
              }

              setState({ list });
            },
          },
          onRow: (r) => ({
            onClick: () => {
              let list = state.list.filter((n) => n.id != r.id);

              if (list.length == state.list.length) {
                list.push(r);
              }

              if (max > 1) {
                list = list.slice(0, max);
              }

              if (max == 1) {
                list = [r];
              }

              setState({ list });
            },
          }),
        }}
        request={(p) => MallApi.getShopCouponList({ params: p })}
        search={[
          <Form.Item label="类型" name="type" initialValue={""}>
            <Radio.Group
              options={[{ id: "", name: "全部" }, ...CouponType].map((n) => ({ label: n.name, value: n.id }))}
              optionType="button"
              buttonStyle="solid"
            />
          </Form.Item>,
          <Form.Item label="名称" name="name">
            <Input placeholder="请输入" allowClear />
          </Form.Item>,
        ]}
        searchNum={2}
        columns={[
          { title: "名称", dataIndex: "name" },
          { title: "优惠方式", dataIndex: "type", render: (c) => CouponType.find((n) => n.id == c)?.name ?? "--" },
          {
            title: "优惠内容",
            render: (c) => (
              <>
                {c.type == 10 && `${c.preferentialMoney} 元`} {c.type == 20 && `${c.discount} 折`}
              </>
            ),
          },
          {
            title: "消费门槛",
            dataIndex: "minConsumeMoney",
            render: (c) => <>{c ? `${c} 元` : "无门槛"}</>,
          },
          {
            title: "失效方式",
            render: (c) => (
              <>
                {c.effectiveStyle == 10 && `领取后${c.effectiveDeferredDays}天生效，有效期${c.effectiveDays}天`}
                {c.effectiveStyle == 20 &&
                  `从 ${dayjs(c.effectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.effectiveEndDate).format(
                    "YYYY-MM-DD"
                  )}`}
              </>
            ),
          },
        ]}
      />
    </WeModal>
  );
}
