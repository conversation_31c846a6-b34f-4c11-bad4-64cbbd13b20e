import { useDebounceFn } from "@/utils/Tools";
import { AutoComplete } from "antd";
import { useRef } from "react";
import { useEffectOnce, useSetState, useUpdateEffect } from "react-use";

const TMap = (window as any).TMap;

const servicesk = "qP6opsvzMATfQrSGAsswkWPHFvfKwgR4";
export const QQMapPicker = (props: { value?: any; onChange?: (e: any) => any; address?: boolean }) => {
  const divRef = useRef(null);
  const mapRef = useRef<any>({});
  const [state, setState] = useSetState({
    options: [] as any[],
    pos: null as any,
  });

  const value = props.value || state.pos;

  useUpdateEffect(() => {
    if (value && value.lat && value.lng) {
      const marker = mapRef.current.marker;
      const pos = new TMap.LatLng(value.lat, value.lng);
      marker?.updateGeometries([{ id: "point", position: pos }]);

      const map = mapRef.current.map;
      map?.panTo(pos);
    }
  }, [value]);

  useEffectOnce(() => {
    // 处理没有lat、lng的情况，设置默认值
    const pos = (value && value.lat && value.lng) ? value : { lat: 29.563, lng: 106.551 };
    const center = new TMap.LatLng(pos.lat, pos.lng);
    const map = new TMap.Map(divRef.current, { center, zoom: 14 });

    map.removeControl(TMap.constants.DEFAULT_CONTROL_ID.ROTATION);

    const marker = new TMap.MultiMarker({ map, geometries: [] });

    if (value && value.lat && value.lng) {
      const pos = new TMap.LatLng(value.lat, value.lng);
      marker.updateGeometries([{ id: "point", position: pos }]);
    }

    map.on("click", async (e: any) => {
      const pos = e.latLng;
      const data: any = { lat: pos.lat, lng: pos.lng };

      if (props.address) {
        const geo = new TMap.service.Geocoder();
        const res = await geo.getAddress({ location: pos, servicesk });
        const addr = res?.result?.formatted_addresses?.recommend || "";
        data.address = addr;
      }

      setState({ pos: data });
      props.onChange?.(data);
    });

    mapRef.current.map = map;
    mapRef.current.marker = marker;

    return () => {
      map.destroy();
      mapRef.current = {};
    };
  });

  const onSearch = useDebounceFn(async (keyword: any) => {
    if (!keyword) return setState({ options: [] });

    const search = new TMap.service.Suggestion({ pageSize: 6, regionFix: false });
    const res = await search.getSuggestions({ keyword, servicesk });
    const options =
      res?.data?.map((n: any) => ({
        key: n.id,
        label: (
          <div className="flex items-center" key={n.id}>
            <div className="flex-1 min-w-0 line-clamp-1">{n.title}</div>
            <div className="text-[.9em] text-#999 ml-10px">{n.province}</div>
          </div>
        ),
        value: n.title,
        pos: n.location,
      })) || [];
    setState({ options });
  }, 800);

  const onSelect = (_: any, opt: any) => {
    const pos = opt.pos;
    const map = mapRef.current?.map;
    map?.setCenter(pos);

    setState({ pos });
    props.onChange?.(pos);
  };

  return (
    <div>
      <div className="pos-relative w-800px h-400px">
        <div ref={divRef} className="size-full"></div>
        <div className="z-1001 pos-absolute top-10px left-10px">
          <AutoComplete className="!w-300px" placeholder="请输入关键字" options={state.options} onSearch={onSearch} onSelect={onSelect} allowClear />
        </div>
      </div>
    </div>
  );
};