import { Switch, SwitchProps } from "antd";
import { FC } from "react";
import { useSetState } from "react-use";

interface Props extends SwitchProps {
  onClick?: () => Promise<any>;
}

const AsyncSwitch: FC<Props> = (props) => {
  const [state, setState] = useSetState({ loading: false });
  const { onClick, ...rect } = props;

  const handleClick = async () => {
    setState({ loading: true });
    await onClick?.().finally(() => setState({ loading: false }));
  };

  return <Switch loading={state.loading} size="small" {...rect} onClick={handleClick} />;
};

export default AsyncSwitch;
