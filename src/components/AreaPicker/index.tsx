import UserApi from "@/services/UserApi";
import { Cascader, CascaderProps } from "antd";
import { useMount, useSetState } from "react-use";

declare global {
  interface Window {
    _area: any[];
  }
}

window._area = [];

const AreaPicker = (props: CascaderProps) => {
  const [state, setState] = useSetState({
    options: [] as any[],
  });

  const genTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];
    list = list || [];
    list.forEach((item) => {
      map[item.id] = item;
    });
    list.forEach((item) => {
      const parent = map[item.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });
    return arr;
  };

  const fetchData = async () => {
    let options = window._area;

    if (options.length <= 0) {
      const params = { typeMin: 2, typeMax: 4 };
      const res = await UserApi.getAreaCode({ params });
      options = window._area = genTree(res?.list);
    }

    setState({ options });
  };

  useMount(() => {
    fetchData();
  });

  return (
    <Cascader
      options={state.options}
      fieldNames={{ label: "name", value: "id", children: "children" }}
      placeholder="请选择省市区"
      allowClear
      {...(props as any)}
    />
  );
};

AreaPicker.serializer = (obj: any) => {
  return [obj.provinceId, obj.cityId, obj.districtId];
};

AreaPicker.deserializer = (arr: any[] = []) => {
  return {
    provinceId: arr[0],
    cityId: arr[1],
    districtId: arr[2],
  };
};

export default AreaPicker;
