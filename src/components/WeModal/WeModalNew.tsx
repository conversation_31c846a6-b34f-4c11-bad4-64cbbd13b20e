import { Modal } from "antd";
import { cloneElement } from "react";
import { useSetState } from "react-use";

const WeModalV2 = (props: { children: any; trigger: any }) => {
  const [state, setState] = useSetState({
    open: false,
    loading: false,
  });

  const handleOk = async () => {};

  return (
    <>
      {cloneElement(props.trigger, { onClick: () => setState({ open: true }) })}
      <Modal
        destroyOnClose
        keyboard={false}
        maskClosable={false}
        open={state.open}
        confirmLoading={state.loading}
        onCancel={() => setState({ open: false })}
        onOk={handleOk}
      >
        {cloneElement(props.children, {})}
      </Modal>
    </>
  );
};

export default WeModalV2;
