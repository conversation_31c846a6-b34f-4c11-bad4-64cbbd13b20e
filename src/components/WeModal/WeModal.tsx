import { Modal, ModalProps } from "antd";
import { ReactElement, cloneElement, forwardRef, useImperativeHandle } from "react";
import { useSetState } from "react-use";

export interface WeModalProps extends ModalProps {
  trigger?: ReactElement;
  onOk?: (e?: any) => Promise<any>;
  onOpen?: Function;
  onOpenOnce?: Function;
}

export interface WeModalRef {
  open: boolean;
  close: Function;
}

const WeModal = forwardRef<any, WeModalProps>((props, ref) => {
  const [state, setState] = useSetState({
    open: false,
    loading: false,
    first: true,
  });

  const { trigger, onCancel, onOk, onOpen, onOpenOnce, ...rect } = props;

  const handleOpen = () => {
    onOpen?.();
    if (state.first) onOpenOnce?.();
    setState({ open: true, first: false });
  };

  const handleClose = (e: any) => {
    onCancel?.(e);
    setState({ open: false });
  };

  const handleOk = async (e: any) => {
    if (!onOk) return;
    setState({ loading: true });
    const res = await onOk(e).finally(() => setState({ loading: false }));

    if (res !== false) handleClose(e);
  };

  useImperativeHandle(
    ref,
    () => {
      return {
        open: state.open,
        close: () => handleClose({}),
      };
    },
    [state.open]
  );

  return (
    <>
      {trigger && cloneElement(trigger, { onClick: handleOpen })}
      <Modal keyboard={false} maskClosable={false} open={state.open} confirmLoading={state.loading} onOk={handleOk} onCancel={handleClose} destroyOnHidden {...rect} />
    </>
  );
});

export default WeModal;
