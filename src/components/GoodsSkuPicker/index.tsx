import { Form, Input, Radio, Table, Tooltip, Typography } from "antd";
import WeModal from "../WeModal/WeModal";
import { TypeTree } from "./TypeTree";
import { makeApi } from "@/utils/Api";
import { useSetState } from "react-use";
import { useDebounceFn } from "@/utils/Tools";

const fetchGoods = makeApi("get", `/api/v2/mallProduct/select_product_specification_list`);

export const GoodsSkuPicker = (props: {
  children?: any;
  max?: number;
  params?: any;
  hideProductModel?: boolean;
  header?: any;
  onOk?: (rows: any[]) => any;
}) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    list: [] as any[],
    page: 1,
    size: 10,
    total: 0,

    glist: [] as any[],
  });

  const fetchList = async (page: number) => {
    const data = await getParams();
    data.pageNum = page;
    data.pageSize = state.size;

    const res = await fetchGoods({ params: data });

    setState({
      list: res?.list || [],
      page: res?.pageNum,
      // size: res?.pageSize,
      total: res?.total,
      glist: [],
    });
  };

  const onOpen = async () => {
    form.resetFields();
    form.setFieldsValue(props.params);
    setState({ glist: [] });

    setTimeout(() => {
      fetchList(1);
    }, 1);
  };

  const getParams = async () => {
    const res = await form.validateFields();
    const data = { ...props.params, ...res };
    return data;
  };

  const onValChange = useDebounceFn(async () => {
    fetchList(1);
  }, 500);

  const onOk = async () => {
    props.onOk?.(state.glist);
  };

  return (
    <WeModal trigger={props.children} width={1200} title="选择商品" onOpen={onOpen} onOk={onOk}>
      <Form form={form} onValuesChange={onValChange}>
        <div className="mt-4">
          <div className="flex gap-4">

            <div className="flex-1 min-w-0 flex gap-4">
              {/* {!props.hideProductModel && ( */}
              <Form.Item name="productModel" initialValue={""}>
                <Radio.Group
                  disabled={!!props.hideProductModel}
                  className="w-200px"
                  options={[
                    { label: "全 部", value: "" },
                    { label: "单 品", value: 1 },
                    { label: "套 餐", value: 2 },
                  ]}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Form.Item>
              {/* )} */}
              <Form.Item name="name">
                <Input className="w-400px" placeholder="请输入搜索名称" allowClear />
              </Form.Item>
            </div>
            <Form.Item name="saleInAll" initialValue={0}>
              <Radio.Group
                //className="w-200px"
                options={[
                  { label: "显示上架商品", value: 0 },
                  { label: "显示所有商品", value: 1 },
                ]}
                optionType="button"
                buttonStyle="solid"
              />
            </Form.Item>
          </div>
          <div className="flex min-h-490px gap-4">
            <div className="w-200px b-(1 solid #ddd) rounded-4px py-10px px-6px">
              <Form.Item noStyle name="shopCategoryId">
                <TypeTree property={props.params?.property} />
              </Form.Item>
            </div>
            <div className="flex-1 min-w-0">
              <Table
                className="[&_.ant-table-row]:cursor-pointer"
                rowKey="productSpecificationId"
                size="small"
                scroll={{ x: "max-content" }}
                pagination={{
                  current: state.page,
                  pageSize: state.size,
                  total: state.total,
                  onChange: (n) => fetchList(n),
                  showTotal: (n) => `共${n}条数据`,
                }}
                onRow={() => {
                  return {
                    onClick: (evt) => {
                      evt.currentTarget.querySelector(".ant-checkbox-wrapper")?.click();
                    },
                  };
                }}
                rowSelection={{
                  preserveSelectedRowKeys: false,
                  selectedRowKeys: state.glist.map((n) => n.productSpecificationId),
                  onChange: (_, rows) => {
                    const max = Math.max(0, props.max ?? 0);
                    let glist: any = [];

                    if (!max) {
                      glist = rows;
                    } else if (max == 1) {
                      const last = rows[rows.length - 1];
                      glist = last ? [last] : [];
                    } else {
                      glist = rows.slice(0, max);
                    }

                    setState({ glist });
                  },
                }}
                dataSource={state.list}
                columns={[
                  {
                    title: "名称",
                    width: 300,
                    render: (item) => (
                      <Tooltip title={item?.productName + (item?.productSpecificationName && item?.productName !== item?.productSpecificationName ? " - " + item.productSpecificationName : "")} placement="topLeft">
                        <div className="line-clamp-1">
                          {item?.productName}
                          {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                            <span className="text-gray-400 ml-2px">
                              {" - " + item.productSpecificationName}
                            </span>
                          )}
                        </div>
                      </Tooltip>
                    )
                  },

                  {
                    title: "模式",
                    dataIndex: "productModel",
                    width: 50,
                    render: (c) => (
                      <>
                        {c == 1 && "单品"}
                        {c == 2 && "套餐"}
                      </>
                    ),
                  },
                  // {
                  //   title: "类型",
                  //   dataIndex: "property",
                  //   render: (c) => (c == 1 ? "门店项目" : "商城商品"),
                  // },
                  {
                    title: "次数",
                    dataIndex: "includeNum",
                    width: 50,
                    render: (c) => (c ? c + "次" : "--"),
                  },
                  {
                    title: "售价",
                    dataIndex: "salePrice",
                    width: 50,
                    render: (c) => (c ?? "--") + "元",
                  },
                  {
                    title: "库存",
                    width: 50,
                    render: (c) => (c.inventoryLimit == 1 ? c.inventoryCount : "无限"),
                  },
                  {
                    title: "分类",
                    dataIndex: "shopCategoryName",
                    width: 50,
                    render: (c) => (
                      <Typography.Text style={{ width: 50 }} ellipsis={{ tooltip: true }}>
                        {c}
                      </Typography.Text>
                    ),
                  },
                ]}
              />
            </div>
          </div>
        </div>
      </Form>
    </WeModal>
  );
};
