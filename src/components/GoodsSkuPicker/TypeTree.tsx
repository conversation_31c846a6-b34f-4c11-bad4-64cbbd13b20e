import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Tree } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";

export const TypeTree = (props: { property?: number; value?: any; onChange?: any }) => {
  const [state, setState] = useSetState({
    types: [] as any[],
  });

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};

    list.forEach((item) => {
      const newItem = { ...item };
      map[item.id] = newItem;
    });

    list.forEach((item) => {
      const newItem = map[item.id];
      const parent = map[newItem.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(newItem);
      } else {
        arr.push(newItem);
      }
    });

    return arr;
  };

  const fetchTypes = async () => {
    const params = { property: props.property, pageSize: 9999 };
    const res = await Mall<PERSON>pi.getShopProductTypeForSelect({ params });
    const types = genTree(res?.list || []);
    setState({ types });
  };

  useEffect(() => {
    fetchTypes();
  }, [props.property]);

  return (
    <Tree.DirectoryTree
      showIcon={false}
      treeData={state.types}
      fieldNames={{ title: "name", key: "id", children: "children" }}
      selectedKeys={[props.value]}
      onSelect={(e) => {
        const key = e[0];
        const val = props.value == key ? null : key;
        props.onChange?.(val);
      }}
    />
  );
};
