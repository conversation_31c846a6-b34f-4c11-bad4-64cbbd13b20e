import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Input, Modal, Table, Tree } from "antd";
import { cloneElement, useEffect } from "react";
import { useDebounce, useSetState } from "react-use";
import Style from "./index.module.scss";

interface ShopEmployeePickerProps {
  children: any;
  max?: number;
  params?: any;
  onSelect?: (rows: any[]) => any;
}

const ShopEmployeePicker = (props: ShopEmployeePickerProps) => {
  const [state, setState] = useSetState({
    open: false,

    name: "",
    nameDelay: "",

    shops: [] as any[],
    shopId: null as any,

    list: [] as any[],
    loading: false,
    total: 0,
    page: 1,
    size: 10,

    glist: [] as any[],
    // gkeys: [] as any,
  });

  useDebounce(() => setState({ nameDelay: state.name }), 600, [state.name]);

  useEffect(() => {
    if (!state.open) return;
    fetchShops();
    setState({ shopId: null });
  }, [state.open]);

  useEffect(() => {
    if (!state.open) return;
    setState({ glist: [] });
    fetchList();
  }, [state.shopId, state.nameDelay, state.open]);

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};

    list.forEach((item) => {
      const newItem = { ...item };
      map[item.id] = newItem;
    });

    list.forEach((item) => {
      const newItem = map[item.id];
      const parent = map[newItem.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(newItem);
      } else {
        arr.push(newItem);
      }
    });

    return arr;
  };

  const fetchShops = async () => {
    const params = { ...props.params, pageSize: 9999, businessMode: 1};
    const res = await MallApi.getShopListForSelect({ params });
    const shops = genTree(res?.list || []);
    setState({ shops });
  };

  const fetchList = async (page = 1) => {
    const params = {
      ...props.params,
      shopIds: state.shopId,
      pageSize: state.size,
      sysUserName: state.nameDelay,
      pageNum: page,
    };
    setState({ loading: false });
    const res = await MallApi.getShopStaffForSelect({ params }).finally(() => setState({ loading: false }));
    setState({ list: res?.list || [], page, total: res?.total || 0 });
  };

  const handleOk = () => {
    setState({ open: false });
    props.onSelect?.(state.glist);
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Modal
        open={state.open}
        closable
        width={1000}
        onCancel={() => setState({ open: false })}
        title={<>人员选择</>}
        keyboard={false}
        onOk={handleOk}
      >
        <div className={Style.head}>
          <Input
            placeholder="请输入姓名"
            style={{ width: 250 }}
            value={state.name}
            onChange={(e) => setState({ name: e.target.value })}
            allowClear
          />
        </div>
        <div className={Style.box}>
          <div className={Style.left}>
            <Tree.DirectoryTree
              showIcon={false}
              treeData={state.shops}
              fieldNames={{ title: "name", key: "id", children: "children" }}
              onSelect={(e) => {
                const key = e[0];
                setState({ shopId: key === state.shopId ? null : key });
              }}
              selectedKeys={[state.shopId]}
            />
          </div>
          <div className={Style.right}>
            <Table
              rowKey={`id`}
              size="small"
              dataSource={state.list}
              loading={{ spinning: state.loading, delay: 300 }}
              rowSelection={{
                preserveSelectedRowKeys: true,
                selectedRowKeys: state.glist.map((n) => n.id),
                onChange: (_, rows) => {
                  const max = props.max ?? 0;

                  if (!max) {
                    setState({ glist: rows });
                  } else if (max === 1) {
                    const last = rows[rows.length - 1];
                    setState({ glist: last ? [last] : [] });
                  } else {
                    setState({ glist: rows.slice(0, max) });
                  }
                },
              }}
              pagination={{
                current: state.page,
                pageSize: state.size,
                total: state.total,
                onChange: (n) => fetchList(n),
                showTotal: (n) => `共${n}条数据`,
              }}
              columns={[
                { title: "门店", dataIndex: "shopName" },
                { title: "姓名", render: (item) => item.user?.name },
              ]}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ShopEmployeePicker;
