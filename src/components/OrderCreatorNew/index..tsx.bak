import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Date<PERSON>icker,
  Divider,
  Form,
  Input,
  InputNumber,
  Row,
  Select,
  Switch,
  Table,
  Tag,
  Typography,
  message,
  Tooltip,
  TableColumnType,
} from "antd";
import { useEffect, useRef, useState } from "react";
import { useSetState } from "react-use";
import PickMember from "../PickMember";
import UserPane from "../Units/UserPane";
import GoodsPicker from "../GoodsPicker";
import Style from "./index.module.scss";
import MallApi from "@/services/MallApi";
import WeModal from "../WeModal/WeModal";
import PickCoupon from "./PickCoupon";
import PayOrderModal from "./PayOrderModal";
import dayjs from "dayjs";
import { QuestionCircleOutlined } from "@ant-design/icons";
import BtnModal from "./BtnModal";

const layout = { row: 10, col: 12 };

const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

const OrderCreatorNew = (props: { children: any; onOk?: Function; userId?: any; params?: any; property?: 1 | 2 }) => {
  const property = props.property || 1;
  const typeName = property === 2 ? "商品" : "项目";
  const reqCtrl1 = useRef<any>(null);
  const reqCtrl2 = useRef<any>(null);
  const [form] = Form.useForm();
  const [maxIntegration, setMaxIntegration] = useState(0); //最大积分
  const [isModalOpen, setIsModalOpen] = useState(false);
  const goodsListName = "goodsList";
  const [state, setState] = useSetState({
    user: null as any,

    coupon: null as any,

    cinfo: null as any,
    clist: [] as any[],
    total: null as any,
    totalLoading: false,

    staff: [] as any[],
    cusPay: 0 as any,
    // payType: -1,

    //===========
    payOpen: false,
    orderId: null,

    adjustment: undefined,
  });

  useEffect(() => {
    setState({ cusPay: state.total?.payMoney ?? 0 });
  }, [state.total?.payMoney]);

  // useEffect(() => {
  //   fetchTotal();
  // }, [state.cinfo]);

  useEffect(() => {
    if (state.user?.id) {
      fetchCoupons();
      form.setFieldValue(goodsListName, []);
    }
    form.setFieldsValue({
      adviserId: state?.user?.adviserId,
      //doctorId: state?.user?.doctorId,
      developerId: state?.user?.developerId,
    });
  }, [state.user]);

  useEffect(() => {
    if (state.user?.id) {
      fetchTotal(10);
    }
  }, [state.coupon]);

  useEffect(() => {
    if (state.adjustment) {
      fetchTotal(30);
    }
  }, [state.adjustment]);

  const handleReset = () => {
    form.resetFields();
    setState({
      coupon: null,
      cinfo: null,
      clist: [],
      total: null,
    });
    setMaxIntegration(0);
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  //计算优惠
  const fetchCoupons = async () => {
    const list = form.getFieldValue(goodsListName);

    if (!list.length) {
      handleReset();
      return;
    }

    let productList: any[] = [];
    list.map((item: any) => {
      productList.push({
        productId: item?.id,
        totalCount: item?.totalCount ?? 1,
      });
    });
    const user = state.user;
    const crtl = (reqCtrl1.current = new AbortController());
    const data = {
      shopVipUserId: user.id,
      productList: productList,
    };
    const res = await MallApi.getOrderCouponNew({ data, signal: crtl.signal });
    setMaxIntegration(res?.integralConvertMoney);
    form.setFieldValue("maxUseIntegral", res?.integralConvertMoney);
    setState({ clist: res?.couponList || [], cinfo: res });
  };

  //计算价格
  const fetchTotal = async (adjustType?: any) => {
    const list = form.getFieldValue(goodsListName);
    const val = form.getFieldsValue();

    if (!list.length) {
      handleReset();
      return;
    }
    let productList: any[] = [];
    list.map((item: any) => {
      productList.push({
        productId: item?.id,
        totalCount: item?.totalCount ?? 1,
        discount: item?.discount,
        lastMoney: item?.lastMoney,
      });
    });

    const user = state.user;
    const crtl = (reqCtrl2.current = new AbortController());
    const data = {
      shopVipUserId: user.id,
      couponId: state.coupon?.id,
      productList: productList,
      useIntegral: val.preferentialTag ? form.getFieldValue("maxUseIntegral") * 100 : undefined,
      useIntegralTag: val.preferentialTag ? 1 : 0,
      lastMoney: Number(state?.adjustment ?? 0),
      adjustType,
    };
    setState({ totalLoading: true });
    const res = await MallApi.getOrderTotalNew({
      data,
      signal: crtl.signal,
    }).finally(() => setState({ totalLoading: false }));
    setState({ total: res });

    let arr: any = [];
    list?.forEach((element: any) => {
      res?.productList?.forEach((item: any) => {
        if (element?.id == item?.productId) {
          arr.push({
            ...element,
            lastMoney: item?.lastMoney,
            discount: item?.discount,
            totalCount: item?.totalCount ?? 0,
          });
        }
      });
    });
    form.setFieldValue(goodsListName, arr);
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.userId);
    setState({ user: res });
  };

  //提交
  const handleSubmit = async () => {
    const list = form.getFieldValue(goodsListName);
    const val = form.getFieldsValue();
    if (!state.user) {
      message.error("请选择用户");
      return false;
    }
    if (!list.length) {
      message.error(`请选择${typeName}`);
      return false;
    }
    const fdate = (n: any, f: string) => (n ? dayjs(n).format(f) : "");
    const data = await form.validateFields();
    let productList: any[] = [];
    list.map((item: any) => {
      productList.push({
        productId: item?.id,
        totalCount: item?.totalCount ?? 1,
        discount: item?.discount,
        lastMoney: item?.lastMoney,
      });
    });
    data.shopVipUserId = state.user.id;
    data.couponId = state.coupon?.id;
    (data.useIntegralTag = form.getFieldValue("maxUseIntegral") ? 1 : 0), (data.custPayMoney = state.cusPay);
    data.useEffectiveStartDate = fdate(data.edate?.[0], "YYYY-MM-DD 00:00:00");
    data.useEffectiveEndDate = fdate(data.edate?.[1], "YYYY-MM-DD 23:59:59");
    delete data.edate;
    const ores = await MallApi.createOrderNew({
      data: {
        ...data,
        ...props.params,
        productList: productList,
        useIntegral: val.preferentialTag ? form.getFieldValue("maxUseIntegral") * 100 : undefined,
        useIntegralTag: val.preferentialTag ? 1 : 0,
        maxUseIntegral: undefined,
      },
    });
    setState({ payOpen: true, orderId: ores.id });
  };

  const handleOpen = () => {
    handleReset();
    setState({ user: null });
    fetchStaff();

    if (props.userId) {
      fetchUser();
    }
  };

  const calculate = (c: any, name: string, e: number | null) => {
    let arr: any[] = [];
    const list = form.getFieldValue(goodsListName);
    const id = form.getFieldValue([goodsListName, c.name, "id"]);

    list.map((item: any) => {
      if (id === item.id) {
        let arrList = item;
        if (name === "lastMoney") {
          arrList[name] = e;
        } else {
          arrList[name] = e;
          let lastm = item?.salePrice * (item?.totalCount ?? 1) * (item?.discount ? item?.discount / 10 : 0);
          arrList["lastMoney"] = lastm.toFixed(2);
        }
        arr.push(arrList);
      } else {
        arr.push(item);
      }
    });

    form.setFieldValue(goodsListName, arr);
    fetchTotal(20);
  };

  return (
    <>
      <WeModal
        trigger={props.children}
        width={800}
        title={
          <div style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}>
            <span style={{ marginRight: 10 }}>{typeName}开单</span>
            {!props.userId && <PickMember onChange={(user) => setState({ user })} style={{ width: 400 }} />}
          </div>
        }
        okButtonProps={{ disabled: state.totalLoading }}
        onOk={handleSubmit}
        onOpen={handleOpen}
        className={Style.mbox}
        okText="提交订单"
      >
        {state.user ? <UserPane user={state.user} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}

        <Form form={form} labelCol={{ flex: "80px" }}>
          <Divider orientation="left">{typeName}信息</Divider>
          <Row gutter={layout.row}>
            <Group style={{ marginTop: 10 }}>
              <Group>
                <Col span={24} style={{ marginTop: 10 }}>
                  <Form.List name={goodsListName} initialValue={[]}>
                    {(fields, action) => {
                      return (
                        <>
                          <Table
                            size="small"
                            locale={{ emptyText: "暂无数据" }}
                            pagination={false}
                            dataSource={fields}
                            columns={(
                              [
                                {
                                  title: "名称",
                                  width: 150,
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "name"]);
                                    return name;
                                  },
                                },
                                {
                                  key: "productModel",
                                  title: "模式",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "productModel"]);
                                    //return name == 2 ? "套餐" : "单品";
                                    if (name == 1) {
                                      return "项目单品";
                                    } else if (name == 2) {
                                      return "项目套餐";
                                    } else if (name == 11) {
                                      return "配送商品";
                                    } else if (name == 12) {
                                      return "服务商品";
                                    }
                                    return "--";
                                  },
                                },
                                {
                                  title: "售价",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "salePrice"]);
                                    return name;
                                  },
                                },
                                {
                                  key: "includeNum",
                                  title: "次数",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "includeNum"]);
                                    return `${name == 0 ? "--" : name} 次`;
                                  },
                                },
                                {
                                  title: "数量",
                                  width: 70,
                                  render: (c) => {
                                    return (
                                      <InputNumber
                                        min={1}
                                        // step={1}
                                        controls={false}
                                        style={{ width: "100%" }}
                                        defaultValue={1}
                                        onChange={(e) => {
                                          calculate(c, "totalCount", e);
                                        }}
                                      />
                                    );
                                  },
                                },
                                {
                                  title: (
                                    <>
                                      折扣
                                      <Tooltip title="0表示免费，10表示不打折。 ">
                                        <QuestionCircleOutlined
                                          style={{
                                            cursor: "pointer",
                                            marginLeft: 4,
                                          }}
                                        />
                                      </Tooltip>
                                    </>
                                  ),
                                  width: 100,
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "discount"]);
                                    return (
                                      <InputNumber
                                        style={{ width: "100%" }}
                                        min={0}
                                        step={0.1}
                                        max={10}
                                        value={name}
                                        onChange={(e: number | null) => {
                                          calculate(c, "discount", e);
                                        }}
                                      />
                                    );
                                  },
                                },
                                {
                                  title: "折后价",
                                  width: 130,
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "lastMoney"]);
                                    return (
                                      <InputNumber
                                        min={0}
                                        // step={1}
                                        style={{ width: "100%" }}
                                        defaultValue={name ?? 0}
                                        value={name}
                                        onChange={(e: number | null) => {
                                          calculate(c, "lastMoney", e || 0);
                                        }}
                                        precision={2}
                                      />
                                    );
                                  },
                                },
                                {
                                  title: "操作",
                                  render: (c) => {
                                    return (
                                      <a
                                        onClick={() => {
                                          action.remove(c.name);
                                          fetchCoupons();
                                          fetchTotal();
                                        }}
                                      >
                                        删除
                                      </a>
                                    );
                                  },
                                },
                              ] as TableColumnType<any>[]
                            ).filter((item) => {
                              if ((item.key === "productModel" || item.key === "includeNum") && props.property == 2)
                                return false;

                              return true;
                            })}
                          />
                          <div style={{ height: 20 }}></div>
                          <Row gutter={[20, 0]}>
                            <Col span={12}>
                              <GoodsPicker
                                property={property}
                                selected={form.getFieldValue(goodsListName)}
                                onSelect={(rows) => {
                                  let list: any[] = [];
                                  rows.forEach((item) => {
                                    list.push({
                                      name: item.name,
                                      productModel: item.productModel,
                                      includeNum: item.includeNum,
                                      salePrice: item.salePrice,
                                      id: item?.id,
                                    });
                                  });
                                  form.setFieldValue(goodsListName, list);
                                  fetchCoupons();
                                  fetchTotal();
                                }}
                              >
                                <Button
                                  // style={{ width: "60%" }}
                                  // type="dashed"
                                  // block
                                  // icon={<PlusOutlined />}
                                  disabled={!state.user}
                                >
                                  添加{typeName}
                                </Button>
                              </GoodsPicker>
                            </Col>
                            <Col span={12}>
                              <div style={{ float: "right", marginRight: "50px" }}>
                                <Button
                                  disabled={!(form.getFieldValue("goodsList")?.length > 0)}
                                  onClick={() => {
                                    setIsModalOpen(true);
                                  }}
                                >
                                  调整总金额
                                </Button>
                              </div>
                            </Col>
                          </Row>
                        </>
                      );
                    }}
                  </Form.List>
                </Col>
              </Group>
            </Group>
          </Row>

          <BtnModal isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} state={state} setState={setState} />

          <Divider orientation="left"></Divider>

          <Row gutter={layout.row} className={Style.sbox}>
            <Group>
              <Col span={layout.col}>
                <Form.Item label="商品总价"></Form.Item>
              </Col>
              <Col span={layout.col}>
                <div className={Style.rnum}>&yen;{state.total?.totalMoney ?? 0}</div>
              </Col>
            </Group>
            <Group>
              <Col span={layout.col}>
                <Form.Item label="优惠券">
                  {state.coupon ? (
                    <Tag closable onClose={() => setState({ coupon: null })}>
                      {state.coupon?.name}
                    </Tag>
                  ) : (
                    <PickCoupon list={state.clist} onOk={(e: any) => setState({ coupon: e })}>
                      <Typography.Link disabled={!state.clist.length}>选择({state.clist.length}张可用)</Typography.Link>
                    </PickCoupon>
                  )}
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <div className={Style.rnum}>- &yen;{state.total?.couponConvertMoney ?? 0}</div>
              </Col>
            </Group>

            <Group>
              <Col span={layout.col}>
                <Form.Item label="M币抵扣">
                  {state.cinfo?.maxUseIntegral ? (
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <div>
                        <Form.Item name={`preferentialTag`} noStyle>
                          <Switch
                            size="small"
                            onChange={(e) => {
                              if (e) {
                                form.setFieldValue("maxUseIntegral", maxIntegration);
                              } else {
                                form.setFieldValue("maxUseIntegral", 0);
                              }
                              fetchTotal(10);
                            }}
                          />
                        </Form.Item>
                      </div>
                      <div style={{ marginLeft: 5 }}>
                        <Form.Item shouldUpdate={(e, c) => e.preferentialTag !== c.preferentialTag} noStyle>
                          {(form) => {
                            let isShould = form?.getFieldValue("preferentialTag");
                            return (
                              isShould && (
                                <div style={{ display: "flex" }}>
                                  <Form.Item name="maxUseIntegral">
                                    <InputNumber
                                      step={1}
                                      min={1}
                                      max={maxIntegration}
                                      size="small"
                                      onChange={() => {
                                        fetchTotal(10);
                                      }}
                                    />
                                  </Form.Item>
                                </div>
                              )
                            );
                          }}
                        </Form.Item>
                      </div>
                      <div style={{ marginLeft: 5 }}>
                        <>
                          <div
                            style={{
                              marginLeft: 10,
                              color: "#666",
                              fontSize: 12,
                            }}
                          >
                            最大抵扣
                            {state.cinfo?.integralConvertMoney}元
                          </div>
                        </>
                      </div>
                    </div>
                  ) : (
                    <div style={{ marginLeft: 5 }}>
                      <div style={{ color: "#666", fontSize: 12 }}>无</div>
                    </div>
                  )}
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <div className={Style.rnum}>- &yen;{state.total?.integralConvertMoney ?? 0}</div>
              </Col>
            </Group>
          </Row>

          <div className={Style.tbox}>
            <div className={Style.item}>
              <div className={Style.name}>应收：</div>
              <div className={Style.value}>&yen;{state.total?.payMoney ?? 0}</div>
            </div>
            {/* <div className={Style.item}>
              <div className={Style.name}>实收：</div>
              <Input
                className={Style.ipt}
                size="small"
                value={state.cusPay}
                onChange={(e) => setState({ cusPay: e.target.value ?? 0 })}
                placeholder={state.total?.payMoney ?? 0}
              />
            </div> */}
          </div>

          <Divider orientation="left">订单信息</Divider>

          <Row gutter={layout.row} style={{ marginTop: 26 }}>
            {property == 1 && (
              <Col span={layout.col}>
                <Form.Item label="使用期限" name={`useEffectiveStyle`} initialValue={0}>
                  <Select
                    style={{ minWidth: 150 }}
                    options={[
                      { label: "永久有效", value: 0 },
                      { label: "固定期限", value: 20 },
                    ]}
                    placeholder="请选择使用期限"
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={layout.col}>
              <Form.Item noStyle dependencies={["useEffectiveStyle"]}>
                {(form) => {
                  const value = form.getFieldValue("useEffectiveStyle");
                  if (value == 20) {
                    return (
                      <Form.Item
                        label="有效时间"
                        name={`edate`}
                        rules={[{ required: true, message: "请选择有效时间" }]}
                        initialValue={[]}
                      >
                        <DatePicker.RangePicker
                          presets={[
                            {
                              label: "30天",
                              value: [dayjs(), dayjs().add(30, "day")],
                            },
                            {
                              label: "60天",
                              value: [dayjs(), dayjs().add(60, "day")],
                            },
                            {
                              label: "90天",
                              value: [dayjs(), dayjs().add(90, "day")],
                            },
                            {
                              label: "180天",
                              value: [dayjs(), dayjs().add(180, "day")],
                            },
                            {
                              label: "1年",
                              value: [dayjs(), dayjs().add(1, "year")],
                            },
                            {
                              label: "2年",
                              value: [dayjs(), dayjs().add(2, "year")],
                            },
                            {
                              label: "3年",
                              value: [dayjs(), dayjs().add(3, "year")],
                            },
                          ]}
                          style={{ width: "100%" }}
                        />
                      </Form.Item>
                    );
                  }
                  return null;
                }}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={layout.row}>
            <Col span={8}>
              <Form.Item label="所属客服" name={`adviserId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  placeholder="请选择所属客服"
                />
              </Form.Item>
            </Col>
            {/* <Col span={8}>
              <Form.Item label="所属医生" name={`doctorId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  placeholder="请选择所属医生"
                />
              </Form.Item>
            </Col> */}
            <Col span={8}>
              <Form.Item label="所属开发" name={`developerId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  placeholder="请选择所属开发"
                />
              </Form.Item>
            </Col>
            <Col span={layout.col}></Col>
            <Col span={layout.col * 2}>
              <Form.Item label="订单备注" name={`orderRemark`}>
                <Input.TextArea placeholder="请输入订单备注" autoSize={{ minRows: 1 }} maxLength={200} allowClear />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </WeModal>
      <PayOrderModal
        open={state.payOpen}
        onOpenChange={(payOpen) => setState({ payOpen })}
        orderId={state.orderId}
        onOk={props.onOk}
      />
    </>
  );
};

export default OrderCreatorNew;
