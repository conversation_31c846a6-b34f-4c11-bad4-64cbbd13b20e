import { Form, InputNumber } from "antd";
import WeModal from "../WeModal/WeModal";
import { useSetState } from "react-use";

export const CusPay = (props: { children: any; num?: any; onOK: any }) => {
  const [state, setState] = useSetState({ num: "" });

  return (
    <WeModal
      trigger={props.children}
      title={"调整总金额"}
      width={300}
      onOpen={() => {
        setState({ num: props.num });
      }}
      onOk={async () => {
        props.onOK?.(state.num);
      }}
    >
      <Form.Item>
        <InputNumber
          className="w-full"
          value={state.num}
          onChange={(e: any) => setState({ num: e })}
          placeholder={props.num ?? 0}
          precision={2}
        />
      </Form.Item>
    </WeModal>
  );
};
