import MallApi from "@/services/MallApi";
import { CheckOutlined, CreditCardOutlined, DollarOutlined, QrcodeOutlined, WalletOutlined } from "@ant-design/icons";
import { Alert, Descriptions, Divider, Modal, message } from "antd";
import { cloneElement, useEffect } from "react";
import { useSetState } from "react-use";
import Style from "./PayOrderModal.module.scss";
import WalletInc from "../UserManager/WalletInc";

const PayTypes = [
  { label: "钱包", value: -1, icon: <WalletOutlined /> },
  { label: "现金", value: 10, icon: <DollarOutlined /> },
  { label: "刷卡", value: 11, icon: <CreditCardOutlined /> },
  { label: "扫码", value: 12, icon: <QrcodeOutlined /> },
];

const PayOrderModal = (props: {
  open?: boolean;
  onOpenChange?: (open: boolean) => any;
  orderId: any;
  onOk?: Function;
}) => {
  // const orderId = "1180570258297548800";
  const [state, setState] = useSetState({
    payType: -1,
    user: null as any,
    order: null as any,
    paying: false,

    isMoneyEnough: true,
  });

  useEffect(() => {
    if (props.open && props.orderId) {
      setState({ user: null, order: null });
      fetchDetail(props.orderId);
    }
  }, [props.open, props.orderId]);

  const fetchDetail = async (orderId: any) => {
    const order = await MallApi.getOrderDetailNew(orderId);
    setState({ order });
    fetchUser(order?.shopVipUserId);
  };

  const fetchUser = async (userId: any) => {
    const user = await MallApi.getMember(userId);
    setState((prev) => ({ user, isMoneyEnough: user.balance >= prev.order.payMoney }));
  };

  const handlePay = async () => {
    if (!state.order) return;
    setState({ paying: true });

    // 0元购
    if (!state.order.payMoney) {
      const data = { orderId: state.order.id };
      await MallApi.payOrderFreeNew({ data }).finally(() => setState({ paying: false }));
      message.success("下单成功！");
    }
    // 钱包支付
    else if (state.payType === -1) {
      const data = { orderId: state.order.id };
      await MallApi.payOrderNew({ data }).finally(() => setState({ paying: false }));
      message.success("钱包支付成功！");
    }
    // 线下支付
    else {
      const data = { orderId: state.order.id, cashType: state.payType };
      await MallApi.payOrderOfflineNew({ data }).finally(() => setState({ paying: false }));
      message.success(`${PayTypes.find((n) => n.value == state.payType)?.label}支付成功`);
    }

    props.onOpenChange?.(false);
    props.onOk?.();
  };

  return (
    <Modal
      open={props.open}
      keyboard={false}
      width={600}
      title="订单收款"
      confirmLoading={state.paying}
      onOk={() => handlePay()}
      okText="确认收款"
      onCancel={() => {
        props.onOpenChange?.(false);
        props.onOk?.();
      }}
    >
      <Divider style={{ margin: "8px 0" }} plain orientation="left">
        订单编号：{state.order?.serialNo}
      </Divider>
      <Descriptions size="small" column={2}>
        <Descriptions.Item label="项目名称">{state.order?.productName}</Descriptions.Item>
        <Descriptions.Item label="合计数量">共 {state.order?.totalCount} 件</Descriptions.Item>
        <Descriptions.Item label="优惠券抵">
          {state.order?.couponConvertMoney ? -state.order?.couponConvertMoney + "元" : "--"}
        </Descriptions.Item>
        <Descriptions.Item label="M币抵扣">
          {state.order?.integralConvertMoney ? -state.order?.integralConvertMoney + "元" : "--"}
        </Descriptions.Item>
        <Descriptions.Item label="折扣优惠">
          {state.order?.discountPreferentialMoney ? -state.order?.discountPreferentialMoney + "元" : "--"}
        </Descriptions.Item>
        <Descriptions.Item label="总共优惠">
          <span style={{ color: "red", fontWeight: "bold" }}>
            {state.order?.preferentialMoney ? -state.order?.preferentialMoney + "元" : "--"}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="折后总价">{state.order?.lastTotalMoney}元</Descriptions.Item>
        <Descriptions.Item label="实收金额">
          <span style={{ color: "red", fontWeight: "bold" }}>{state.order?.payMoney}元</span>
        </Descriptions.Item>
      </Descriptions>

      <Divider style={{ margin: "8px 0" }} plain orientation="left">
        收款方式
      </Divider>
      <div className={Style.payTypes}>
        {PayTypes.map((item) => (
          <div
            className={`${Style.item} ${state.payType === item.value ? Style.on : ""}`}
            key={item.value}
            onClick={() => setState({ payType: item.value })}
          >
            {cloneElement(item.icon, { className: Style.ico })}
            <div className={Style.name}>{item.label}</div>
            <CheckOutlined className={Style.ckd} />
          </div>
        ))}
      </div>

      {state.payType === -1 && state.user && (
        <>
          <Alert
            showIcon
            type={state.isMoneyEnough ? "info" : "error"}
            message={<>用户钱包余额：&yen;{state.user?.balance}元</>}
            action={
              <WalletInc userId={state.order.shopVipUserId} onOk={() => fetchUser(state.order.shopVipUserId)}>
                <a>去充值 {state.isMoneyEnough ? "" : "(余额不足)"}</a>
              </WalletInc>
            }
          />
        </>
      )}

      {state.payType !== -1 && <Alert showIcon type="warning" message={<>请确认已经线下收款成功</>} />}
    </Modal>
  );
};

export default PayOrderModal;
