import WeModal from "@/components/WeModal/WeModal";
import { Button, Table } from "antd";
import dayjs from "dayjs";
import { useRef } from "react";

const PickCoupon = (props: { children: any; list: any[]; onOk: Function }) => {
  const modalRef = useRef<any>(null);

  return (
    <WeModal ref={modalRef} trigger={props.children} title="选择优惠券" width={600} footer={null}>
      <Table
        rowKey={"id"}
        dataSource={props.list}
        pagination={false}
        columns={[
          { title: "优惠券名称", dataIndex: "name" },
          { title: "门槛", dataIndex: "minConsumeMoney", render: (c) => (c ? "满" + c + "元" : "无门槛") },
          {
            title: "优惠金额",
            render: (c) => (
              <>
                {c.type == 10 && <>{c.preferentialMoney}元</>}
                {c.type == 20 && <>{c.discount}折</>}
              </>
            ),
          },
          {
            title: "过期时间",
            dataIndex: "clearEndDate",
            render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
          },
          {
            title: "操作",
            render: (item, rect, val) => (
              <>
                <Button
                  size="small"
                  onClick={() => {
                    props.onOk?.(item, rect, val);
                    modalRef.current?.close();
                  }}
                >
                  选择
                </Button>
              </>
            ),
          },
        ]}
      />
    </WeModal>
  );
};

export default PickCoupon;
