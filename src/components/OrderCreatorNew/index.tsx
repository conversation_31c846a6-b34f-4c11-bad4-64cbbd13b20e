import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Date<PERSON>icker, Divider, Form, Input, InputNumber, Row, Select, Table, Tag, Typography, Tooltip, TableColumnType, message } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";
import VipPlusPicker from "../VipPlusPicker";
import UserPane from "../Units/UserPane";
import Style from "./index.module.scss";
import MallApi from "@/services/MallApi";
import WeModal from "../WeModal/WeModal";
import PickCoupon from "./PickCoupon";
import PayOrderModal from "./PayOrderModal";
import dayjs from "dayjs";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { useDebounceFn } from "@/utils/Tools";
import { Switch01 } from "../Units/Switch01";
import { CusPay } from "./CusPay";
import { GoodsSkuPicker } from "../GoodsSkuPicker";

const layout = { row: 10, col: 12 };

const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

const OrderCreatorNew = (props: { children: any; onOk?: Function; userId?: any; params?: any; property?: 1 | 2 }) => {
  const property = props.property || 1;
  const typeName = property === 2 ? "商品" : "项目";
  const [form] = Form.useForm();
  // const [maxIntegration, setMaxIntegration] = useState(0); //最大积分
  // const [isModalOpen, setIsModalOpen] = useState(false);
  const goodsListName = "goodsList";

  const [state, setState] = useSetState({
    user: null as any,

    coupon: null as any,

    cinfo: null as any,
    clist: [] as any[],
    total: null as any,
    totalLoading: false,

    staff: [] as any[],
    cusPay: 0 as any,

    //===========
    payOpen: false,
    orderId: null,

    adjustment: undefined,
  });

  useEffect(() => {
    handleReset();

    if (state.user?.id) {
      form.setFieldsValue({
        adviserId: state.user?.adviserId,
        developerId: state.user?.developerId,
      });
    }
  }, [state.user?.id]);

  useEffect(() => {
    fetchTotal(10);
  }, [state.coupon]);

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.userId);
    setState({ user: res });
  };

  const fetchCoupons = async () => {
    const list = form.getFieldValue(goodsListName);
    if (!list?.length) return handleReset();

    const plist = list.map((item: any) => ({
      productId: item?.id,
      productSpecificationId: item?.skuId,
      totalCount: item?.totalCount,
      discount: item?.discount,
      lastMoney: item?.lastMoney,
    }));

    const user = state.user;
    const data = {
      shopVipUserId: user.id,
      productList: plist,
    };
    const res = await MallApi.getOrderCouponNew({ data });

    form.setFieldValue("useIntegral", res?.integralConvertMoney);
    setState({ clist: res?.couponList || [], cinfo: res });
  };

  const fetchTotal = async (adjustType?: any, cusPay?: any) => {
    const val = form.getFieldsValue();
    const list: any[] = val?.[goodsListName];

    if (!list?.length) return handleReset();

    const user = state.user;
    const plist = list.map((item: any) => ({
      productId: item?.id,
      productSpecificationId: item?.skuId,
      totalCount: item?.totalCount,
      discount: item?.discount,
      lastMoney: item?.lastMoney,
    }));

    const data = {
      shopVipUserId: user.id,
      couponId: state.coupon?.id,
      productList: plist,
      useIntegralTag: val.useIntegralTag,
      useIntegral: val.useIntegral * 100,
      // lastMoney: Number(state?.adjustment ?? 0),
      lastMoney: cusPay,
      adjustType,
    };

    setState({ totalLoading: true });
    const res = await MallApi.getOrderTotalNew({ data }).finally(() => setState({ totalLoading: false }));
    setState({ total: res });

    const clist = list.map((item) => {
      const ext = res?.productList?.find((n: any) => n.productId == item.id && n.productSpecificationId == item.skuId);

      if (!ext) return item;

      return {
        ...item,
        lastMoney: ext?.lastMoney,
        discount: ext?.discount,
        totalCount: ext?.totalCount ?? 0,
      };
    });

    form.setFieldValue(goodsListName, clist);
  };

  const handleReset = () => {
    form.resetFields();
    setState({
      coupon: null,
      cinfo: null,
      clist: [],
      total: null,
    });
  };

  const calcPrice = useDebounceFn((key: any, name: string, val: any) => {
    const path = [goodsListName, key];
    const item = form.getFieldValue(path);

    if (name == "totalCount") {
      if (!val) {
        return;
      }
      item.lastMoney = val * (item.discount / 10) * item.salePrice;
      item.lastMoney = item.lastMoney.toFixed(2);
    }

    if (name == "discount") {
      item.lastMoney = item.totalCount * (val / 10) * item.salePrice;
      item.lastMoney = item.lastMoney.toFixed(2);
    }

    if (name == "lastMoney") {
      item.lastMoney = Math.min(val, item.totalCount * item.salePrice).toFixed(2);
      item.discount = ((item.lastMoney / item.totalCount / item.salePrice) * 10).toFixed(1);
    }

    form.setFieldValue(path, item);
    fetchCoupons();
    fetchTotal(20);
  }, 500);

  const handleOpen = async () => {
    setState({ user: null });
    fetchStaff();
    if (props.userId) fetchUser();
  };

  const handleSubmit = async () => {
    const fdate = (n: any, f: string) => (n ? dayjs(n).format(f) : "");
    const data = await form.validateFields();
    const list: any[] = data?.goodsList || [];

    if (!state.user) {
      message.error("请选择用户");
      return false;
    }
    if (!list.length) {
      message.error(`请选择${typeName}`);
      return false;
    }

    data.shopVipUserId = state.user.id;
    data.couponId = state.coupon?.id;
    data.useEffectiveStartDate = fdate(data.edate?.[0], "YYYY-MM-DD 00:00:00");
    data.useEffectiveEndDate = fdate(data.edate?.[1], "YYYY-MM-DD 23:59:59");
    delete data.edate;
    data.useIntegral = data.useIntegral * 100;
    // data.custPayMoney = state.cusPay
    data.productList = list.map((item) => ({
      productId: item?.id,
      productSpecificationId: item?.skuId,
      totalCount: item?.totalCount,
      discount: item?.discount,
      lastMoney: item?.lastMoney,
    }));

    const ores = await MallApi.createOrderNew({
      data: {
        ...data,
        ...props.params,
      },
    });
    setState({ payOpen: true, orderId: ores.id });
  };

  return (
    <>
      <WeModal
        trigger={props.children}
        width={950}
        title={
          <div style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}>
            <span style={{ marginRight: 10 }}>{typeName}开单</span>
            {!props.userId && <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} />}
          </div>
        }
        okButtonProps={{ disabled: state.totalLoading }}
        onOk={handleSubmit}
        onOpen={handleOpen}
        className={Style.mbox}
        okText="提交订单"
      >
        {state.user ? <UserPane user={state.user} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}

        <Form form={form} labelCol={{ flex: "80px" }}>
          <Divider orientation="left">{typeName}信息</Divider>
          <Row gutter={layout.row}>
            <Group style={{ marginTop: 10 }}>
              <Group>
                <Col span={24} style={{ marginTop: 10 }}>
                  <Form.List name={goodsListName} initialValue={[]}>
                    {(fields, action) => {
                      return (
                        <>
                          <Table
                            size="small"
                            locale={{ emptyText: "暂无数据" }}
                            pagination={false}
                            dataSource={fields}
                            columns={(
                              [
                                {
                                  title: "项目名称",
                                  render: (item) => {
                                    const productName = form.getFieldValue([goodsListName, item.name, "name"]);
                                    const productSpecificationName = form.getFieldValue([goodsListName, item.name, "skuName"]);
                                    return <div className="line-clamp-1">
                                      {productName}
                                      {productSpecificationName && productName !== productSpecificationName && (
                                        <span className="text-gray-400 ml-2px">
                                          {" - " + productSpecificationName}
                                        </span>
                                      )}
                                    </div>;
                                  },
                                },
                                {
                                  key: "productModel",
                                  title: "模式",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "productModel"]);
                                    if (name == 1) {
                                      return "项目单品";
                                    } else if (name == 2) {
                                      return "项目套餐";
                                    } else if (name == 11) {
                                      return "配送商品";
                                    } else if (name == 12) {
                                      return "服务商品";
                                    }
                                    return "--";
                                  },
                                },
                                {
                                  title: "售价",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "salePrice"]);
                                    return name;
                                  },
                                },
                                {
                                  key: "includeNum",
                                  title: "次数",
                                  render: (c) => {
                                    const name = form.getFieldValue([goodsListName, c.name, "includeNum"]);
                                    return `${name == 0 ? "--" : name} 次`;
                                  },
                                },
                                {
                                  title: "数量",
                                  width: 100,
                                  render: (c) => {
                                    return (
                                      <Form.Item noStyle name={[c.name, "totalCount"]}>
                                        <InputNumber min={1} className="w-full" onChange={(e) => calcPrice(c.name, "totalCount", e)} />
                                      </Form.Item>
                                    );
                                  },
                                },
                                {
                                  title: (
                                    <>
                                      折扣
                                      <Tooltip title="0-10">
                                        <QuestionCircleOutlined
                                          style={{
                                            cursor: "pointer",
                                            marginLeft: 4,
                                          }}
                                        />
                                      </Tooltip>
                                    </>
                                  ),
                                  width: 100,
                                  render: (c) => {
                                    return (
                                      <Form.Item noStyle name={[c.name, "discount"]}>
                                        <InputNumber min={0} max={10} step={0.1} className="w-full" onChange={(e) => calcPrice(c.name, "discount", e)} />
                                      </Form.Item>
                                    );
                                  },
                                },
                                {
                                  title: "折后价",
                                  width: 130,
                                  render: (c) => {
                                    return (
                                      <Form.Item noStyle name={[c.name, "lastMoney"]}>
                                        <InputNumber min={0} className="w-full" onChange={(e) => calcPrice(c.name, "lastMoney", e)} />
                                      </Form.Item>
                                    );
                                  },
                                },
                                {
                                  title: "操作",
                                  render: (c) => {
                                    return (
                                      <a
                                        onClick={() => {
                                          action.remove(c.name);
                                          fetchCoupons();
                                          fetchTotal();
                                        }}
                                      >
                                        删除
                                      </a>
                                    );
                                  },
                                },
                              ] as TableColumnType<any>[]
                            ).filter((item) => {
                              if ((item.key === "productModel" || item.key === "includeNum") && props.property == 2) return false;

                              return true;
                            })}
                          />
                          <div style={{ height: 20 }}></div>
                          <Row gutter={[20, 0]}>
                            <Col span={12}>
                              <GoodsSkuPicker
                                params={{ property: property}}
                                onOk={(rows) => {
                                  const prev: any[] = form.getFieldValue(goodsListName);
                                  const list: any[] = rows
                                    ?.filter((n) => !prev.some((m) => m.id == n.id))
                                    .map((item) => ({
                                      id: item?.productId,
                                      name: item.productName,
                                      skuId: item.productSpecificationId,
                                      skuName: item.productSpecificationName,
                                      productModel: item.productModel,
                                      salePrice: item.salePrice,
                                      includeNum: item.includeNum,
                                      totalCount: 1,
                                    }));

                                  form.setFieldValue(goodsListName, [...prev, ...list]);
                                  fetchCoupons();
                                  fetchTotal();
                                }}
                              >
                                <Button disabled={!state.user}>添加{typeName}</Button>
                              </GoodsSkuPicker>
                            </Col>
                            <Col span={12}>
                              <div style={{ float: "right", marginRight: "50px" }}>
                                <CusPay
                                  num={state.total?.payMoney ?? 0}
                                  onOK={(num: any) => {
                                    fetchTotal(30, num);
                                  }}
                                >
                                  <Button disabled={!(form.getFieldValue("goodsList")?.length > 0)}>调整总金额</Button>
                                </CusPay>
                              </div>
                            </Col>
                          </Row>
                        </>
                      );
                    }}
                  </Form.List>
                </Col>
              </Group>
            </Group>
          </Row>

          <Divider orientation="left"></Divider>

          <Row gutter={layout.row} className={Style.sbox}>
            <Group>
              <Col span={layout.col}>
                <Form.Item label="商品总价"></Form.Item>
              </Col>
              <Col span={layout.col}>
                <div className={Style.rnum}>&yen;{state.total?.totalMoney ?? 0}</div>
              </Col>
            </Group>
            <Group>
              <Col span={layout.col}>
                <Form.Item label="优惠券">
                  {state.coupon ? (
                    <Tag closable onClose={() => setState({ coupon: null })}>
                      {state.coupon?.name}
                    </Tag>
                  ) : (
                    <PickCoupon list={state.clist} onOk={(e: any) => setState({ coupon: e })}>
                      <Typography.Link disabled={!state.clist.length}>选择({state.clist.length}张可用)</Typography.Link>
                    </PickCoupon>
                  )}
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <div className={Style.rnum}>- &yen;{state.total?.couponConvertMoney ?? 0}</div>
              </Col>
            </Group>

            {state.cinfo?.maxIntegralConvertMoney > 0 && (
              <Group>
                <Col span={layout.col}>
                  <Form.Item label="M币抵扣">
                    {state.cinfo?.maxIntegralConvertMoney ? (
                      <div style={{ display: "flex", alignItems: "center" }}>
                        <div>
                          <Form.Item name={`useIntegralTag`} noStyle>
                            <Switch01
                              size="small"
                              onChange={(e: any) => {
                                const num = e ? state.cinfo?.maxIntegralConvertMoney : 0;
                                form.setFieldValue("useIntegral", num);
                                fetchTotal(10);
                              }}
                            />
                          </Form.Item>
                        </div>
                        <div style={{ marginLeft: 5 }}>
                          <Form.Item shouldUpdate={(e, c) => e.useIntegralTag !== c.useIntegralTag} noStyle>
                            {(form) => {
                              let isShould = !!form?.getFieldValue("useIntegralTag");
                              return (
                                <div style={{ display: "flex" }}>
                                  <Form.Item name="useIntegral" hidden={!isShould}>
                                    <InputNumber
                                      step={1}
                                      min={1}
                                      max={state.cinfo?.maxIntegralConvertMoney}
                                      size="small"
                                      onChange={() => {
                                        fetchTotal(10);
                                      }}
                                    />
                                  </Form.Item>
                                </div>
                              );
                            }}
                          </Form.Item>
                        </div>
                        <div style={{ marginLeft: 5 }}>
                          <>
                            <div
                              style={{
                                marginLeft: 10,
                                color: "#666",
                                fontSize: 12,
                              }}
                            >
                              最大抵扣
                              {state.cinfo?.maxIntegralConvertMoney}元
                            </div>
                          </>
                        </div>
                      </div>
                    ) : (
                      <div style={{ marginLeft: 5 }}>
                        <div style={{ color: "#666", fontSize: 12 }}>无</div>
                      </div>
                    )}
                  </Form.Item>
                </Col>
                <Col span={layout.col}>
                  <div className={Style.rnum}>- &yen;{state.total?.integralConvertMoney ?? 0}</div>
                </Col>
              </Group>
            )}
          </Row>

          <div className={Style.tbox}>
            <div className={Style.item}>
              <div className={Style.name}>应收：</div>
              <div className={Style.value}>&yen;{state.total?.payMoney ?? 0}</div>
            </div>
            {/* <div className={Style.item}>
              <div className={Style.name}>实收：</div>
              <Input
                className={Style.ipt}
                size="small"
                value={state.cusPay}
                onChange={(e) => setState({ cusPay: e.target.value ?? 0 })}
                placeholder={state.total?.payMoney ?? 0}
              />
            </div> */}
          </div>

          <Divider orientation="left">订单信息</Divider>

          <Row gutter={layout.row} style={{ marginTop: 26 }}>
            {property == 1 && (
              <Col span={layout.col}>
                <Form.Item label="使用期限" name={`useEffectiveStyle`} initialValue={0}>
                  <Select
                    style={{ minWidth: 150 }}
                    options={[
                      { label: "永久有效", value: 0 },
                      { label: "固定期限", value: 20 },
                    ]}
                    placeholder="请选择使用期限"
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={layout.col}>
              <Form.Item noStyle dependencies={["useEffectiveStyle"]}>
                {(form) => {
                  const value = form.getFieldValue("useEffectiveStyle");
                  if (value == 20) {
                    return (
                      <Form.Item label="有效时间" name={`edate`} rules={[{ required: true, message: "请选择有效时间" }]} initialValue={[]}>
                        <DatePicker.RangePicker
                          presets={[
                            { label: "30天", value: [dayjs(), dayjs().add(30, "day")] },
                            { label: "60天", value: [dayjs(), dayjs().add(60, "day")] },
                            { label: "90天", value: [dayjs(), dayjs().add(90, "day")] },
                            { label: "180天", value: [dayjs(), dayjs().add(180, "day")] },
                            { label: "1年", value: [dayjs(), dayjs().add(1, "year")] },
                            { label: "2年", value: [dayjs(), dayjs().add(2, "year")] },
                            { label: "3年", value: [dayjs(), dayjs().add(3, "year")] },
                          ]}
                          style={{ width: "100%" }}
                        />
                      </Form.Item>
                    );
                  }
                  return null;
                }}
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={layout.row}>
            <Col span={8}>
              <Form.Item label="所属客服" name={`adviserId`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属客服" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="所属开发" name={`developerId`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属开发" />
              </Form.Item>
            </Col>
            <Col span={layout.col}></Col>
            <Col span={layout.col * 2}>
              <Form.Item label="订单备注" name={`orderRemark`}>
                <Input.TextArea placeholder="请输入订单备注" autoSize={{ minRows: 2 }} maxLength={200} allowClear />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </WeModal>
      <PayOrderModal open={state.payOpen} onOpenChange={(payOpen) => setState({ payOpen })} orderId={state.orderId} onOk={props.onOk} />
    </>
  );
};

export default OrderCreatorNew;
