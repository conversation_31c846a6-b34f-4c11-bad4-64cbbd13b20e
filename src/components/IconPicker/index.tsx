import { Input, Modal } from "antd";
import { useSetState } from "react-use";
import Icon from "@ant-design/icons";
import icons from "./icons";
import css from "./index.module.scss";

const names = Object.keys(icons);

interface Props {
  value?: string;
  onChange?: Function;
}

export const IconRender = ({ type = "", ...props }) => {
  if (!type) return null;
  return <Icon component={(icons as any)[type]} {...props} />;
};

const IconPicker = (props: Props) => {
  const [state, setState] = useSetState({
    open: false,
    value: "",
  });

  const handleOpen = () => {
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handlePick = (name: string) => {
    setState({ value: name });
    props.onChange?.(name);
    handleClose();
  };

  const handleChange = (value: string) => {
    props.onChange?.(value);
    setState({ value });
  };

  const value = props.value ?? state.value;

  return (
    <>
      <Input
        allowClear
        prefix={<IconRender type={value} style={{ fontSize: 18 }} />}
        addonAfter={
          <div className={css.btn} onClick={handleOpen}>
            选择
          </div>
        }
        value={value}
        onChange={(e) => handleChange(e.target.value)}
      />
      <div></div>

      <Modal title="图标选择" width={1000} open={state.open} onCancel={handleClose}>
        <div className={css.box}>
          {names.map((n) => {
            return (
              <div className={css.item} onClick={() => handlePick(n)} key={n}>
                <Icon component={(icons as any)[n]} />
              </div>
            );
          })}
        </div>
      </Modal>
    </>
  );
};

export default IconPicker;
