import { AccountBookOutlined } from "@ant-design/icons";
import { AimOutlined } from "@ant-design/icons";
import { AlertOutlined } from "@ant-design/icons";
import { <PERSON><PERSON><PERSON>Outlined } from "@ant-design/icons";
import { AlignCenterOutlined } from "@ant-design/icons";
import { AlignLeftOutlined } from "@ant-design/icons";
import { AlignRightOutlined } from "@ant-design/icons";
import { AlipayCircleOutlined } from "@ant-design/icons";
import { AlipayOutlined } from "@ant-design/icons";
import { AliwangwangOutlined } from "@ant-design/icons";
import { AliyunOutlined } from "@ant-design/icons";
import { AmazonOutlined } from "@ant-design/icons";
import { AndroidOutlined } from "@ant-design/icons";
import { AntCloudOutlined } from "@ant-design/icons";
import { AntDesignOutlined } from "@ant-design/icons";
import { ApartmentOutlined } from "@ant-design/icons";
import { ApiOutlined } from "@ant-design/icons";
import { AppleOutlined } from "@ant-design/icons";
import { AppstoreAddOutlined } from "@ant-design/icons";
import { AppstoreOutlined } from "@ant-design/icons";
import { AreaChartOutlined } from "@ant-design/icons";
import { ArrowDownOutlined } from "@ant-design/icons";
import { ArrowLeftOutlined } from "@ant-design/icons";
import { ArrowRightOutlined } from "@ant-design/icons";
import { ArrowUpOutlined } from "@ant-design/icons";
import { ArrowsAltOutlined } from "@ant-design/icons";
import { AudioMutedOutlined } from "@ant-design/icons";
import { AudioOutlined } from "@ant-design/icons";
import { AuditOutlined } from "@ant-design/icons";
import { BackwardOutlined } from "@ant-design/icons";
import { BankOutlined } from "@ant-design/icons";
import { BarChartOutlined } from "@ant-design/icons";
import { BarcodeOutlined } from "@ant-design/icons";
import { BarsOutlined } from "@ant-design/icons";
import { BehanceOutlined } from "@ant-design/icons";
import { BehanceSquareOutlined } from "@ant-design/icons";
import { BellOutlined } from "@ant-design/icons";
import { BgColorsOutlined } from "@ant-design/icons";
import { BlockOutlined } from "@ant-design/icons";
import { BoldOutlined } from "@ant-design/icons";
import { BookOutlined } from "@ant-design/icons";
import { BorderBottomOutlined } from "@ant-design/icons";
import { BorderHorizontalOutlined } from "@ant-design/icons";
import { BorderInnerOutlined } from "@ant-design/icons";
import { BorderLeftOutlined } from "@ant-design/icons";
import { BorderOuterOutlined } from "@ant-design/icons";
import { BorderOutlined } from "@ant-design/icons";
import { BorderRightOutlined } from "@ant-design/icons";
import { BorderTopOutlined } from "@ant-design/icons";
import { BorderVerticleOutlined } from "@ant-design/icons";
import { BorderlessTableOutlined } from "@ant-design/icons";
import { BoxPlotOutlined } from "@ant-design/icons";
import { BranchesOutlined } from "@ant-design/icons";
import { BugOutlined } from "@ant-design/icons";
import { BuildOutlined } from "@ant-design/icons";
import { BulbOutlined } from "@ant-design/icons";
import { CalculatorOutlined } from "@ant-design/icons";
import { CalendarOutlined } from "@ant-design/icons";
import { CameraOutlined } from "@ant-design/icons";
import { CarOutlined } from "@ant-design/icons";
import { CaretDownOutlined } from "@ant-design/icons";
import { CaretLeftOutlined } from "@ant-design/icons";
import { CaretRightOutlined } from "@ant-design/icons";
import { CaretUpOutlined } from "@ant-design/icons";
import { CarryOutOutlined } from "@ant-design/icons";
import { CheckCircleOutlined } from "@ant-design/icons";
import { CheckOutlined } from "@ant-design/icons";
import { CheckSquareOutlined } from "@ant-design/icons";
import { ChromeOutlined } from "@ant-design/icons";
import { CiCircleOutlined } from "@ant-design/icons";
import { CiOutlined } from "@ant-design/icons";
import { ClearOutlined } from "@ant-design/icons";
import { ClockCircleOutlined } from "@ant-design/icons";
import { CloseCircleOutlined } from "@ant-design/icons";
import { CloseOutlined } from "@ant-design/icons";
import { CloseSquareOutlined } from "@ant-design/icons";
import { CloudDownloadOutlined } from "@ant-design/icons";
import { CloudOutlined } from "@ant-design/icons";
import { CloudServerOutlined } from "@ant-design/icons";
import { CloudSyncOutlined } from "@ant-design/icons";
import { CloudUploadOutlined } from "@ant-design/icons";
import { ClusterOutlined } from "@ant-design/icons";
import { CodeOutlined } from "@ant-design/icons";
import { CodeSandboxOutlined } from "@ant-design/icons";
import { CodepenCircleOutlined } from "@ant-design/icons";
import { CodepenOutlined } from "@ant-design/icons";
import { CoffeeOutlined } from "@ant-design/icons";
import { ColumnHeightOutlined } from "@ant-design/icons";
import { ColumnWidthOutlined } from "@ant-design/icons";
import { CommentOutlined } from "@ant-design/icons";
import { CompassOutlined } from "@ant-design/icons";
import { CompressOutlined } from "@ant-design/icons";
import { ConsoleSqlOutlined } from "@ant-design/icons";
import { ContactsOutlined } from "@ant-design/icons";
import { ContainerOutlined } from "@ant-design/icons";
import { ControlOutlined } from "@ant-design/icons";
import { CopyOutlined } from "@ant-design/icons";
import { CopyrightCircleOutlined } from "@ant-design/icons";
import { CopyrightOutlined } from "@ant-design/icons";
import { CreditCardOutlined } from "@ant-design/icons";
import { CrownOutlined } from "@ant-design/icons";
import { CustomerServiceOutlined } from "@ant-design/icons";
import { DashOutlined } from "@ant-design/icons";
import { DashboardOutlined } from "@ant-design/icons";
import { DatabaseOutlined } from "@ant-design/icons";
import { DeleteColumnOutlined } from "@ant-design/icons";
import { DeleteOutlined } from "@ant-design/icons";
import { DeleteRowOutlined } from "@ant-design/icons";
import { DeliveredProcedureOutlined } from "@ant-design/icons";
import { DeploymentUnitOutlined } from "@ant-design/icons";
import { DesktopOutlined } from "@ant-design/icons";
import { DiffOutlined } from "@ant-design/icons";
import { DingdingOutlined } from "@ant-design/icons";
import { DingtalkOutlined } from "@ant-design/icons";
import { DisconnectOutlined } from "@ant-design/icons";
import { DislikeOutlined } from "@ant-design/icons";
import { DollarCircleOutlined } from "@ant-design/icons";
import { DollarOutlined } from "@ant-design/icons";
import { DotChartOutlined } from "@ant-design/icons";
import { DoubleLeftOutlined } from "@ant-design/icons";
import { DoubleRightOutlined } from "@ant-design/icons";
import { DownCircleOutlined } from "@ant-design/icons";
import { DownOutlined } from "@ant-design/icons";
import { DownSquareOutlined } from "@ant-design/icons";
import { DownloadOutlined } from "@ant-design/icons";
import { DragOutlined } from "@ant-design/icons";
import { DribbbleOutlined } from "@ant-design/icons";
import { DribbbleSquareOutlined } from "@ant-design/icons";
import { DropboxOutlined } from "@ant-design/icons";
import { EditOutlined } from "@ant-design/icons";
import { EllipsisOutlined } from "@ant-design/icons";
import { EnterOutlined } from "@ant-design/icons";
import { EnvironmentOutlined } from "@ant-design/icons";
import { EuroCircleOutlined } from "@ant-design/icons";
import { EuroOutlined } from "@ant-design/icons";
import { ExceptionOutlined } from "@ant-design/icons";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { ExclamationOutlined } from "@ant-design/icons";
import { ExpandAltOutlined } from "@ant-design/icons";
import { ExpandOutlined } from "@ant-design/icons";
import { ExperimentOutlined } from "@ant-design/icons";
import { ExportOutlined } from "@ant-design/icons";
import { EyeInvisibleOutlined } from "@ant-design/icons";
import { EyeOutlined } from "@ant-design/icons";
import { FacebookOutlined } from "@ant-design/icons";
import { FallOutlined } from "@ant-design/icons";
import { FastBackwardOutlined } from "@ant-design/icons";
import { FastForwardOutlined } from "@ant-design/icons";
import { FieldBinaryOutlined } from "@ant-design/icons";
import { FieldNumberOutlined } from "@ant-design/icons";
import { FieldStringOutlined } from "@ant-design/icons";
import { FieldTimeOutlined } from "@ant-design/icons";
import { FileAddOutlined } from "@ant-design/icons";
import { FileDoneOutlined } from "@ant-design/icons";
import { FileExcelOutlined } from "@ant-design/icons";
import { FileExclamationOutlined } from "@ant-design/icons";
import { FileGifOutlined } from "@ant-design/icons";
import { FileImageOutlined } from "@ant-design/icons";
import { FileJpgOutlined } from "@ant-design/icons";
import { FileMarkdownOutlined } from "@ant-design/icons";
import { FileOutlined } from "@ant-design/icons";
import { FilePdfOutlined } from "@ant-design/icons";
import { FilePptOutlined } from "@ant-design/icons";
import { FileProtectOutlined } from "@ant-design/icons";
import { FileSearchOutlined } from "@ant-design/icons";
import { FileSyncOutlined } from "@ant-design/icons";
import { FileTextOutlined } from "@ant-design/icons";
import { FileUnknownOutlined } from "@ant-design/icons";
import { FileWordOutlined } from "@ant-design/icons";
import { FileZipOutlined } from "@ant-design/icons";
import { FilterOutlined } from "@ant-design/icons";
import { FireOutlined } from "@ant-design/icons";
import { FlagOutlined } from "@ant-design/icons";
import { FolderAddOutlined } from "@ant-design/icons";
import { FolderOpenOutlined } from "@ant-design/icons";
import { FolderOutlined } from "@ant-design/icons";
import { FolderViewOutlined } from "@ant-design/icons";
import { FontColorsOutlined } from "@ant-design/icons";
import { FontSizeOutlined } from "@ant-design/icons";
import { ForkOutlined } from "@ant-design/icons";
import { FormOutlined } from "@ant-design/icons";
import { FormatPainterOutlined } from "@ant-design/icons";
import { ForwardOutlined } from "@ant-design/icons";
import { FrownOutlined } from "@ant-design/icons";
import { FullscreenExitOutlined } from "@ant-design/icons";
import { FullscreenOutlined } from "@ant-design/icons";
import { FunctionOutlined } from "@ant-design/icons";
import { FundOutlined } from "@ant-design/icons";
import { FundProjectionScreenOutlined } from "@ant-design/icons";
import { FundViewOutlined } from "@ant-design/icons";
import { FunnelPlotOutlined } from "@ant-design/icons";
import { GatewayOutlined } from "@ant-design/icons";
import { GifOutlined } from "@ant-design/icons";
import { GiftOutlined } from "@ant-design/icons";
import { GithubOutlined } from "@ant-design/icons";
import { GitlabOutlined } from "@ant-design/icons";
import { GlobalOutlined } from "@ant-design/icons";
import { GoldOutlined } from "@ant-design/icons";
import { GoogleOutlined } from "@ant-design/icons";
import { GooglePlusOutlined } from "@ant-design/icons";
import { GroupOutlined } from "@ant-design/icons";
import { HddOutlined } from "@ant-design/icons";
import { HeartOutlined } from "@ant-design/icons";
import { HeatMapOutlined } from "@ant-design/icons";
import { HighlightOutlined } from "@ant-design/icons";
import { HistoryOutlined } from "@ant-design/icons";
import { HolderOutlined } from "@ant-design/icons";
import { HomeOutlined } from "@ant-design/icons";
import { HourglassOutlined } from "@ant-design/icons";
import { Html5Outlined } from "@ant-design/icons";
import { IdcardOutlined } from "@ant-design/icons";
import { IeOutlined } from "@ant-design/icons";
import { ImportOutlined } from "@ant-design/icons";
import { InboxOutlined } from "@ant-design/icons";
import { InfoCircleOutlined } from "@ant-design/icons";
import { InfoOutlined } from "@ant-design/icons";
import { InsertRowAboveOutlined } from "@ant-design/icons";
import { InsertRowBelowOutlined } from "@ant-design/icons";
import { InsertRowLeftOutlined } from "@ant-design/icons";
import { InsertRowRightOutlined } from "@ant-design/icons";
import { InstagramOutlined } from "@ant-design/icons";
import { InsuranceOutlined } from "@ant-design/icons";
import { InteractionOutlined } from "@ant-design/icons";
import { IssuesCloseOutlined } from "@ant-design/icons";
import { ItalicOutlined } from "@ant-design/icons";
import { KeyOutlined } from "@ant-design/icons";
import { LaptopOutlined } from "@ant-design/icons";
import { LayoutOutlined } from "@ant-design/icons";
import { LeftCircleOutlined } from "@ant-design/icons";
import { LeftOutlined } from "@ant-design/icons";
import { LeftSquareOutlined } from "@ant-design/icons";
import { LikeOutlined } from "@ant-design/icons";
import { LineChartOutlined } from "@ant-design/icons";
import { LineHeightOutlined } from "@ant-design/icons";
import { LineOutlined } from "@ant-design/icons";
import { LinkOutlined } from "@ant-design/icons";
import { LinkedinOutlined } from "@ant-design/icons";
import { Loading3QuartersOutlined } from "@ant-design/icons";
import { LoadingOutlined } from "@ant-design/icons";
import { LockOutlined } from "@ant-design/icons";
import { LoginOutlined } from "@ant-design/icons";
import { LogoutOutlined } from "@ant-design/icons";
import { MacCommandOutlined } from "@ant-design/icons";
import { MailOutlined } from "@ant-design/icons";
import { ManOutlined } from "@ant-design/icons";
import { MedicineBoxOutlined } from "@ant-design/icons";
import { MediumOutlined } from "@ant-design/icons";
import { MediumWorkmarkOutlined } from "@ant-design/icons";
import { MehOutlined } from "@ant-design/icons";
import { MenuFoldOutlined } from "@ant-design/icons";
import { MenuOutlined } from "@ant-design/icons";
import { MenuUnfoldOutlined } from "@ant-design/icons";
import { MergeCellsOutlined } from "@ant-design/icons";
import { MessageOutlined } from "@ant-design/icons";
import { MinusCircleOutlined } from "@ant-design/icons";
import { MinusOutlined } from "@ant-design/icons";
import { MinusSquareOutlined } from "@ant-design/icons";
import { MobileOutlined } from "@ant-design/icons";
import { MoneyCollectOutlined } from "@ant-design/icons";
import { MonitorOutlined } from "@ant-design/icons";
import { MoreOutlined } from "@ant-design/icons";
import { NodeCollapseOutlined } from "@ant-design/icons";
import { NodeExpandOutlined } from "@ant-design/icons";
import { NodeIndexOutlined } from "@ant-design/icons";
import { NotificationOutlined } from "@ant-design/icons";
import { NumberOutlined } from "@ant-design/icons";
import { OneToOneOutlined } from "@ant-design/icons";
import { OrderedListOutlined } from "@ant-design/icons";
import { PaperClipOutlined } from "@ant-design/icons";
import { PartitionOutlined } from "@ant-design/icons";
import { PauseCircleOutlined } from "@ant-design/icons";
import { PauseOutlined } from "@ant-design/icons";
import { PayCircleOutlined } from "@ant-design/icons";
import { PercentageOutlined } from "@ant-design/icons";
import { PhoneOutlined } from "@ant-design/icons";
import { PicCenterOutlined } from "@ant-design/icons";
import { PicLeftOutlined } from "@ant-design/icons";
import { PicRightOutlined } from "@ant-design/icons";
import { PictureOutlined } from "@ant-design/icons";
import { PieChartOutlined } from "@ant-design/icons";
import { PlayCircleOutlined } from "@ant-design/icons";
import { PlaySquareOutlined } from "@ant-design/icons";
import { PlusCircleOutlined } from "@ant-design/icons";
import { PlusOutlined } from "@ant-design/icons";
import { PlusSquareOutlined } from "@ant-design/icons";
import { PoundCircleOutlined } from "@ant-design/icons";
import { PoundOutlined } from "@ant-design/icons";
import { PoweroffOutlined } from "@ant-design/icons";
import { PrinterOutlined } from "@ant-design/icons";
import { ProfileOutlined } from "@ant-design/icons";
import { ProjectOutlined } from "@ant-design/icons";
import { PropertySafetyOutlined } from "@ant-design/icons";
import { PullRequestOutlined } from "@ant-design/icons";
import { PushpinOutlined } from "@ant-design/icons";
import { QqOutlined } from "@ant-design/icons";
import { QrcodeOutlined } from "@ant-design/icons";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { QuestionOutlined } from "@ant-design/icons";
import { RadarChartOutlined } from "@ant-design/icons";
import { RadiusBottomleftOutlined } from "@ant-design/icons";
import { RadiusBottomrightOutlined } from "@ant-design/icons";
import { RadiusSettingOutlined } from "@ant-design/icons";
import { RadiusUpleftOutlined } from "@ant-design/icons";
import { RadiusUprightOutlined } from "@ant-design/icons";
import { ReadOutlined } from "@ant-design/icons";
import { ReconciliationOutlined } from "@ant-design/icons";
import { RedEnvelopeOutlined } from "@ant-design/icons";
import { RedditOutlined } from "@ant-design/icons";
import { RedoOutlined } from "@ant-design/icons";
import { ReloadOutlined } from "@ant-design/icons";
import { RestOutlined } from "@ant-design/icons";
import { RetweetOutlined } from "@ant-design/icons";
import { RightCircleOutlined } from "@ant-design/icons";
import { RightOutlined } from "@ant-design/icons";
import { RightSquareOutlined } from "@ant-design/icons";
import { RiseOutlined } from "@ant-design/icons";
import { RobotOutlined } from "@ant-design/icons";
import { RocketOutlined } from "@ant-design/icons";
import { RollbackOutlined } from "@ant-design/icons";
import { RotateLeftOutlined } from "@ant-design/icons";
import { RotateRightOutlined } from "@ant-design/icons";
import { SafetyCertificateOutlined } from "@ant-design/icons";
import { SafetyOutlined } from "@ant-design/icons";
import { SaveOutlined } from "@ant-design/icons";
import { ScanOutlined } from "@ant-design/icons";
import { ScheduleOutlined } from "@ant-design/icons";
import { ScissorOutlined } from "@ant-design/icons";
import { SearchOutlined } from "@ant-design/icons";
import { SecurityScanOutlined } from "@ant-design/icons";
import { SelectOutlined } from "@ant-design/icons";
import { SendOutlined } from "@ant-design/icons";
import { SettingOutlined } from "@ant-design/icons";
import { ShakeOutlined } from "@ant-design/icons";
import { ShareAltOutlined } from "@ant-design/icons";
import { ShopOutlined } from "@ant-design/icons";
import { ShoppingCartOutlined } from "@ant-design/icons";
import { ShoppingOutlined } from "@ant-design/icons";
import { ShrinkOutlined } from "@ant-design/icons";
import { SisternodeOutlined } from "@ant-design/icons";
import { SketchOutlined } from "@ant-design/icons";
import { SkinOutlined } from "@ant-design/icons";
import { SkypeOutlined } from "@ant-design/icons";
import { SlackOutlined } from "@ant-design/icons";
import { SlackSquareOutlined } from "@ant-design/icons";
import { SlidersOutlined } from "@ant-design/icons";
import { SmallDashOutlined } from "@ant-design/icons";
import { SmileOutlined } from "@ant-design/icons";
import { SnippetsOutlined } from "@ant-design/icons";
import { SolutionOutlined } from "@ant-design/icons";
import { SortAscendingOutlined } from "@ant-design/icons";
import { SortDescendingOutlined } from "@ant-design/icons";
import { SoundOutlined } from "@ant-design/icons";
import { SplitCellsOutlined } from "@ant-design/icons";
import { StarOutlined } from "@ant-design/icons";
import { StepBackwardOutlined } from "@ant-design/icons";
import { StepForwardOutlined } from "@ant-design/icons";
import { StockOutlined } from "@ant-design/icons";
import { StopOutlined } from "@ant-design/icons";
import { StrikethroughOutlined } from "@ant-design/icons";
import { SubnodeOutlined } from "@ant-design/icons";
import { SwapLeftOutlined } from "@ant-design/icons";
import { SwapOutlined } from "@ant-design/icons";
import { SwapRightOutlined } from "@ant-design/icons";
import { SwitcherOutlined } from "@ant-design/icons";
import { SyncOutlined } from "@ant-design/icons";
import { TableOutlined } from "@ant-design/icons";
import { TabletOutlined } from "@ant-design/icons";
import { TagOutlined } from "@ant-design/icons";
import { TagsOutlined } from "@ant-design/icons";
import { TaobaoCircleOutlined } from "@ant-design/icons";
import { TaobaoOutlined } from "@ant-design/icons";
import { TeamOutlined } from "@ant-design/icons";
import { ThunderboltOutlined } from "@ant-design/icons";
import { ToTopOutlined } from "@ant-design/icons";
import { ToolOutlined } from "@ant-design/icons";
import { TrademarkCircleOutlined } from "@ant-design/icons";
import { TrademarkOutlined } from "@ant-design/icons";
import { TransactionOutlined } from "@ant-design/icons";
import { TranslationOutlined } from "@ant-design/icons";
import { TrophyOutlined } from "@ant-design/icons";
import { TwitterOutlined } from "@ant-design/icons";
import { UnderlineOutlined } from "@ant-design/icons";
import { UndoOutlined } from "@ant-design/icons";
import { UngroupOutlined } from "@ant-design/icons";
import { UnlockOutlined } from "@ant-design/icons";
import { UnorderedListOutlined } from "@ant-design/icons";
import { UpCircleOutlined } from "@ant-design/icons";
import { UpOutlined } from "@ant-design/icons";
import { UpSquareOutlined } from "@ant-design/icons";
import { UploadOutlined } from "@ant-design/icons";
import { UsbOutlined } from "@ant-design/icons";
import { UserAddOutlined } from "@ant-design/icons";
import { UserDeleteOutlined } from "@ant-design/icons";
import { UserOutlined } from "@ant-design/icons";
import { UserSwitchOutlined } from "@ant-design/icons";
import { UsergroupAddOutlined } from "@ant-design/icons";
import { UsergroupDeleteOutlined } from "@ant-design/icons";
import { VerifiedOutlined } from "@ant-design/icons";
import { VerticalAlignBottomOutlined } from "@ant-design/icons";
import { VerticalAlignMiddleOutlined } from "@ant-design/icons";
import { VerticalAlignTopOutlined } from "@ant-design/icons";
import { VerticalLeftOutlined } from "@ant-design/icons";
import { VerticalRightOutlined } from "@ant-design/icons";
import { VideoCameraAddOutlined } from "@ant-design/icons";
import { VideoCameraOutlined } from "@ant-design/icons";
import { WalletOutlined } from "@ant-design/icons";
import { WarningOutlined } from "@ant-design/icons";
import { WechatOutlined } from "@ant-design/icons";
import { WeiboCircleOutlined } from "@ant-design/icons";
import { WeiboOutlined } from "@ant-design/icons";
import { WeiboSquareOutlined } from "@ant-design/icons";
import { WhatsAppOutlined } from "@ant-design/icons";
import { WifiOutlined } from "@ant-design/icons";
import { WindowsOutlined } from "@ant-design/icons";
import { WomanOutlined } from "@ant-design/icons";
import { YahooOutlined } from "@ant-design/icons";
import { YoutubeOutlined } from "@ant-design/icons";
import { YuqueOutlined } from "@ant-design/icons";
import { ZhihuOutlined } from "@ant-design/icons";
import { ZoomInOutlined } from "@ant-design/icons";
import { ZoomOutOutlined } from "@ant-design/icons";

export default {
  AccountBookOutlined,
  AimOutlined,
  AlertOutlined,
  AlibabaOutlined,
  AlignCenterOutlined,
  AlignLeftOutlined,
  AlignRightOutlined,
  AlipayCircleOutlined,
  AlipayOutlined,
  AliwangwangOutlined,
  AliyunOutlined,
  AmazonOutlined,
  AndroidOutlined,
  AntCloudOutlined,
  AntDesignOutlined,
  ApartmentOutlined,
  ApiOutlined,
  AppleOutlined,
  AppstoreAddOutlined,
  AppstoreOutlined,
  AreaChartOutlined,
  ArrowDownOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  ArrowUpOutlined,
  ArrowsAltOutlined,
  AudioMutedOutlined,
  AudioOutlined,
  AuditOutlined,
  BackwardOutlined,
  BankOutlined,
  BarChartOutlined,
  BarcodeOutlined,
  BarsOutlined,
  BehanceOutlined,
  BehanceSquareOutlined,
  BellOutlined,
  BgColorsOutlined,
  BlockOutlined,
  BoldOutlined,
  BookOutlined,
  BorderBottomOutlined,
  BorderHorizontalOutlined,
  BorderInnerOutlined,
  BorderLeftOutlined,
  BorderOuterOutlined,
  BorderOutlined,
  BorderRightOutlined,
  BorderTopOutlined,
  BorderVerticleOutlined,
  BorderlessTableOutlined,
  BoxPlotOutlined,
  BranchesOutlined,
  BugOutlined,
  BuildOutlined,
  BulbOutlined,
  CalculatorOutlined,
  CalendarOutlined,
  CameraOutlined,
  CarOutlined,
  CaretDownOutlined,
  CaretLeftOutlined,
  CaretRightOutlined,
  CaretUpOutlined,
  CarryOutOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CheckSquareOutlined,
  ChromeOutlined,
  CiCircleOutlined,
  CiOutlined,
  ClearOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  CloseOutlined,
  CloseSquareOutlined,
  CloudDownloadOutlined,
  CloudOutlined,
  CloudServerOutlined,
  CloudSyncOutlined,
  CloudUploadOutlined,
  ClusterOutlined,
  CodeOutlined,
  CodeSandboxOutlined,
  CodepenCircleOutlined,
  CodepenOutlined,
  CoffeeOutlined,
  ColumnHeightOutlined,
  ColumnWidthOutlined,
  CommentOutlined,
  CompassOutlined,
  CompressOutlined,
  ConsoleSqlOutlined,
  ContactsOutlined,
  ContainerOutlined,
  ControlOutlined,
  CopyOutlined,
  CopyrightCircleOutlined,
  CopyrightOutlined,
  CreditCardOutlined,
  CrownOutlined,
  CustomerServiceOutlined,
  DashOutlined,
  DashboardOutlined,
  DatabaseOutlined,
  DeleteColumnOutlined,
  DeleteOutlined,
  DeleteRowOutlined,
  DeliveredProcedureOutlined,
  DeploymentUnitOutlined,
  DesktopOutlined,
  DiffOutlined,
  DingdingOutlined,
  DingtalkOutlined,
  DisconnectOutlined,
  DislikeOutlined,
  DollarCircleOutlined,
  DollarOutlined,
  DotChartOutlined,
  DoubleLeftOutlined,
  DoubleRightOutlined,
  DownCircleOutlined,
  DownOutlined,
  DownSquareOutlined,
  DownloadOutlined,
  DragOutlined,
  DribbbleOutlined,
  DribbbleSquareOutlined,
  DropboxOutlined,
  EditOutlined,
  EllipsisOutlined,
  EnterOutlined,
  EnvironmentOutlined,
  EuroCircleOutlined,
  EuroOutlined,
  ExceptionOutlined,
  ExclamationCircleOutlined,
  ExclamationOutlined,
  ExpandAltOutlined,
  ExpandOutlined,
  ExperimentOutlined,
  ExportOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  FacebookOutlined,
  FallOutlined,
  FastBackwardOutlined,
  FastForwardOutlined,
  FieldBinaryOutlined,
  FieldNumberOutlined,
  FieldStringOutlined,
  FieldTimeOutlined,
  FileAddOutlined,
  FileDoneOutlined,
  FileExcelOutlined,
  FileExclamationOutlined,
  FileGifOutlined,
  FileImageOutlined,
  FileJpgOutlined,
  FileMarkdownOutlined,
  FileOutlined,
  FilePdfOutlined,
  FilePptOutlined,
  FileProtectOutlined,
  FileSearchOutlined,
  FileSyncOutlined,
  FileTextOutlined,
  FileUnknownOutlined,
  FileWordOutlined,
  FileZipOutlined,
  FilterOutlined,
  FireOutlined,
  FlagOutlined,
  FolderAddOutlined,
  FolderOpenOutlined,
  FolderOutlined,
  FolderViewOutlined,
  FontColorsOutlined,
  FontSizeOutlined,
  ForkOutlined,
  FormOutlined,
  FormatPainterOutlined,
  ForwardOutlined,
  FrownOutlined,
  FullscreenExitOutlined,
  FullscreenOutlined,
  FunctionOutlined,
  FundOutlined,
  FundProjectionScreenOutlined,
  FundViewOutlined,
  FunnelPlotOutlined,
  GatewayOutlined,
  GifOutlined,
  GiftOutlined,
  GithubOutlined,
  GitlabOutlined,
  GlobalOutlined,
  GoldOutlined,
  GoogleOutlined,
  GooglePlusOutlined,
  GroupOutlined,
  HddOutlined,
  HeartOutlined,
  HeatMapOutlined,
  HighlightOutlined,
  HistoryOutlined,
  HolderOutlined,
  HomeOutlined,
  HourglassOutlined,
  Html5Outlined,
  IdcardOutlined,
  IeOutlined,
  ImportOutlined,
  InboxOutlined,
  InfoCircleOutlined,
  InfoOutlined,
  InsertRowAboveOutlined,
  InsertRowBelowOutlined,
  InsertRowLeftOutlined,
  InsertRowRightOutlined,
  InstagramOutlined,
  InsuranceOutlined,
  InteractionOutlined,
  IssuesCloseOutlined,
  ItalicOutlined,
  KeyOutlined,
  LaptopOutlined,
  LayoutOutlined,
  LeftCircleOutlined,
  LeftOutlined,
  LeftSquareOutlined,
  LikeOutlined,
  LineChartOutlined,
  LineHeightOutlined,
  LineOutlined,
  LinkOutlined,
  LinkedinOutlined,
  Loading3QuartersOutlined,
  LoadingOutlined,
  LockOutlined,
  LoginOutlined,
  LogoutOutlined,
  MacCommandOutlined,
  MailOutlined,
  ManOutlined,
  MedicineBoxOutlined,
  MediumOutlined,
  MediumWorkmarkOutlined,
  MehOutlined,
  MenuFoldOutlined,
  MenuOutlined,
  MenuUnfoldOutlined,
  MergeCellsOutlined,
  MessageOutlined,
  MinusCircleOutlined,
  MinusOutlined,
  MinusSquareOutlined,
  MobileOutlined,
  MoneyCollectOutlined,
  MonitorOutlined,
  MoreOutlined,
  NodeCollapseOutlined,
  NodeExpandOutlined,
  NodeIndexOutlined,
  NotificationOutlined,
  NumberOutlined,
  OneToOneOutlined,
  OrderedListOutlined,
  PaperClipOutlined,
  PartitionOutlined,
  PauseCircleOutlined,
  PauseOutlined,
  PayCircleOutlined,
  PercentageOutlined,
  PhoneOutlined,
  PicCenterOutlined,
  PicLeftOutlined,
  PicRightOutlined,
  PictureOutlined,
  PieChartOutlined,
  PlayCircleOutlined,
  PlaySquareOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  PlusSquareOutlined,
  PoundCircleOutlined,
  PoundOutlined,
  PoweroffOutlined,
  PrinterOutlined,
  ProfileOutlined,
  ProjectOutlined,
  PropertySafetyOutlined,
  PullRequestOutlined,
  PushpinOutlined,
  QqOutlined,
  QrcodeOutlined,
  QuestionCircleOutlined,
  QuestionOutlined,
  RadarChartOutlined,
  RadiusBottomleftOutlined,
  RadiusBottomrightOutlined,
  RadiusSettingOutlined,
  RadiusUpleftOutlined,
  RadiusUprightOutlined,
  ReadOutlined,
  ReconciliationOutlined,
  RedEnvelopeOutlined,
  RedditOutlined,
  RedoOutlined,
  ReloadOutlined,
  RestOutlined,
  RetweetOutlined,
  RightCircleOutlined,
  RightOutlined,
  RightSquareOutlined,
  RiseOutlined,
  RobotOutlined,
  RocketOutlined,
  RollbackOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SafetyCertificateOutlined,
  SafetyOutlined,
  SaveOutlined,
  ScanOutlined,
  ScheduleOutlined,
  ScissorOutlined,
  SearchOutlined,
  SecurityScanOutlined,
  SelectOutlined,
  SendOutlined,
  SettingOutlined,
  ShakeOutlined,
  ShareAltOutlined,
  ShopOutlined,
  ShoppingCartOutlined,
  ShoppingOutlined,
  ShrinkOutlined,
  SisternodeOutlined,
  SketchOutlined,
  SkinOutlined,
  SkypeOutlined,
  SlackOutlined,
  SlackSquareOutlined,
  SlidersOutlined,
  SmallDashOutlined,
  SmileOutlined,
  SnippetsOutlined,
  SolutionOutlined,
  SortAscendingOutlined,
  SortDescendingOutlined,
  SoundOutlined,
  SplitCellsOutlined,
  StarOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  StockOutlined,
  StopOutlined,
  StrikethroughOutlined,
  SubnodeOutlined,
  SwapLeftOutlined,
  SwapOutlined,
  SwapRightOutlined,
  SwitcherOutlined,
  SyncOutlined,
  TableOutlined,
  TabletOutlined,
  TagOutlined,
  TagsOutlined,
  TaobaoCircleOutlined,
  TaobaoOutlined,
  TeamOutlined,
  ThunderboltOutlined,
  ToTopOutlined,
  ToolOutlined,
  TrademarkCircleOutlined,
  TrademarkOutlined,
  TransactionOutlined,
  TranslationOutlined,
  TrophyOutlined,
  TwitterOutlined,
  UnderlineOutlined,
  UndoOutlined,
  UngroupOutlined,
  UnlockOutlined,
  UnorderedListOutlined,
  UpCircleOutlined,
  UpOutlined,
  UpSquareOutlined,
  UploadOutlined,
  UsbOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  UserOutlined,
  UserSwitchOutlined,
  UsergroupAddOutlined,
  UsergroupDeleteOutlined,
  VerifiedOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignMiddleOutlined,
  VerticalAlignTopOutlined,
  VerticalLeftOutlined,
  VerticalRightOutlined,
  VideoCameraAddOutlined,
  VideoCameraOutlined,
  WalletOutlined,
  WarningOutlined,
  WechatOutlined,
  WeiboCircleOutlined,
  WeiboOutlined,
  WeiboSquareOutlined,
  WhatsAppOutlined,
  WifiOutlined,
  WindowsOutlined,
  WomanOutlined,
  YahooOutlined,
  YoutubeOutlined,
  YuqueOutlined,
  ZhihuOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
};
