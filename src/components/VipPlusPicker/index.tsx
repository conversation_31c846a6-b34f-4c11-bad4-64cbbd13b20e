import { Button, Empty, Input, Pagination, Popover, Spin } from "antd";
import { CloseOutlined, ExclamationCircleOutlined, ManOutlined, SearchOutlined, WomanOutlined } from "@ant-design/icons";
import { useDebounce, useSetState } from "react-use";
import dayjs from "dayjs";
import { CSSProperties, useEffect, useRef } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const VipPlusPicker = (props: { onChange?: (u?: any) => any; onInputChange?: (v?: any) => any; style?: CSSProperties; params?: any }) => {
  const divRef = useRef<any>(null);
  const [state, setState] = useSetState({
    keyword: "",
    // focus: false,
    show: false,

    list: [] as any[],
    loading: false,
    total: 0,
    page: 1,
    size: 5,
  });

  useDebounce(
    () => {
      const keyword = (state.keyword || "").trim();
      if (!keyword) {
        setState({ loading: false, list: [], total: 0, page: 1 });
      } else {
        searchUser(1);
      }
    },
    600,
    [state.keyword]
  );

  useEffect(() => {
    props.onInputChange?.(state.keyword);
    setState({ show: !!state.keyword });
  }, [state.keyword]);

  // useEffect(() => {
  //   if (state.focus && state.keyword) {
  //     setState({ show: true });
  //   } else if (!state.keyword && state.show) {
  //     setState({ show: false });
  //   }
  // }, [state.focus, state.keyword]);

  // useEffect(() => {
  //   const handleClick = (e: any) => {
  //     console.log(1111, e.target);
  //     if (divRef.current && !divRef.current.contains(e.target)) {
  //       setState({ show: false });
  //     }
  //   };

  //   document.addEventListener("click", handleClick);

  //   return () => {
  //     document.removeEventListener("click", handleClick);
  //   };
  // }, [divRef]);

  const handlePick = (item: any) => {
    // console.log(item);
    setState({ show: false });
    props.onChange?.(item);
  };

  const searchUser = async (page = 1) => {
    const searchKey = (state.keyword || "").trim();
    const params = { searchKey, pageSize: state.size, pageNum: page, ...props.params };
    setState({ loading: true });
    const res = await MallApi.getMemberForSelect({ params }).finally(() => setState({ loading: false }));
    let list: any[] = res?.list || [];
    list = list.map((item) => {
      let _cdate = item.createDate ? dayjs(item.createDate).format("YYYY-MM-DD") : "--";
      let _age: any = item.birthday ? dayjs().year() - dayjs(item.birthday).year() : "--";
      let _fdate = item.firstServiceTime ? dayjs(item.firstServiceTime).format("YYYY-MM-DD") : "--";
      let _ldate = item.lastServiceTime ? dayjs(item.lastServiceTime).format("YYYY-MM-DD") : "--";

      return { ...item, _age, _cdate, _fdate, _ldate };
    });

    setState({
      list,
      total: res.total || 0,
      size: res.pageSize,
      page: page,
    });
  };

  // console.log(props.style);

  return (
    <div className="pos-relative" ref={divRef} style={props.style}>
      <Popover
        open={state.show}
        onOpenChange={(e) => setState({ show: e })}
        trigger={["click"]}
        arrow={false}
        placement="bottomLeft"
        classNames={{ body: "!p-0" }}
        content={
          <div className="relative w-500px bg-white rounded-lg shadow-lg">
            <div className="flex items-center justify-between p-2 border-b border-gray-200">
              <div className="flex items-center gap-1">
                <ExclamationCircleOutlined className="text-yellow-500" />
                <span className="text-sm ml-1">请选择用户</span>
              </div>
              <Button type="link" size="small" icon={<CloseOutlined className="text-gray-500" />} onClick={() => setState({ show: false })} />
            </div>
            <Spin spinning={state.loading} delay={300}>
              <div className="max-h-400px overflow-y-auto p-2">
                {state.list.map((item) => (
                  <div className="p-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100" key={item.id} onClick={() => handlePick(item)}>
                    <div className="font-bold mb-1">
                      {item.name} [{item.vipCard}] {item.sex == 1 && <ManOutlined className="text-blue-500" />}
                      {item.sex == 2 && <WomanOutlined className="text-red-500" />}
                      {item.sex == 0 && "??"} {item._age}岁
                    </div>
                    <div className="text-sm text-gray-600 mb-1">
                      手机号：{item.mobile}
                      &nbsp;&nbsp;&nbsp;&nbsp; 客户来源：{item?.sourceName ?? "--"}
                    </div>
                    <div className="text-sm text-gray-600 mb-1">
                      首次到院：{item._fdate}
                      &nbsp;&nbsp;&nbsp;&nbsp; 最近到院：{item._ldate}
                    </div>
                    <div className="text-sm text-gray-600">
                      建档门店：{item.shopName}
                      &nbsp;&nbsp;&nbsp;&nbsp; 建档时间：{item._cdate}
                    </div>
                  </div>
                ))}
                {state.list.length <= 0 && (
                  <div className="flex justify-center items-center p-4">
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                  </div>
                )}
              </div>
            </Spin>
            <div className="flex justify-between items-center p-2 border-t border-gray-200">
              <div className="text-sm text-gray-500">
                搜索到
                <b className="mx-1">{state.total}</b>
                条用户信息
              </div>
              <Pagination size="small" total={state.total} pageSize={state.size} current={state.page} onChange={(p) => searchUser(p)} />
            </div>
          </div>
        }
      >
        <Input
          prefix={<SearchOutlined style={{ color: "#999" }} />}
          placeholder="会员号/姓名/手机号"
          value={state.keyword}
          onChange={(e) => {
            setState({ keyword: e.target.value });
            // console.log(e.target.value);
          }}
          allowClear
          onClick={() => setState({ show: !!state.keyword })}
          // onFocus={() => setState({ focus: true })}
          // onBlur={() => setState({ focus: false })}
        />
      </Popover>
    </div>
  );
};

export default VipPlusPicker;
