.box {
  display: flex;
  min-height: 490px;

  :global {
    .ant-tree-title {
      display: block;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .ant-tree-node-content-wrapper {
      min-width: 0;
    }
  }
}

.left {
  width: 250px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 6px;
  margin-right: 15px;
}

.right {
  flex: 1;
  min-width: 0;
}

.head {
  display: flex;
  align-items: center;
  padding: 10px 0;
}
