import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Input, Modal, Table, Tree } from "antd";
import { cloneElement, useEffect } from "react";
import { useDebounce, useSetState } from "react-use";
import Style from "./index.module.scss";

interface MatterPickerProps {
  children: any;
  max?: number;
  params?: any;
  onSelect?: (rows: any[]) => any;
}

const MatterPicker = (props: MatterPickerProps) => {
  const [state, setState] = useSetState({
    open: false,

    name: "",
    nameDelay: "",

    types: [] as any[],
    typeId: null as any,

    list: [] as any[],
    loading: false,
    total: 0,
    page: 1,
    size: 10,

    glist: [] as any[],
    // gkeys: [] as any,
  });

  useDebounce(() => setState({ nameDelay: state.name }), 600, [state.name]);

  useEffect(() => {
    if (!state.open) return;
    fetchTypes();
    setState({ typeId: null });
  }, [state.open]);

  useEffect(() => {
    if (!state.open) return;
    setState({ glist: [] });
    fetchList();
  }, [state.typeId, state.nameDelay, state.open]);

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};

    list.forEach((item) => {
      const newItem = { ...item };
      map[item.id] = newItem;
    });

    list.forEach((item) => {
      const newItem = map[item.id];
      const parent = map[newItem.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(newItem);
      } else {
        arr.push(newItem);
      }
    });

    return arr;
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getGoodsTypeForSelect({ params });
    const types = genTree(res?.list || []);
    setState({ types });
  };

  const fetchList = async (page = 1) => {
    const params = {
      ...props.params,
      goodsCategoryId: state.typeId,
      pageSize: state.size,
      name: state.nameDelay,
      pageNum: page,
    };
    setState({ loading: false });
    const res = await MallApi.getGoodsForPicker({ params }).finally(() => setState({ loading: false }));
    setState({ list: res?.list || [], page, total: res?.total || 0 });
  };

  const handleOk = () => {
    setState({ open: false });
    props.onSelect?.(state.glist);
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Modal
        open={state.open}
        closable
        width={1000}
        onCancel={() => setState({ open: false })}
        title={<>物资选择</>}
        keyboard={false}
        onOk={handleOk}
      >
        <div className={Style.head}>
          <Input
            placeholder="请输入名称"
            style={{ width: 250 }}
            value={state.name}
            onChange={(e) => setState({ name: e.target.value })}
            allowClear
          />
        </div>
        <div className={Style.box}>
          <div className={Style.left}>
            <Tree.DirectoryTree
              showIcon={false}
              treeData={state.types}
              fieldNames={{ title: "name", key: "id", children: "children" }}
              onSelect={(e) => {
                const key = e[0];
                setState({ typeId: key === state.typeId ? null : key });
              }}
              selectedKeys={[state.typeId]}
            />
          </div>
          <div className={Style.right}>
            <Table
              rowKey={`id`}
              size="small"
              dataSource={state.list}
              loading={{ spinning: state.loading, delay: 300 }}
              rowSelection={{
                preserveSelectedRowKeys: true,
                selectedRowKeys: state.glist.map((n) => n.id),
                onChange: (_, rows) => {
                  const max = props.max ?? 0;

                  if (!max) {
                    setState({ glist: rows });
                  } else if (max === 1) {
                    const last = rows[rows.length - 1];
                    setState({ glist: last ? [last] : [] });
                  } else {
                    setState({ glist: rows.slice(0, max) });
                  }
                },
              }}
              pagination={{
                current: state.page,
                pageSize: state.size,
                total: state.total,
                onChange: (n) => fetchList(n),
                showTotal: (n) => `共${n}条数据`,
              }}
              columns={[
                { title: "编号", dataIndex: "serialNo" },
                { title: "名称", dataIndex: "name" },
                { title: "分类", dataIndex: "goodsCategoryName" },
                { title: "规格", width: 150, dataIndex: "specs" },
                { title: "库存", dataIndex: "inventoryDesc" },
              ]}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default MatterPicker;
