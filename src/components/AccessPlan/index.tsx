import { Card, DatePicker, Empty, Form, Input, Timeline } from "antd";
import WeModal from "../WeModal/WeModal";
import WeTable from "../WeTable";
import MallApi from "@/services/MallApi";
import DictPicker from "../Units/DictPicker";
import { useSetState } from "react-use";
import { useEffect } from "react";

const INIT_STATE = {
  crt: null as any,
  info: null as any,
  infoLoading: false,
  date: null as any,
};

export const AccessPlan = (props: { children: any; onOk: Function }) => {
  const [state, setState] = useSetState({ ...INIT_STATE });

  const onOpen = () => {
    setState({ ...INIT_STATE });
  };

  const onOK = async () => {
    props.onOk({ info: state.info, date: state.date });
  };

  const fetchInfo = async () => {
    setState({ infoLoading: true });
    const res = await Mall<PERSON>pi.getAccessTempInfo(state.crt?.id).finally(() => setState({ infoLoading: false }));
    setState({ info: res });
  };

  useEffect(() => {
    if (state.crt) {
      fetchInfo();
    }
  }, [state.crt]);

  return (
    <WeModal trigger={props.children} width={900} centered title="计划模板" onOpen={onOpen} onOk={onOK}>
      <div className="flex min-h-600px">
        <div className="w-2/3 [&_.ant-table-row]:hover:cursor-pointer">
          <WeTable
            tableProps={{
              size: "small",
              rowSelection: {
                type: "radio",
                selectedRowKeys: [state.crt?.id],
              },
              onRow: (row) => {
                return {
                  onClick: () => setState({ crt: row }),
                };
              },
            }}
            searchNum={2}
            search={[
              <Form.Item name={`typeId`}>
                <DictPicker type="accessType" />
              </Form.Item>,
              <Form.Item name={`name`}>
                <Input placeholder="计划名称" allowClear />
              </Form.Item>,
            ]}
            request={(p) => MallApi.getAccessTempList({ params: p })}
            columns={[
              { title: "模板名称", dataIndex: "name" },
              { title: "回访类型", dataIndex: "typeName" },
              { title: "备注", dataIndex: "content" },
            ]}
          />
        </div>
        <div className="flex-1 ml-10px">
          {!state.crt && (
            <Card>
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            </Card>
          )}
          {!!state.crt && (
            <Card
              size="small"
              title={state.crt?.name}
              extra={<div className="text-(12px #666)">[{state.crt?.typeName}]</div>}
              loading={state.infoLoading}
            >
              <div className="pb-20px">
                <DatePicker
                  className="w-full"
                  placeholder="重设起点时间"
                  value={state.date}
                  onChange={(e) => setState({ date: e })}
                />
              </div>
              <Timeline
                items={state.info?.planDataList?.map((n: any) => ({
                  color: "gray",
                  children: (
                    <div className="text-(12px #666)">
                      <div>回访时间: {n.accessAfterDay}天后</div>
                      <div>回访主题: {n.accessTitle}</div>
                      <div>
                        回访推送:{" "}
                        {
                          [
                            { value: 10, label: "所属开发" },
                            { value: 20, label: "所属客服" },
                            { value: 30, label: "执行医生" },
                            { value: 40, label: "执行治疗师" },
                          ].find((o) => o.value === n.accessRole)?.label
                        }
                      </div>
                    </div>
                  ),
                }))}
              />
            </Card>
          )}
        </div>
      </div>
    </WeModal>
  );
};
