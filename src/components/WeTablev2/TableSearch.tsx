import { ClearOutlined, DownOutlined, SearchOutlined, UpOutlined } from "@ant-design/icons";
import { Button, Card } from "antd";
import { useState } from "react";

export const TableSearch = (props: { children?: any; onSearch?: any; onReset?: any }) => {
  const [fold, setFold] = useState(true);
  // const [state, setState] =

  return (
    <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
      <div className="flex [&_.ant-form-item]:mb-0">
        <div className="flex-1 grid cols-4 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
          {props.children}
        </div>
        <div className="flex gap-2 pl-3">
          <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
          <Button type="primary" icon={<SearchOutlined />} onClick={props.onSearch}>
            搜索
          </Button>
          <Button type="default" icon={<ClearOutlined />} onClick={props.onReset} />
        </div>
      </div>
    </Card>
  );
};
