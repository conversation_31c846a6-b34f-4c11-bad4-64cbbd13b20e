import Configs from "@/utils/Configs";
import { FileImageOutlined, FileOutlined, VideoCameraOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons";
import { Image, Modal, Upload, UploadProps, Button } from "antd";
import { useMemo } from "react";
import { useSetState } from "react-use";
import Compressor from "compressorjs";

const _UploadV2 = (props: UploadProps & { mode?: "img" | "file" | "video"; value?: any, compress?: boolean }) => {
  const [state, setState] = useSetState({
    flist: [] as any[],
    preview: false,
    preview2: false,
    url: "",
    currentIndex: 0,
  });

  const flist = props.value ?? props.fileList ?? state.flist ?? [];
  const maxCount = props.maxCount || 1;
  const mode = props.mode ?? "img";
  const accept = mode == "file" ? "" : mode == "video" ? "video/*" : "image/*";

  const child = useMemo(() => {
    if (props.children) return props.children;

    if (mode == "img") {
      return (
        <div>
          <FileImageOutlined className="text-30px text-#666" />
          <div className="text-14px mt-5px text-#666">上传图片</div>
        </div>
      );
    } else if (mode == "video") {
      return (
        <div>
          <VideoCameraOutlined className="text-30px text-#666" />
          <div className="text-14px mt-5px text-#666">上传视频</div>
        </div>
      );
    } else {
      return (
        <div>
          <FileOutlined className="text-30px text-#666" />
          <div className="text-14px mt-5px text-#666">上传文件</div>
        </div>
      );
    }
  }, [mode]);

  const onChange = (res: any) => {
    let flist: any[] = [...res.fileList];

    flist = flist.map((item) => {
      const url = item.url || item.response?.data?.allFullPath || "";
      return { ...item, url };
    });

    setState({ flist });
    props.onChange?.(flist as any);
  };

  // 添加移动图片位置的函数
  const moveImage = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= flist.length) return;
    
    const newFlist = [...flist];
    const [movedItem] = newFlist.splice(fromIndex, 1);
    newFlist.splice(toIndex, 0, movedItem);
    
    setState({ flist: newFlist });
    props.onChange?.(newFlist as any);
  };

  const onPreview = (e: any) => {
    const url = e.url;
    const type: string = e.type;

    if (type?.startsWith("image") || mode == "img") {
      setState({ url, preview: true });
    } else if (type?.startsWith("video") || mode == "video") {
      setState({ url, preview2: true });
    } else {
      const link = document.createElement("a");
      link.href = url;
      link.download = "";
      link.click();
    }
  };

  const beforeUpload: any = (file: any) => {
    if (file?.type == "image/gif") return file;
    return new Promise((resolve) => {
      new Compressor(file, {
        maxWidth: 1920,
        convertSize: 1024 * 200,
        success: (res) => resolve(res),
        error: () => resolve(file),
      });
    });
  };

  // 自定义渲染上传列表项
  const itemRender = (originNode: React.ReactNode, file: any, fileList: any[]/**, actions: { download: () => void; preview: () => void; remove: () => void } */) => {
    const index = fileList.indexOf(file);
    const isImage = file.type?.startsWith("image") || mode == "img";
    
    return (
      <div className="relative inline-block" onMouseEnter={(e) => {
          const target = e.currentTarget;
          target.classList.add('hover');
        }} onMouseLeave={(e) => {
          const target = e.currentTarget;
          target.classList.remove('hover');
        }}>
        {originNode}
        {isImage && maxCount > 1 && (
          <div className="absolute top-0 left-0 right-0 flex justify-between p-1 bg-black bg-opacity-30 z-10 opacity-0 transition-opacity duration-200 hover:opacity-100">
            <Button 
              type="text" 
              size="small" 
              icon={<LeftOutlined />} 
              onClick={(e) => {
                e.stopPropagation();
                moveImage(index, index - 1);
              }}
              disabled={index === 0}
              className="text-white hover:text-white hover:bg-transparent"
            />
            <Button 
              type="text" 
              size="small" 
              icon={<RightOutlined />} 
              onClick={(e) => {
                e.stopPropagation();
                moveImage(index, index + 1);
              }}
              disabled={index === fileList.length - 1}
              className="text-white hover:text-white hover:bg-transparent"
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="[&_.ant-image]:block">
      <Upload
        className="min-h-105px min-w-105px"
        action={Configs.uploadHost}
        listType="picture-card"
        fileList={flist}
        accept={accept}
        maxCount={maxCount}
        onPreview={onPreview}
        iconRender={(f) => {
          if (f.type?.startsWith("image") || mode == "img") {
            return undefined;
          }

          if (f.type?.startsWith("video") || mode == "video") {
            return <VideoCameraOutlined className="!text-#1677ff" />;
          }

          return <FileOutlined className="!text-#1677ff" />;
        }}
        beforeUpload={(props.compress ?? true) ? beforeUpload : undefined}
        itemRender={maxCount > 1 ? itemRender : undefined}
        {...props}
        onChange={onChange}
        children={flist.length < maxCount && child}
      />
      <Image
        src={state.url}
        style={{ display: "none" }}
        preview={{
          visible: state.preview,
          onVisibleChange: (preview) => setState({ preview }),
          getContainer: "body",
        }}
      />
      <Modal
        centered
        open={state.preview2}
        width={600}
        onCancel={() => setState({ preview2: false })}
        classNames={{ content: "!p-0" }}
        footer={null}
        closable={false}
        maskClosable={true}
      >
        <video className="block w-full" controls src={state.url} />
      </Modal>
    </div>
  );
};

export const UploadV2 = Object.assign(_UploadV2, {
  arr2str: (o: any[]) => o?.map((n) => n.url || "").join(",") ?? "",
  str2arr: (s: string) =>
    s
      ?.split(",")
      .filter((n) => n)
      .map((url, idx) => ({ uid: "-" + idx, name: `file` + (idx + 1), url, status: "done" })) ?? [],
});