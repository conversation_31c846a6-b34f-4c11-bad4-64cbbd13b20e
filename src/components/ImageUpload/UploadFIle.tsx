import Configs from "@/utils/Configs";
import { Button, Upload, UploadProps } from "antd";

const UploadFileComp = (props: UploadProps & { onChange?: Function; value?: any }) => {
  const { onChange, maxCount = 1, ...rect } = props;

  const handleChange = (res: any) => {
    let flist: any[] = [...res.fileList];

    flist = flist.map((item) => {
      const url = item.url || item.response?.data?.allFullPath || "";
      return { ...item, url };
    });

    onChange?.(flist as any);
  };

  return (
    <Upload
      action={Configs.uploadHost}
      multiple={maxCount != 1}
      onChange={handleChange}
      fileList={props.value}
      maxCount={maxCount}
      {...rect}
    >
      <Button>上传附件</Button>
    </Upload>
  );
};

const UploadFile = Object.assign(UploadFileComp, {
  toStr: (arr: any[] = []) => {
    return arr
      .map((item) => item.url || item.response?.data?.allFullPath || "")
      .filter((n) => n)
      .join(",");
  },
  toArr: (str = "") => {
    return str
      .split(",")
      .filter((s) => s)
      .map((url, idx) => ({ uid: "-" + idx, name: url.split("/").pop(), url, status: "done" }));
  },
});

export default UploadFile;
