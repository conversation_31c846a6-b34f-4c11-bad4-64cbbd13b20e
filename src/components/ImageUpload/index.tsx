import { PlusOutlined } from "@ant-design/icons";
import { Image, Upload, UploadProps } from "antd";
import { FC } from "react";
import { useSetState } from "react-use";
import css from "./index.module.scss";
import Configs from "@/utils/Configs";
import Compressor from "compressorjs";

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>上传</div>
  </div>
);

const ImageUpload: FC<UploadProps> & {
  serializer: (s: string) => UploadProps["fileList"];
  deserializer: (f: any[]) => string;
  children?: any;
} = (props) => {
  const { onChange, maxCount = 1, ...rect } = props;
  const [state, setState] = useSetState({
    flist: [] as any[],
    preview: false,
    url: "",
  });

  const handleChange = (res: any) => {
    let flist: any[] = [...res.fileList];

    flist = flist.map((item) => {
      const url = item.url || item.response?.data?.allFullPath || "";
      return { ...item, url };
    });

    setState({ flist });
    onChange?.(flist as any);
  };

  const handlePreview = (e: any) => {
    const url = e.url;
    setState({ url, preview: true });
  };

  const beforeUpload: any = (file: any) => {
    if (file?.type == "image/gif") return file;
    return new Promise((resolve) => {
      new Compressor(file, {
        maxWidth: 1920,
        convertSize: 1024 * 200,
        success: (res) => resolve(res),
      });
    });
  };

  const flist = props.fileList || state.flist || [];

  return (
    <>
      <Upload
        action={Configs.uploadHost}
        listType="picture-card"
        fileList={flist}
        onChange={handleChange}
        accept="image/*"
        maxCount={maxCount}
        className={css.uploader}
        // children={uploadButton}
        onPreview={handlePreview}
        beforeUpload={beforeUpload}
        {...rect}
        children={flist.length < maxCount && (props.children ?? uploadButton)}
      />
      <Image
        src={state.url}
        style={{ display: "none" }}
        preview={{
          visible: state.preview,
          onVisibleChange: (preview) => setState({ preview }),
        }}
      />
    </>
  );
};

ImageUpload.serializer = (str = "") => {
  return str
    .split(",")
    .filter((s) => s)
    .map((url, idx) => ({ uid: "-" + idx, name: url, url, status: "done" }));
};

ImageUpload.deserializer = (arr = []) => {
  return (
    arr
      // .filter((n) => n.status === "done")
      .map((item) => item.url || item.response?.data?.allFullPath || "")
      .filter((n) => n)
      .join(",")
  );
};

export default ImageUpload;
