import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Modal, Table } from "antd";
import { cloneElement, useEffect } from "react";
import { useSetState } from "react-use";

interface ShopPickerProps {
  children: any;
  max?: number;
  params?: any;
  onSelect?: (rows: any[]) => any;
}

const ShopPicker = (props: ShopPickerProps) => {
  const [state, setState] = useSetState({
    open: false,


    list: [] as any[],
    loading: false,
    total: 0,
    page: 1,
    size: 10,

    glist: [] as any[],
    // gkeys: [] as any,
  });


  useEffect(() => {
    if (!state.open) return;
    setState({ glist: [] });
    fetchList();
  }, [state.open]);



  const fetchList = async (page = 1) => {

    const params = {
      ...props.params,
      pageSize: state.size,
      pageNum: page,
      businessMode: 1
    };
    setState({ loading: false });
    const res = await MallApi.getShopListForSelect({ params }).finally(() => setState({ loading: false }));
    //const shops = genTree(res?.list || []);
    setState({ list: res?.list || [], page, total: res?.total || 0 });



  };

  const handleOk = () => {
    setState({ open: false });
    props.onSelect?.(state.glist);
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Modal
        open={state.open}
        closable
        width={1000}
        onCancel={() => setState({ open: false })}
        title={<>门店选择</>}
        keyboard={false}
        onOk={handleOk}
      >
        <Table
          rowKey={`id`}
          size="small"
          dataSource={state.list}
          loading={{ spinning: state.loading, delay: 300 }}
          rowSelection={{
            preserveSelectedRowKeys: true,
            selectedRowKeys: state.glist.map((n) => n.id),
            onChange: (_, rows) => {
              const max = props.max ?? 0;

              if (!max) {
                setState({ glist: rows });
              } else if (max === 1) {
                const last = rows[rows.length - 1];
                setState({ glist: last ? [last] : [] });
              } else {
                setState({ glist: rows.slice(0, max) });
              }
            },
          }}
          pagination={{
            current: state.page,
            pageSize: state.size,
            total: state.total,
            onChange: (n) => fetchList(n),
            showTotal: (n) => `共${n}条数据`,
          }}
          columns={[
            { title: "门店", dataIndex: "name" },
            { title: "地址", dataIndex: "fullAddress" },
          ]}
        />
      </Modal>
    </>
  );
};

export default ShopPicker;
