import { SwapRightOutlined } from "@ant-design/icons";
import { Cascader, Form } from "antd";
import dayjs from "dayjs";

const opts = (() => {
  const days = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  const arr: any[] = [];
  const padZore = (n: any) => (n < 10 ? "0" + n : n + "");
  days.forEach((d, m) => {
    m = m + 1;
    const tmp = Object.keys(Array(d).fill(null))
      .map((n) => +n + 1)
      .map((n) => ({
        value: padZore(n),
        label: n + "日",
      }));
    arr.push({ value: padZore(m), label: m + "月", children: tmp });
  });
  return arr;
})();

const BirthdayPicker = (props: { onChange?: Function; value?: any; placeholder?: any }) => {
  const arr = (props.value || "-").split("-").filter((n: any) => n || undefined);

  return (
    <Cascader
      options={opts}
      displayRender={(_, o) => {
        return o?.map((n) => n?.value).join("-");
      }}
      placeholder={props.placeholder}
      onChange={(e) => {
        props.onChange?.(e?.join("-"));
      }}
      value={arr}
    />
  );
};

const BirthdayRangePicker = (props: { name?: any; label?: any; initialValue?: any[] }) => {
  const initialValue = props.initialValue || [];
  const name = props.name || "BirthdayWithoutYear";

  return (
    <Form.Item label={props.label || "生日"}>
      <div style={{ display: "flex" }}>
        <Form.Item noStyle name={`start` + name} initialValue={initialValue[0]}>
          <BirthdayPicker placeholder={`开始日期`} />
        </Form.Item>
        <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
        <Form.Item
          noStyle
          dependencies={["start" + name]}
          name={`end` + name}
          initialValue={initialValue[1]}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                const start = getFieldValue("start" + name);
                const end = value;

                if (start && end && dayjs(`2023-${start}`).isAfter(dayjs(`2023-${end}`))) {
                  return Promise.reject(new Error("开始时间小于结束时间"));
                }

                return Promise.resolve();
              },
            }),
          ]}
        >
          <BirthdayPicker placeholder={`结束日期`} />
        </Form.Item>
      </div>
    </Form.Item>
  );
};

export default BirthdayRangePicker;
