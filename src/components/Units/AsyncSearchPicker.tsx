import { Select, SelectProps, Spin } from "antd";
import { useDebounce, useSetState, useUpdateEffect } from "react-use";

const AsyncSearchPicker = (props: SelectProps & { fetchOptions: (...args: any[]) => Promise<any> }) => {
  const { fetchOptions, placeholder, ...rect } = props;
  const [state, setState] = useSetState({
    loading: false,
    keyword: "",
    keywordDelay: "",
    options: [] as any[],
  });

  useDebounce(() => setState({ keywordDelay: state.keyword }), 600, [state.keyword]);

  useUpdateEffect(() => {
    fetchOpts(state.keywordDelay);
  }, [state.keywordDelay]);

  const fetchOpts = async (keyword: string) => {
    setState({ loading: true });
    const options = (await fetchOptions(keyword).finally(() => setState({ loading: false }))) || [];
    setState({ options });
  };

  return (
    <Select
      // labelInValue
      allowClear
      showSearch
      filterOption={false}
      notFoundContent={state.loading ? <Spin size="small" /> : null}
      onSearch={(e) => setState({ keyword: e })}
      placeholder={placeholder ?? "请输入关键字搜索"}
      options={state.options}
      {...rect}
    />
  );
};

export default AsyncSearchPicker;
