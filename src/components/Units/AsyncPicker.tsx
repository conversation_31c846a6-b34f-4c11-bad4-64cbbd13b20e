import { Select, SelectProps } from "antd";
import { useMount, useSetState } from "react-use";

export default function AsyncPicker(props: SelectProps & { fetch: any }) {
  const { fetch, ...rect } = props;

  const [state, setState] = useSetState({
    opts: [] as any[],
  });

  useMount(async () => {
    const res = await fetch();
    setState({ opts: res });
  });

  return <Select options={state.opts} {...rect} />;
}
