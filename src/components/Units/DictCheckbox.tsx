import UserApi from "@/services/UserApi";
import { Checkbox, SelectProps } from "antd";
import { FC, useEffect } from "react";
import { useSetState } from "react-use";

const DictCheckbox: FC<SelectProps & { type?: string; formater?: (arr: any[]) => any[] }> = (props) => {
  const { type, formater = (n: any[]) => n.map((o) => ({ label: o.name, value: o.id })), value, onChange, ...rect } = props;

  const [state, setState] = useSetState({
    list: [] as any[],
  });

  useEffect(() => {
    if (!props.type) {
      setState({ list: [] });
    } else {
      fetchData();
    }
  }, [props.type]);

  const fetchData = async () => {
    const params = { typeEnCode: props.type, pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = formater?.(res) ?? [];
    setState({ list });
  };

  const handleChange = (checkedValues: any[]) => {
    if (onChange) {
      onChange(checkedValues ? checkedValues.join(',') : '');
    }
  };

  // 将字符串格式的 value 转换为数组格式
  const normalizedValue = typeof value === 'string' ? value.split(',') : value;

  return (
    <Checkbox.Group
      options={state.list}
      value={normalizedValue}
      onChange={handleChange}
      {...rect}
    />
  );
};

export default DictCheckbox;