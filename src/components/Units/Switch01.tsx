import { Checkbox, Switch } from "antd";
import { CheckboxGroupProps } from "antd/es/checkbox";
import { SwitchProps } from "antd/lib";

export const Switch01 = (props: SwitchProps & { value?: any; onChange?: any }) => {
  const { value, ...rect } = props;
  return (
    <>
      <Switch checked={!!props.value} {...rect} onChange={(e) => props.onChange(Number(e))} />
    </>
  );
};

export const SingleCheck = (props: { options?: CheckboxGroupProps["options"]; value?: any; onChange?: any }) => {
  return (
    <div className="flex gap-4">
      {props.options?.map((n: any, i) => (
        <Checkbox
          key={i}
          value={n.value}
          checked={props.value === n.value}
          onChange={(e) => {
            let val = e.target.value;
            if (val == props.value) props.onChange?.(undefined);
            else props.onChange?.(val);
          }}
        >
          {n.label}
        </Checkbox>
      ))}
    </div>
  );
};
