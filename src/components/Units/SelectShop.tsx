import MallApi from "@/services/MallApi";
import { Select, SelectProps } from "antd";
import { useEffect, useState } from "react";

const SelectShop = (props: SelectProps & { params?: any }) => {
  const [shops, setShops] = useState<any[]>([]);

  useEffect(() => {
    fetchShop();
  }, []);

  const fetchShop = async () => {
    const params = { ...props.params, pageSize: 9999, businessMode: 1 };
    const res = await MallApi.getShopListForSelect({ params });
    setShops(res?.list || []);
  };

  return (
    <Select
      options={shops}
      fieldNames={{ label: "name", value: "id" }}
      placeholder="请选择门店"
      allowClear
      {...props}
    ></Select>
  );
};

export default SelectShop;
