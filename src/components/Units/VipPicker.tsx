import Mall<PERSON>pi from "@/services/MallApi";
import AsyncSearchPicker from "./AsyncSearchPicker";
import { SelectProps } from "antd";

const VipPicker = (props: SelectProps & { params?: any }) => {
  return (
    <AsyncSearchPicker
      fetchOptions={async (name) => {
        const params = { searchKey: name, pageSize: 20, ...props.params };
        const res = await MallApi.getMemberForSelect({ params });
        let list: any[] = res?.list || [];
        list = list.map((item) => ({ label: `${item.vipCard} / ${item.name}`, value: item.id }));
        return list;
      }}
      {...props}
    />
  );
};

export default VipPicker;
