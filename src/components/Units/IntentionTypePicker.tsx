import UserApi from "@/services/UserApi";
import { TreeSelect, TreeSelectProps } from "antd";
import { useMount, useSetState } from "react-use";

const genTree = (list: any[]) => {
  const map: any = {};
  const arr: any[] = [];
  list = list || [];
  list.forEach((item) => {
    map[item.id] = item;
  });
  list.forEach((item) => {
    const parent = map[item.parentId];
    if (parent) {
      parent.children = parent.children || [];
      parent.children.push(item);
    } else {
      arr.push(item);
    }
  });
  Object.keys(map).forEach((key) => {
    const item = map[key];
    item.selectable = !item.children?.length;
  });
  return arr;
};

export const IntentionTypePicker = (props: TreeSelectProps) => {
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  useMount(() => {
    fetchList();
  });

  const fetchList = async () => {
    const params = { typeEnCode: "intentionType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const tree = genTree(res);
    setState({ list: tree });
  };

  return (
    <TreeSelect
      className="w-70"
      treeData={state.list}
      fieldNames={{ label: "name", value: "name", children: "children" }}
      treeNodeFilterProp="name"
      allowClear
      placeholder="请选择"
      multiple={false}
      maxTagCount="responsive"
      {...props}
    />
  );
};
