import UserApi from "@/services/UserApi";
import { Select, SelectProps } from "antd";
import { FC, useEffect } from "react";
import { useSetState } from "react-use";

const DictPicker: FC<SelectProps & { type?: string; formater?: (arr: any[]) => any[] }> = (props) => {
  const { type, formater = (n: any[]) => n.map((o) => ({ label: o.name, value: o.id })), ...rect } = props;

  const [state, setState] = useSetState({
    list: [] as any[],
  });

  useEffect(() => {
    if (!props.type) {
      setState({ list: [] });
    } else {
      fetchData();
    }
  }, [props.type]);

  const fetchData = async () => {
    const params = { typeEnCode: props.type, pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = formater?.(res) ?? [];
    setState({ list });
  };

  return (
    <Select
      options={state.list}
      allowClear
      showSearch
      optionFilterProp="label"
      {...rect}
      placeholder={props.placeholder ?? "请选择"}
    />
  );
};

// DictPicker.defaultProps = {
//   formater: (n: any[]) => n.map((o) => ({ label: o.name, value: o.id })),
// };

export default DictPicker;
