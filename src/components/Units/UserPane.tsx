import { ManOutlined, UserOutlined, WechatOutlined, <PERSON>Outlined } from "@ant-design/icons";
import { Ava<PERSON>, Button } from "antd";
import Style from "./UserPane.module.scss";
import { useEffect } from "react";
import dayjs from "dayjs";
import UserInfoEdit from "../UserManager/UserInfoEdit";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { useSetState } from "react-use";

const UserPane = (props: { user?: any; onUpdate?: any }) => {
  const [state, setState] = useSetState({
    user: null as any,
    update: 0,
  });

  useEffect(() => {
    fetchUser();
  }, [props.user?.id]);

  const fetchUser = async () => {
    if (!props.user?.id) return null;

    let user: any = await MallA<PERSON>.getMember(props.user?.id);

    const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : null);

    const _cdate = fdate(user.createDate) ?? "--";
    const _fdate = fdate(user.firstServiceTime) ?? "--";
    const _ldate = fdate(user.lastServiceTime) ?? "--";
    const _age = user.birthday ? dayjs().year() - dayjs(user.birthday).year() : null;
    const _ageStr = (_age ?? "??") + "岁";
    const _expireDate = fdate(user.expireDate) ?? "--";

    let _sex: any = null;

    if (user.sex == 1) _sex = <ManOutlined style={{ color: "#76aad8" }} />;
    if (user.sex == 2) _sex = <WomanOutlined style={{ color: "#e35c55" }} />;

    user = {
      ...user,
      _age,
      _ageStr,
      _cdate,
      _fdate,
      _ldate,
      _sex,
      _expireDate,
    };

    // console.log(user);
    setState({ user });
  };

  const user = state.user;

  if (!user) return null;

  return (
    <div className={Style.user}>
      <div className={Style.avatar}>
        <Avatar size={60} icon={<UserOutlined />} src={user.photo} />
      </div>
      <div className={Style.cont}>
        <div className={Style.head}>
          <div className={Style.un}>
            <div className="flex items-center gap-1">
              <WechatOutlined
                className="text-([16px] #2aae67) data-[disabled=true]:hidden"
                data-disabled={!user.wxsRegisterTag}
              />
              <span>{user.name}</span>
            </div>
          </div>
          <div className={Style.un}>({user.vipCard})</div>

          <div className={Style.gu}></div>

          <div className={Style.un}>{user._ageStr}</div>
          <div className={Style.un}>{user._sex}</div>

          <div className={Style.gu}></div>

          <div className={Style.un}>{user.mobile}</div>

          <div className={Style.gu}></div>
          <div className={Style.gu}></div>

          <div className={Style.un}>顾客来源：{user.sourceName ?? "--"}</div>

          <UserInfoEdit
            title={`编辑用户信息 `}
            data={props.user}
            onOk={() => {
              fetchUser();
              props.onUpdate?.();
            }}
          >
            <Button className="ml-a" type="link">
              编辑
            </Button>
          </UserInfoEdit>
        </div>
        <div className={Style.row}>
          <div className={Style.col}>会员等级: {user.vipLevelName ?? "--"}</div>
          <div className={Style.col}>有效期: {user._expireDate}</div>
          <div className={Style.col}>累计消费: {user.totalConsumeMoney ?? "--"}元</div>
          <div className={Style.col}>建档时间: {user._cdate}</div>
        </div>
        <div className={Style.row}>
          <div className={Style.col}>所属客服: {user.adviserName ?? "--"}</div>
          {/* <div className={Style.col}>所属医生: {user.doctorName ?? "--"}</div> */}
          <div className={Style.col}>所属开发: {user.developerName ?? "--"}</div>
          <div className={Style.col}> 首次到院: {user._fdate}</div>
          <div className={Style.col}> 最近到院: {user._ldate}</div>
        </div>
      </div>
    </div>
  );
};

export default UserPane;
