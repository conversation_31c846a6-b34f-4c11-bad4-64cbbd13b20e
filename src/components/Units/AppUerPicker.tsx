import Mall<PERSON>pi from "@/services/MallApi";
import AsyncSearchPicker from "./AsyncSearchPicker";
import { SelectProps } from "antd";

const AppUerPicker = (props: SelectProps & { params?: any }) => {
  return (
    <AsyncSearchPicker
      fetchOptions={async (name) => {
        const params = { ...props.params, searchKey: name, pageSize: 20 };
        const res = await MallApi.getAppUser({ params });
        let list: any[] = res?.list || [];
        list = list.map((item) => ({ label: `${item.serialNo} / ${item.name} / ${item.mobile}`, value: item.id }));
        return list;
      }}
      {...props}
    />
  );
};

export default AppUerPicker;
