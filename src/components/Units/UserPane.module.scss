.user {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.06);
  padding: 10px;
  display: flex;

  .avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
  }

  .cont {
    flex: 1;
    min-width: 0;
  }

  .head {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #333;
    // padding-bottom: 4px;

    .un {
      // margin-right: 8px;
      font-weight: 500;
    }

    .gu {
      width: 20px;
    }
  }

  .row {
    display: flex;
    color: #666;
    font-size: 12px;
    margin-top: 4px;
    // padding: 8px 0;

    .col {
      // min-width: 180px;
      min-width: 120px;
      margin-right: 30px;
    }
  }
}
