import DictPicker from "@/components/Units/DictPicker";
import WeModal from "@/components/WeModal/WeModal";
import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Button, Col, DatePicker, Divider, Form, Input, Row, Select, Space, Table, message } from "antd";
import { useSetState, useUpdateEffect } from "react-use";
import Style from "./AccessEdit.module.scss";
import MallA<PERSON> from "@/services/MallApi";
import { useEffect } from "react";
import dayjs from "dayjs";
import UserApi from "@/services/UserApi";
import UserPane from "../Units/UserPane";
import { AccessPlan } from "../AccessPlan";

const layout = { row: 10, col: 8 };

const AccessEditV2 = (props: { children: any; title?: any; onOk?: Function; data?: any; type: "single" | "plans" }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    types: [] as any[],
    user: null as any,
    plans: [] as any[],
    idx: 0,
    staffs: [] as any[],
  });

  useEffect(() => {
    form.setFieldsValue({ factAccessTime: dayjs() });
  }, []);

  useUpdateEffect(() => {
    if (state.user?.id) {
      getAccessList();
    }
  }, [state.user?.id]);

  const handleOpen = () => {
    getTypes();
    getStaffs();

    form.resetFields();
    setState({ user: null });

    if (props.data) {
      const { ...data } = props.data;
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      data.factAccessTime = fdate(data.factAccessTime);

      form.setFieldsValue(data);

      if (props.data?.shopVipUserId) {
        getUser();
      }
    }
  };

  const handleSubmit = async () => {
    if (!state.user) {
      message.error("请先选择用户");
      return false;
    }

    const { ...data } = await form.validateFields();
    const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");
    data.factAccessTime = fdate(data.factAccessTime);

    data.shopVipUserId = data.shopVipUserId || state.user.id;
    data.planAccessList = data.planAccessList?.map((item: any) => ({
      ...item,
      preAccessTime: fdate(item.preAccessTime, "YYYY-MM-DD"),
    }));

    if (props.type == "single") {
      await MallApi.putUserAccessLog({ data });
      message.success("修改回访成功");
    }

    if (props.type == "plans") {
      await MallApi.putUserAccessLogPlan({ data });
      message.success("修改计划成功");
    }

    props.onOk?.();
  };

  const getTypes = async () => {
    const params = { typeEnCode: "accessType" };
    const res = await UserApi.getDictDataByCode({ params });
    const types = (res || []).map((item: any) => ({
      label: item.name,
      value: item.id,
    }));

    setState({ types });
  };

  const getStaffs = async () => {
    const params = { pageSize: 9999, positionCode: "ShopHeader,Doctor,Therapist,Nurse" };
    const res = await MallApi.getShopStaffForSelect({ params });
    let staffs: any[] = res.list || [];
    staffs = staffs.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staffs });
  };

  const getUser = async () => {
    const user = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user });
  };

  const getAccessList = async () => {
    const params = {
      pageSize: 9999,
      shopVipUserId: state.user?.id ?? props.data?.shopVipUserId,
      state: "10,40",
    };
    const res = await MallApi.getUserAccessLog({ params });
    let list: any[] = res?.list || [];
    list = list
      // .filter((item) => item.id !== props.data.id)
      .map((item) => ({
        ...item,
        preAccessTime: item.preAccessTime ? dayjs(item.preAccessTime) : undefined,
      }));

    form.setFieldsValue({ planAccessList: list });
  };

  return (
    <WeModal trigger={props.children} width={1000} title={props.title} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        {state.user ? <UserPane user={state.user} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}

        <Row gutter={layout.row}>
          <Form.Item hidden name={`id`}>
            <Input />
          </Form.Item>

          {props.type == "single" && (
            <>
              <Divider orientation="left">本次回访结果</Divider>
              <Col span={layout.col}>
                <Form.Item label="回访类型" name={`typeId`} rules={[{ required: true, message: "请选择回访类型" }]}>
                  {/* <DictPicker type="accessType" placeholder="请选择回访类型" /> */}
                  <Select placeholder="请选择回访类型" options={state.types} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item label="回访时间" name={`factAccessTime`}>
                  <DatePicker style={{ width: "100%" }} placeholder="请选择回访时间" disabled />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item
                  label="联系成功"
                  name={`accessResultTag`}
                  initialValue={1}
                  rules={[{ required: true, message: "请选择联系结果" }]}
                >
                  <Select
                    options={[
                      { label: "是", value: 1 },
                      { label: "否", value: 0 },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={layout.col * 2}>
                <Form.Item
                  label="回访主题"
                  name={`accessTitle`}
                  rules={[{ required: true, message: "请输入回访主题" }]}
                >
                  <Input placeholder="请输入回访主题" disabled />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item label="回访方式" name={`styleId`} rules={[{ required: true, message: "请选择回访方式" }]}>
                  <DictPicker type="accessStyle" placeholder="请选择回访方式" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="回访内容" name={`accessLog`} rules={[{ required: true, message: "请输入回访内容" }]}>
                  <Input.TextArea placeholder="请输入回访内容" autoSize={{ minRows: 2 }} maxLength={300} showCount />
                </Form.Item>
              </Col>
            </>
          )}
          {props.type == "plans" && (
            <Form.List name={`planAccessList`} initialValue={[]}>
              {(fields, operation) => {
                return (
                  <>
                    <Divider orientation="left">
                      下次回访计划
                      <AccessPlan
                        onOk={async (e: any) => {
                          const info = e?.info;
                          // const list: any[] = info?.planDataList || [];
                          const date = dayjs(e?.date || undefined);

                          const res = await MallApi.getAccessTempPlan({
                            params: {
                              templateId: info?.id,
                              shopVipUserId: state.user?.id,
                              fromDate: date.format("YYYY-MM-DD"),
                            },
                          });

                          const arr = res?.list?.map((item: any) => ({
                            ...item,
                            preAccessTime: item.preAccessTime ? dayjs(item.preAccessTime) : undefined,
                          }));

                          form.setFieldsValue({ planAccessList: arr });

                          // console.log(res);

                          // const arr = list.map((item) => ({
                          //   typeId: info?.typeId,
                          //   preAccessTime: date.add(item.accessAfterDay, "day"),
                          //   preEmployeeId: "",
                          //   accessTitle: item.accessTitle,
                          // }));

                          // form.setFieldsValue({ planAccessList: arr });
                        }}
                      >
                        <a className="ml-10px text-14px opacity-60">[使用模板]</a>
                      </AccessPlan>
                    </Divider>

                    <Col span={24} className={Style.table}>
                      <Table
                        dataSource={fields}
                        pagination={false}
                        locale={{ emptyText: "暂无计划" }}
                        columns={[
                          {
                            width: 160,
                            title: "回访类型",
                            render: (item) => (
                              <Form.Item
                                name={[item.name, "typeId"]}
                                style={{ margin: 0, width: "100%" }}
                                rules={[{ required: true, message: "请选择回访类型" }]}
                              >
                                <Select placeholder="请选择" options={state.types} />
                              </Form.Item>
                            ),
                          },
                          {
                            width: 160,
                            title: "应回访日期",
                            render: (item) => (
                              <Form.Item
                                name={[item.name, "preAccessTime"]}
                                style={{ margin: 0 }}
                                rules={[{ required: true, message: "请选择应回访日期" }]}
                              >
                                <DatePicker style={{ width: "100%" }} placeholder="请选择" />
                              </Form.Item>
                            ),
                          },
                          {
                            width: 160,
                            title: "应回访店员",
                            render: (item) => (
                              <Form.Item
                                name={[item.name, "preEmployeeId"]}
                                style={{ margin: 0 }}
                                rules={[{ required: true, message: "请选择回访人" }]}
                              >
                                <Select placeholder="请选择" options={state.staffs} allowClear />
                              </Form.Item>
                            ),
                          },
                          {
                            title: "回访主题",
                            render: (item) => (
                              <Form.Item
                                name={[item.name, "accessTitle"]}
                                style={{ margin: 0 }}
                                rules={[{ required: true, message: "请输入回访主题" }]}
                              >
                                <Input.TextArea autoSize placeholder="请输入" maxLength={300} />
                              </Form.Item>
                            ),
                          },
                          {
                            width: 60,
                            title: "操作",
                            render: (item) => (
                              <Space>
                                <a onClick={() => operation.remove(item.name)}>移除</a>
                              </Space>
                            ),
                          },
                        ]}
                      />
                    </Col>
                    <Col span={24} style={{ paddingTop: 20 }}>
                      <Button type="dashed" icon={<PlusOutlined />} block onClick={() => operation.add()}>
                        添加计划
                      </Button>
                    </Col>
                  </>
                );
              }}
            </Form.List>
          )}
        </Row>
      </Form>
    </WeModal>
  );
};

export default AccessEditV2;
