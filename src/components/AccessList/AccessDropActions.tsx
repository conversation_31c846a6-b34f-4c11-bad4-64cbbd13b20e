import { Dropdown, Typography } from "antd";
import AccessEdit from "./AccessEdit";

const AccessDropActions = (props: { onOk?: Function; userId?: any }) => {
  return (
    <Dropdown
      menu={{
        items: [
          {
            key: "1",
            label: (
              <AccessEdit title={`新增回访`} onOk={props.onOk} data={{ shopVipUserId: props.userId }}>
                <Typography.Link>新增回访</Typography.Link>
              </AccessEdit>
            ),
          },
          {
            key: "2",
            label: (
              <AccessEdit title={`新增计划`} onOk={props.onOk} data={{ shopVipUserId: props.userId }} planOnly>
                <Typography.Link>新增计划</Typography.Link>
              </AccessEdit>
            ),
          },
        ],
      }}
    >
      <Typography.Link>回访</Typography.Link>
    </Dropdown>
  );
};

export default AccessDropActions;
