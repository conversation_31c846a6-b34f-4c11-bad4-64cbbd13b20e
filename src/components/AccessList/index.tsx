import MallApi from "@/services/MallApi";
import { <PERSON><PERSON>, Card, Checkbox, DatePicker, Dropdown, Form, Modal, Pagination, Popconfirm, Select, Space, Switch, Table, Tag, Typography, message } from "antd";
import AccessEdit from "./AccessEdit";
import { useState } from "react";
import { AccessState } from "./typs";
import dayjs from "dayjs";
import UserManager from "@/components/UserManager";
import VipPickerInput from "../VipPlusPicker/PickMemberInput";
import { useMount, useSetState } from "react-use";
import DictPicker from "../Units/DictPicker";
import AccessEditV2 from "./AccessEditV2";
import { useTable } from "../WeTablev2/useTable";
import { ClearOutlined, DeleteOutlined, DownOutlined, EditOutlined, PlusOutlined, SearchOutlined, UpOutlined } from "@ant-design/icons";
import AsyncPicker from "../Units/AsyncPicker";
import { useForm } from "antd/es/form/Form";
import { DatePresetRanges } from "@/utils/Tools";

const AccessList = (props: { type?: "status" | "user" | "today"; userId?: any; preEmployeeId?: any; queryType?: any }) => {
  // 回访状态
  const type_is_status = props.type == "status";
  // 今日回访
  const type_is_today = props.type == "today";
  // 个人中心
  const type_is_user = props.type == "user";

  const [fold, setFold] = useState(true);
  const [state, setState] = useSetState({
    keys: [] as any[],

    staff: [] as any[],
    editOpen: false,
  });

  const fetchList = async (params: any) => {
    params.state = params.state?.join(",");
    const res = await MallApi.getUserAccessLog({ params });
    return res;
  };

  const table = useTable({
    size: type_is_user ? 15 : undefined,
    params: { queryType: props.queryType, shopVipUserId: props.userId },
    onFetch: fetchList,
  });

  const [editform] = useForm();

  useMount(() => {
    fetchStaff();
  });

  const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "--");

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  const handlDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delUserAccessLog({ data });
    message.success("作废成功");
    handleReload();
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  return (
    <div>
      <Form form={table.form} onValuesChange={table.onFormChange}>
        <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
          <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
            <div className="flex-1 grid cols-3 gap-4 overflow-hidden data-[on=true]:h-8" data-on={fold}>
              <Form.Item label="应回访时间" name={`PreAccessTime`} initialValue={(type_is_user || type_is_status) ? [] : [dayjs().subtract(30, "day").startOf("day"), dayjs().endOf("day")]}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              {!type_is_user && (
                <Form.Item label="会员" name={`shopVipUserId`}>
                  <VipPickerInput />
                </Form.Item>
              )}
              <Form.Item label="状态" name={`state`} initialValue={type_is_status || type_is_today ? [10] : null}>
                <Checkbox.Group options={AccessState} />
              </Form.Item>
              {type_is_user && (
                <Form.Item
                  label="数据过滤"
                  name={`accessFilter`}
                  initialValue={true}
                  tooltip={
                    <>
                      <div>最近3次已回访数据</div>
                      <div>所有已超时数据</div>
                      <div>所有未回访数据</div>
                    </>
                  }
                >
                  <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                </Form.Item>
              )}
              {!type_is_today && (
                <Form.Item label="应回访人" name={`preEmployeeId`}>
                  <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择应回访人" />
                </Form.Item>
              )}
              <Form.Item label="实际回访人" name={`factEmployeeId`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择实际回访人" />
              </Form.Item>
              <Form.Item label="类型" name={`typeId`}>
                <DictPicker type="AccessType" />
              </Form.Item>
              <Form.Item label="方式" name={`styleId`}>
                <DictPicker type="AccessStyle" />
              </Form.Item>
            </div>
            <div className="flex gap-2 pl-3">
              <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
              <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                搜索
              </Button>
              <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
            </div>
          </div>
        </Card>

        <Card
          classNames={{ body: "!p-0" }}
          title={
            <Space>
              <AccessEdit title={`新增回访`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
                <Button type="primary" icon={<PlusOutlined />}>
                  新增回访
                </Button>
              </AccessEdit>
              <AccessEdit title={`新增计划`} onOk={handleReload} data={{ shopVipUserId: props.userId }} planOnly>
                <Button type="primary" icon={<PlusOutlined />}>
                  新增计划
                </Button>
              </AccessEdit>
            </Space>
          }
        >
          <Table
            rowKey={"id"}
            scroll={{ x: "max-content" }}
            dataSource={table.state.list}
            columns={[
              {
                fixed: "left",
                title: "会员姓名",
                dataIndex: "vipUser",
                hidden: type_is_user,
                render: (c) => (
                  c ? <UserManager userId={c?.id}>
                    <a>{c?.name}</a>
                  </UserManager>
                    : "--"
                ),
              },
              { title: "门店", dataIndex: "shopName", hidden: !type_is_user },
              { title: "类型", dataIndex: "typeName" },
              { title: "回访主题", dataIndex: "accessTitle" },
              { title: "应回访人", dataIndex: "preEmployeeName" },
              {
                title: "应回访时间",
                //dataIndex: "preAccessTime",
                sorter: true,
                render: (item) => {
                  let v = fdate(item.preAccessTime);
                  let before = item.preAccessTime < dayjs().startOf("day");
                  let after = item.preAccessTime > dayjs().endOf("day");
                  let color = item.state == 30 ? "green" : "blue";
                  if (item.state == 10 && before) {
                    color = "red";
                  }
                  if (item.state == 10 && after) {
                    color = "gray";
                  }
                  return <div style={{ color: `${color}` }}>{v}</div>;
                },
              },
              { title: "实际回访人", dataIndex: "factEmployeeName", render: (c) => c || "--" },
              {
                title: "实际回访时间",
                dataIndex: "factAccessTime",
                render: (c) => <div>{fdate(c, "YYYY-MM-DD HH:mm:ss")}</div>,
              },
              {
                title: "回访内容",
                dataIndex: "accessLog",
                render: (c) => (
                  <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                    {c}
                  </Typography.Text>
                ),
              },
              {
                title: "联系成功",
                dataIndex: "accessResultTag",
                render: (c, item) => {
                  if (item.state == 30) {

                    return <>
                      {c == 1 && <div>是</div>}
                      {c == 0 && <div>否</div>}
                    </>
                  } else {
                    return <div>--</div>
                  }
                },
              },
              { title: "方式", dataIndex: "styleName", render: (c) => c ?? "--" },
              {
                fixed: "right",
                title: "状态",
                //dataIndex: "state",
                render: (item) => {
                  let v = AccessState.find((i) => i.value == item.state)?.label;
                  let before = item.preAccessTime < dayjs().startOf("day");
                  let after = item.preAccessTime > dayjs().endOf("day");
                  let color = item.state == 30 ? "green" : "blue";
                  if (item.state == 10 && before) {
                    color = "red";
                  }
                  if (item.state == 10 && after) {
                    color = "gray";
                  }
                  return <Tag color={`${color}`}>{v}</Tag>;
                  //return <div style={{ color: `${color}` }}>{v}</div>
                },
              },
              {
                fixed: "right",
                title: "操作",
                render: (item) => (
                  <Space>
                    {!type_is_status && (
                      <>
                        <AccessEdit title={`确认回访`} data={item} onOk={handleReload} access>
                          <Typography.Link disabled={![10].includes(item.state)}>
                            {item.state == 30 ? (
                              "回访完成"
                            ) : item.preAccessTime < dayjs().startOf("day") ? (
                              <span style={{ color: "red" }}>开始回访</span>
                            ) : item.preAccessTime > dayjs().endOf("day") ? (
                              <span style={{ color: "gray" }}>提前回访</span>
                            ) : (
                              <span style={{ color: "blue" }}>开始回访</span>
                            )}
                          </Typography.Link>
                        </AccessEdit>

                        <AccessEditV2 title={`修改回访`} data={item} onOk={handleReload} type="single">
                          <Typography.Link disabled={![30].includes(item.state)}>修改回访</Typography.Link>
                        </AccessEditV2>
                      </>
                    )}

                    {!type_is_today && (
                      <>
                        <AccessEditV2 title={`修改计划`} data={item} onOk={handleReload} type="plans">
                          <Typography.Link disabled={![10].includes(item.state)}>修改计划</Typography.Link>
                        </AccessEditV2>

                        <Popconfirm title={`确定要删除这条回访吗？`} onConfirm={() => handlDel(item)}>
                          <Typography.Link disabled={![10, 30].includes(item.state)}>删除</Typography.Link>
                        </Popconfirm>
                      </>
                    )}
                  </Space>
                ),
              },
            ]}
            pagination={false}
            rowSelection={
              type_is_today
                ? undefined
                : {
                  selectedRowKeys: state.keys,
                  onChange: (selectedRowKeys) => {
                    setState({ keys: selectedRowKeys });
                  },
                }
            }
          />
          <div className="p-4 flex items-center">
            {!type_is_today && (
              <Space>
                <Button
                  onClick={() => {
                    const len = state.keys.length;
                    if (len == table.state.list.length) {
                      setState({ keys: [] });
                    } else {
                      setState({ keys: table.state.list.map((n) => n.id) });
                    }
                  }}
                >
                  全选
                </Button>
                <Dropdown
                  menu={{
                    items: [
                      { label: "修改应回访人", key: "edit", icon: <EditOutlined /> },
                      { label: "批量删除", key: "del", icon: <DeleteOutlined />, danger: true },
                    ],
                    onClick: (e) => {
                      if (!state.keys.length) return message.error("请先进行选择");

                      if (e.key == "edit") {
                        setState({ editOpen: true });
                        editform.resetFields();
                      }

                      if (e.key == "del") {
                        Modal.confirm({
                          title: "确定要批量删除这些记录吗？",
                          onOk: async () => {
                            const data = { ids: state.keys.join(",") };
                            await MallApi.delUserAccessLog({ data });
                            message.success("批量删除成功");
                            handleReload();
                          },
                        });
                      }
                    },
                  }}
                >
                  <Button>
                    批量操作
                    <DownOutlined />
                  </Button>
                </Dropdown>
                <div className="text-#666 text-xs">
                  {table.state.list.length}条数据(已选{state.keys.length}条)
                </div>
              </Space>
            )}
            <Pagination
              className="ml-a"
              {...{
                current: table.state.page,
                pageSize: table.state.size,
                total: table.state.total,
                showSizeChanger: true,
                showTotal: (total) => <div>共 {total} 条数据</div>,
                pageSizeOptions: [5, 10, 20, 50, 100],
                onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
              }}
            />
          </div>
        </Card>
      </Form>

      <Modal
        open={state.editOpen}
        title="批量修改"
        width={400}
        onCancel={() => setState({ editOpen: false })}
        onOk={async () => {
          const val = await editform.validateFields();
          const data = { ids: state.keys.join(","), preEmployeeId: val.preEmployeeId };
          await MallApi.putUserAccessLog2({ data });
          setState({ editOpen: false });
          message.success("批量修改成功");
          handleReload();
        }}
      >
        <Form form={editform}>
          <Form.Item label="应回访人" name={"preEmployeeId"} rules={[{ required: true, message: "请选择应回访人" }]}>
            <AsyncPicker
              fetch={async () => {
                const params = { pageSize: 9999, positionCode: "ShopHeader,Doctor,Therapist,Nurse" };
                const res = await MallApi.getShopStaffForSelect({ params });
                let staffs: any[] = res.list || [];
                staffs = staffs.map((item) => ({
                  label: item.user?.name,
                  value: item.user?.id,
                }));
                return staffs;
              }}
              placeholder="请选择应回访人"
              allowClear
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AccessList;
