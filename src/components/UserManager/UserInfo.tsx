import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Button, Descriptions, Input, Tag, Tooltip, message } from "antd";
import { useMount, useSetState } from "react-use";
import { SexMaps } from "./types";
import dayjs from "dayjs";
import { EyeInvisibleOutlined, EyeOutlined, RubyOutlined, SaveOutlined, UserOutlined } from "@ant-design/icons";
import UserInfoEdit from "./UserInfoEdit";
import { VipEdit } from "./VipEdit";
import { str2arr } from "@/utils/Tools";

const UserInfo = (props: { data: any; queryType?: any }) => {
  const [state, setState] = useSetState({
    user: {} as any,
    tag: false,
    rfmTag: false,
  });

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  const saveUserContent = async () => {
    const data = { id: state.user.id, content: state.user.content };
    await MallApi.modifyVipContent({ data });
    message.success("修改成功");
  };

  useMount(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await MallApi.getMember(props.data.id);
    setState({ user });
  };

  return (
    <div>
      <div style={{ height: 20 }}></div>
      <Descriptions
        title="基本信息"
        bordered={false}
        size="small"
        column={4}
        extra={
          <div className="flex items-center">
            <UserInfoEdit title={`编辑用户信息`} data={state.user} onOk={() => fetchUser()}>
              <a>
                <UserOutlined />
                <span className="ml-5px">编辑用户</span>
              </a>
            </UserInfoEdit>

            <VipEdit data={state.user} onOk={() => fetchUser()}>
              <a className="ml-10px">
                <RubyOutlined />
                <span className="ml-5px">编辑VIP</span>
              </a>
            </VipEdit>
          </div>
        }
      >
        <Descriptions.Item label="姓名">{state.user.name}</Descriptions.Item>
        <Descriptions.Item label="会员卡号">{state.user.vipCard}</Descriptions.Item>
        <Descriptions.Item label="性别">{SexMaps.find((n) => n.id === state.user.sex)?.value || "--"}</Descriptions.Item>
        <Descriptions.Item label="手机号">{state.user.mobile}</Descriptions.Item>
        <Descriptions.Item label="身份证号">{state.user.identityCard || "--"}</Descriptions.Item>
        <Descriptions.Item label="生日">{fdate(state.user.birthday, "YYYY-MM-DD") || "--"}</Descriptions.Item>
        <Descriptions.Item label="家庭地址">{state.user.address || "--"}</Descriptions.Item>
        <Descriptions.Item label="邮箱地址">{state.user.email || "--"}</Descriptions.Item>
      </Descriptions>
      <div style={{ height: 20 }}></div>
      <Descriptions title="归属信息" bordered={false} size="small" column={4}>
        <Descriptions.Item label="建档门店">{state.user.shopName || "--"}</Descriptions.Item>
        <Descriptions.Item label="顾客来源">{state.user.sourceName || "--"}</Descriptions.Item>
        <Descriptions.Item label="渠道类型">{state.user.channelName || "--"}</Descriptions.Item>
        <Descriptions.Item label="推荐人" span={2}>
          {state.user.promotionUser ? (
            <>
              {state.user.promotionUser?.vipCard} / {state.user.promotionUser?.name}
            </>
          ) : (
            "--"
          )}
        </Descriptions.Item>
        <Descriptions.Item label="所属客服">{state.user.adviserName || "--"}</Descriptions.Item>
        {/* <Descriptions.Item label="所属医生">{state.user.doctorName || "--"}</Descriptions.Item> */}
        <Descriptions.Item label="所属开发" span={3}>
          {state.user.developerName || "--"}
        </Descriptions.Item>
        {/* <Descriptions.Item label="所属代理">{state.user.agentCompanyName || "--"}</Descriptions.Item>
        <Descriptions.Item label="所属分销">{state.user.distributionCompanyName || "--"}</Descriptions.Item> */}
      </Descriptions>
      <div style={{ height: 20 }}></div>
      <Descriptions title="VIP资料" bordered={false} size="small" column={4}>
        <Descriptions.Item label="会员等级">{state.user.vipLevelName || "--"}</Descriptions.Item>
        <Descriptions.Item label="有效期">{state.user.foreverTag === 1 ? "永久有效" : fdate(state.user.expireDate, "YYYY-MM-DD")}</Descriptions.Item>
      </Descriptions>
      <div style={{ height: 20 }}></div>
      <Descriptions title="扩充信息" bordered={false} size="small" column={4}>
        <Descriptions.Item label="RFM标签">
          {!state.user.rfmLabelName && "--"}
          {!!state.user.rfmLabelName && (
            <>
              {state.rfmTag ? <Tooltip title={state.user.rfmStrategyContent}>{state.user.rfmLabelName}</Tooltip> : <span>****</span>}
              <div onClick={() => setState({ rfmTag: !state.rfmTag })} className="ml-2">
                {state.rfmTag ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </div>
            </>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="用户标签" span={3}>
          {!state.user.labels && "--"}
          {!!state.user.labels && (
            <>
              {state.tag ? str2arr(state.user.labels).map((n) => <Tag key={n}>{n}</Tag>) : <span>****</span>}
              <div onClick={() => setState({ tag: !state.tag })} className="ml-2">
                {state.tag ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              </div>
            </>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="建档时间">{fdate(state.user.createDate) || "--"}</Descriptions.Item>
        <Descriptions.Item label="最后到院" span={3}>
          {fdate(state.user.lastServiceTime) || "--"}
        </Descriptions.Item>
        {/* <Descriptions.Item label="修改时间">{fdate(state.user.updateDate)}</Descriptions.Item> */}
        <Descriptions.Item label="消费备注" span={4}>
          {/* <pre>{state.user.content|| "--"}</pre> */}
          <Input.TextArea
            name={`content`}
            autoSize={{ minRows: 8, maxRows: 10 }}
            placeholder="请输入消费备注"
            value={state.user.content}
            onChange={(e) => setState({ user: { ...state.user, content: e.target.value } })}
          />
        </Descriptions.Item>
      </Descriptions>
      <Button type="primary" icon={<SaveOutlined />} onClick={saveUserContent}>
        保存
      </Button>
    </div>
  );
};

export default UserInfo;
