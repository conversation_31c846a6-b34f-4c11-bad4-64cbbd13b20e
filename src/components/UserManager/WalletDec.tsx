import { Col, Form, Input, InputNumber, Radio, Row, message } from "antd";
import WeModal from "../WeModal/WeModal";
import { CashType } from "./WalletInc";
import Mall<PERSON>pi from "@/services/MallApi";

const layout = { row: 10, col: 12 };

const WalletDec = (props: { children: any; onOk?: Function; userId: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.shopVipUserId = props.userId;
    await MallApi.decWallet({ data });
    message.success("钱包退费成功");
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title="钱包退费" width={600} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              label="退费金额"
              name={`reduceMoney`}
              initialValue={0}
              rules={[{ required: true, message: "请输入退费金额" }]}
            >
              <InputNumber min={0} placeholder="请输入退费金额" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            {/* <Form.Item label="额外扣除积分" name={`reduceIntegral`} initialValue={0}>
              <InputNumber min={0} placeholder="请输入额外扣除积分" style={{ width: "100%" }} />
            </Form.Item> */}
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="退费备注" name={`remark`}>
              <Input.TextArea placeholder="请输入退费备注" autoSize={{ minRows: 2 }} maxLength={100} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="退款方式" name={`cashType`} rules={[{ required: true, message: "请选择退款方式" }]}>
              <Radio.Group options={CashType.map((n) => ({ label: n.name, value: n.id }))} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default WalletDec;
