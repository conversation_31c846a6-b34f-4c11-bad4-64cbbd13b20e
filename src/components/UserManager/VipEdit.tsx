import { Checkbox, Col, DatePicker, Form, Input, message, Row } from "antd";
import WeModal from "../WeModal/WeModal";
import AsyncPicker from "../Units/AsyncPicker";
import MallA<PERSON> from "@/services/MallApi";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };
const fdate = (n: any) => (n ? dayjs(n) : "");
export const VipEdit = (props: any) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    foreverTag: false,
    levelList: [],
    level: null as any,
  });

  const onOpen = () => {
    if (props.data) {
      const data = { ...props.data };
      data.expireDate = data.foreverTag === 1 ? "" : fdate(data.expireDate);
      form.setFieldsValue(data);
      setState({ foreverTag: data.foreverTag ? true : false });
    }
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.foreverTag = Number(data.foreverTag);
    data.expireDate = data.expireDate?.format?.("YYYY-MM-DD 23:59:59") || "";
    if (state.level?.foreverTag == 1) {
      data.foreverTag = 1;
    }
    if (data.foreverTag == 0 && !data.expireDate) {
      message.error("请选择会员有效期");
      return false;
    }
    await MallApi.putMemberLv({ data });
    message.success("编辑成功");
    props.onOk?.();
  };

  const levelChange = async (v: any) => {
    const level = state.levelList.find((item: any) => item.id == v);
    setState({ level: level, foreverTag: false });
    form.setFieldsValue({ foreverTag: 0 });
  };

  const checkChange = (e: any) => {
    form.setFieldsValue({ expireDate: undefined });
    if (e.target.checked) {
      setState({ foreverTag: true });
    } else {
      setState({ foreverTag: false });
    }
  };

  return (
    <WeModal trigger={props.children} title="编辑VIP" width={400} onOpen={onOpen} onOk={onOk}>
      <div className="h-10px"></div>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`vipLevelId`} label="会员等级" rules={[{ required: true, message: "请选择会员等级" }]}>
              <AsyncPicker
                fetch={async () => {
                  const params = { pageSize: 9999 };
                  const res = await MallApi.getMLevelForSelect({ params });
                  const list = res?.list || [];
                  setState({ levelList: list });
                  if (props.data) {
                    setState({ level: (res?.list || []).find((item: any) => item.id == props.data.vipLevelId) });
                  }
                  return list;
                }}
                placeholder="请选择会员等级"
                fieldNames={{ label: "name", value: "id" }}
                onChange={levelChange}
              />
            </Form.Item>
          </Col>
          {!state.level?.foreverTag && (
            <>
              <Col span={18}>
                <Form.Item
                  name={`expireDate`}
                  label="有效期"
                  rules={[{ required: false, message: "请选择会员有效期" }]}
                >
                  <DatePicker placeholder="请选择有效期" disabled={state.foreverTag} style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item name={`foreverTag`} initialValue={false} valuePropName="checked">
                  <Checkbox value={1} onChange={checkChange}>
                    永久
                  </Checkbox>
                </Form.Item>
              </Col>
            </>
          )}
        </Row>
      </Form>
    </WeModal>
  );
};
