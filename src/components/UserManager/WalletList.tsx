import MallApi from "@/services/MallApi";
import WeTable, { WeTableRef } from "../WeTable";
import { useEffect, useMemo, useRef } from "react";
import { DatePicker, Form, Select } from "antd";
import dayjs from "dayjs";
import WalletInc from "./WalletInc";
import WalletDec from "./WalletDec";
import IntegralEdit from "./IntegralEdit";
import { useMount, useSetState } from "react-use";
import { DatePresetRanges } from "@/utils/Tools";
import { makeApi } from "@/utils/Api";
import CouponList from "./CouponList";
import CouponEdit from "./CouponEdit";

const tabs = [
  { key: "coin", name: "M币", source: 2 },
  { key: "wallet", name: "钱包", source: 1 },
  { key: "coupon", name: "优惠券" },
];

const getCouponApi = makeApi("get", "/api/appUserCoupon/query_state_total");

const WalletList = (props: { userId: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    user: null as any,
    types: [] as any[],

    coupon: null as any,
    couponUpdate: 1,

    tab: "coin",
  });

  const tabItem = useMemo(() => {
    return tabs.find((n) => n.key == state.tab);
  }, [state.tab]);

  useMount(() => {
    fetchUser();
    fetchCoupon();
  });

  useEffect(() => {
    fetchTypes();
  }, [state.tab]);

  const fetchUser = async () => {
    const user = await MallApi.getMember(props.userId);
    setState({ user });
  };

  const fetchTypes = async () => {
    const params = { source: tabItem?.source };
    const res = await MallApi.getWalletType({ params });
    setState({ types: res?.list || [] });
  };

  const fetchCoupon = async () => {
    const params = { appUserId: props.userId };
    const res = await getCouponApi({ params });
    const total = res?.list?.find((n: any) => n.state == 10)?.totalCount || 0;
    setState({ coupon: total });
  };

  const handleReload = () => {
    fetchUser();
    fetchCoupon();
    tableRef.current?.reload();
  };

  return (
    <div>
      <div className="flex items-center justify-center pb-5 gap-5">
        {tabs.map((item) => {
          if (item.key == "coin") {
            return (
              <div
                key={item.key}
                className="group w-60 b-(1 solid #eee) rounded-2 p-3 cursor-pointer flex data-[on=true]:(bg-#eee)"
                data-on={state.tab == item.key}
                onClick={() => setState({ tab: item.key })}
              >
                <div className="flex-1 flex flex-col justify-center">
                  <div className="text-(5 #333) fw-bold flex items-center justify-between">
                    <span>{item.name}</span>
                    <div className="flex gap-1 text-3 justify-center invisible group-data-[on=true]:visible">
                      <IntegralEdit type="inc" userId={props.userId} onOk={handleReload}>
                        <a>增加</a>
                      </IntegralEdit>
                      <IntegralEdit type="dec" userId={props.userId} onOk={handleReload}>
                        <a>扣除</a>
                      </IntegralEdit>
                    </div>
                  </div>
                  <div className="text-(#666) mt-2 flex">
                    <div className="w-1/2">余额：{state.user?.coin}</div>
                    <div className="w-1/2">可提现：{state.user?.cashCoin}</div>
                  </div>
                </div>
              </div>
            );
          }

          if (item.key == "wallet") {
            return (
              <div
                key={item.key}
                className="group w-60 b-(1 solid #eee) rounded-2 p-3 cursor-pointer flex data-[on=true]:(bg-#eee)"
                data-on={state.tab == item.key}
                onClick={() => setState({ tab: item.key })}
              >
                <div className="flex-1 flex flex-col justify-center">
                  <div className="text-(5 #333) fw-bold flex items-center justify-between">
                    <span>{item.name}</span>
                    <div className="flex gap-1 text-3 justify-center invisible group-data-[on=true]:visible">
                      <WalletInc userId={props.userId} onOk={handleReload}>
                        <a>充值</a>
                      </WalletInc>
                      <WalletDec userId={props.userId} onOk={handleReload}>
                        <a>退费</a>
                      </WalletDec>
                    </div>
                  </div>
                  <div className="text-(#666) mt-2">
                    余额：&yen;{state.user?.balance}
                  </div>
                </div>
              </div>
            );
          }

          if (item.key == "coupon") {
            return (
              <div
                key={item.key}
                className="group w-60 b-(1 solid #eee) rounded-2 p-3 cursor-pointer flex data-[on=true]:(bg-#eee)"
                data-on={state.tab == item.key}
                onClick={() => setState({ tab: item.key })}
              >
                <div className="flex-1 flex flex-col justify-center">
                  <div className="text-(5 #333) fw-bold flex items-center justify-between">
                    <span>{item.name}</span>
                    <div className="flex gap-1 text-3 justify-center invisible group-data-[on=true]:visible">
                      <CouponEdit
                        title={`赠送优惠券`}
                        data={state.user}
                        onOk={() => {
                          setState({ couponUpdate: state.couponUpdate + 1 });
                          handleReload();
                        }}
                      >
                        <a>赠送</a>
                      </CouponEdit>
                    </div>
                  </div>
                  <div className="text-(#666) mt-2">
                    可用：{state.coupon}张
                  </div>
                </div>
              </div>
            );
          }
          return null;
        })}
      </div>

      {["coin", "wallet"].includes(state.tab) && (
        <div>
          <WeTable
            ref={tableRef}
            size={props.userId ? 10 : undefined}
            tableProps={{ scroll: { x: "max-content" } }}
            request={(params) => {
              return MallApi.getWalletLog({ params });
            }}
            params={{ shopVipUserId: props.userId, source: tabItem?.source }}
            search={[
              <Form.Item label="日期" name={`CreateDate`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>,
              <Form.Item label="类型" name={`moneyType`}>
                <Select placeholder="请选择类型" options={state.types} fieldNames={{ label: "typeName", value: "typeId" }} allowClear showSearch optionFilterProp="typeName" />
              </Form.Item>,
            ]}
            columns={[
              {
                title: "日期",
                dataIndex: "createDate",
                render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
                width: 200,
              },
              { width: 200, title: "类型", dataIndex: "moneyTypeName" },
              { width: 200, title: `变动${tabItem?.name}`, render: (c) => ([2, 3].includes(c.source) ? c.coin : c.money) },
              // { title: `结余${sourceName}`, render: (c) => (c.source == 2 ? c.finalCoin : c.finalMoney) },
              // { title: "关联号", dataIndex: "relateExtId" },
              { width: 200, title: "操作人", dataIndex: "employeeName", render: (c) => c || "--" },
              { title: "备注", dataIndex: "moneyDesc", width: 400 },
            ]}
          />
        </div>
      )}

      {state.tab == "coupon" && <CouponList data={state.user} queryType={props.queryType || 9} onOk={handleReload} updateKey={state.couponUpdate} />}
    </div>
  );
};

export default WalletList;
