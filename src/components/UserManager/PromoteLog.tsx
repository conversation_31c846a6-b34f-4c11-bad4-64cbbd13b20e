import WeTable from "../WeTable";
import { formatDate } from "@/utils/Tools";
import { Form, Input, Radio, Tooltip } from "antd";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { BindStyle } from "./types";

export default function PromoteLog(props: { userId?: any; queryType?: any }) {
  return (
    <div>
      <WeTable
        size={10}
        tableProps={{ scroll: { x: "max-content" } }}
        // ref={tableRef}
        search={[
          <Form.Item label="会员" name={`searchKey`}>
            <Input placeholder="会员号/姓名/手机号" />
          </Form.Item>,

          <Form.Item label="到院状态" name={`serviceTag`} initialValue={""}>
            <Radio.Group>
              <Radio value="">全部</Radio>
              <Radio value={1}>已到院</Radio>
              <Radio value={0}>未到院</Radio>
            </Radio.Group>
          </Form.Item>,
        ]}
        params={{ promotionUserId: props.userId }}
        request={(p) => MallApi.getMemberChild({ params: p })}
        columns={[
          { title: "会员姓名", dataIndex: "name" },
          { title: "会员号", dataIndex: "vipCard" },
          { title: "手机", dataIndex: "mobile" },
          // {
          //   title: "推荐人/会员号",
          //   dataIndex: "promotionUser",
          //   render: (c) => (c ? c.name + " / " + c.vipCard : "--"),
          // },
          {
            title: "首次到院",
            dataIndex: "firstServiceTime",
            render: (c) => (c ? formatDate(c) : <span className="text-red-4">暂无</span>),
          },
          {
            title: "最近到院",
            dataIndex: "lastServiceTime",
            render: (c) => (c ? formatDate(c) : <span className="text-red-4">暂无</span>),
          },
          {
            title: "会员建档时间",
            dataIndex: "createDate",
            render: (c) => formatDate(c),
          },
          {
            title: "建立关系方式",
            dataIndex: "bindStyle",
            render: (c) => {
              return BindStyle.find((n) => n.value === c)?.label || "--";
            },
          },
          {
            title: (
              <>
                建立关系时间
                <Tooltip title="此时间为会员与推荐人建立关系的时间，仅作为参考">
                  <QuestionCircleOutlined style={{ cursor: "pointer", marginLeft: 4 }} />
                </Tooltip>
              </>
            ),
            dataIndex: "promotionDate",
            render: (c) => (c ? formatDate(c) : "--"),
          },
        ]}
      />
    </div>
  );
}
