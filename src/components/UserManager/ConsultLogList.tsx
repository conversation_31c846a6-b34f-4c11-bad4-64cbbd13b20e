import Mall<PERSON><PERSON> from "@/services/MallApi";
import WeTable, { WeTableRef } from "../WeTable";
import { useRef } from "react";
import { DatePicker, Form, Popconfirm, Space, Typography, message } from "antd";
import DictPicker from "../Units/DictPicker";
import ConsultLogEdit from "./ConsultLogEdit";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";

const ConsultLogList = (props: { userId: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delConsultLog({ data });
    message.success("删除操作成功");
    handleReload();
  };
  const handleFinish = async (item: any) => {
    const data = { id: item.id, state: 20 };
    await MallApi.putConsultLog({ data });
    message.success("操作成功");
    handleReload();
  };

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  return (
    <WeTable
      size={props.userId ? 10 : undefined}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      params={{ queryType: props.queryType, shopVipUserId: props.userId }}
      request={(params) => MallApi.getConsultLog({ params })}
      title={
        <ConsultLogEdit title={`添加咨询`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
          <WeTable.AddBtn />
        </ConsultLogEdit>
      }
      search={[
        <Form.Item label="日期" name={`CreateDate`}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
        <Form.Item label="咨询类型" name={`consultTypeId`}>
          <DictPicker type="consultType" />
        </Form.Item>,
      ]}
      columns={[
        { title: "门店", dataIndex: "shopName" },
        { title: "咨询类型", dataIndex: "consultTypeName" },
        {
          title: "咨询意向",
          dataIndex: "intentionType",
          width: 500,
          render: (c) => (
            <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
        {
          title: "备注",
          dataIndex: "content",
          width: 300,
          render: (c) => (
            <Typography.Text style={{ width: 300 }} ellipsis={{ tooltip: true }}>
              {c || "--"}
            </Typography.Text>
          ),
        },
        { title: "登记人", dataIndex: "employeeName" },
        {
          title: "状态",
          dataIndex: "state",
          render: (c) => (c === 10 ? <span style={{ color: "green" }}>进行中</span> : <span style={{ color: "gray" }}>已结束</span>),
        },
        { title: "日期", dataIndex: "createDate", render: (c) => fdate(c) },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <Popconfirm title={`确定要结束这条记录吗？`} onConfirm={() => handleFinish(item)}>
                <Typography.Link>结束</Typography.Link>
              </Popconfirm>
              <ConsultLogEdit title={`修改咨询`} data={{ id: item.id }} onOk={handleReload}>
                <Typography.Link>修改</Typography.Link>
              </ConsultLogEdit>
              <Popconfirm title={`确定要删除这条记录吗？`} onConfirm={() => handleDel(item)}>
                <Typography.Link>删除</Typography.Link>
              </Popconfirm>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default ConsultLogList;
