import { Col, Form, Input, Row, message } from "antd";
import WeModal from "../WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const layout = { row: 10, col: 12 };

const NoteEdit = (props: {
  children: any;
  title: any;
  data: { id?: any; shopVipUserId?: any; triageId?: any };
  onOk?: Function;
}) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();

    if (props.data?.id) {
      const { ...data } = await MallApi.getNoteItem(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();

    if (data.id) {
      await MallApi.putNote({ data });
      message.success("编辑备注成功");
    } else {
      data.shopVipUserId = props.data?.shopVipUserId;
      data.triageId = props.data?.triageId;

      await MallApi.addNote({ data });
      message.success("添加备注成功");
    }

    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={700} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="内容" name={`content`}>
              <Input.TextArea autoSize={{ minRows: 5 }} maxLength={300} showCount allowClear placeholder="请输入内容" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default NoteEdit;
