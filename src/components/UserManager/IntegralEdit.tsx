import MallApi from "@/services/MallApi";
import { Checkbox, Col, Form, Input, InputNumber, Row, message } from "antd";
import WeModal from "../WeModal/WeModal";

const layout = { row: 10, col: 12 };

const IntegralEdit = (props: { children: any; onOk?: Function; userId: any; type: "inc" | "dec" }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.shopVipUserId = props.userId;
    data.cashTag = Number(data.cashTag);

    if (props.type === "inc") {
      await MallApi.incIntegral({ data });
      message.success("M币增加成功");
      props.onOk?.();
    } else if (props.type === "dec") {
      await MallApi.decIntegral({ data });
      message.success("M币扣除成功");
      props.onOk?.();
    }
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.type === "inc" ? "M币增加" : "M币扣除"}
      width={600}
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          {props.type === "inc" && (
            <>
              <Col span={layout.col}>
                <Form.Item
                  label="增加M币"
                  name={`increaseCoin`}
                  initialValue={0}
                  rules={[{ required: true, message: "请输入增加M币" }]}
                >
                  <InputNumber min={0} placeholder="请输入增加M币" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item name={`cashTag`} initialValue={false} valuePropName="checked">
                  <Checkbox>可提现</Checkbox>
                </Form.Item>
              </Col>
            </>
          )}
          {props.type === "dec" && (
            <>
              <Col span={layout.col}>
                <Form.Item
                  label="扣除M币"
                  name={`reduceCoin`}
                  initialValue={0}
                  rules={[{ required: true, message: "请输入扣除M币" }]}
                >
                  <InputNumber min={0} placeholder="请输入扣除M币" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item name={`cashTag`} initialValue={false} valuePropName="checked">
                  <Checkbox>可提现</Checkbox>
                </Form.Item>
              </Col>
            </>
          )}
          <Col span={layout.col * 2}>
            <Form.Item label="操作备注" name={`remark`}>
              <Input.TextArea placeholder="请输入操作备注" autoSize={{ minRows: 2 }} maxLength={100} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default IntegralEdit;
