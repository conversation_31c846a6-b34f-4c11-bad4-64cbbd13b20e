import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, Popconfirm, Select, Space, Tag, message } from "antd";
import { CouponSource, CouponState, CouponType } from "./types";
import { useRef } from "react";
import dayjs from "dayjs";
import { useUpdateEffect } from "react-use";

const CouponList = (props: { onOk?: Function; data?: any; queryType?: any; updateKey?: any }) => {
  const tableRef = useRef<WeTableRef>(null);

  useUpdateEffect(() => {
    handleReload();
  }, [props.updateKey]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const renderTag = (types: any[], id: any) => {
    const crt = types.find((n) => n.id == id);
    return <Tag color={crt?.color}>{crt?.name}</Tag>;
  };

  const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD") : "");

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delUserCoupon({ data });
    message.success("删除成功");
    handleReload();
    props.onOk?.();
  };

  return (
    <WeTable
      size={props.data?.id ? 10 : undefined}
      ref={tableRef}
      params={{ appUserId: props.data?.appUserId }}
      request={(params) => MallApi.getUserCoupon({ params })}
      // title={
      //   <CouponEdit title={`赠送优惠券`} data={props.data} onOk={handleReload}>
      //     <WeTable.AddBtn>赠送优惠券</WeTable.AddBtn>
      //   </CouponEdit>
      // }
      search={[
        <Form.Item label="优惠券名称" name={`name`}>
          <Input placeholder="请输入优惠券名称" allowClear />
        </Form.Item>,
        <Form.Item label="优惠券状态" name={`state`} initialValue={10}>
          <Select options={CouponState.map((item) => ({ label: item.name, value: item.id }))} placeholder="请选择优惠券状态" allowClear />
        </Form.Item>,
      ]}
      columns={[
        { title: "优惠券名称", dataIndex: "name" },
        { title: "类型", dataIndex: "type", render: (c) => CouponType.find((n) => n.value == c)?.label || "--" },
        { title: "优惠金额/折扣", render: (c) => <>{c.type == 10 ? `${c.preferentialMoney}元` : `${c.discount}折`}</> },
        { title: "消费门槛", dataIndex: "minConsumeMoney", render: (c) => (c ? `满${c}元` : "无门槛") },
        {
          title: "有效期",
          width: 200,
          render: (c) => (
            <>
              <div>
                {fdate(c.effectiveStartDate)} 至 {fdate(c.effectiveEndDate)}
              </div>
            </>
          ),
        },
        // {
        //   title: "范围",
        //   render: (c) => (
        //     <>
        //       {c.scope == 20 && `--`}
        //       {c.scope == 30 && <Tag>{c.productName}</Tag>}
        //     </>
        //   ),
        // },
        // { title: "范围", dataIndex: "scope", render: (c) => renderTag(CouponScope, c) },
        { title: "来源", dataIndex: "source", render: (c) => CouponSource.find((n) => n.value == c)?.label || "--" },
        { title: "备注", dataIndex: "content", width: 200, ellipsis: true },
        { title: "状态", dataIndex: "state", render: (c) => renderTag(CouponState, c) },
        {
          title: "操作",
          render: (item) => {
            return (
              <Space>
                <Popconfirm title={`确定要删除这条优惠券吗？`} onConfirm={() => handleDel(item)}>
                  <a>删除</a>
                </Popconfirm>
              </Space>
            );
          },
        },
      ]}
    />
  );
};

export default CouponList;
