import Mall<PERSON>pi from "@/services/MallApi";
import WeTable, { WeTableRef } from "../WeTable";
import { useRef } from "react";
import { DatePicker, Form, Popconfirm, Space, Typography, message } from "antd";
import NoteEdit from "./NoteEdit";
import dayjs from "dayjs";
import { DatePresetRanges } from "@/utils/Tools";

const NoteList = (props: { userId: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delNote({ data });
    message.success("删除操作成功");
    handleReload();
  };
  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  return (
    <WeTable
      size={props.userId ? 10 : undefined}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      request={(params) => MallApi.getNote({ params })}
      params={{ queryType: props.queryType, shopVipUserId: props.userId }}
      title={
        <NoteEdit title={`添加备注`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
          <WeTable.AddBtn />
        </NoteEdit>
      }
      search={[
        <Form.Item label="日期" name={`CreateDate`}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
      ]}
      columns={[
        { title: "门店", dataIndex: "shopName" },
        { title: "内容", dataIndex: "content", width: "500px" },
        { title: "日期", dataIndex: "createDate", render: (c) => fdate(c) },
        { title: "创建人", dataIndex: "employeeName" },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <NoteEdit title={`修改备注`} data={{ id: item.id }} onOk={handleReload}>
                <Typography.Link>修改</Typography.Link>
              </NoteEdit>
              <Popconfirm title={`确定要删除这条记录吗？`} onConfirm={() => handleDel(item)}>
                <Typography.Link>删除</Typography.Link>
              </Popconfirm>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default NoteList;
