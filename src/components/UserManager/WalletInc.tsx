import { Col, Form, Input, InputNumber, Radio, Row, Select, message } from "antd";
import WeModal from "../WeModal/WeModal";
import { useSetState } from "react-use";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const layout = { row: 10, col: 12 };

export const CashType = [
  { id: 10, name: "现金" },
  { id: 11, name: "银行卡" },
  { id: 12, name: "二维码" },
];

const WalletInc = (props: { children: any; onOk?: Function; userId: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    staff: [] as any[],
  });

  const handleOpen = () => {
    fetchStaff();
    form.resetFields();
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.shopVipUserId = props.userId;
    await MallApi.incWallet({ data });
    message.success("钱包充值成功");
    props.onOk?.();
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.user?.name, value: item.user?.id }));
    setState({ staff: list });
  };

  return (
    <WeModal trigger={props.children} title="钱包充值" width={600} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              label="充值金额"
              name={`rechargeMoney`}
              initialValue={0}
              rules={[{ required: true, message: "请输入充值金额" }]}
            >
              <InputNumber min={0} placeholder="请输入充值金额" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            {/* <Form.Item label="额外赠送积分" name={`increaseIntegral`} initialValue={0}>
              <InputNumber min={0} placeholder="请输入额外赠送积分" style={{ width: "100%" }} />
            </Form.Item> */}
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="充值备注" name={`remark`}>
              <Input.TextArea placeholder="请输入充值备注" autoSize={{ minRows: 2 }} maxLength={100} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="归属客服" name={`adviserId`}>
              <Select
                options={state.staff}
                allowClear
                showSearch
                optionFilterProp="label"
                placeholder="请选择归属客服"
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="归属开发" name={`developerId`}>
              <Select
                options={state.staff}
                allowClear
                showSearch
                optionFilterProp="label"
                placeholder="请选择归属开发"
              />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="收款方式" name={`cashType`} rules={[{ required: true, message: "请选择收款方式" }]}>
              <Radio.Group options={CashType.map((n) => ({ label: n.name, value: n.id }))} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default WalletInc;
