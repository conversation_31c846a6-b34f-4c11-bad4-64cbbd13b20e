import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Input, InputNumber, Row, Select, message } from "antd";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };

const CouponEdit = (props: { title: any; children: any; onOk?: Function; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    clist: [] as any[],
  });

  const handleOpen = () => {
    form.resetFields();
    fetchList();
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.shopVipUserId = props.data?.id;

    await MallApi.addUserCoupon({ data });
    props.onOk?.();
    message.success("赠送优惠券成功");
  };

  const fetchList = async () => {
    const shopId = props.data?.shopId;
    const params = { shopId, saleIn: 1, pageSize: 9999 };
    const res = await MallApi.getShopCouponList({ params });
    let clist: any[] = res?.list || [];
    clist = clist.map((item) => {
      // 10.满减券 20.折扣券
      const save = item.type == 10 ? item.preferentialMoney + "元" : item.discount + "折";
      const min = item.minConsumeMoney ? "满" + item.minConsumeMoney : "无门槛";
      const _name = `${item.name} - ${min} - ${save}`;

      return { ...item, _name };
    });
    setState({ clist });
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`mallCouponId`} label={`优惠券`} rules={[{ required: true, message: "请选择优惠券" }]}>
              <Select placeholder="请选择优惠券" options={state.clist} fieldNames={{ label: "_name", value: "id" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`count`}
              label={`赠送数量`}
              rules={[{ required: true, message: "请填写赠送数量" }]}
              initialValue={1}
            >
              <InputNumber min={1} step={1} placeholder="请填写赠送数量" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`content`} label={`备注说明`} rules={[{ required: true, message: "请填写备注说明" }]}>
              <Input.TextArea placeholder="请填写备注说明" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default CouponEdit;
