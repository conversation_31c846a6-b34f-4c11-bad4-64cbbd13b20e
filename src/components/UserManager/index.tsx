import { Drawer, Tabs } from "antd";
import { cloneElement, useEffect } from "react";
import { useSetState } from "react-use";
import Style from "./index.module.scss";
import MallApi from "@/services/MallApi";
import WalletList from "./WalletList";
import UserInfo from "./UserInfo";
import UserPane from "../Units/UserPane";
// import OrderPage1 from "@/pages/admin/mall/ordersNew1/index.page";
import ServiceOrder from "@/pages/admin/mall/serviceOrder/index.page";
import ServicePhoto from "@/pages/admin/mall/servicePhoto/index.page";
import Reservation from "@/pages/admin/mall/ReservationList/index.page";
import TriageList from "@/pages/admin/mall/triage/index.page";
import ConsultLogList from "./ConsultLogList";
// import OrderPage2 from "@/pages/admin/mall/orders2/index.page";
import UseProject from "@/pages/admin/mall/useProject/index.page";
import NoteList from "./NoteList";
import OrderListPage from "@/pages/admin/mall/ShopOrder/OrderList";
import AccessList from "../AccessList";
import PromoteLog from "./PromoteLog";

const tabItems = [
  { key: "user", label: "用户信息" },
  { key: "wallet", label: "资产管理" },
  { key: "yuyue", label: "预约记录" },
  { key: "fenzhen", label: "到院记录" },
  { key: "zhixun", label: "咨询记录" },
  { key: "ordersNew1", label: "订单记录" },
  { key: "useProject", label: "项目清单" },
  { key: "serviceOrder", label: "治疗单" },
  { key: "note", label: "治疗备注" },
  { key: "photo", label: "对比照" },
  { key: "access", label: "回访记录" },
  { key: "promoteLog", label: "推荐记录" },
];

const UserManager = (props: { children: any; userId: any; queryType?: any }) => {
  const [state, setState] = useSetState({
    show: false,
    user: {} as any,
    tabKey: tabItems[0].key,
  });

  useEffect(() => {
    if (!state.show) return;
    fetchUser();
  }, [state.show]);

  const fetchUser = async () => {
    const user = await MallApi.getMember(props.userId);
    setState({ user });
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ show: true, tabKey: "user" }),
      })}
      <Drawer
        open={state.show}
        // headerStyle={{ display: "none" }}
        classNames={{ header: "hidden" }}
        width={"80%"}
        onClose={() => {
          setState({ show: false });
        }}
        // destroyOnClose={true}
        destroyOnHidden
      >
        <div className={Style.body}>
          <UserPane user={state.user} />
          <Tabs
            style={{ marginTop: 8 }}
            items={tabItems}
            onChange={(e) => {
              setState({ tabKey: e });
            }}
            activeKey={state.tabKey}
          />

          {state.user?.id && (
            <div className={Style.tabCont}>
              {state.tabKey === "user" && <UserInfo data={state.user} queryType={props.queryType || 1} />}
              {state.tabKey === "wallet" && <WalletList userId={state.user?.id} queryType={props.queryType || 9} />}
              {state.tabKey === "yuyue" && <Reservation userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "fenzhen" && <TriageList userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "zhixun" && <ConsultLogList userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "ordersNew1" && <OrderListPage userId={state.user?.id} queryType={props.queryType || 3} />}
              {state.tabKey === "useProject" && <UseProject userId={state.user?.id} queryType={props.queryType || 3} />}
              {state.tabKey === "serviceOrder" && <ServiceOrder userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "note" && <NoteList userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "photo" && <ServicePhoto userId={state.user?.id} queryType={props.queryType || 1} />}
              {state.tabKey === "access" && <AccessList userId={state.user?.id} type="user" queryType={props.queryType || 1} />}
              {state.tabKey === "promoteLog" && <PromoteLog userId={state.user?.id} queryType={props.queryType || 9} />}
            </div>
          )}
        </div>
      </Drawer>
    </>
  );
};

export default UserManager;
