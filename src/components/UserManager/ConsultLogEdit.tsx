import { Col, Form, Input, Row, message } from "antd";
import WeModal from "../WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import DictPicker from "../Units/DictPicker";
import { IntentionTypePicker } from "../Units/IntentionTypePicker";

const layout = { row: 10, col: 12 };

// const GoodsPickerInput = (props: any) => {
//   return (
//     <GoodsPicker
//       max={10}
//       property={1}
//       onSelect={(rows) => {
//         // const item = rows[0];
//         const item = rows;
//         props.onChange?.(item);
//       }}
//     >
//       {/* <Input readOnly value={props.value?.name} placeholder="请选择项目" /> */}
//       <Input readOnly value={props?.value?.map((m: any) => m.name)} placeholder="请选择项目" />
//     </GoodsPicker>
//   );
// };

const ConsultLogEdit = (props: {
  children: any;
  title: any;
  data: { id?: any; shopVipUserId?: any; triageId?: any };
  onOk?: Function;
}) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();

    if (props.data?.id) {
      const { ...data } = await MallApi.getConsultLogItem(props.data.id);
      data.intentionType = (data.intentionType || "").split(",").filter((n: any) => n);

      /* const func = (name: string) => {
        // data[name] = { id: data[name], name: data[name.replace(/Id$/, "Name")] };
        if (!data[name]) return;
        const idArr = data[name]?.split(",");
        const nameArr = data[name.replace(/Id$/, "Name")]?.split(",");
        data[name] = idArr.map((c: any, i: number) => {
          return { id: c, name: nameArr[i] };
        });
      };

      func("consultProductId");
      func("intentionProductId");
      func("recommendProductId");
      func("potentialProductId"); */

      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    // data.consultProductId = data.consultProductId?.map((c: any) => c.id).join(",") || "";
    // data.intentionProductId = data.intentionProductId?.map((c: any) => c.id).join(",") || "";
    // data.recommendProductId = data.recommendProductId?.map((c: any) => c.id).join(",") || "";
    // data.potentialProductId = data.potentialProductId?.map((c: any) => c.id).join(",") || "";

    data.intentionType = data.intentionType.join(",");

    if (data.id) {
      await MallApi.putConsultLog({ data });
      message.success("编辑意向成功");
    } else {
      data.shopVipUserId = props.data?.shopVipUserId;
      data.triageId = props.data?.triageId;

      await MallApi.addConsultLog({ data });
      message.success("添加意向成功");
    }

    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={700} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="咨询类型" name={`consultTypeId`} rules={[{ required: true, message: "请选择咨询类型" }]}>
              <DictPicker type={`consultType`} placeholder="请选择咨询类型" />
            </Form.Item>
          </Col>
          <Col span={layout.col}></Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`intentionType`} label="咨询意向" initialValue={[]} tooltip="仅根节点可选">
              <IntentionTypePicker multiple />
            </Form.Item>
          </Col>

          <Col span={layout.col * 2}>
            <Form.Item label="咨询备注" name={`content`}>
              <Input.TextArea autoSize={{ minRows: 8 }} maxLength={2000} showCount allowClear placeholder="请输入备注" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ConsultLogEdit;
