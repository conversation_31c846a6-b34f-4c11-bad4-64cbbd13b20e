import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, DatePicker, Divider, Form, Input, Radio, Row, Select, message } from "antd";
import { useSetState } from "react-use";
import { SexMaps } from "./types";
import dayjs from "dayjs";
import DictPicker from "../Units/DictPicker";
import VipPicker from "../Units/VipPicker";

const layout = { row: 10, col: 8 };

const Group = (props: any) => {
  return (
    <Col span={24}>
      <Row gutter={layout.row}>{props.children}</Row>
    </Col>
  );
};

const UserInfoEdit = (props: { title: any; children: any; onOk?: Function; data?: any; reservationId?: any; preName?: string; preMobile?: string }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    staff: [] as any[],
  });


  const handleOpen = () => {
    fetchStaff();

    form.resetFields();

    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : "");
      const data = { ...props.data };
      data.birthday = fdate(data.birthday);
      data.labels = (data.labels || "").split(",").filter((n: any) => n);
      const p = data.promotionUser;
      data.promotionUserId = p ? { value: p.id, label: `${p.vipCard} / ${p.name}` } : undefined;

      form.setFieldsValue(data);
    }
    if (props.preName) {
      form.setFieldsValue({ name: props.preName });
    }
    if (props.preMobile) {
      form.setFieldsValue({ mobile: props.preMobile });
    }
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.birthday = data.birthday?.format?.("YYYY-MM-DD 00:00:00") || "";
    data.labels = data.labels.join(",");
    data.promotionUserId = data.promotionUserId?.value ?? "";
    if (props.reservationId) {
      data.reservationId = props.reservationId;
    }

    // console.log(data);
    // return false;

    if (data.id) {
      await MallApi.putMember({ data });
      message.success("修改会员成功");
    } else {
      await MallApi.addMember({ data });
      message.success("添加会员成功");
    }

    props.onOk?.();
  };

  const randomVipNo = async () => {
    if (isEdit) return;
    const res = await MallApi.randomMemberNo();
    form.setFieldsValue({ vipCard: res?.randCardNo });
  };

  const isEdit = !!props.data?.id;

  return (
    <WeModal trigger={props.children} onOpen={handleOpen} title={props.title} width={1000} onOk={handleSubmit}>
      <Form form={form} layout="vertical">
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Divider orientation="left">基本信息</Divider>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="会员姓名" rules={[{ required: true, message: "请输入会员姓名" }]}>
              <Input placeholder="请输入会员姓名" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`vipCard`} label="会员号" rules={[{ required: true, message: "会员号" }]}>
              <Input
                placeholder="请输入会员号"
                readOnly={true}
                addonAfter={
                  isEdit ? undefined : (
                    <div
                      style={{
                        cursor: "pointer",
                        fontSize: 12,
                        userSelect: "none",
                      }}
                      onClick={randomVipNo}
                    >
                      生成
                    </div>
                  )
                }
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sex`} label="性别" rules={[{ required: true, message: "请选择性别" }]}>
              <Radio.Group options={SexMaps.map((n) => ({ label: n.value, value: n.id }))} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`mobile`} label="手机号" rules={[{ required: true, message: "请输入手机号" }]}>
              <Input type="tel" placeholder="请输入手机号" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`identityCard`} label="身份证号">
              <Input
                placeholder="请输入身份证号"
                onBlur={(e) => {
                  const val = e.target.value || "";
                  const birthday = form.getFieldValue("birthday");
                  if (val.length === 18 && !birthday) {
                    const y = val.substring(6, 10);
                    const m = val.substring(10, 12);
                    const d = val.substring(12, 14);
                    form.setFieldsValue({
                      birthday: dayjs(`${y}-${m}-${d} 00:00:00`),
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`birthday`} label="生日">
              <DatePicker placeholder="请选择生日" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`address`} label="家庭地址">
              <Input placeholder="请输入家庭地址" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`email`} label="邮箱地址">
              <Input type="email" placeholder="请输入邮箱地址" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">归属信息</Divider>
        <Row gutter={layout.row}>
          <Group>
            <Col span={layout.col}>
              <Form.Item label="顾客来源" name={`sourceId`} rules={[{ required: true, message: "请选择顾客来源" }]}>
                <DictPicker type={`shopVipSource`} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="渠道类型" name={`channelId`} rules={[{ required: true, message: "请选择渠道类型" }]} tooltip="渠道类型仅可编辑一次。不支持再次修改。轻谨慎操作！">
                <DictPicker type={`shopVipChannel`} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item name={`promotionUserId`} label="推荐人">
                <VipPicker labelInValue placeholder="会员号/姓名/手机号" />
              </Form.Item>
            </Col>
          </Group>

          <Col span={layout.col}>
            <Form.Item name={`adviserId`} label="所属客服" rules={[{ required: true, message: "请选择所属客服" }]}>
              <Select
                options={state.staff}
                placeholder="请选择所属客服"
                allowClear
                showSearch
                optionFilterProp="label"
              />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`doctorId`} label="所属医生">
              <Select
                options={state.staff}
                placeholder="请选择所属医生"
                allowClear
                showSearch
                optionFilterProp="label"
              />
            </Form.Item>
          </Col> */}
          <Col span={layout.col}>
            <Form.Item name={`developerId`} label="所属开发">
              <Select
                options={state.staff}
                placeholder="请选择所属开发"
                allowClear
                showSearch
                optionFilterProp="label"
              />
            </Form.Item>
          </Col>

        </Row>
        {/* <Divider orientation="left">VIP资料</Divider>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              name={`vipLevelId`}
              label="会员等级"
              rules={[{ required: true, message: "请选择会员等级" }]}
            >
              <Select
                options={state.lvOpt}
                placeholder="请选择会员等级"
                fieldNames={{ label: "name", value: "id" }}
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`expireDate`} label="有效期"  rules={[{ required: true, message: "请选择会员有效期" }]}>
              <DatePicker
                placeholder="请选择有效期"
                style={{ width: "100%" }}
              />
            </Form.Item>
          </Col>
        </Row> */}

        <Divider orientation="left">扩充信息</Divider>
        <Row gutter={layout.row}>
          <Col span={24}>
            <Form.Item name={`labels`} label="标签" initialValue={[]} tooltip="可自定义 使用回车分割">
              <DictPicker
                type="shopVipTag"
                mode="tags"
                formater={(arr) => arr.map((n) => ({ label: n.name, value: n.name }))}
              />
            </Form.Item>
          </Col>
        </Row>
        {/* <Row gutter={layout.row}>
          <Col span={24}>
            <Form.Item name={`content`} label="消费备注">
              <Input.TextArea autoSize={{ minRows: 2, maxRows: 10 }} placeholder="请输入消费备注" />
            </Form.Item>
          </Col>
        </Row> */}
      </Form>
    </WeModal>
  );
};

export default UserInfoEdit;
