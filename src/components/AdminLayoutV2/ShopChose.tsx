import { SwapOutlined } from "@ant-design/icons";
import { Button } from "antd";
import { useMount, useSetState } from "react-use";

import ShopChoseModal from "./ShopChoseModal";
import MallA<PERSON> from "@/services/MallApi";

const ShopChose = (props: { onChange?: Function; user: any }) => {
  const [state, setState] = useSetState({
    open: false,
    shops: [] as any[],
  });

  const canSwitchShop = state.shops.length > 1;

  useMount(() => {
    fetchShops();
  });

  const fetchShops = async () => {
    const res = await MallApi.getShopListForManage();
    setState({ shops: res?.list || [] });
  };

  if (!state.shops.length) return null;

  return (
    <div className="ml-a mr-4 flex items-center">
      <Button
        type="dashed"
        ghost
        className="mr-2"
        onClick={() => {
          if (canSwitchShop) {
            setState({ open: true });
          }
        }}
      >
        <span>当前：{props.user?.currentShopName || "未设置门店"}</span>
        {canSwitchShop && <SwapOutlined />}
      </Button>

      <ShopChoseModal shops={state.shops} current={props.user?.currentShopId} open={state.open} onOpenChange={(open: any) => setState({ open })} onChange={props.onChange} />
    </div>
  );
};

export default ShopChose;
