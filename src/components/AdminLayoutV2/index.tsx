import UserApi from "@/services/UserApi";
import { Popover } from "antd";
import { useEffect, useMemo } from "react";
import { Link, matchPath, Outlet, useLocation, useNavigate } from "react-router-dom";
import { useSetState, useTitle } from "react-use";
import { IconRender } from "../IconPicker";
import { Header } from "./header";
// import { useLayoutMode } from "./admin-layout";

export default function AdminLayoutV2() {
  // const [mode] = useLayoutMode();
  const location = useLocation();
  const nav = useNavigate();

  const [state, setState] = useSetState({
    modules: [] as any[],
    module: null as any,
    flattenNavs: [] as any[],
    subNav: null as any,
    navIds: [] as any[],

    user: null as any,
    pageKey: 1,
  });

  useTitle((state.module?.name ? state.module?.name + "-" : "") + `搞美医疗数字诊所`);

  const mode = useMemo(() => {
    const excludePaths = ["mall/ReservationPanel", "operation/ReservationCenter"];

    if (excludePaths.some((n) => location.pathname.endsWith(n))) {
      return "custom";
    }

    return "normal";
  }, [location.pathname]);

  useEffect(() => {
    fetchUserInfo();
    fetchMenu();
  }, [state.pageKey]);

  useEffect(() => {
    const crt = state.flattenNavs?.filter((n) => n.type == 2).find((n) => matchPath({ path: `/admin/` + state.module.enCode + n.href }, location.pathname));
    let ids: string[] = [];
    let subNav: any = null;

    if (crt) {
      ids = crt.fullIds?.split(",").filter(Boolean);
    }

    if (ids.length > 1) {
      subNav = state.flattenNavs?.find((n) => n.id == ids[0]) || null;
    }

    setState({ subNav, navIds: ids });
  }, [location.pathname, state.module, state.flattenNavs]);

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user });
  };

  const fetchMenu = async () => {
    const modules: any[] = await UserApi.getNavMenu2();

    let module: any = null;

    if (modules?.length) {
      let prefix = location.pathname.replace(/^\/admin\/([^\/]+)\/.*$/, "$1");
      module = modules.find((n) => n.enCode == prefix);

      // invalid module
      if (!module) {
        module = modules[0];
        prefix = module.enCode;
        let page = module.defaultPage;

        if (prefix && page) {
          nav(`/admin/` + prefix + page, { replace: true });
        }
      }
    }

    const flattenNavs = flattenTree(module?.tree || []);

    setState({ modules, module, flattenNavs });
  };

  const flattenTree = (tree: any[]): any[] => {
    const result: any[] = [];

    const flatten = (items: any[]) => {
      items.forEach((item) => {
        result.push(item);
        if (item.children && item.children.length > 0) {
          flatten(item.children);
        }
      });
    };

    flatten(tree);
    return result;
  };

  return (
    <div key={state.pageKey} className="h-100vh overflow-hidden flex flex-col bg-#f5f5f5">
      <Header user={state.user} module={state.module} onChange={() => setState({ pageKey: state.pageKey + 1 })} />
      <div className="flex-1 min-h-0 flex">
        <div className="w-32 bg-#202033 flex flex-col">
          <div className="py-1 flex-1 min-h-0 overflow-y-auto [&::-webkit-scrollbar]:hidden">
            {state.module?.tree.map((lv1: any) => {
              const prefix = "/admin/" + state.module.enCode;
              const ison = state.navIds.includes(lv1.id);

              // 页面
              if (lv1.type == 2) {
                return (
                  <Link
                    key={lv1.id}
                    to={prefix + lv1.href}
                    className="px-2 py-2 my-3 mx-1 rounded text-(sm #fff/80) flex items-center cursor-pointer hover:text-#fff data-[on=true]:( bg-#c48053 text-#fff)"
                    data-on={ison}
                  >
                    <IconRender className="size-4 mr-2" type={lv1.icon} />
                    <span className="line-clamp-1 flex-1 min-w-0">{lv1.name}</span>
                  </Link>
                );
              }

              // 目录
              if (lv1.type == 1) {
                return (
                  <Popover
                    key={lv1.id}
                    placement="rightTop"
                    color="#5f5f66"
                    classNames={{ body: "!p-0" }}
                    trigger={["click", "hover"]}
                    content={
                      <div className="w-52 text-(sm #fff) px-3 py-1 min-h-10">
                        <div className="grid cols-2 gap-x-2">
                          {(lv1.children as any[])
                            ?.filter((n) => n.type == 2)
                            .map((lv2) => {
                              return (
                                <Link to={prefix + lv2.href} key={lv2.id} className="block py-2 text-(sm #fff/80) py-1 rounded hover:text-#fff">
                                  <span className="line-clamp-1">{lv2.name}</span>
                                </Link>
                              );
                            })}
                        </div>

                        {(lv1.children as any[])
                          ?.filter((n) => n.type == 1)
                          .map((lv2) => {
                            return (
                              <div>
                                <div className="text-(xs #fff/50) pt-2 pb-0">{lv2.name}</div>
                                <div className="grid cols-2 gap-x-2">
                                  {(lv2.children as any[])
                                    ?.filter((n) => n.type == 2)
                                    .map((lv3) => {
                                      return (
                                        <Link to={prefix + lv3.href} key={lv3.id} className="block py-2 text-(sm #fff/80) py-1 rounded hover:text-#fff">
                                          <span className="line-clamp-1">{lv3.name}</span>
                                        </Link>
                                      );
                                    })}
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    }
                  >
                    <div
                      className="px-2 py-2 my-3 mx-1 rounded text-(sm #fff/80) flex items-center cursor-pointer hover:text-#fff data-[on=true]:( bg-#c48053 text-#fff)"
                      data-on={ison}
                      onClick={() => {
                        let target: any = null;

                        for (const lv2 of lv1?.children) {
                          for (const lv3 of lv2?.children) {
                            if (lv3.type == 2) {
                              target = lv3;
                              break;
                            }
                          }

                          if (lv2.type == 2) {
                            target = lv2;
                            break;
                          }
                        }

                        if (target) {
                          nav(prefix + target?.href);
                        }

                        // else {
                        //   setState({ subNav: lv1 });
                        // }
                      }}
                    >
                      <IconRender className="size-4 mr-2" type={lv1.icon} />
                      <span className="line-clamp-1 flex-1 min-w-0">{lv1.name}</span>
                    </div>
                  </Popover>
                );
              }

              return null;
            })}
          </div>
          <footer className="p-2 flex flex-col gap-2">
            {state.modules
              .filter((n) => n.id != state.module?.id)
              .map((md) => {
                const path = `/admin/` + md.enCode + (md.defaultPage || "/");
                return (
                  <Link
                    key={md.id}
                    to={path}
                    target={md.id}
                    className="py-2 text-(14px #fff/80 center) bg-#373748 rounded flex items-center justify-center"
                    onClick={() => setState({ pageKey: state.pageKey + 1 })}
                  >
                    <IconRender className="text-18px mr-2" type={md.icon} /> {md.name}
                  </Link>
                );
              })}
          </footer>
        </div>

        <div className="flex-1 min-w-0 flex flex-col">
          {/* placeholder */}
          <div className="flex-1 min-h-0 flex">
            {!!state.subNav && !!state.subNav?.children?.length && (
              <div className="w-30 bg-#fff overflow-y-auto">
                <div className="px-2 py-2">
                  <div className="grid cols-1 gap-1">
                    {(state.subNav.children as any[])
                      ?.filter((lv2) => lv2.type == 2)
                      .map((lv2) => {
                        const path = `/admin/` + state.module.enCode + lv2.href;
                        const ison = state.navIds.includes(lv2.id);

                        return (
                          <Link key={lv2.id} to={path} className="text-(sm gray-5) px-2 py-2 rounded no-underline data-[on=true]:(bg-blue-4/10 text-#c48053)" data-on={ison}>
                            {lv2.name}
                          </Link>
                        );
                      })}
                  </div>
                  {(state.subNav.children as any[])
                    ?.filter((lv2) => lv2.type == 1)
                    .map((lv2) => {
                      return (
                        <div key={lv2.id}>
                          <div className="my-2 text-(xs gray-4)">{lv2.name}</div>
                          <div className="grid cols-1 gap-1">
                            {(lv2.children as any[])
                              ?.filter((lv3) => lv3.type == 2)
                              .map((lv3) => {
                                const path = `/admin/` + state.module.enCode + lv3.href;
                                const ison = state.navIds.includes(lv3.id);

                                return (
                                  <Link to={path} className="text-(sm gray-5) px-2 py-2 rounded no-underline data-[on=true]:(bg-blue-4/10 text-#c48053)" data-on={ison}>
                                    {lv3.name}
                                  </Link>
                                );
                              })}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}

            {mode === "normal" && (
              <main className="flex-1 min-w-0 overflow-y-scroll">
                <div className="p-2">
                  <Outlet></Outlet>
                </div>
              </main>
            )}

            {mode === "custom" && <Outlet></Outlet>}
          </div>
        </div>
      </div>
    </div>
  );
}
