import { LockOutlined, LogoutOutlined, UserOutlined } from "@ant-design/icons";
import { App, Avatar, Dropdown } from "antd";
import { useNavigate } from "react-router-dom";
import ResetPwdModal from "./ResetPwd";
import ShopChose from "./ShopChose";

export const Header = (props: { user?: any; onChange?: Function; module?: any }) => {
  const nav = useNavigate();
  const app = App.useApp();

  const handleLogout = () => {
    app.modal.confirm({
      title: "确定要退出当前用户吗？",
      onOk: () => {
        localStorage.removeItem("auth");
        localStorage.removeItem("token");
        nav("/login");
      },
    });
  };

  const items = [
    {
      key: "pwd",
      label: (
        <ResetPwdModal>
          <a>修改密码</a>
        </ResetPwdModal>
      ),
      icon: <LockOutlined />,
    },
    { key: "logout", label: <a onClick={handleLogout}>退出登陆</a>, icon: <LogoutOutlined /> },
  ];

  return (
    <header className="z-50 pos-sticky top-0 h-14 flex bg-#202033 b-b-(1 solid #eee)">
      <div className="w-32 h-full box-border p-2">
        <img className="block size-full object-contain" src="https://oss.gaomei168.com/file-release/20250527/1376978979550326784_1000_269.png" alt="" />
      </div>
      <div className="py-2 flex items-end">
        {!!props.module && (
          <div className="px-3">
            <div className="px-0 text-(20px #fff) fw-bold flex items-center rounded">
              <div>{props.module.name}</div>
            </div>
          </div>
        )}
      </div>
      <div className="flex-1"></div>
      <div className="px-2 flex items-center">
        <ShopChose user={props.user} onChange={props.onChange} />
      </div>
      <Dropdown menu={{ items }}>
        <div className="flex items-center px-4 mr-5px">
          <div className="flex-1 mr-2 text-right">
            <div className="ml-2 text-(sm #fff)">{props.user?.name || null}</div>
            <div className="ml-2 text-(sm #fff)">{props.user?.companyName || null}</div>
          </div>
          <Avatar className="size-8" icon={<UserOutlined />} />
        </div>
      </Dropdown>
    </header>
  );
};
