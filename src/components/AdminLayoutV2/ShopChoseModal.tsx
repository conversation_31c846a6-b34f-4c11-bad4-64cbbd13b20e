import { Button, List, Modal, Typography, message } from "antd";
import { useSetState } from "react-use";
// import Style from "./ShopChoseModal.module.scss";
import MallApi from "@/services/MallApi";
import { EnvironmentOutlined } from "@ant-design/icons";

const AsyncBtn = (props: any) => {
  const [state, setState] = useSetState({ loading: false });
  return (
    <Button
      // className={Style.btn}
      size="small"
      loading={state.loading}
      onClick={async () => {
        setState({ loading: true });
        await props.onClick().finally(() => setState({ loading: false }));
      }}
    >
      进入
    </Button>
  );
};

const ShopChoseModal = (props: {
  onChange?: Function;
  open?: boolean;
  onOpenChange?: Function;
  current?: string;
  shops: any[];
}) => {
  const [state, setState] = useSetState({
    open: false,
  });

  const open = props.open ?? state.open;

  const setOpen = (open: boolean) => {
    setState({ open });
    props.onOpenChange?.(open);
  };

  const choseShop = async (item: any) => {
    const data = { shopId: item.id };
    await MallApi.setCurrentShop({ data });
    message.success("选择门店成功");

    setOpen(false);
    props.onChange?.(item);
  };

  return (
    <Modal
      open={open}
      width={450}
      footer={false}
      wrapClassName="[&_.ant-modal-content]:p-2 [&_.ant-modal-close]:hidden"
      onCancel={() => setOpen(false)}
    >
      <Typography.Title level={5} style={{ textAlign: "center" }}>
        选择门店
      </Typography.Title>
      {/* <Spin> */}
      <List
        bordered
        dataSource={props.shops}
        renderItem={(item) => (
          <List.Item extra={<AsyncBtn onClick={() => choseShop(item)} />}>
            <div>
              <EnvironmentOutlined /> {item.name}
            </div>
          </List.Item>
        )}
      />
      {/* </Spin> */}
    </Modal>
  );
};

export default ShopChoseModal;
