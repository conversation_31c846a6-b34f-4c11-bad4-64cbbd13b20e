import { Form, Input, message } from "antd";
import { md5 } from "hash-wasm";
import WeModal from "../WeModal/WeModal";
import UserApi from "@/services/UserApi";

const ResetPwdModal = (props: { children: any }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const data = {
      oldLoginPwd: await md5(val.oldLoginPwd),
      newLoginPwd: await md5(val.newLoginPwd),
    };
    await UserApi.putUserPwd({ data });
    message.success("修改密码成功");
  };

  return (
    <WeModal
      width={400}
      title="修改密码"
      trigger={props.children}
      onOk={handleSubmit}
      onOpen={() => form.resetFields()}
    >
      <Form layout="vertical" form={form}>
        <Form.Item
          label="原密码"
          name={`oldLoginPwd`}
          rules={[{ required: true, message: "请输入原密码" }]}
        >
          <Input type="password" placeholder="请输入原密码" />
        </Form.Item>
        <Form.Item
          label="新密码"
          name={`newLoginPwd`}
          rules={[{ required: true, message: "请输入新密码" }]}
        >
          <Input type="password" placeholder="请输入新密码" />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name={`reLoginPwd`}
          rules={[
            { required: true, message: "请再次输入新密码" },
            {
              validator: (_, value) => {
                if (value && form.getFieldValue("newLoginPwd") !== value) {
                  return Promise.reject("两次新密码不匹配");
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
          dependencies={["newLoginPwd"]}
        >
          <Input type="password" placeholder="请再次输入新密码" />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default ResetPwdModal;
