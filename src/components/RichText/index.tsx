import { useRef } from "react";
import { Editor } from "@tinymce/tinymce-react";
import api from "@/utils/Api";
import Configs from "@/utils/Configs";
import { useSetState } from "react-use";
import { RichImageUpload } from "./RichImageUpload";

const RichText = (props: { value?: any; onChange?: Function; placeholder?: any }) => {
  const divRef = useRef<any>(null);
  const [state, setState] = useSetState({
    open: false,
    long: false,
  });

  const handleUpload: any = async (blobInfo: any, progress: any) => {
    const fdata = new FormData();
    fdata.append("file", blobInfo.blob(), blobInfo.name());

    const res: any = await api.post(Configs.uploadHost, fdata, {
      onUploadProgress: (evt) => progress((evt.progress || 0) * 100),
    });

    return res?.allFullPath || "";
  };

  return (
    <>
      <RichImageUpload
        open={state.open}
        long={state.long}
        onOk={(e: any[] = []) => {
          setState({ open: false });
          if (e) {
            const editor: Editor["editor"] = divRef.current;
            const html = e.map((n) => `<img style="display: block;" src="${n}">`).join("");
            editor?.insertContent(html);
          }
        }}
      />
      <Editor
        tinymceScriptSrc="https://registry.npmmirror.com/tinymce/6.8.3/files/tinymce.min.js"
        init={{
          language_url: "https://oss.gaomei168.com/file-release/20250303/1346150672715485184.js",
          language: "zh-Hans",
          images_upload_handler: handleUpload as any,
          promotion: false,
          branding: false,

          placeholder: props.placeholder ?? "请输入内容",
          plugins: [
            "advlist",
            "autolink",
            "lists",
            "link",
            "image",
            "charmap",
            "anchor",
            "searchreplace",
            "visualblocks",
            "code",
            "fullscreen",
            "insertdatetime",
            "media",
            "table",
            "preview",
            "help",
            "wordcount",
          ],
          toolbar: [
            "undo redo | blocks fontsizeinput | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | numlist bullist | link cusimg1 cusimg2 | fullscreen",
          ],
          setup: (editor) => {
            editor.ui.registry.addButton("cusimg1", {
              icon: "image",
              onAction: () => {
                setState({ open: true, long: false });
              },
            });
            editor.ui.registry.addButton("cusimg2", {
              icon: "edit-image",
              // text: "长图优化",
              onAction: () => {
                setState({ open: true, long: true });
              },
            });
          },
        }}
        onInit={(_, editor) => (divRef.current = editor)}
        value={props.value}
        onEditorChange={(e) => props.onChange?.(e)}
      />
    </>
  );
};

export default RichText;
