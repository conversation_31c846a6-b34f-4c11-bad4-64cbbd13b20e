import Configs from "@/utils/Configs";
import { FileImageOutlined } from "@ant-design/icons";
import { Alert, Modal } from "antd";
import axios from "axios";
import Compressor from "compressorjs";

export const RichImageUpload = (props: { open?: boolean; onOk?: any; long?: boolean }) => {
  const loadImg = (url: any) => {
    return new Promise((res) => {
      const img = new Image();
      img.onload = () => res(img);
      img.src = url;
    });
  };

  const beforeUpload: any = (file: any) => {
    if (file?.type == "image/gif") return file;
    return new Promise((resolve) => {
      new Compressor(file, {
        maxWidth: 1920,
        convertSize: 1024 * 200,
        success: (res) => resolve(res),
        error: () => resolve(file),
      });
    });
  };

  const upload = async (file: any) => {
    const res = await axios.postForm(Configs.uploadHost, { file });
    const link = res?.data?.data?.allFullPath ?? "";
    return link;
  };

  const uploadNormal = async (files: any[]) => {
    const links: string[] = [];
    for (let file of files) {
      const link = await upload(file);
      links.push(link);
    }
    props.onOk?.(links);
  };

  const uploadLongImage = async (files: any[]) => {
    const file = files?.[0];
    const link = URL.createObjectURL(file);
    const img: any = await loadImg(link);
    const cw = img.width;
    const ch = img.height;
    URL.revokeObjectURL(link);

    const cvs = document.createElement("canvas");
    const ctx = cvs.getContext("2d")!;
    cvs.width = cw;

    const toBlob = () => new Promise((res) => cvs.toBlob((b) => res(b), "image/jpeg", 0.8));

    let hs = 0;
    let links: string[] = [];

    while (hs < ch) {
      let he = Math.min(hs + cw, ch);
      let hh = he - hs;

      cvs.height = hh;
      ctx.clearRect(0, 0, cw, hh);
      ctx.drawImage(img, 0, hs, cw, hh, 0, 0, cw, hh);

      const blob = await toBlob();
      const link = await upload(blob);
      links.push(link);

      hs = he;
    }

    props.onOk?.(links);
  };

  return (
    <Modal
      open={props.open}
      title={props.long ? "长图上传" : "图片上传"}
      width={420}
      onCancel={() => props.onOk?.()}
      footer={null}
    >
      {props.long && <Alert message="长图上传优化，将原图切割为正方形小图，然后拼接" type="warning" />}
      <div className="mt-20px pos-relative h-200px mx-auto rounded-10px b-(2px dashed #ddd) flex flex-col items-center justify-center">
        <input
          className="pos-absolute top-0 left-0 size-full cursor-pointer opacity-0"
          type="file"
          accept="image/*"
          multiple
          onInput={async (e) => {
            let files: any = e.currentTarget.files;
            files = [...files];
            e.currentTarget.value = "";

            if (!files?.length) return;

            let arr: any = [];
            for (let file of files) {
              const res = await beforeUpload(file);
              arr.push(res);
            }

            if (props.long) {
              uploadLongImage(arr);
            } else {
              uploadNormal(arr);
            }
          }}
        ></input>
        <FileImageOutlined className="text-(40px #999)" />
        <div className="text-(18px #666) mt-10px">拖拽或点击上传</div>
      </div>

      {/* <div className="flex items-center pt-10px">
        <Switch value={state.long} onChange={(e) => setState({ long: e })} />
        <div className="ml-10px">长图切割</div>
        <div className="ml-5px text-(12px #999)">(切割为正方形小图 然后拼接)</div>
      </div> */}
    </Modal>
  );
};
