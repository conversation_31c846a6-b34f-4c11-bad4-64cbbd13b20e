import Mall<PERSON>pi from "@/services/MallApi";
import { TreeSelect, TreeSelectProps } from "antd";
import { useMount, useSetState } from "react-use";

export const GoodsTypePicker = (props: TreeSelectProps) => {
  const [state, setState] = useSetState({
    types: [] as any[],
  });

  useMount(() => {
    fetchTypes();
  });

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};

    list.forEach((item) => {
      const newItem = { ...item };
      map[item.id] = newItem;
    });

    list.forEach((item) => {
      const newItem = map[item.id];
      let parent = map[newItem.parentId];

      if (parent) {
        newItem.fname = parent.fname + "-" + newItem.name;
        parent.children = parent.children || [];
        parent.children.push(newItem);
      } else {
        arr.push(newItem);
      }
    });

    return arr;
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopProductTypeForSelect({ params });
    let list: any[] = res?.list || [];
    list.forEach((n) => {
      if (!n.parentId) n.parentId = n.property;
    });

    list = [
      ...list,
      { name: "门店项目", id: 1, checkable: false },
      { name: "线上商城", id: 2, checkable: false },
    ];

    const types = genTree(list);

    setState({ types: types });
  };

  return (
    <TreeSelect
      treeData={state.types}
      fieldNames={{ label: "name", value: "id", children: "children" }}
      showSearch
      treeNodeFilterProp="name"
      multiple
      allowClear
      treeCheckable
      showCheckedStrategy={TreeSelect.SHOW_PARENT}
      treeDefaultExpandedKeys={[1, 2, 3]}
      placeholder="请选择分类"
      // treeDefaultExpandAll
      {...props}
    />
  );
};
