import Mall<PERSON>pi from "@/services/MallApi";
import { Input, Modal, Radio, Space, Table, Tree } from "antd";
import { cloneElement, useEffect } from "react";
import { useDebounce, useSetState } from "react-use";
import Style from "./index.module.scss";
import { makeApi } from "@/utils/Api";

const fetchGoods = makeApi("get", `/api/v2/mallProduct/select_product_list`);

interface GoodsPickerProps {
  children: any;
  property?: 1 | 2 | 3;
  //forbidMixSelect?: boolean,
  max?: number;
  params?: any;
  onSelect?: (rows: any[]) => any;
  selected?: any[]; //选中的数据
}

const GoodsPicker = (props: GoodsPickerProps) => {
  const [state, setState] = useSetState({
    open: false,

    property: 1,
    productModel: "",
    name: "",
    nameDelay: "",

    types: [] as any[],
    typeId: null as any,

    list: [] as any[],
    loading: false,
    total: 0,
    page: 1,
    size: 10,

    glist: [] as any[],
  });

  const property = props.property ?? state.property;
  useDebounce(() => setState({ nameDelay: state.name }), 600, [state.name]);

  useEffect(() => {
    if (!state.open) return;
    fetchTypes();
    setState({ typeId: null });
  }, [state.open, property]);

  useEffect(() => {
    if (!state.open) return;
    setState({ glist: props.selected ?? [] });
    fetchList();
  }, [state.typeId, state.nameDelay, state.open, property, state.productModel, props.selected]);

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};

    list.forEach((item) => {
      const newItem = { ...item };
      map[item.id] = newItem;
    });

    list.forEach((item) => {
      const newItem = map[item.id];
      const parent = map[newItem.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(newItem);
      } else {
        arr.push(newItem);
      }
    });

    return arr;
  };

  const fetchTypes = async () => {
    const params = { property, pageSize: 9999 };
    const res = await MallApi.getShopProductTypeForSelect({ params });
    const types = genTree(res?.list || []);
    setState({ types });
  };

  const fetchList = async (page = 1) => {
    const params = {
      productModel: state.productModel,
      ...props.params,
      property,
      shopCategoryId: state.typeId,
      pageNum: page,
      pageSize: state.size,
      name: state.nameDelay,
    };
    setState({ loading: false });
    const res = await fetchGoods({ params }).finally(() => setState({ loading: false }));
    setState({ list: res?.list || [], page, total: res?.total || 0 });
  };

  const handleOk = () => {
    setState({ open: false });
    props.onSelect?.(state.glist);
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Modal
        open={state.open}
        closable
        width={1000}
        onCancel={() => setState({ open: false })}
        title={<>选择项目/商品</>}
        keyboard={false}
        onOk={handleOk}
      >
        <div className={Style.head}>
          <Space>
            <Input
              placeholder="请输入名称"
              style={{ width: 250 }}
              value={state.name}
              onChange={(e) => setState({ name: e.target.value })}
              allowClear
            />
            {!props.property && (
              <Radio.Group
                options={[
                  { label: "门店项目", value: 1 },
                  { label: "线上项目", value: 2 },
                  //{ label: "线上商城", value: 3 },
                ]}
                optionType="button"
                buttonStyle="solid"
                value={state.property}
                onChange={(e) => setState({ property: e.target.value })}
              />
            )}
            {property == 1 && !props.params?.productModel && (
              <Radio.Group
                options={[
                  { label: "全部", value: "" },
                  { label: "项目单品", value: 1 },
                  { label: "项目套餐", value: 2 },
                ]}
                optionType="button"
                buttonStyle="solid"
                value={state.productModel}
                onChange={(e) => setState({ productModel: e.target.value })}
              />
            )}
            {property == 2 && !props.params?.productModel && (
              <Radio.Group
                options={[
                  { label: "全部", value: "" },
                  { label: "项目单品", value: 1 },
                  { label: "项目套餐", value: 2 },
                ]}
                optionType="button"
                buttonStyle="solid"
                value={state.productModel}
                onChange={(e) => setState({ productModel: e.target.value })}
              />
            )}
            {/* {property == 3 && !props.params?.productModel && (
              <Radio.Group
                options={[
                  { label: "全部", value: "" },
                  { label: "配送商品", value: 11 },
                  { label: "服务商品", value: 12 },
                ]}
                optionType="button"
                buttonStyle="solid"
                value={state.productModel}
                onChange={(e) => setState({ productModel: e.target.value })}
              />
            )} */}
          </Space>
        </div>
        <div className={Style.box}>
          <div className={Style.left}>
            <Tree.DirectoryTree
              showIcon={false}
              treeData={state.types}
              fieldNames={{ title: "name", key: "id", children: "children" }}
              onSelect={(e) => {
                const key = e[0];
                setState({ typeId: key === state.typeId ? null : key });
              }}
              selectedKeys={[state.typeId]}
            />
          </div>
          <div className={Style.right}>
            <Table
              rowKey={`id`}
              size="small"
              scroll={{ x: "max-content" }}
              dataSource={state.list}
              loading={{ spinning: state.loading, delay: 300 }}
              rowSelection={{
                preserveSelectedRowKeys: true,
                selectedRowKeys: state.glist.map((n) => n.id),
                onChange: (_, rows) => {
                  //禁止混合选择
                  // if (props.forbidMixSelect) {
                  //   if (rows.length > 0) {
                  //     if (rows[0].productModel !== rows[rows.length - 1].productModel && rows[0].productModel == 1) {
                  //       message.error("只能选择单品");
                  //       return;
                  //     }
                  //     if ((rows[0].productModel !== rows[rows.length - 1].productModel && rows[rows.length - 1].productModel == 1)
                  //       || rows.filter((item: any) => item.productModel == 2).length >= 2) {
                  //       message.error("只能选择一种套餐");
                  //       return;
                  //     }
                  //   }
                  // }

                  const max = props.max ?? 0;
                  if (!max) {
                    setState({ glist: rows });
                  } else if (max === 1) {
                    const last = rows[rows.length - 1];
                    setState({ glist: last ? [last] : [] });
                  } else {
                    setState({ glist: rows.slice(0, max) });
                  }
                },
              }}
              pagination={{
                current: state.page,
                pageSize: state.size,
                total: state.total,
                onChange: (n) => fetchList(n),
                showTotal: (n) => `共${n}条数据`,
              }}
              columns={[
                { title: "名称", dataIndex: "name" },
                { title: "分类", dataIndex: "shopCategoryName" },
                {
                  title: "类型",
                  dataIndex: "property",
                  render: (c) => (c == 1 ? "门店项目" : "商城商品"),
                },
                {
                  title: "模式",
                  dataIndex: "productModel",
                  //return c == 2 ? "套餐" : "单品";
                  render: (c) => {
                    if (c == 1) {
                      return "项目单品";
                    } else if (c == 2) {
                      return "项目套餐";
                    } else if (c == 11) {
                      return "配送商品";
                    } else if (c == 12) {
                      return "服务商品";
                    }
                  },
                },
                {
                  title: "次数",
                  dataIndex: "includeNum",
                  render: (c) => (c ? c + "次" : "--"),
                },
                {
                  title: "划线价",
                  dataIndex: "oldPrice",
                  render: (c) => (c ?? "--") + "元",
                },
                {
                  title: "售价",
                  dataIndex: "salePrice",
                  render: (c) => (c ?? "--") + "元",
                },
                {
                  title: "库存",
                  render: (c) => (c.inventoryLimit == 1 ? c.inventoryCount : "无限"),
                },
              ]}
            />
          </div>
        </div>
      </Modal>
    </>
  );
};

export default GoodsPicker;
