import { Input, Space } from "antd";
import Style from "./index.module.scss";
import { useSetState } from "react-use";
import axios from "axios";

const UploadPage = () => {
  const [state, setState] = useSetState({
    domain: "https://vip.gaomei168.com/",
    res: "",
  });

  const upload = async (file: any) => {
    const fdata = new FormData();
    fdata.append("file", file);
    const res = await axios.post("http://guanjia.x9w.com:9000/service-resource/upload", fdata);
    let url = res.data?.data?.allFullPath;
    if (state.domain) url = url.replace("https://guanjia.x9w.com/", state.domain);

    return { name: file.name, url };
  };

  return (
    <div>
      <div className={Style.center}>
        <Space direction="vertical" style={{ display: "flex" }}>
          <Input
            placeholder="域名"
            value={state.domain}
            onInput={(e) => setState({ domain: e.currentTarget.value })}
            addonBefore="域名"
          />

          <div className={Style.upload}>
            <input
              className={Style.ipt}
              type="file"
              multiple
              onChange={async (e) => {
                const files = [...(e.target.files as any)];
                const list = await Promise.all(files.map(upload));
                setState({ res: JSON.stringify(list, null, 2) });
              }}
            />
            <div>上传文件</div>
          </div>

          <Input.TextArea autoSize={{ minRows: 10 }} placeholder="请上传文件" readOnly value={state.res} />
        </Space>
      </div>
    </div>
  );
};

export default UploadPage;
