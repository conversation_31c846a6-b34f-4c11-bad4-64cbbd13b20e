import ShopChoseModal from "@/components/AdminLayoutV2/ShopChoseModal";
import MallApi from "@/services/MallApi";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useMount, useSetState } from "react-use";

const LoadShop = () => {
  const nav = useNavigate();
  const [search] = useSearchParams();
  const [state, setState] = useSetState({ shops: [] as any[] });

  useMount(() => {
    fetchShops();
  });

  const fetchShops = async () => {
    const res = await MallApi.getShopListForManage();
    setState({ shops: res?.list || [] });
  };

  return (
    <div>
      <ShopChoseModal
        shops={state.shops}
        open={true}
        onChange={() => {
          const from = search.get("from") || "/admin";
          nav(from);
        }}
      />
    </div>
  );
};

export default LoadShop;
