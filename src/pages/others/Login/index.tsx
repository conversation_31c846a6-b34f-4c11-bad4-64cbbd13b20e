import { LockOutlined, UserOutlined } from "@ant-design/icons";
import { Button, Form, Input } from "antd";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useLocalStorage, useMount, useSetState } from "react-use";
import { md5 } from "hash-wasm";
import UserApi from "@/services/UserApi";
import css from "./index.module.scss";
import dayjs from "dayjs";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const LoginPage = () => {
  const [search] = useSearchParams();
  const nav = useNavigate();
  const [, setToken] = useLocalStorage("token", "", { raw: true });
  const [state, setState] = useSetState({
    img: ``,
    kaptchaKey: "",
    loading: false,
  });

  const handleLogin = async (e: any) => {
    const loginPwd = await md5(e.loginPwd);
    const data = { ...e, loginPwd, kaptchaKey: state.kaptchaKey };
    setState({ loading: true });
    try {
      const res = await UserApi.login({ data });
      res.create = dayjs().format("YYYY-MM-DD HH:mm:ss");
      localStorage.setItem("auth", JSON.stringify(res));
      setToken(res.token);

      const from = search.get("from") || "/admin";

      const { list: shops } = await MallApi.getShopListForManage();
      if (shops && shops.length > 1) {
        nav(`/loadShop?from=${from}`);
      } else {
        nav(from);
      }
    } catch (e) {
      getCaptcha();
    } finally {
      setState({ loading: false });
    }
  };

  const getCaptcha = async () => {
    const res = await UserApi.getCaptcha();
    setState({ img: res.captchaBase64, kaptchaKey: res.kaptchaKey });
  };

  useMount(() => {
    getCaptcha();
  });

  return (
    <div className={css.page}>
      <Form className={css.login} layout="vertical" onFinish={handleLogin}>
        <h2 className={css.title}>搞美医疗数字诊所系统</h2>
        <Form.Item name={`loginAccount`} rules={[{ required: true, message: "请输入用户名或手机号" }]}>
          <Input prefix={<UserOutlined />} size="large" placeholder="请输入用户名或手机号" />
        </Form.Item>
        <Form.Item name={`loginPwd`} rules={[{ required: true, message: "请输入密码" }]}>
          <Input prefix={<LockOutlined />} size="large" type="password" placeholder="请输入密码" />
        </Form.Item>
        <Form.Item
          name={`kaptchaValue`}
          rules={[
            { required: true, message: "请输入验证码" },
            { pattern: /^[0-9a-zA-z]{4}$/, message: "验证码格式错误" },
          ]}
        >
          <div className={css.captcha}>
            <div className={css.ipt}>
              <Form.Item noStyle>
                <Input size="large" placeholder="请输入验证码" maxLength={4} />
              </Form.Item>
            </div>
            <img className={css.img} src={state.img} alt="" onClick={() => getCaptcha()} />
          </div>
        </Form.Item>
        <Form.Item style={{ marginTop: 20 }}>
          <Button type="primary" size="large" htmlType="submit" block loading={state.loading}>
            登录
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default LoginPage;
