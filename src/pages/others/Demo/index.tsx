import MallApi from "@/services/MallApi";
import { Button, Input } from "antd";
import axios from "axios";
import { useSetState } from "react-use";

export default function Demo() {
  const [state, setState] = useSetState({
    id: "1381577006446608384",
    loading: false,
  });

  const onPrint2 = async () => {
    setState({ loading: true });
    const res = await MallApi.getOrderDetailNew(state.id).finally(() => setState({ loading: false }));
    console.log(res);
    await axios.post("http://localhost:31000/api/print", res);
  };

  return (
    <div style={{ padding: "20px", backgroundColor: "#f5f5f5" }}>
      <div className="w-300px space-y-2">
        <Input placeholder="小票ID" value={state.id} onChange={(e) => setState({ id: e.target.value })} addonBefore="订单ID" />

        <Button onClick={onPrint2} style={{ marginBottom: "20px" }} disabled={!state.id} loading={state.loading}>
          打印小票
        </Button>
      </div>
    </div>
  );
}
