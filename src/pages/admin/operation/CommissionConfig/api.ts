import { makeApi } from "@/utils/Api";

const MallCommissionConfigApi = {
    getMallCommissionConfig: makeApi("get", `/api/mallCommissionConfig`),
    getMallCommissionConfigInfo: makeApi("get", `/api/mallCommissionConfig`, true),
    addMallCommissionConfig: makeApi("post", `/api/mallCommissionConfig`),
    putMallCommissionConfig: makeApi("put", `/api/mallCommissionConfig`),
    delMallCommissionConfig: makeApi("delete", `/api/mallCommissionConfig`),
    selectMallCommissionConfig: makeApi("get", `/api/mallCommissionConfig/select_list`),
};

export default MallCommissionConfigApi;
