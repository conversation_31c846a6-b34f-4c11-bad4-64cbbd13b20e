import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallCommissionConfigApi from "./api";
import MallCommissionConfigEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { StateMap } from "./types";

const MallCommissionConfigPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallCommissionConfigApi.delMallCommissionConfig({ data: { ids: item.id } });
    message.success("删除佣金方案成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallCommissionConfigApi.getMallCommissionConfig({ params: p })}
        title={
          <MallCommissionConfigEdit title={"新增佣金方案"} onOk={handleReload}>
            <WeTable.AddBtn />
          </MallCommissionConfigEdit>
        }
        search={[
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
        ]}
        columns={[
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          // { title: "佣金比例", dataIndex: "commissionRate", render: (c) => <span>{(c * 100).toFixed(2)}%</span> },
          // { title: "复购佣金比例", dataIndex: "repurchaseCommissionRate", render: (c) => <span>{(c * 100).toFixed(2)}%</span> },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => StateMap.find((i) => i.value === c)?.label },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallCommissionConfigEdit title={`编辑佣金方案`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallCommissionConfigEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallCommissionConfigPage;
