import WeModal from "@/components/WeModal/WeModal";
import MallCommissionConfigApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { InputNumber } from "antd";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };

const MallCommissionConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await MallCommissionConfigApi.getMallCommissionConfigInfo(props.data.id);
      //data.commissionRate = (data.commissionRate * 100).toFixed(2);
      //data.repurchaseCommissionRate = (data.repurchaseCommissionRate * 100).toFixed(2);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    //data.commissionRate = (data.commissionRate / 100).toFixed(4);
    //data.repurchaseCommissionRate = (data.repurchaseCommissionRate / 100).toFixed(4);
    if (!data.id) {
      await MallCommissionConfigApi.addMallCommissionConfig({ data });
      message.success("添加佣金方案成功");
    }
    if (data.id) {
      await MallCommissionConfigApi.putMallCommissionConfig({ data });
      message.success("修改佣金方案成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="佣金方案名称" rules={[{ required: false, message: "请输入佣金方案名称" }]}>
              <Input placeholder="请输入佣金方案名称" />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`commissionRate`} label="首购佣金比例(%)" rules={[{ required: false, message: "请输入首购佣金比例" }]} initialValue={0} >
              <InputNumber
                min={0}
                max={100}
                step={1}
                style={{ width: "100%" }}
                addonAfter="百分比"
                placeholder="请输入佣金比例0~100" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`repurchaseCommissionRate`} label="复购佣金比例(%)" rules={[{ required: false, message: "请输入复购佣金比例" }]} initialValue={0}>
              <InputNumber
                min={0}
                max={100}
                style={{ width: "100%" }}
                step={1}
                addonAfter="百分比"
                placeholder="请输入复购佣金比例0~100" />
            </Form.Item>
          </Col> */}
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form >
    </WeModal >
  );
};

export default MallCommissionConfigEdit;
