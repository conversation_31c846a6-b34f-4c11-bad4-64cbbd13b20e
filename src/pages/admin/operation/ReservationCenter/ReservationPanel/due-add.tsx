import VipPlusPicker from "@/components/VipPlusPicker";
import UserPane from "@/components/Units/UserPane";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import User<PERSON>pi from "@/services/UserApi";
import { DeleteOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, App, Button, Card, Col, Form, Input, Modal, Radio, Row } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";
import { PickDateTime } from "./PickDateTime";
import { formatDate } from "@/utils/Tools";
import AsyncPicker from "@/components/Units/AsyncPicker";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

export const DueAdd = (props: { open?: boolean; onClose?: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const [state, setState] = useSetState({
    isNew: false,
    user: null as any,
    types: [] as any[],
  });

  useEffect(() => {
    if (!props.open) return;

    form.resetFields();
    setState({ user: null });
    fetchTypes();

    form.setFieldsValue({
      reservationDataList: [
        {
          typeId: props.data?.typeId || "",
          preTime: props.data?.preTime || "",
          content: props.data?.content || "",
        },
      ],
    });
  }, [props.open]);

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = (res || [])?.map((n: any) => ({ label: n.name, value: n.id }));
    setState({ types: list });
  };

  const onOk = async () => {
    const data = await form.validateFields();

    if (!state.isNew) {
      data.shopVipUserId = state.user?.id;
      data.preMobile = state.user?.mobile;
      if (!data.shopVipUserId) {
        message.error("请先选择用户");
        return false;
      }
    }

    data.reservationDataList = data.reservationDataList.map((n: any) => {
      return { ...n, preTime: formatDate(n.preTime, "YYYY-MM-DD HH:mm:00"), images: UploadV2.arr2str(n.images) };
    });

    await MallApi.addReservationGroup({ data });
    message.success("新增预约成功");
    props.onClose?.();
    props.onOk?.(data);
  };

  return (
    <Modal
      destroyOnHidden
      open={props.open}
      title={
        <div className="flex gap-4 fw-normal">
          <div className="fw-bold">新增预约</div>

          <div>
            <Radio.Group
              options={[
                { label: "已建档", value: false },
                { label: "未建档", value: true },
              ]}
              value={state.isNew}
              onChange={(e) => setState({ isNew: e.target.value })}
            />
          </div>

          <div>
            <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} params={{ queryType: 9 }} />
          </div>
        </div>
      }
      width={1000}
      onOk={onOk}
      onCancel={props.onClose}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <div className="h-4"></div>

        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>

        {!state.user?.id && !state.isNew && (
          <div className="mb-4">
            <Alert message="请先选择用户信息" type="warning" showIcon />
          </div>
        )}

        {state.user?.id && (
          <div className="mb-4 min-h-110px">
            <UserPane user={state.user} />
          </div>
        )}

        {state.isNew && (
          <Card className="mb-4" size="small" title="客户信息">
            <Row>
              <Col span={8}>
                <Form.Item label="客户姓名" name={`preName`} rules={[{ required: false, message: "请输入客户姓名" }]}>
                  <Input placeholder="请输入客户姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="客户手机" name={`preMobile`} rules={[{ required: false, message: "请输入客户手机" }]}>
                  <Input placeholder="请输入客户手机" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        <Row>
          <Col span={8}>
            <Form.Item label="选择门店" name={`shopId`} rules={[{ required: true, message: "请选择门店" }]}>
              <AsyncPicker
                fetch={async () => {
                  const params = { pageSize: 9999, businessMode: 1, queryType: 9 };
                  const res = await MallApi.getShopListForSelect({ params });
                  return res?.list || [];
                }}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择门店"
                allowClear
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.List name={"reservationDataList"} initialValue={[{}]}>
          {(fields, act) => {
            return (
              <div>
                {fields.map((field, idx) => {
                  return (
                    <Card
                      key={idx}
                      size="small"
                      title={"预约信息" + (idx + 1)}
                      className="mb-4"
                      extra={!!idx && <Button size="small" type="link" icon={<DeleteOutlined className="text-red" />} onClick={() => act.remove(field.name)} />}
                    >
                      <Form.Item label="预约类型" name={[field.name, `typeId`]} rules={[{ required: true, message: "请选择预约类型" }]}>
                        <Radio.Group options={state.types} />
                      </Form.Item>

                      <Row>
                        <Col span={8}>
                          <Form.Item
                            noStyle
                            shouldUpdate={(p, c) =>
                              p["reservationDataList"][field.name]["typeId"] !== c["reservationDataList"][field.name]["typeId"] ||
                              p["reservationDataList"][field.name]["shopId"] !== c["reservationDataList"][field.name]["shopId"]
                            }
                          >
                            {(ins) => {
                              const typeId = ins.getFieldValue(["reservationDataList", field.name, `typeId`]);
                              const shopId = ins.getFieldValue("shopId");
                              return (
                                <Form.Item label="预约日期" name={[field.name, `preTime`]} rules={[{ required: true, message: "请选择预约日期" }]}>
                                  <PickDateTime shopId={shopId} typeId={typeId} />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>
                        </Col>
                      </Row>

                      <Row>
                        <Col span={16}>
                          <Form.Item label="预约备注" name={[field.name, `content`]} rules={[{ required: true, message: "请输入预约备注" }]}>
                            <Input.TextArea autoSize={{ minRows: 2 }} placeholder="请输入预约备注" maxLength={400} showCount />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Form.Item label="附件" name={[field.name, `images`]}>
                        <UploadV2 maxCount={20} />
                      </Form.Item>
                    </Card>
                  );
                })}

                <Button
                  type="dashed"
                  block
                  onClick={() =>
                    act.add({
                      typeId: props.data?.typeId || "",
                      preTime: props.data?.preTime || "",
                      content: props.data?.content || "",
                    })
                  }
                >
                  新增预约信息
                </Button>
              </div>
            );
          }}
        </Form.List>
      </Form>
    </Modal>
  );
};
