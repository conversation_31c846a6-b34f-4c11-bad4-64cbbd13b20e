import { makeApi } from "@/utils/Api";
import { CalendarOutlined, CaretLeftOutlined, CaretRightOutlined } from "@ant-design/icons";
import { Button, Calendar, Dropdown, Empty, Input, message, Tag } from "antd";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useSetState } from "react-use";

const getDueList = makeApi("get", `/api/appUserReservationDate/query_date_list`);

export const PickDateTime = (props: { typeId: any; shopId: any; value?: any; onChange?: any }) => {
  const [state, setState] = useSetState({
    open: false,
    date: dayjs(),
    list: [] as any[],
    current: null as any,
  });

  useEffect(() => {
    fetchData();
  }, [props.typeId, props.shopId, state.date]);

  useEffect(() => {
    if (state.open && !props.value) {
      setState({ current: null, date: dayjs() });
    }
  }, [state.open]);

  // 处理外部传入的值，设置当前选中项和日期
  useEffect(() => {
    if (props.value) {
      const dateValue = dayjs(props.value, "YYYY-MM-DD HH:mm");
      if (dateValue.isValid()) {
        setState({
          current: props.value,
          date: dateValue,
        });
      }
    } else {
      setState({ current: null });
    }
  }, [props.value]);

  const fetchData = async () => {
    const typeId = props.typeId;
    const shopId = props.shopId;
    let start = state.date.format("YYYY-MM-DD");
    let end = state.date.format("YYYY-MM-DD");
    let list: any[] = [];

    if (typeId && !state.date.isBefore(dayjs(), "day")) {
      const params = { typeId, shopId, startPreDate: start, endPreDate: end };
      const res = await getDueList({ params });
      list = res?.list?.[0]?.timeList || [];
    }

    list = list.map((n) => ({
      ...n,
      date: state.date.format("YYYY-MM-DD"),
      full: state.date.format(`YYYY-MM-DD ${n.preTime}`),
    }));

    setState({ list });

    // 如果当前有选中的值且在当前日期列表中，确保它被正确标记为选中
    if (state.current) {
      const exists = list.find((item) => item.full === state.current);
      if (!exists) {
        // 如果当前选中的值不在新获取的列表中，清除选中状态
        setState({ current: null });
      }
    }
  };

  return (
    <Dropdown
      open={state.open}
      trigger={["click"]}
      onOpenChange={(e) => setState({ open: e })}
      dropdownRender={() => (
        <div className="bg-#fff border-(1 solid #ddd) rounded-md">
          <div className="flex">
            <div className="w-250px border-r-(1 solid #ddd)">
              <Calendar
                value={state.date}
                onChange={(e) => setState({ date: e })}
                fullscreen={false}
                disabledDate={(crt) => crt && crt.isBefore(dayjs().startOf("day"))}
                headerRender={(data) => {
                  const now = data.value.format("YYYY-MM");
                  return (
                    <div className="flex items-center justify-between h-11 px-4 select-none" key={data.value.unix()}>
                      <CaretLeftOutlined onClick={() => data.onChange(data.value.subtract(1, "month"))} />
                      <span className="text-sm font-bold">{now}</span>
                      <CaretRightOutlined onClick={() => data.onChange(data.value.add(1, "month"))} />
                    </div>
                  );
                }}
              ></Calendar>
            </div>
            <div className="w-280px h-320px overflow-y-auto">
              {!state.list.length && (
                <div className="size-full flex items-center justify-center">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无排期" />
                </div>
              )}
              {!!state.list.length && (
                <div className="grid cols-3 gap-2 p-2">
                  {state.list.map((item) => {
                    return (
                      <div
                        key={item.id}
                        className="group h-8 border-(1 solid #F7F4EF) text-#c48053/80 rounded-md cursor-pointer bg-#F7F4EF flex items-center justify-center data-[disabled=true]:(bg-#ddd border-#f5f5f5 text-#999 pointer-events-none cursor-not-allowed) data-[selected=true]:(bg-#c48053 border-#c48053 text-#fff)"
                        // 人满也可以约
                        // data-disabled={!item.preTag}
                        data-disabled={false}
                        data-selected={state.current == item.full}
                        onClick={() => setState({ current: item.full })}
                      >
                        {item.preTime}
                        {!!item.reservedNum && <small className="ml-1 text-xs">已约{item.reservedNum}</small>}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
          <div className="border-t-(1 solid #eee) h-11 flex items-center px-4 gap-4">
            <span className="text-xs text-gray-400">当前选中:</span>
            <Tag bordered={false} color="blue">
              {state.current ?? "无"}
            </Tag>
            <div className="flex-1"></div>
            <Button size="small" onClick={() => setState({ open: false })}>
              取消
            </Button>
            <Button
              size="small"
              type="primary"
              onClick={() => {
                if (!state.current) return message.error("请选择时间");
                props.onChange?.(state.current);
                setState({ open: false });
              }}
            >
              确定
            </Button>
          </div>
        </div>
      )}
    >
      <div
        className="flex items-center"
        onClick={() => {
          if (!props.typeId) return message.error("缺少预约类型");
          if (!props.shopId) return message.error("缺少预约门店");
          setState({ open: !state.open });
        }}
      >
        <Input placeholder="请选择预约日期" readOnly suffix={<CalendarOutlined className="text-gray-400" />} value={props.value}></Input>
      </div>
    </Dropdown>
  );
};
