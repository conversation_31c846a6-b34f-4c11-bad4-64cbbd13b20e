import DictPicker from "@/components/Units/DictPicker";
import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { Col, Form, Input, Row, Select, message } from "antd";
import { useSetState } from "react-use";

const layout = { row: 10, col: 12 };

const DaoDian = (props: { children: any; title: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    staff: [] as any[],
    user: null as any,
  });

  const handleOpen = () => {
    fetchUser();
    fetchStaff();

    if (props.data) {
      const { ...data } = props.data;
      delete data.content;
      form.setFieldsValue(data);
    }
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999, queryType: 9 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.shopVipUserId = props.data.shopVipUserId;
    data.reservationId = props.data.id;

    await MallApi.addAppUserTriage({ data });
    message.success("操作成功");
    props.onOk();
  };

  return (
    <WeModal width={800} trigger={props.children} title={props.title} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <UserPane user={state.user} />
        <div style={{ height: 20 }}></div>

        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="到院类型" name={`triageTypeId`} rules={[{ required: true, message: "请选择到院类型" }]}>
              <DictPicker type="triageType" placeholder="请选择到院类型" />
            </Form.Item>
          </Col>
          <Col/>
          <Col span={layout.col}>
            <Form.Item label="所属客服" name={`adviserId`}>
              <Select options={state.staff} placeholder="请选择所属客服" allowClear optionFilterProp="label" />
            </Form.Item>
          </Col>
          
          <Col span={layout.col}>
            <Form.Item label="所属开发" name={`developerId`}>
              <Select options={state.staff} placeholder="请选择所属开发" allowClear optionFilterProp="label" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="到院备注" name={`content`}>
              <Input.TextArea placeholder="请输入到院备注" autoSize={{ minRows: 4 }} maxLength={400} showCount />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default DaoDian;
