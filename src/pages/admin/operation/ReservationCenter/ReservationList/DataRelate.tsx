import VipPicker from "@/components/Units/VipPicker";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Row, message } from "antd";

const layout = { row: 10, col: 24 };

const DataRelate = (props: { children: any; title: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    if (!props.data) {
      message.error("请选择用户");
      return false;
    }
    const { ...data } = await form.validateFields();
    data.id = props.data.id;
    data.shopVipUserId = data.shopVipUserId?.value ?? "";
    await MallApi.relateReservation({ data });
    message.success("关联预约成功");
    props.onOk();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={500}
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row>
          <Col span={layout.col}>
            <Form.Item name={`shopVipUserId`} label="关联会员">
              <VipPicker labelInValue placeholder="会员号/姓名/手机号" params={{ queryType: 9 }}  />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default DataRelate;
