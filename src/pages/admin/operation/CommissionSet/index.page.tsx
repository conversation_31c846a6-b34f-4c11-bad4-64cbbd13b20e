import Style from "./index.module.scss";
import { useState } from "react";
import { Card, Form, Input, Radio, Select, Tree, Typography, message, Modal, Button, Table, Space, Dropdown, Pagination } from "antd";
import { useMount, useSetState } from "react-use";
import User<PERSON>pi from "@/services/UserApi";
import { Api } from "./Api";
import MallCommissionConfigApi from "../CommissionConfig/api";
import AsyncSwitch from "@/components/AsyncSwitch";
import { ProductModelType } from "@/components/GoodsPicker/types";
import { MallProductProperty, SaleInType } from "./types";
import { useTable } from "@/components/WeTablev2/useTable";
import { CheckCircleOutlined, ClearOutlined, DownOutlined, FormOutlined, SearchOutlined, StopOutlined, UpOutlined, VerticalAlignBottomOutlined, VerticalAlignTopOutlined } from "@ant-design/icons";
import { CommissionConfigModal } from "./CommissionConfigModal";

const MallMedicalControlPage = () => {

  const [fold, setFold] = useState(true);

  const [state, setState] = useSetState({
    keys: [] as any[],
    companys: [] as any[],
    companyId: null as any,
    commissionConfigList: [] as any[],
    commissionModalOpen: false, // 添加佣金方案模态框状态
  });

  const table = useTable({
    params: { companyIds: state.companyId, queryType: 9 },
    onFetch: (p) => {
      setState({ keys: [] });
      return Api.getProductList({ params: p });
    },
  });

  useMount(() => {
    fetchCompanys();
    fetchCommissionConfig();
  });

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  const fetchCompanys = async () => {
    const params = { pageSize: 9999 };
    const res = await UserApi.selectCompanyList({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ companys: types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      arr.push(format(item));
    });

    return arr;
  };

  const fetchCommissionConfig = async () => {
    const res = await MallCommissionConfigApi.selectMallCommissionConfig();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ commissionConfigList: list });
  };

  // 添加处理佣金方案选择的函数
  const handleCommissionConfigSelect = async (commissionConfigId: any) => {
    const data = { commissionConfigId, ids: state.keys.join(",") };
    await Api.batchModifyCommissionConfig({ data });
    message.success("批量修改佣金方案成功");
    setState({ commissionModalOpen: false });
    handleReload();
  };

  return (
    <div className="flex gap-2">
      <div className="w-250px">
        <Card
          bodyStyle={{ padding: "10px 0px", height: "100%" }}
          title={`公司列表`}
          style={{ height: "100%" }}
        >
          <Tree.DirectoryTree
            className={Style.tree}
            treeData={state.companys}
            showIcon={false}
            selectedKeys={[state.companyId]}
            onSelect={(e) => {
              const key = e[0];
              if (state.companyId === key) {
                setState({ companyId: "" });
              } else {
                setState({ companyId: key });
              }
            }}
            titleRender={(item) => {
              return (
                <div className={Style.row} key={item.key}>
                  <div className={Style.title}>{item.title}</div>
                </div>
              );
            }}
          />
        </Card>
      </div>
      <div className="flex-1 min-w-0">
        <Form form={table.form} onValuesChange={table.onFormChange}>
          <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
            <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
              <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
                <Form.Item label="来源" name={`property`} initialValue={1}>
                  <Radio.Group options={MallProductProperty} optionType="button" buttonStyle="solid" />
                </Form.Item>
                <Form.Item label="项目名称" name={`name`}>
                  <Input placeholder="请输入项目名称" />
                </Form.Item>
                <Form.Item label="项目编号" name={`serialNo`}>
                  <Input placeholder="请输入项目编号" />
                </Form.Item>
                <Form.Item label="模式" name={`productModel`}>
                  <Select options={ProductModelType} fieldNames={{ label: "name", value: "id" }} placeholder="请选择模式" allowClear />
                </Form.Item>
                <Form.Item label="佣金方案" name={`commissionConfigId`}>
                  <Select options={state.commissionConfigList} allowClear showSearch optionFilterProp="label" placeholder="请选择佣金方案" />
                </Form.Item>
                <Form.Item label="上下架" name={`saleIn`}>
                  <Select options={SaleInType} fieldNames={{ label: "name", value: "id" }} placeholder="请选择上下架" allowClear />
                </Form.Item>
                <Form.Item label="推荐标识" name={`recommendTag`}>
                  <Select
                    options={[
                      { label: "开启", value: 1 },
                      { label: "关闭", value: 0 },
                    ]}
                    placeholder="请选择推荐标识"
                    allowClear
                  />
                </Form.Item>
                <Form.Item label="M币赠送" name={`integralTag`}>
                  <Select
                    options={[
                      { label: "开启", value: 1 },
                      { label: "关闭", value: 0 },
                    ]}
                    placeholder="请选择M币赠送"
                    allowClear
                  />
                </Form.Item>
                <Form.Item label="会员折扣" name={`preferentialTag`}>
                  <Select
                    options={[
                      { label: "开启", value: 1 },
                      { label: "关闭", value: 0 },
                    ]}
                    placeholder="请选择会员折扣"
                    allowClear
                  />
                </Form.Item>
              </div>
              <div className="flex gap-2 pl-3">
                <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
                <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                  搜索
                </Button>
                <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
              </div>
            </div>
          </Card>

          <Card
            classNames={{ body: "!p-0" }}
          >
            <Table
              rowKey={"id"}
              dataSource={table.state.list}
              columns={[
                { fixed: "left", title: "项目编号", dataIndex: "serialNo", width: 100 },
                { fixed: "left", title: "项目名称", dataIndex: "name", width: 300 },
                { title: "模式", dataIndex: "productModel", width: 100, render: (c) => ProductModelType.find((i) => i.id === c)?.name || "-" },
                {
                  title: "分类",
                  dataIndex: "shopCategoryName",
                  width: 150,
                  render: (c) => (
                    <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                      {c}
                    </Typography.Text>
                  ),
                },
                { title: "划线价", dataIndex: "oldPrice", render: (c) => <del>&yen;{c}</del> },
                { title: "售价", dataIndex: "salePrice", render: (c) => <span>&yen;{c}</span> },
                {
                  title: "佣金方案",
                  dataIndex: "commissionConfig",
                  render: (c, item) => (<>
                    {c?.name || "-"}
                    <a style={{ marginLeft: 10 }} onClick={() => {
                      // 单行编辑佣金方案
                      let commissionConfigId: any = null;
                      Modal.confirm({
                        title: "请选择佣金方案",
                        content: (
                          <Select
                            style={{ width: "100%" }}
                            options={state.commissionConfigList}
                            placeholder="请选择佣金方案"
                            onChange={(value) => {
                              commissionConfigId = value;
                            }}
                            showSearch
                            optionFilterProp="label"
                          />
                        ),
                        onOk: async () => {
                          if (!commissionConfigId) {
                            message.error("请选择佣金方案");
                            return Promise.reject();
                          }
                          const data = { commissionConfigId, ids: item.id };
                          await Api.batchModifyCommissionConfig({ data });
                          message.success("修改佣金方案成功");
                          handleReload();
                        },
                      });
                    }}>
                      <FormOutlined />
                    </a>
                  </>
                  )
                },
                {
                  title: "上下架",
                  render: (c) => (
                    <AsyncSwitch
                      unCheckedChildren="下架" checkedChildren="上架"
                      onClick={async () => {
                        await Api.batchModifySaleIn({ data: { operType: 1, ids: c.id, saleIn: Number(!c.saleIn) } });
                        handleReload();
                      }}
                      value={!!c.saleIn}
                    />
                  ),
                },
                {
                  title: "推荐",
                  render: (c) => (
                    <AsyncSwitch
                      unCheckedChildren="关闭" checkedChildren="开启"
                      onClick={async () => {
                        await Api.batchModifyRecommendTag({ data: { operType: 1, ids: c.id, recommendTag: Number(!c.recommendTag) } });
                        handleReload();
                      }}
                      value={!!c.recommendTag}
                    />
                  ),
                },
                {
                  title: "M币赠送",
                  render: (c) => (
                    <AsyncSwitch
                      unCheckedChildren="关闭" checkedChildren="开启"
                      onClick={async () => {
                        await Api.batchModifyIntegralTag({ data: { operType: 1, ids: c.id, integralTag: Number(!c.integralTag) } });
                        handleReload();
                      }}
                      value={!!c.integralTag}
                    />
                  ),
                },
                {
                  title: "会员折扣",
                  render: (c) => (
                    <AsyncSwitch
                      unCheckedChildren="关闭" checkedChildren="开启"
                      onClick={async () => {
                        await Api.batchModifyPreferentialTag({ data: { operType: 1, ids: c.id, preferentialTag: Number(!c.preferentialTag) } });
                        handleReload();
                      }}
                      value={!!c.preferentialTag}
                    />
                  ),
                },
              ]}
              pagination={false}
              rowSelection={{
                selectedRowKeys: state.keys,
                onChange: (selectedRowKeys) => {
                  setState({ keys: selectedRowKeys });
                },
              }}
            />
            <div className="p-4 flex items-center">
              <Space>
                <Button
                  onClick={() => {
                    const len = state.keys.length;
                    if (len == table.state.list.length) {
                      setState({ keys: [] });
                    } else {
                      setState({ keys: table.state.list.map((n) => n.id) });
                    }
                  }}
                >
                  全选
                </Button>
                <Dropdown
                  menu={{
                    items: [
                      { label: "批量修改佣金方案", key: "modifyCommission", icon: <FormOutlined /> },
                      { label: "批量上架", key: "up", icon: <VerticalAlignTopOutlined /> },
                      { label: "批量下架", key: "down", icon: <VerticalAlignBottomOutlined /> },
                      { label: "批量开启推荐", key: "openRecommend", icon: <CheckCircleOutlined /> },
                      { label: "批量关闭推荐", key: "closeRecommend", icon: <StopOutlined /> },
                      { label: "批量开启M币赠送", key: "openIntegral", icon: <CheckCircleOutlined /> },
                      { label: "批量关闭M币赠送", key: "closeIntegral", icon: <StopOutlined /> },
                      { label: "批量开启会员折扣", key: "openPreferential", icon: <CheckCircleOutlined /> },
                      { label: "批量关闭会员折扣", key: "closePreferential", icon: <StopOutlined /> },
                    ],
                    onClick: (e) => {
                      if (!state.keys.length) return message.error("请先进行选择");

                      if (e.key == "modifyCommission") {
                        setState({ commissionModalOpen: true });
                      }

                      if (e.key == "up") {
                        Modal.confirm({
                          title: "确定要批量上架这些商品吗？",
                          onOk: async () => {
                            const data = { saleIn: 1, ids: state.keys.join(",") };
                            await Api.batchModifySaleIn({ data });
                            message.success("批量上架成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key == "down") {
                        Modal.confirm({
                          title: "确定要批量下架这些商品吗？",
                          onOk: async () => {
                            const data = { saleIn: 0, ids: state.keys.join(",") };
                            await Api.batchModifySaleIn({ data });
                            message.success("批量下架成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "openRecommend") {
                        Modal.confirm({
                          title: "确定要批量开启推荐吗？",
                          onOk: async () => {
                            const data = { recommendTag: 1, ids: state.keys.join(",") };
                            await Api.batchModifyRecommendTag({ data });
                            message.success("批量开启推荐成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "closeRecommend") {
                        Modal.confirm({
                          title: "确定要批量关闭推荐吗？",
                          onOk: async () => {
                            const data = { recommendTag: 0, ids: state.keys.join(",") };
                            await Api.batchModifyRecommendTag({ data });
                            message.success("批量关闭推荐成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "openIntegral") {
                        Modal.confirm({
                          title: "确定要批量开启M币赠送吗？",
                          onOk: async () => {
                            const data = { integralTag: 1, ids: state.keys.join(",") };
                            await Api.batchModifyIntegralTag({ data });
                            message.success("批量开启M币赠送成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "closeIntegral") {
                        Modal.confirm({
                          title: "确定要批量关闭M币赠送吗？",
                          onOk: async () => {
                            const data = { integralTag: 0, ids: state.keys.join(",") };
                            await Api.batchModifyIntegralTag({ data });
                            message.success("批量关闭M币赠送成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "openPreferential") {
                        Modal.confirm({
                          title: "确定要批量开启会员折扣吗？",
                          onOk: async () => {
                            const data = { preferentialTag: 1, ids: state.keys.join(",") };
                            await Api.batchModifyPreferentialTag({ data });
                            message.success("批量开启会员折扣成功");
                            handleReload();
                          },
                        });
                      }

                      if (e.key === "closePreferential") {
                        Modal.confirm({
                          title: "确定要批量关闭会员折扣吗？",
                          onOk: async () => {
                            const data = { preferentialTag: 0, ids: state.keys.join(",") };
                            await Api.batchModifyPreferentialTag({ data });
                            message.success("批量关闭会员折扣成功");
                            handleReload();
                          },
                        });
                      }
                    },
                  }}
                >
                  <Button>
                    批量操作
                    <DownOutlined />
                  </Button>
                </Dropdown>
                <div className="text-#666 text-xs">
                  {table.state.list.length}条数据(已选{state.keys.length}条)
                </div>
              </Space>
              <Pagination
                className="ml-a"
                {...{
                  current: table.state.page,
                  pageSize: table.state.size,
                  total: table.state.total,
                  showSizeChanger: true,
                  showTotal: (total) => <div>共 {total} 条数据</div>,
                  pageSizeOptions: [5, 10, 20, 50, 100],
                  onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
                }}
              />
            </div>
          </Card>
        </Form>
      </div>
      {/* 添加佣金方案选择模态框 */}
      <CommissionConfigModal
        open={state.commissionModalOpen}
        commissionConfigList={state.commissionConfigList}
        onOk={handleCommissionConfigSelect}
        onCancel={() => setState({ commissionModalOpen: false })}
      />
    </div>
  );
};

export default MallMedicalControlPage;