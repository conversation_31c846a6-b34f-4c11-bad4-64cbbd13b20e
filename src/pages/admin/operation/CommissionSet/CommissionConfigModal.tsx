import { Modal, Select, message } from "antd";
import { useState } from "react";

interface CommissionConfigModalProps {
  open: boolean;
  commissionConfigList: any[];
  onOk: (commissionConfigId: any) => void;
  onCancel: () => void;
}

export const CommissionConfigModal = (props: CommissionConfigModalProps) => {
  const { open, commissionConfigList, onOk, onCancel } = props;
  const [commissionConfigId, setCommissionConfigId] = useState<any>(null);

  const handleOk = async () => {
    if (!commissionConfigId) {
      message.error("请选择佣金方案");
      return Promise.reject();
    }
    onOk(commissionConfigId);
  };

  const handleCancel = () => {
    setCommissionConfigId(null);
    onCancel();
  };

  return (
    <Modal
      title="请选择佣金方案"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <Select
        style={{ width: "100%" }}
        options={commissionConfigList}
        placeholder="请选择佣金方案"
        onChange={(value) => {
          setCommissionConfigId(value);
        }}
        showSearch
        optionFilterProp="label"
      />
    </Modal>
  );
};