import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import dayjs from "dayjs";
import { Badge, Button, DatePicker, Form, Input, Select, } from "antd";
import UserManager from "@/components/UserManager";
import { DownloadOutlined, SwapRightOutlined, WechatOutlined } from "@ant-design/icons";
import { useMount, useSetState } from "react-use";
import { DatePresetRanges, downloadExcel } from "@/utils/Tools";
import VipPicker from "@/components/Units/VipPicker";

const StartEndGroup = (props: { name: any }) => {
  return (
    <div style={{ display: "flex" }}>
      <Form.Item noStyle name={`start${props.name}`}>
        <Input placeholder="开始值" allowClear />
      </Form.Item>
      <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
      <Form.Item noStyle name={`end${props.name}`}>
        <Input placeholder="结束值" allowClear />
      </Form.Item>
    </div>
  );
};

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "--");

const MCoinUserPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    staff: [] as any[],
    vipLv: [] as any[],
    shops: [] as any[],
    total: {} as any,
  });

  useMount(() => {
    fetchShops();
    fetchStaff();
    fetchVipLv();
  });

  const fetchShops = async () => {
    const params = { pageSize: 9999, businessMode: 1 };
    const res = await MallApi.getShopListForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item?.name,
      value: item?.id,
    }));
    setState({ shops: list });
  };

  const fetchTotal = async (p: any) => {
    const res = await MallApi.getMemberTotal({ params: p });
    setState({ total: res });
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  return (
    <WeTable
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      params={{ queryType: 9, startIntegral: 1 }}
      request={(p) => {
        fetchTotal(p);
        return MallApi.getMemberList({ params: p });
      }}
      paramsFormat={(p) => {
        let b: any = p.birthday;
        b = b ? dayjs(b).format("YYYY-MM-DD") : undefined;
        return { ...p, birthday: b };
      }}
      extra={
        <>
          <div className="text-(14px #000/80)">
            <Badge key={"1"} color={"green"} text={`M币：${state.total?.coin ?? "--"}`} style={{ margin: "0 15px" }} />
            <Badge key={"2"} color={"blue"} text={`可提M币：${state.total?.cashCoin ?? "--"}`} style={{ margin: "0 15px" }} />
            <Badge key={"3"} color={"purple"} text={`累计提现M币：${state.total?.totalWithdrawCoin ?? "--"}`} style={{ margin: "0 15px" }} />
          </div>
        </>
      }
      title={
        <>
          <Button
            icon={<DownloadOutlined />}
            type="primary"
            onClick={() => {
              downloadExcel(`/api/mallShopVipUser/exportExcel`, {
                ...tableRef.current?.getParams(),
              });
            }}
          >
            导出
          </Button>
        </>
      }
      search={
        [
          <Form.Item label="会员" name={`searchKey`}>
            <Input placeholder="会员号/姓名/手机号" />
          </Form.Item>,
          <Form.Item label="M币">
            <StartEndGroup name={`Coin`} />
          </Form.Item>,
          <Form.Item label="可提M币">
            <StartEndGroup name={`CashCoin`} />
          </Form.Item>,
          <Form.Item label="累计提现M币">
            <StartEndGroup name={`TotalWithdrawCoin`} />
          </Form.Item>,
          <Form.Item label="会员等级" name={`vipLevelId`}>
            <Select options={state.vipLv} allowClear showSearch optionFilterProp="label" placeholder="请选择会员等级" />
          </Form.Item>,
          <Form.Item label="会员有效期" name={`ExpireDate`}>
            <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
          </Form.Item>,
          <Form.Item label="推荐人" name={`promotionUserId`}>
            <VipPicker placeholder="会员号/姓名/手机号" params={{ queryType: 9 }} />
          </Form.Item>,
          <Form.Item label="建档门店" name={`shopId`}>
            <Select options={state.shops} allowClear showSearch optionFilterProp="label" placeholder="请选择建档门店" />
          </Form.Item>,
          <Form.Item label="建档时间" name={`CreateDate`}>
            <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
          </Form.Item>,
        ]}
      columns={
        [
          {
            fixed: "left",
            title: "会员姓名",
            dataIndex: "name",
            render: (c, item) => (
              <div className="flex items-center gap-1">
                <WechatOutlined
                  className="text-([16px] #2aae67) data-[disabled=true]:invisible"
                  data-disabled={!item?.wxsRegisterTag}
                />
                {c ? (
                  <UserManager userId={item?.id} queryType={9}>
                    <a>{c}</a>
                  </UserManager>
                ) : ('--')}
              </div>
            ),
          },
          { title: "会员号", dataIndex: "vipCard" },
          { title: "手机", dataIndex: "mobile" },
          { title: "会员等级", dataIndex: "vipLevelName" },
          {
            title: "有效期",
            dataIndex: "expireDate",
            sorter: true,
            render: (c, item) => (item.foreverTag == 1 ? "永久有效" : c ? dayjs(c).format("YYYY-MM-DD") : "--"),
          },
          { title: "合计M币", dataIndex: "coin", sorter: true },
          { title: "可提M币", dataIndex: "cashCoin", sorter: true },
          { title: "累计提现M币", dataIndex: "totalWithdrawCoin", sorter: true },
          {
            title: "推荐人",
            dataIndex: "promotionUser",
            render: (c) =>
              c ? (
                <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
              ) : (
                "--"
              ),
          },
          { title: "建档门店", dataIndex: "shopName", render: (c) => c || "--" },
          {
            title: "建档时间",
            dataIndex: "createDate",
            width: 160,
            sorter: true,
            render: (c) => fdate(c),
          },
        ]}
    />
  );
};

export default MCoinUserPage;
