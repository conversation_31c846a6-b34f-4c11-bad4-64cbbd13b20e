import WeModal from "@/components/WeModal/WeModal";
import { Descriptions, Table, Tooltip } from "antd";
import { CancelTypes, CostType, OrderState, PayType } from "./types";
import { formatDate } from "@/utils/Tools";
import { QuestionCircleOutlined } from "@ant-design/icons";

const ModalDetail = (props: { children: any; data: any }) => {
  const data = props.data;
  const user = data.vipUser;

  return (
    <WeModal
      // title={`订单详情`}
      trigger={props.children}
      width={1000}
      // onOpen={handleOpen}
      okButtonProps={{ hidden: true }}
      cancelText="关闭"
    >
      <Descriptions
        className="mt-5"
        size="small"
        title="基本信息"
        items={[
          {
            label: "会员信息",
            children: (
              <>
                {user?.name} ({user?.vipCard})
              </>
            ),
          },
          { label: "订单来源", children: data.sourceName },
          { label: "订单编号", children: data.serialNo },
          { label: "订单状态", children: OrderState.find((n) => n.id == data.orderState)?.name },
          { label: "消费类型", children: CostType.find((n) => n.id == data.billConsumeType)?.name },
          { label: "开单人", children: data.employeeName || "--" },
          { label: "所属客服", children: data.adviserName || "--" },
          { label: "所属开发", children: data.developerName || "--" },
          {
            label: "有效期",
            children: (
              <>
                {data.useEffectiveStyle == 0 && "永久有效"}
                {data.useEffectiveStyle == 20 && `从 ${formatDate(data.useEffectiveStartDate, "YYYY-MM-DD")} 到 ${formatDate(data.useEffectiveEndDate, "YYYY-MM-DD")}`}
              </>
            ),
          },
          { label: "订单备注", children: data.orderRemark || "--" },
        ]}
      />
      <Descriptions
        className="mt-5"
        size="small"
        title="收银信息"
        items={[
          { label: "商品合计", children: data.totalMoney },
          { label: "门诊费", children: data.totalOutpatientFee },
          { label: "治疗费", children: data.totalServiceFee },
          {
            label: (
              <>
                总优惠
                <Tooltip title="总优惠包含：会员折扣、优惠券抵扣、手工折扣、其他优惠等。">
                  <QuestionCircleOutlined style={{ cursor: "pointer", marginLeft: 4 }} />
                </Tooltip>
              </>
            ),
            children: -data.preferentialMoney,
          },
          { label: "会员折扣", children: -data.discountPreferentialMoney },
          { label: "券抵扣", children: -data.couponConvertMoney },
          { label: "实收金额", children: data.payMoney },
          { label: "支付方式", children: PayType.find((n) => n.id == data.payType)?.name },
          { label: "支付时间", children: formatDate(data.payDate) },
        ]}
      />

      {[70, 80].includes(data.orderState) && (
        <Descriptions
          className="mt-5"
          size="small"
          title="退款信息"
          items={[
            { label: "退款方", children: data.cancelFrom == 2 ? "商家" : "用户" },
            { label: "退款金额", children: data.refundApplyMoney, span: 2 },
            { label: "退款原因", children: CancelTypes.find((item) => item.value == data.cancelType)?.label },
            { label: "原因说明", children: data.cancelDesc || "--", span: 2 },
            { label: "申请时间", children: formatDate(data.refundApplyDate) },
            { label: "退款时间", children: formatDate(data.refundFactDate) },
          ]}
        />
      )}

      {!!data.group?.id && (
        <Descriptions
          className="mt-5"
          size="small"
          title="团购信息"
          items={[
            { label: "团购模式", children: data.group?.groupNum + "人团" },
            {
              label: "团购状态",
              children: (
                {
                  10: "拼团中",
                  20: "拼团成功",
                  30: "拼团失败",
                } as any
              )[data.group?.state],
            },
          ]}
        />
      )}

      {data.evaluationState == 2 && (
        <Descriptions
          className="mt-5"
          size="small"
          title="评论信息"
          items={[
            { label: "评论星级", children: data.evaluationLevel },
            { label: "评论时间", children: formatDate(data.evaluationDate), span: 2 },
            { label: "评论内容", children: data.evaluationDesc },
          ]}
        />
      )}

      <Descriptions className="mt-5" size="small" title={"商品清单"} />
      <Table
        dataSource={data.itemList}
        size="small"
        bordered={false}
        pagination={false}
        columns={[
          {
            title: "项目名称",
            render: (item) => (
              <div className="line-clamp-1">
                {item?.productName}
                {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                  <span className="text-gray-400 ml-2px">
                    {" - " + item.productSpecificationName}
                  </span>
                )}
              </div>
            )
          },
          { title: "单价(元)", dataIndex: "productPrice", render: (c) => <span>&yen;{c}</span> },
          { title: "数量", dataIndex: "totalCount" },
          { title: "合计(元)", dataIndex: "totalMoney", render: (c) => <span>&yen;{c}</span> },
          {
            title: "实收(元)",
            dataIndex: "lastTotalMoney",
            render: (c) => (
              <span>
                <b>&yen;{c}</b>
              </span>
            ),
          },
        ]}
      //footer={() => ("共 " + data.itemList.length + " 件商品")}
      ></Table>
    </WeModal>
  );
};

export default ModalDetail;
