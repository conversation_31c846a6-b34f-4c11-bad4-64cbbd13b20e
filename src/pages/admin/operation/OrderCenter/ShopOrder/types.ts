export const SexType = [
  { id: 0, name: "保密" },
  { id: 1, name: "男" },
  { id: 2, name: "女" },
];

export const OrderState = [
  { id: 0, name: "待付款", color: "orange" },
  { id: 10, name: "待消费", color: "green", flash: true },
  { id: 40, name: "已完成", color: "blue" },
  { id: 70, name: "退款确认中", color: "red" },
  { id: 80, name: "已退款", color: "purple" },
  { id: 99, name: "交易关闭", color: "gray" },
];

export const OrderSourceSelectFull = "20,70,21,90,91,92,93,94,95,96,97";
export const OrderSourceSelect = [
  { id: 20, name: "门店订单" },
  { id: 21, name: "商城订单" },
  { id: 92, name: "团购订单" },
  { id: 95, name: "秒杀订单" },
  { id: 96, name: "同行订单" },
  { id: 97, name: "砍价订单" },
  { id: 94, name: "新人订单" },
  { id: 93, name: "推广订单" },
  { id: 90, name: "M币订单" },
  { id: 91, name: "集卡订单" },
  { id: 70, name: "系统赠送" },
];

export const OrderSourceShow = [
  { id: 20, name: "门店订单", color: "#D32F2F" }, // 深红色
  { id: 10, name: "配送订单", color: "#FF6F00" }, // 深橙色
  { id: 11, name: "服务订单", color: "#FBC02D" }, // 金色（偏橄榄）
  { id: 21, name: "商城订单", color: "#388E3C" }, // 深绿色
  { id: 30, name: "充值订单", color: "#00796B" }, // 深青绿色
  { id: 31, name: "退费订单", color: "#0288D1" }, // 深蓝色
  { id: 40, name: "套餐卡订单", color: "#7B1FA2" }, // 深紫色
  { id: 50, name: "VIP订单", color: "#4527A0" }, // 深蓝紫
  { id: 60, name: "分享卡订单", color: "#AFB42B" }, // 橄榄黄
  { id: 70, name: "系统赠送", color: "#9E9D24" },// 深橄榄色
  { id: 90, name: "M币订单", color: "#D32F2F" }, // 深红色
  { id: 91, name: "集卡订单", color: "#5D4037" }, // 深棕色
  { id: 92, name: "团购订单", color: "#303F9F" }, // 深蓝紫色
  { id: 93, name: "推广订单", color: "#1B5E20" }, // 深森林绿
  { id: 94, name: "新人订单", color: "#1565C0" }, // 深钢蓝
  { id: 95, name: "秒杀订单", color: "#6A1B9A" }, // 深玫瑰紫
  { id: 96, name: "同行订单", color: "#006064" }, // 深钢青色
    { id: 97, name: "砍价订单", color: "#4A148C" }, // 深紫色
];


export const OrderRefundStateSelectFull = "70,80";
export const OrderRefundStateSelect = [
  { id: 70, name: "退款确认中", color: "red" },
  { id: 80, name: "已退款", color: "purple" },
];


export const ModelState = [
  { id: 1, name: "到店服务" },
  { id: 2, name: "送货到家" },
];

// =========

export const CostType = [
  { id: 1, name: "首购", color: "green" },
  { id: 2, name: "复购", color: "gray" },
];

export const CancelTypes = [
  { value: 1, label: "项目等信息错误" },
  { value: 2, label: "重复下单/误下单" },
  { value: 3, label: "不想要了" },
  { value: 9, label: "其它" },
];

export const PayType = [
  { id: 0, name: "未知" },
  { id: 1, name: "余额" },
  { id: 2, name: "微信" },
  { id: 3, name: "支付宝" },
  { id: 10, name: "现金" },
  { id: 11, name: "刷卡" },
  { id: 12, name: "扫码" },
];
