import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import dayjs from "dayjs";
import { Button, DatePicker, Form, Input, Select } from "antd";
import UserManager from "@/components/UserManager";
import { DatePresetRanges, downloadExcel } from "@/utils/Tools";
import VipPicker from "@/components/Units/VipPicker";
import SelectShop from "@/components/Units/SelectShop";
import { Api } from "@/utils/Api";
import { IsArrived } from "./types";
import { DownloadOutlined } from "@ant-design/icons";

const MemberPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "--");

  return (
    <WeTable
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      request={(p) => MallApi.getNewAppUserList({ params: p })}
      paramsFormat={(p) => {
        let b: any = p.birthday;
        b = b ? dayjs(b).format("YYYY-MM-DD") : undefined;
        return { ...p, birthday: b };
      }}
      title={
        <Button
          icon={<DownloadOutlined />}
          type="primary"
          onClick={() => {
            downloadExcel(`/api/appUser/exportNewUserExcel`, {
              ...tableRef.current?.getParams(),
            });
          }}
        >
          导出
        </Button>
      }
      search={[
        <Form.Item
          label="建档时间"
          name={`CreateDate`}
          initialValue={[dayjs().subtract(7, "day"), dayjs().endOf("day")]}
        >
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="会员" name={`searchKey`}>
          <Input placeholder="会员号/姓名/手机号" />
        </Form.Item>,
        <Form.Item label="建档门店" name={`shopId`}>
          <SelectShop placeholder="请选择建档门店" />
        </Form.Item>,
        <Form.Item label="首次到院" name={`FirstServiceTime`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="最近到院" name={`LastServiceTime`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="推荐人" name={`promotionUserId`}>
          <VipPicker placeholder="会员号/姓名/手机号" params={{ queryType: 9 }} />
        </Form.Item>,
        <Form.Item label="推荐人建档门店" name={`promotionShopId`}>
          <SelectShop placeholder="请选择推荐人建档门店" />
        </Form.Item>,
        <Form.Item label="是否到院" name={`isArrived`}>
          <Select
            options={IsArrived}
            placeholder="请选择是否到院"
            allowClear
          />
        </Form.Item>,
        <Form.Item label="是否有推荐人" name={`hasPromoter`}>
          <Select
            options={IsArrived}
            placeholder="请选择是否有推荐人"
            allowClear
          />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "name",
          render: (c, item) => (
            <UserManager userId={item.id} queryType={9}>
              <a>{c}</a>
            </UserManager>
          ),
        },
        { title: "会员号", dataIndex: "vipCard" },
        { title: "手机", dataIndex: "mobile" },
        {
          title: "手机归属地",
          dataIndex: "mobileArea",
          render: (c, r) =>
            c ? (
              c
            ) : (
              <a
                onClick={async () => {
                  await Api({
                    method: "put",
                    url: `/api/mallShopVipUser/refresh_mobile_area`,
                    data: { id: r?.id },
                  });
                  handleReload();
                }}
              >
                获取归属地
              </a>
            ),
        },
        { title: "建档门店", dataIndex: "shopName", render: (c) => c || "--" },
        {
          title: "首次到院",
          dataIndex: "firstServiceTime",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "--"),
        },
        {
          title: "最近到院",
          dataIndex: "lastServiceTime",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "--"),
        },
        {
          title: "推荐人",
          dataIndex: "promotionUser",
          render: (c) =>
            c ? <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
              : "--"
        },
        {
          title: "推荐人建档门店",
          dataIndex: "promotionShopName",
          render: (c) => c || "--",
        },
        {
          title: "建档时间",
          dataIndex: "createDate",
          width: 160,
          render: (c) => fdate(c),
        },
      ]}
    />
  );
};

export default MemberPage;
