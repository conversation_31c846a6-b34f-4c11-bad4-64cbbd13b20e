import WeTable from "@/components/WeTable";
import UserApi from "@/services/UserApi";
import { DatePresetRanges, formatDate } from "@/utils/Tools";
import { DatePicker, Form, Input } from "antd";

const LoginLogs = () => {
  const fetchList = (params: any) => {
    return UserApi.getLoginLogList({ params });
  };

  return (
    <div>
      <WeTable
        searchNum={4}
        search={[
          <Form.Item label="账号" name={`account`} >
            <Input placeholder="请输入账号" allowClear />
          </Form.Item>,
          <Form.Item label="日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        request={fetchList}
        columns={[
          { title: "账号", dataIndex: "account" },
          { title: "姓名", dataIndex: "userName" },
          { title: "公司名称", dataIndex: "companyName" },
          { title: "组织名称", dataIndex: "organizeName" },
          { title: "登录IP", dataIndex: "loginIp" },
          { title: "地域", dataIndex: "regional" },
          { title: "浏览器情况", dataIndex: "browserDesc" },
          { title: "状态说明", dataIndex: "resultMessage" },
          { title: "创建时间", dataIndex: "createDate", render: (c) => formatDate(c) },
        ]}
      />
    </div>
  );
};

export default LoginLogs;
