import WeTable from "@/components/WeTable";
import UserApi from "@/services/UserApi";
import { DatePresetRanges, formatDate } from "@/utils/Tools";
import { DatePicker, Form, Input } from "antd";

const LogActionsPage = () => {
  return (
    <div>
      <WeTable
        searchNum={4}
        request={(params) => UserApi.getActionsLogList({ params })}
        search={[
          <Form.Item label="账号" name={`account`} >
            <Input placeholder="请输入账号" />
          </Form.Item>,
          <Form.Item label="产品类型" name={`productType`}>
            <Input placeholder="请输入产品类型" />
          </Form.Item>,
          <Form.Item label="操作类型" name={`operType`}>
            <Input placeholder="请输入操作类型" />
          </Form.Item>,
          <Form.Item label="日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "账号", dataIndex: "account" },
          { title: "姓名", dataIndex: "userName" },
          { title: "公司名称", dataIndex: "companyName" },
          { title: "组织名称", dataIndex: "organizeName" },
          { title: "产品类型", dataIndex: "productType" },
          { title: "操作类型", dataIndex: "operType" },
          { title: "状态说明", dataIndex: "resultMessage" },
          { title: "创建时间", dataIndex: "createDate", render: (c) => formatDate(c) },
        ]}
      />
    </div>
  );
};

export default LogActionsPage;
