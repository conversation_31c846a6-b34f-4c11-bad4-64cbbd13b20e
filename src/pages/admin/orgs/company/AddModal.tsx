import { cloneElement } from "react";
import { useSetState } from "react-use";
import { Col, Form, Input, Modal, Radio, Row, Select, message } from "antd";
import UserApi from "@/services/UserApi";
import AreaPicker from "@/components/AreaPicker";
import { StateMap } from "./types";
import SysTenantApi from "../../system/tenant/api";
import HangyePicker from "@/components/IndustryPicker";

const layout = {
  row: 15,
  col: 12,
};

const AddModal = (props: { children: any; onOk?: Function; data?: any; title?: string }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
    ctypes: [] as any[],
    currentTenant: null as any,
  });

  const handleOpen = () => {
    form.resetFields();

    fetchCurrentTenant();

    fetchTypes();

    if (props.data) {
      const data = {
        ...props.data,
        area: AreaPicker.serializer(props.data),
        industryIds: props.data?.industryIds?.split(","),
      };
      form.setFieldsValue(data);
    }

    setState({ open: true });
  };


  const fetchCurrentTenant = async () => {
    const res = await SysTenantApi.getCurrentTenant();
    setState({ currentTenant: res });
  };



  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();

    const data = {
      ...val,
      ...AreaPicker.deserializer(val.area),
      industryIds: val.industryIds?.join(",") || val.industryIds,
    };

    delete data.area;

    if (data.id) {
      await UserApi.putCompany({ data });
      message.success("修改成功");
    } else {
      await UserApi.addCompany({ data });
      message.success("添加成功");
    }

    handleClose();
    props.onOk?.();

    // console.log(val);
  };

  const fetchTypes = async () => {
    const params = {};
    const res = await UserApi.getCompanyType({ params });
    setState({ ctypes: res.list || [] });
    // console.log(res);
  };

  const isEdit = !!props.data?.id;

  return (
    <>
      {cloneElement(props.children, { onClick: handleOpen })}
      <Modal width={800} title={props.title} open={state.open} onCancel={handleClose} onOk={handleSubmit}>
        <Form form={form} layout="vertical">
          <Row gutter={[layout.row, layout.row]}>
            <Col span={layout.col} hidden>
              <Form.Item name={`id`}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="公司名称" name={`name`} rules={[{ required: true, message: "请填写公司名称" }]}>
                <Input placeholder="请填写公司名称" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="公司类型"
                name={`enterprisetypeId`}
                rules={[{ required: true, message: "请选择公司类型" }]}
              >
                <Select
                  placeholder="请选择公司类型"
                  options={state.ctypes}
                  fieldNames={{ label: "name", value: "id" }}
                />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="行业"
                name={`industryIds`}
                rules={[{ required: true, message: "请选择行业" }]}
                initialValue={[]}
              >
                <HangyePicker />
              </Form.Item>
            </Col>

            <Col span={layout.col}>
              <Form.Item label="联系人" name={`contactName`} rules={[{ required: true, message: "请填写联系人" }]}>
                <Input placeholder="请填写联系人" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="联系电话" name={`contactPhone`} rules={[{ required: true, message: "请填写联系电话" }]}>
                <Input placeholder="请填写联系电话" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="状态" name={`state`} initialValue={1}>
                <Radio.Group options={StateMap} />
              </Form.Item>
            </Col>
            <Col span={layout.col * 2}>
              <Form.Item label="省市区" name={`area`} rules={[{ required: true, message: "请选择省市区" }]}>
                <AreaPicker />
              </Form.Item>
            </Col>
            <Col span={layout.col * 2}>
              <Form.Item label="街道门牌" name={`street`} rules={[{ required: true, message: "请填写街道门牌" }]}>
                <Input placeholder="请填写街道门牌" />
              </Form.Item>
            </Col>

            {/* <Col span={24}>
              <Form.Item label="地图" name={`industryIds`} rules={[{ required: true, message: "请填写详细地址" }]}>
                <Input placeholder="请填写详细地址" />
              </Form.Item>
            </Col> */}

            {state.currentTenant?.accountPrefix
              ? <Col span={layout.col * 2} hidden={isEdit} >
                <Form.Item
                  label="管理账号"
                  name={`manageAccount`}
                  rules={[{ required: true, message: "账号只能包含字母、数字和下划线" }]}
                >
                  <Input placeholder="账号只能包含字母、数字和下划线" addonBefore={state.currentTenant?.accountPrefix + "@"} />
                </Form.Item>
              </Col>
              : <Col span={layout.col} hidden={isEdit} >
                <Form.Item
                  label="管理账号"
                  name={`manageAccount`}
                  rules={[{ required: true, message: "账号只能包含字母、数字和下划线" }]}
                >
                  <Input placeholder="账号只能包含字母、数字和下划线" />
                </Form.Item>
              </Col>
            }



            <Col span={24}>
              <Form.Item label="备注" name={`description`}>
                <Input.TextArea autoSize={{ minRows: 3 }} placeholder="请填写备注" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default AddModal;
