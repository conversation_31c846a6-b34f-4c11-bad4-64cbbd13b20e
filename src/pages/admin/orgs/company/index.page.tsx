import { useRef } from "react";
import { Form, Input, Popconfirm, Space, Tag, message } from "antd";
import WeTable, { WeTableRef } from "@/components/WeTable";
import UserApi from "@/services/UserApi";
import AddModal from "./AddModal";
import { useSetState } from "react-use";

const CompList = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    tree: [] as any[],
    idx: 0,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchList = async (params: any) => {

    const res = await UserApi.getCompanyList({ params });
    const tree = genTree(res?.list || []);

    setState({ tree, idx: state.idx + 1 });
    return { list: tree };

  };

  const genTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];

    list.forEach((item) => {
      const rect = { ...item };
      map[item.id] = rect;

      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(rect);
      }

      if (!parent) {
        arr.push(rect);
      }
    });

    return arr;
  };


  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delCompany({ data });
    message.success("删除成功");
    handleReload();
  };

  const handleResetPwd = async (item: any) => {
    const data = { id: item.id };
    await UserApi.putCompanyPass({ data });
    message.success("密码重置成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        size={9999}
        request={fetchList}
        tableProps={{ defaultExpandAllRows: true, pagination: false, key: state.idx } as any}
        title={
          <AddModal title={"新增公司"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AddModal>
        }
        search={[
          <Form.Item label="公司名称" name={`name`}>
            <Input placeholder="请输入公司名称" />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "公司名称",
            dataIndex: "name",
          },
          {
            title: "上级机构",
            dataIndex: "parentName",
            render: (c) => {
              return c || "--";
            },
          },
          {
            title: "联系人",
            dataIndex: "contactName",
          },
          {
            title: "联系方式",
            dataIndex: "contactPhone",
          },
          {
            title: "管理账号",
            dataIndex: "manageAccount",
          },
          {
            title: "企业类型",
            dataIndex: "enterprisetypeName",
            render: (c) => {
              return c || "--";
            },
          },
          {
            fixed: "right",
            title: "状态",
            dataIndex: "state",
            render: (c) => {
              if (c) return <Tag color="success">启用</Tag>;
              return <Tag color="default">禁用</Tag>;
            },
          },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AddModal title={"编辑公司"} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AddModal>
                  {item.layer > 1 && (
                    <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  )}
                  <Popconfirm title={`确定要重置密码吗？- ${item.name}`} onConfirm={() => handleResetPwd(item)}>
                    <a>重置密码</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default CompList;
