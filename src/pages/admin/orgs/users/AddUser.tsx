import WeModal from "@/components/WeModal/WeModal";
import { Button, Col, Form, Input, Row, Select, Space, Table, TreeSelect, message } from "antd";
import { SexMaps, StateMap } from "./types";
import { useSetState } from "react-use";
import User<PERSON>pi from "@/services/UserApi";
import SysTenantApi from "../../system/tenant/api";
import { PlusOutlined } from "@ant-design/icons";
import AsyncPicker from "@/components/Units/AsyncPicker";
import MallApi from "@/services/MallApi";
import DictPicker from "@/components/Units/DictPicker";

const layout = { row: 10, col: 8 };

const AddUser = (props: { children: any; title: any; data?: any; onOk?: Function; orgs: any[] }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    roles: [] as any[],
    shops: [] as any[],

    currentTenant: null as any,
  });

  const uid = props.data?.id;
  const isEdit = !!uid;

  const handleOpen = async () => {
    form.resetFields();
    setState({ shops: [] });
    
    if (uid) {
      const res = await UserApi.getUser(uid);
      form.setFieldsValue(res);
      setState({ shops: res?.shopEmployeeData || [] });
    }

    getRoleList();
    fetchCurrentTenant();
  };

  const handleOk = async () => {
    const val = await form.validateFields();
    const data = {
      ...val,
      roleIds: val.roleIds.join(","),
      shopEmployeeData: state.shops,
    };

    if (data.id) {
      await UserApi.putUser({ data });
      message.success("编辑用户成功");
    } else {
      await UserApi.addUser({ data });
      message.success("添加用户成功");
    }

    props.onOk?.();
  };

  const fetchCurrentTenant = async () => {
    const res = await SysTenantApi.getCurrentTenant();
    setState({ currentTenant: res });
  };

  const getRoleList = async () => {
    const res = await UserApi.getUserRoles({ params: { userId: uid } });
    const list: any[] = res || [];
    const checked: any[] = [];
    const roles: any[] = [];

    list.forEach((item) => {
      roles.push({ label: item.name, value: item.id });
      if (item.checked) checked.push(item.id);
    });

    form.setFieldsValue({ roleIds: checked });
    setState({ roles });
  };

  return (
    <>
      <WeModal width={800} trigger={props.children} title={props.title} onOk={handleOk} onOpen={handleOpen}>
        <Form form={form} layout="vertical" preserve={false}>
          <Row gutter={layout.row}>
            <Col span={layout.col} hidden>
              <Form.Item name={`id`}>
                <Input />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Row gutter={layout.row}>
                {state.currentTenant?.accountPrefix ? (
                  <Col span={layout.col * 3} hidden={isEdit}>
                    <Form.Item label="账号" name={`account`} rules={[{ required: true, message: "账号只能包含字母、数字和下划线" }]}>
                      <Input placeholder="账号只能包含字母、数字和下划线" addonBefore={state.currentTenant?.accountPrefix + "@"} />
                    </Form.Item>
                  </Col>
                ) : (
                  <Col span={layout.col} hidden={isEdit}>
                    <Form.Item label="账号" name={`account`} rules={[{ required: true, message: "账号只能包含字母、数字和下划线" }]}>
                      <Input placeholder="账号只能包含字母、数字和下划线" />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>

            <Col span={layout.col}>
              <Form.Item label="组织" name={`organizeId`} rules={[{ required: true, message: "请选择组织" }]}>
                <TreeSelect key={props.orgs.length}
                  placeholder="请选择组织"
                  treeData={props.orgs}
                  fieldNames={{ label: "name", value: "id" }}
                  treeDefaultExpandAll
                //disabled={isEdit}
                />
              </Form.Item>
            </Col>

            <Col span={layout.col}>
              <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="手机号" name={`mobile`} rules={[{ required: true, message: "请输入手机号" }]}>
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="性别" name={`gender`} initialValue={0}>
                <Select placeholder="请选择性别" options={SexMaps} fieldNames={{ label: "name", value: "id" }} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="邮箱地址" name={`email`}>
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
            </Col>

            <Col span={layout.col}>
              <Form.Item label="状态" name={`state`} initialValue={1}>
                <Select placeholder="请选择状态" options={StateMap} />
              </Form.Item>
            </Col>

            <Col span={layout.col * 3}>
              <Form.Item label="家庭住址" name={`address`}>
                <Input placeholder="请输入家庭住址" />
              </Form.Item>
            </Col>

            <Col span={layout.col * 3}>
              <Form.Item label="备注" name={`description`}>
                <Input.TextArea autoSize={{ minRows: 3 }} maxLength={50} showCount placeholder="请输入备注" />
              </Form.Item>
            </Col>
            <Col span={layout.col * 3}>
              <Form.Item label="选择角色" name={`roleIds`} initialValue={[]}>
                <Select options={state.roles} placeholder="请选择角色" mode="multiple" allowClear />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="关联门店">
                <div>
                  <Table
                    rowKey={"id"}
                    size="small"
                    locale={{ emptyText: "暂无内容" }}
                    pagination={false}
                    dataSource={state.shops}
                    columns={[
                      { title: "门店", dataIndex: "shopName" },
                      { title: "职位", dataIndex: "position" },
                      {
                        title: "操作",
                        render: (c) => {
                          return (
                            <Space>
                              <a
                                onClick={() => {
                                  const shops = [...state.shops].filter((n) => n.shopId != c.shopId);
                                  setState({ shops });
                                }}
                              >
                                删除
                              </a>
                            </Space>
                          );
                        },
                      },
                    ]}
                  />
                  <ShopModal
                    onOk={(e) => {
                      const shops = [...state.shops].filter((n) => n.shopId != e.shopId);
                      shops.push(e);
                      setState({ shops });
                    }}
                  >
                    <Button className="mt-2" block type="dashed" icon={<PlusOutlined />}>
                      添加门店
                    </Button>
                  </ShopModal>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </WeModal>
    </>
  );
};

const ShopModal = (props: { children?: any; data?: any; onOk?: (e: any) => any }) => {
  const [form] = Form.useForm();

  const onOpen = async () => {
    form.resetFields();
    const data = props.data;
    if (data) {
      form.setFieldsValue({ ...data });
    }
  };

  const onOk = async () => {
    const val = await form.validateFields();
    // console.log(val);
    const data = props.data || {};
    data.shopId = val.shop.value;
    data.shopName = val.shop.label;
    data.positionId = val.pos.value;
    data.position = val.pos.label;
    props.onOk?.(data);
  };

  return (
    <WeModal trigger={props.children} width={400} title="关联门店" onOpen={onOpen} onOk={onOk}>
      <div className="h-2"></div>
      <Form form={form} layout="vertical" preserve={false}>
        <Row gutter={10}>
          <Col span={24} hidden>
            <Form.Item name={`id`}>
              <Input />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="门店" name={`shop`} rules={[{ required: true, message: "请选择门店" }]}>
              <AsyncPicker
                fetch={async () => {
                  const params = { pageSize: 9999, businessMode: 1 };
                  const res = await MallApi.getShopListForSelect({ params });
                  return res?.list || [];
                }}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择门店"
                allowClear
                labelInValue
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="职位" name={`pos`} rules={[{ required: true, message: "请选择职位" }]}>
              <DictPicker type="EmployeePosition" placeholder="请选择职位" labelInValue />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AddUser;
