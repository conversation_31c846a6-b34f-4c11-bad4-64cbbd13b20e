import WeTable, { WeTableRef } from "@/components/WeTable";
import User<PERSON><PERSON> from "@/services/UserApi";
import { SexMaps, StateMap } from "./types";
import AddUser from "./AddUser";
import { useRef } from "react";
import { Card, Col, Form, Input, Popconfirm, Row, Select, Space, Tag, Tree, message } from "antd";
import { useMount, useSetState } from "react-use";
import AsyncPicker from "@/components/Units/AsyncPicker";
import MallApi from "@/services/MallApi";

const UsersPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    orgs: [] as any[],
    organizeId: "",
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delUser({ data });
    message.success("删除成功");
    handleReload();
  };

  const handleResetPwd = async (item: any) => {
    const data = { id: item.id };
    await UserApi.initUserPwd({ data });
    message.success("重置密码成功");
    handleReload();
  };

  const fetchOrgs = async () => {
    const res = await UserApi.getUserOrgsList();

    const map: any = {};
    const arr: any[] = [];

    res?.list?.forEach((item: any) => {
      const rect = { ...item };
      map[rect.id] = rect;
      const parent = map[rect.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(rect);
      } else {
        arr.push(rect);
      }
    });

    setState({ orgs: arr });
  };

  const handleTreeSelect = (e: any[]) => {
    const organizeId = e[0] ? e[0] : "";
    setState({ organizeId });
    setTimeout(() => handleReload());
  };

  useMount(() => {
    fetchOrgs();
  });

  return (
    <div>
      <Row gutter={20} wrap={false}>
        <Col flex={"280px"}>
          <Card title="部门" style={{ minHeight: "100%", boxSizing: "border-box" }}>
            <Tree key={state.orgs.length} treeData={state.orgs} fieldNames={{ title: "name", key: "id" }} defaultExpandAll onSelect={handleTreeSelect} />
          </Card>
        </Col>
        <Col flex={"auto"}>
          <WeTable
            ref={tableRef}
            title={
              <AddUser title={"添加用户"} onOk={handleReload} orgs={state.orgs}>
                <WeTable.AddBtn>添加用户</WeTable.AddBtn>
              </AddUser>
            }
            request={(params) => UserApi.getUserList({ params: { ...params, organizeId: state.organizeId } })}
            search={[
              <Form.Item label="账号" name={`account`}>
                <Input placeholder="请输入账号" />
              </Form.Item>,
              <Form.Item label="名称" name={`name`}>
                <Input placeholder="请输入名称" />
              </Form.Item>,
              <Form.Item label="手机号" name={`mobile`}>
                <Input placeholder="请输入手机号" />
              </Form.Item>,
              <Form.Item label="状态" name={`state`}>
                <Select options={StateMap} placeholder="请选择状态" allowClear />
              </Form.Item>,
              <Form.Item label="关联门店" name={`shopId`}>
                <AsyncPicker
                  fetch={async () => {
                    const params = { pageSize: 9999, businessMode: 1 };
                    const res = await MallApi.getShopListForSelect({ params });
                    return res?.list || [];
                  }}
                  fieldNames={{ label: "name", value: "id" }}
                  placeholder="请选择门店"
                  allowClear
                  labelInValue
                />
              </Form.Item>,
            ]}
            columns={[
              { title: "账号", dataIndex: "account" },
              { title: "名称", dataIndex: "name" },
              { title: "手机号", dataIndex: "mobile" },
              {
                title: "性别",
                dataIndex: "gender",
                render: (c) => SexMaps.find((n) => n.id === c)?.name,
              },
              { title: "所属部门", dataIndex: "organizeName" },
              {
                title: "关联门店",
                dataIndex: "shopEmployeeData",
                render: (c) => (
                  <div>
                    {c?.map((n: any) => (
                      <div>{n?.shopName}</div>
                    ))}
                  </div>
                ),
              },
              {
                fixed: "right",
                title: "状态",
                dataIndex: "state",
                render: (c) => {
                  return c === 1 ? <Tag color="success">启用</Tag> : <Tag color="default">禁用</Tag>;
                },
              },
              {
                title: "操作",
                render: (item) => (
                  <Space>
                    <AddUser title={"编辑用户"} data={item} onOk={handleReload} orgs={state.orgs}>
                      <a>编辑</a>
                    </AddUser>
                    <Popconfirm title={`确定要删除 ${item.account} 吗？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                    <Popconfirm title={`确定要重置 ${item.account} 的密码吗？`} onConfirm={() => handleResetPwd(item)}>
                      <a>重置密码</a>
                    </Popconfirm>
                  </Space>
                ),
              },
            ]}
          />
        </Col>
      </Row>
    </div>
  );
};

export default UsersPage;
