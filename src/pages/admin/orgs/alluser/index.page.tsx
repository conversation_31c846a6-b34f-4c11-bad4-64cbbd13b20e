import WeTable, { WeTableRef } from "@/components/WeTable";
import User<PERSON><PERSON> from "@/services/UserApi";
import { SexMaps, UserStateMaps } from "./types";
import { useRef } from "react";
import { Form, Input, Popconfirm, Select, Space, Tag, message } from "antd";

const UsersPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleResetPwd = async (item: any) => {
    const data = { id: item.id };
    await UserApi.initUserPwd({ data });
    message.success("重置密码成功");
    handleReload();
  };

  return (
    <WeTable
      ref={tableRef}
      request={(params) => UserApi.getAllUserList({ params })}
      search={[
        <Form.Item label="账号" name={`account`}>
          <Input placeholder="请输入账号" />
        </Form.Item>,
        <Form.Item label="名称" name={`name`}>
          <Input placeholder="请输入名称" />
        </Form.Item>,
        <Form.Item label="手机号" name={`mobile`}>
          <Input placeholder="请输入手机号" />
        </Form.Item>,
        <Form.Item label="状态" name={`state`}>
          <Select options={UserStateMaps.map((item) => ({ label: item.name, value: item.id }))} placeholder="请选择用户状态" allowClear />
        </Form.Item>,
      ]}
      columns={[
        { title: "用户编号", dataIndex: "serialNo" },
        { title: "公司", dataIndex: "companyName" },
        { title: "组织", dataIndex: "organizeName" },
        { title: "账号", dataIndex: "account" },
        { title: "手机号", dataIndex: "mobile" },
        { title: "名称", dataIndex: "name" },
        {
          title: "性别",
          dataIndex: "gender",
          render: (c) => SexMaps.find((n) => n.id === c)?.name,
        },
        {
          title: "状态",
          dataIndex: "state",
          render: (c) => {
            return c === 1 ? <Tag color="success">启用</Tag> : <Tag color="default">禁用</Tag>;
          },
        },
        { title: "备注", dataIndex: "description" },
        {
          title: "操作",
          render: (item) => (
            <Space>
              <Popconfirm
                title={`确定要重置 ${item.account} 的密码吗？`}
                onConfirm={() => handleResetPwd(item)}
              >
                <a>重置密码</a>
              </Popconfirm>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default UsersPage;
