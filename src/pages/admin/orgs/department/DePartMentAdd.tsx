import UserApi from "@/services/UserApi";
import { Col, Form, Input, Modal, Radio, Row, TreeSelect, message } from "antd";
import { cloneElement } from "react";
import { useSetState } from "react-use";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };
const DePartMentAdd = (props: {
  children: any;
  title: string;
  tree: any[];
  data?: any;
  onOk?: Function;
  isAddSub?: boolean;
}) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
  });

  const handleOpen = () => {
    form.resetFields();
    let data = { ...props.data };

    if (props.isAddSub) {
      data = { parentId: props.data?.id };
    }

    form.setFieldsValue(data);
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();

    const data = { ...val };

    if (isEdit) {
      await UserApi.putDepartment({ data });
      message.success("修改成功");
    } else {
      await UserApi.addDepartment({ data });
      message.success("添加成功");
    }

    handleClose();
    props.onOk?.();
    // console.log(val);
  };

  const isEdit = !!(props?.data?.id && !props.isAddSub);

  return (
    <>
      {cloneElement(props.children, { onClick: handleOpen })}
      <Modal title={props.title} open={state.open} onCancel={handleClose} onOk={handleSubmit}>
        <Form form={form} labelCol={{ flex: "100px" }}>
          <Row gutter={layout.row}>
            <Col span={layout.col} hidden>
              <Form.Item name={`id`}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="上级"
                name={`parentId`}
                rules={[{ required: true, message: "请选择上级" }]}
              >
                <TreeSelect
                  placeholder="请选择上级"
                  treeData={props.tree}
                  fieldNames={{ value: "id", label: "name" }}
                  allowClear
                  treeDefaultExpandAll
                  disabled={isEdit}
                />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="名称"
                name={`name`}
                rules={[{ required: true, message: "请输入名称" }]}
              >
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="联系人"
                name={`contactName`}
                rules={[{ message: "请输入联系人" }]}
              >
                <Input placeholder="请输入联系人" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="联系电话"
                name={`contactPhone`}
                rules={[{ message: "请输入联系电话" }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="备注" name={`description`}>
                <Input.TextArea
                  autoSize={{ minRows: 3 }}
                  maxLength={50}
                  showCount
                  placeholder="请输入备注"
                />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="状态" name={`state`} initialValue={1}>
                <Radio.Group options={StateMap} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default DePartMentAdd;
