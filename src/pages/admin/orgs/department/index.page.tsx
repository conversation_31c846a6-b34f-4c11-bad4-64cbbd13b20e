import WeTable, { WeTableRef } from "@/components/WeTable";
import User<PERSON><PERSON> from "@/services/UserApi";
import { Form, Input, Popconfirm, Space, Tag, message } from "antd";
import DePartMentAdd from "./DePartMentAdd";
import { useSetState } from "react-use";
import { useRef } from "react";

const DepartMentList = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    tree: [] as any[],
    idx: 0,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchList = async (params: any) => {
    const res = await UserApi.getDepartmentList({ params });
    const tree = genTree(res?.list || []);

    setState({ tree, idx: state.idx + 1 });
    return { list: tree };
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delDepartment({ data });
    message.success("删除成功");
    handleReload();
  };

  const genTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];

    list.forEach((item) => {
      const rect = { ...item };
      map[item.id] = rect;

      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(rect);
      }

      if (!parent) {
        arr.push(rect);
      }
    });

    return arr;
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        size={9999}
        tableProps={{ defaultExpandAllRows: true, pagination: false, key: state.idx } as any}
        title={
          <DePartMentAdd title="添加部门" tree={state.tree} onOk={handleReload}>
            <WeTable.AddBtn>添加部门</WeTable.AddBtn>
          </DePartMentAdd>
        }
        request={fetchList}
        search={[
          <Form.Item label="部门名称" name={`name`}>
            <Input placeholder="请输入部门名称" />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "名称",
            dataIndex: "name",
          },
          {
            title: "联系人",
            dataIndex: "contactName",
          },
          {
            title: "联系电话",
            dataIndex: "contactPhone",
          },
          {
            fixed: "right",
            title: "状态",
            dataIndex: "state",
            render: (c) => {
              return c ? <Tag color="success">启用</Tag> : <Tag color="default">禁用</Tag>;
            },
          },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  {item.property > 1 && (
                    <>
                      <DePartMentAdd
                        title={`编辑部门 - ${item.name}`}
                        tree={state.tree}
                        data={item}
                        onOk={handleReload}
                      >
                        <a>编辑</a>
                      </DePartMentAdd>
                      <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                        <a>删除</a>
                      </Popconfirm>
                    </>
                  )}
                  <DePartMentAdd title={`添加下级`} tree={state.tree} data={item} onOk={handleReload} isAddSub>
                    <a>添加下级</a>
                  </DePartMentAdd>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default DepartMentList;
