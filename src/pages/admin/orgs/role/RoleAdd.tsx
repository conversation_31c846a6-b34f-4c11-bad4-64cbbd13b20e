import User<PERSON>pi from "@/services/UserApi";
import { Col, Form, Input, InputNumber, Modal, Radio, Row, Select, message } from "antd";
import { cloneElement } from "react";
import { useMount, useSetState } from "react-use";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };
const RoleAdd = (props: { children: any; title: string; data?: any; onOk?: Function }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
    dataScope: [] as any[],
    superMaster: false as boolean, // 是否超级管理员
  });

  useMount(() => {
    fetchUserInfo();
  });

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ superMaster: user?.property == 99 });
  };

  const getDataScope = async () => {
    const res = await UserApi.getRoleScope();
    let dataScope: any[] = res?.list || [];
    dataScope = dataScope.map((n) => ({ value: n.value, label: n.name }));
    setState({ dataScope });
  };

  const handleOpen = () => {
    getDataScope();

    const data = { ...props.data };

    form.resetFields();
    form.setFieldsValue(data);
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();

    const data = { ...val };

    if (data.id) {
      await UserApi.putRole({ data });
      message.success("修改成功");
    } else {
      await UserApi.addRole({ data });
      message.success("添加成功");
    }

    handleClose();
    props.onOk?.();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: handleOpen })}

      <Modal width={500} title={props.title} open={state.open} onCancel={handleClose} onOk={handleSubmit}>
        <Form form={form} labelCol={{ flex: "80px" }}>
          <Row gutter={layout.row}>
            <Col span={layout.col} hidden>
              <Form.Item name={`id`}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="角色名" name={`name`} rules={[{ required: true, message: "请输入角色名" }]}>
                <Input placeholder="请输入角色名" disabled={!state.superMaster && props.data?.systemTag === 1} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="英文编码" name={`enCode`} rules={[{ required: true, message: "请输入英文编码" }]}>
                <Input placeholder="请输入英文编码" disabled={!state.superMaster && props.data?.systemTag === 1} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="数据权限" name={`dataScope`} rules={[{ required: true, message: "请选择数据权限" }]} initialValue={10}>
                <Select placeholder="请选择数据权限" options={state.dataScope} showSearch allowClear />
              </Form.Item>
            </Col>
            <Col span={layout.col / 2}>
              <Form.Item label="状态" name={`state`} initialValue={1}>
                <Radio.Group options={StateMap} disabled={!state.superMaster && props.data?.systemTag === 1} />
              </Form.Item>
            </Col>
            <Col span={layout.col / 2}>
              <Form.Item label="排序" name={`sort`}>
                <InputNumber placeholder="999" style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            {state.superMaster && (
              <>
                <Col span={layout.col}>
                  <Form.Item label="系统参数" name={`systemTag`} rules={[{ required: true, message: "请选择系统参数" }]} initialValue={0}>
                    <Radio.Group
                      options={[
                        { label: "是", value: 1 },
                        { label: "否", value: 0 },
                      ]}
                    />
                  </Form.Item>
                </Col>
              </>
            )}
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default RoleAdd;
