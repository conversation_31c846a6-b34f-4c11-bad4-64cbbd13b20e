import WeTable, { WeTableRef } from "@/components/WeTable";
import User<PERSON><PERSON> from "@/services/UserApi";
import { Form, Input, Popconfirm, Select, Space, Tag, message } from "antd";
import { useRef } from "react";
import RoleAdd from "./RoleAdd";
import RoleUser from "./RoleUser";
import { useMount, useSetState } from "react-use";
import { EditAuth } from "./EditAuth";
import { StateMap } from "./types";

const RoleList = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    superMaster: false as boolean, // 是否超级管理员

    authOpen: false,
    authData: null as any,
  });

  useMount(() => {
    fetchUserInfo();
  });

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ superMaster: user?.property == 99 });
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchRoleList = (params: any) => {
    params = { ...params };
    return UserApi.getRoleList({ params });
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delRole({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        autoLoad={true}
        ref={tableRef}
        request={fetchRoleList}
        title={
          <RoleAdd title="添加角色" onOk={handleReload}>
            <WeTable.AddBtn>添加角色</WeTable.AddBtn>
          </RoleAdd>
        }
        search={[
          <Form.Item label="角色名称" name={`name`}>
            <Input placeholder="请输入角色名称" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "角色名称",
            dataIndex: "name",
            render: (c, item) => (
              <div>
                {item?.systemTag === 1 && (
                  <Tag color="red" style={{ marginRight: 3 }}>
                    系
                  </Tag>
                )}
                {c}
              </div>
            ),
          },
          { title: "英文编码", dataIndex: "enCode" },
          { title: "排序", dataIndex: "sort" },
          {
            fixed: "right",
            title: "状态",
            dataIndex: "state",
            render: (c) => {
              return c === 1 ? <Tag color="success">启用</Tag> : <Tag color="default">禁用</Tag>;
            },
          },
          {
            title: "操作",
            render: (item) => (
              <Space>
                <RoleAdd title="编辑角色" data={item} onOk={handleReload}>
                  <a>编辑</a>
                </RoleAdd>
                {(state.superMaster || item?.systemTag === 0) && (
                  <>
                    <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </>
                )}
                <a onClick={() => setState({ authData: item, authOpen: true })}>权限管理</a>
                <RoleUser data={item} onOk={handleReload}>
                  <a>关联用户</a>
                </RoleUser>
              </Space>
            ),
          },
        ]}
      />
      <EditAuth data={state.authData} open={state.authOpen} onClose={() => setState({ authOpen: false })} />
    </div>
  );
};

export default RoleList;
