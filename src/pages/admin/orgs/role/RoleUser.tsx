import WeModal from "@/components/WeModal/WeModal";
import UserApi from "@/services/UserApi";
import { Form, Tree } from "antd";
import { useSetState } from "react-use";

const RoleUser = (props: { children: any; data: any; onOk?: Function }) => {
  const [state, setState] = useSetState({
    treeData: [] as any[],
    checkedKeys: [] as any[],
  });

  const fetchUsersList = async () => {
    const params = { roleId: props.data.id };
    const [res1, res2] = await Promise.all([UserApi.getUserOrgsList(), UserApi.getRoleUserList({ params })]);

    const map: any = {};
    const arr: any[] = [];
    const checkedKeys: any[] = [];

    let orgs: any[] = res1?.list || [];
    orgs = [...orgs];

    orgs.forEach((org) => {
      map[org.id] = org;
      org.checkable = false;

      // if (!org.parentId) {
      //   arr.push(org);
      // }
    });

    orgs.forEach((org) => {
      const parent = map[org.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(org);
      } else {
        arr.push(org);
      }
    });

    res2.forEach((user: any) => {
      const parent = map[user.organizeId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(user);
      }

      if (user.checked) {
        checkedKeys.push(user.id);
      }
    });

    setState({ treeData: arr, checkedKeys });
  };

  const handleChecked = (e: any) => {
    setState({ checkedKeys: e });
  };

  const handleOpen = () => {
    fetchUsersList();
  };

  const handleSubmit = async () => {
    const data = { roleId: props.data.id, userIds: state.checkedKeys.join(",") };
    await UserApi.putRoleUser({ data });
    props.onOk?.();
    // console.log(res);
  };

  return (
    <WeModal title="关联用户" trigger={props.children} onOpen={handleOpen} onOk={handleSubmit}>
      <div>
        <Form layout="vertical">
          <Tree
            key={state.treeData.length}
            checkedKeys={state.checkedKeys}
            onCheck={handleChecked}
            treeData={state.treeData}
            fieldNames={{ title: "name", key: "id" }}
            checkable
            defaultExpandAll
          />
        </Form>
      </div>
    </WeModal>
  );
};

export default RoleUser;
