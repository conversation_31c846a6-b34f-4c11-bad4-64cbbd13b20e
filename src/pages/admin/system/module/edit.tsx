import WeModal from "@/components/WeModal/WeModal";
import SysModuleApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";
import IconPicker from "@/components/IconPicker";

const layout = { row: 10, col: 24 };

const SysModuleEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await SysModuleApi.getSysModuleInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await SysModuleApi.addSysModule({ data });
      message.success("添加模块成功");
    }
    if (data.id) {
      await SysModuleApi.putSysModule({ data });
      message.success("修改模块成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: true, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`image`} label="图标" >
              <IconPicker />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default SysModuleEdit;
