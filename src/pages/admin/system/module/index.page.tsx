import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import SysModuleApi from "./api";
import SysModuleEdit from "./edit";
import { Drawer, Form, Input, Popconfirm, Space, message } from "antd";
import dayjs from "dayjs";
import { useSetState } from "react-use";
import { IconRender } from "@/components/IconPicker";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const SysModulePage = (props: { children: any; title: any }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
  });


  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await SysModuleApi.delSysModule({ data: { ids: item.id } });
    message.success("删除模块成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{}}//默认参数
          request={(p) => SysModuleApi.getSysModule({ params: p })}
          title={
            <SysModuleEdit title={"新增模块"} onOk={handleReload}>
              <WeTable.AddBtn />
            </SysModuleEdit>
          }
          search={[
            <Form.Item label="名称" name={`name`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
            <Form.Item label="英文编码" name={`enCode`}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>,
          ]}
          columns={[
            {
              title: "名称",
              dataIndex: "name",
              render: (c, item) => (<>
                <IconRender className="mr-1" type={item.image} />{c}
              </>
              )
            },
            { title: "英文编码", dataIndex: "enCode", render: (c) => c ? c : "--" },
            //{ title: "图标", dataIndex: "image", render: (c) => c ? c : "--" },
            { title: "排序", dataIndex: "sort" },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <SysModuleEdit title={`编辑模块`} data={item} onOk={handleReload}>
                      <a>编辑</a>
                    </SysModuleEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default SysModulePage;
