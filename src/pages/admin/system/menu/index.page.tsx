import WeTable, { WeTableRef } from "@/components/WeTable";
import User<PERSON><PERSON> from "@/services/UserApi";
import { <PERSON>ton, Card, Popconfirm, Space, Tag, message } from "antd";
import { useMount, useSetState } from "react-use";
import { MenuTypes } from "./types";
import AppMenuEdit from "./AppMenuEdit";
import { useEffect, useRef } from "react";
import SysModulePage from "../module/index.page";
import SysModuleApi from "../module/api";
import { BorderLeftOutlined } from "@ant-design/icons";
import MoveModule from "./MoveModule";
import { IconRender } from "@/components/IconPicker";

const AppMenu = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    modules: [] as any[],
    moduleKey: "",
    list: [] as any[],
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchModuleList = async () => {
    const res = await SysModuleApi.getSysModule();
    const modules = (res?.list || []).map((item: any) => {
      return { ...item, tab: item.name, key: item.id };
    });

    setState({ modules, moduleKey: modules[0]?.key ?? "" });
  };

  const fetchList = async (params: any) => {
    params = { ...params, moduleId: state.moduleKey };
    const res = await UserApi.getAppMenu({ params });
    const list = genTree(res?.list || []);

    setState({ list });
    return { list };
  };

  const genTree = (list: any[] = [], filter: (n?: any) => boolean = () => true) => {
    const map: any = {};
    const arr: any[] = [];

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      if (!filter(item)) return;

      map[item.id] = item;

      if (!item.parentId) {
        arr.push(item);
      }
    });

    list.forEach((item) => {
      const parant = map[item.parentId];
      if (parant) {
        parant.children = parant.children || [];
        parant.children.push(item);
      }
    });

    return arr;
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delAppMenu({ data });
    message.success("删除成功");
    handleReload();
  };

  useMount(() => {
    fetchModuleList();
  });

  useEffect(() => {
    if (!state.moduleKey) return;
    handleReload();
  }, [state.moduleKey]);

  return (
    <div>
      <Card
        tabList={state.modules}
        activeTabKey={state.moduleKey}
        // bodyStyle={{ display: "none" }}
        classNames={{ body: "hidden" }}
        style={{ marginBottom: 10 }}
        onTabChange={(moduleKey) => setState({ moduleKey })}
        tabBarExtraContent={
          <Space>
            <SysModulePage title={"模块管理"}>
              <Button type="primary" icon={<BorderLeftOutlined />}>
                模块管理
              </Button>
            </SysModulePage>
          </Space>
        }
      />

      <WeTable
        autoLoad={false}
        ref={tableRef}
        title={
          <Space>
            <AppMenuEdit moduleId={state.moduleKey} tree={state.list} onOk={handleReload}>
              <WeTable.AddBtn>添加菜单</WeTable.AddBtn>
            </AppMenuEdit>
          </Space>
        }
        request={fetchList}
        columns={[
          {
            title: "名称",
            dataIndex: "name",
            render: (c, item) => (
              <>
                <IconRender className="mr-1" type={item.icon} />
                {c}
              </>
            ),
          },
          { title: "英文编码", dataIndex: "enCode" },
          {
            title: "类型",
            dataIndex: "type",
            render: (c) => {
              const crt = MenuTypes.find((n) => n.id === c);
              if (crt) return <Tag color={crt.color}>{crt.name}</Tag>;
            },
          },
          { title: "路径", dataIndex: "href" },
          {
            title: "状态",
            dataIndex: "state",
            render: (c) => {
              return c === 1 ? <Tag color="success">已启用</Tag> : <Tag color="default">已禁用</Tag>;
            },
          },
          { title: "排序", dataIndex: "sort" },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppMenuEdit moduleId={state.moduleKey} tree={state.list} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppMenuEdit>

                  <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>

                  {[1, 2].includes(item.type) && (
                    <AppMenuEdit moduleId={state.moduleKey} tree={state.list} data={item} onOk={handleReload} pushSub>
                      <a>添加下级</a>
                    </AppMenuEdit>
                  )}
                  {!item.parentId && (
                    <MoveModule title={`模块变更- ${item.name}`} data={item} onOk={handleReload}>
                      <a>模块变更</a>
                    </MoveModule>
                  )}
                </Space>
              );
            },
          },
        ]}
      ></WeTable>
    </div>
  );
};

export default AppMenu;
