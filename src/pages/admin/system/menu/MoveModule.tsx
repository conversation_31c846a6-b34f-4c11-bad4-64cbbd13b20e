import { Col, Form, Modal, Row, Select, message } from "antd";
import { useSetState } from "react-use";
import React, { FC } from "react";
import User<PERSON><PERSON> from "@/services/UserApi";
import SysModuleApi from "../module/api";

const layout = { row: 10, col: 24 };

interface Props {
  data?: any;
  onOk?: any;
  title?: string;
  children: any;
}

const MoveModule: FC<Props> = (props) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
    modules: [] as any[],
  });

  const handleOpen = () => {
    form.resetFields();
    fetchModules();
    setState({ open: true });
  };

  const fetchModules = async () => {
    const params = {};
    const res = await SysModuleApi.getSysModule({ params });
    setState({ modules: res.list || [] });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.id = props.data?.id;

    await UserApi.moveAppMenuModule({ data });
    message.success("移动成功");

    handleClose();
    props.onOk?.();
  };


  return (
    <>
      {React.cloneElement(props.children, { onClick: handleOpen })}
      <Modal open={state.open} width={420} title={props.title} onOk={handleSubmit} onCancel={handleClose}>
        <Form form={form} labelCol={{ flex: "80px" }}>
          <Row gutter={layout.row}>
            <Col span={layout.col}>
              <Form.Item
                label="移动到"
                name={`targetModuleId`}
                rules={[{ required: true, message: "请选择目标模块" }]}
              >
                <Select
                  placeholder="请选择目标模块"
                  options={state.modules}
                  fieldNames={{ label: "name", value: "id" }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default MoveModule;
