import { Form, Input, InputNumber, Modal, Radio, Switch, TreeSelect, message } from "antd";
import { useSetState } from "react-use";
import React, { FC } from "react";

import UserApi from "@/services/UserApi";
import IconPicker from "@/components/IconPicker";

import { MenuTypes } from "./types";

interface Props {
  tree: any[];
  pushSub?: boolean;
  data?: any;
  moduleId: any;
  onOk?: any;
  children: any;
}

const AppMenuEdit: FC<Props> = (props) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({ open: false });

  const handleOpen = () => {
    let data = {};

    if (props.pushSub) {
      data = { parentId: props.data.id };
    } else {
      data = props.data;
    }
    // const data = props.pushSub ? { parentId: props.data.id } : props.data;
    form.resetFields();
    form.setFieldsValue(data);
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const data = { ...val, state: Number(val.state) };
    data.moduleId = props.moduleId;

    if (data.id) {
      await UserApi.putAppMenu({ data });
      message.success("修改成功");
    } else {
      await UserApi.addAppMenu({ data });
      message.success("添加成功");
    }

    handleClose();
    props.onOk?.();
  };

  const treeData = [{ name: "顶级节点", id: "", children: props.tree }];
  const isEdit = !!props.data?.id;
  const isPush = !!props.pushSub;
  const title = isPush ? `添加下级` : isEdit ? `编辑菜单` : `新增菜单`;

  const typeValue = Form.useWatch("type", form);

  return (
    <>
      {React.cloneElement(props.children, { onClick: handleOpen })}
      <Modal open={state.open} width={420} title={title} onOk={handleSubmit} onCancel={handleClose}>
        <Form form={form} labelCol={{ span: 5 }} style={{ paddingTop: 15 }}>
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>


          <Form.Item label="类型" name={`type`} rules={[{ required: true, message: "请选择类型" }]}>
            <Radio.Group
              options={MenuTypes.map((item) => ({ label: item.name, value: item.id }))}
              optionType="button"
              buttonStyle="solid"
              disabled={!isPush && isEdit}
            />
          </Form.Item>

          <Form.Item label="上级" name={`parentId`} initialValue={""}>
            <TreeSelect
              showSearch
              treeData={treeData}
              treeNodeFilterProp="title"
              fieldNames={{ label: "name", value: "id" }}
              disabled={isEdit && typeValue != 2}
            />
          </Form.Item>

          <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请填写名称" }]}>
            <Input />
          </Form.Item>
          <Form.Item
            label="英文编码"
            name={`enCode`}
            rules={[{ required: true, message: "请填写英文编码" }]}
          >
            <Input />
          </Form.Item>
          <Form.Item label="路径" name={`href`}>
            <Input />
          </Form.Item>
          <Form.Item label="图标" name={`icon`}>
            {/* <Input /> */}
            <IconPicker />
          </Form.Item>
          <Form.Item label="排序" name={`sort`} tooltip="数字越小越靠前">
            <InputNumber />
          </Form.Item>
          <Form.Item label="状态" name={`state`} valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default AppMenuEdit;
