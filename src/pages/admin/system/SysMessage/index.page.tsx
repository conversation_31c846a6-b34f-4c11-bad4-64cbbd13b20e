import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import SysMessageApi from "./api";
import SysMessageEdit from "./edit";
import { DatePicker, Form, Input, Popconfirm, Select, Space, message } from "antd";
import { Typography } from "antd";
import dayjs from "dayjs";
import { StateMap, TypeMap } from "./types";
import { DatePresetRanges } from "@/utils/Tools";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const SysMessagePage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await SysMessageApi.delSysMessage({ data: { ids: item.id } });
    message.success("删除消息待办成功");
    handleReload();
  };


  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ state: 0 }}//默认参数
        request={(p) => SysMessageApi.getMyMessage({ params: p })}
        search={[
          <Form.Item label="状态" name={`state`} initialValue={0}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="标题" name={`title`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "日期", dataIndex: "createDate", width: 200, render: (c) => fdate(c) },
          { title: "类型", dataIndex: "type", width: 200, render: (c) => TypeMap.find((i) => i.value === c)?.label },
          {
            title: "标题",
            dataIndex: "title",
            width: 500,
            render: (c) => (
              <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          // {
          //   title: "内容",
          //   dataIndex: "content",
          //   width: 500,
          //   render: (c) => (
          //     <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
          //       {c ? c : "--"}
          //     </Typography.Text>
          //   ),
          // },
          { title: "状态", dataIndex: "state", width: 200, render: (c) => StateMap.find((i) => i.value === c)?.label },
          {
            fixed: "right",
            width: 200,
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <SysMessageEdit title={`查看消息待办`} data={item} onCancel={handleReload} hideSubmit={true}>
                    <a>查看</a>
                  </SysMessageEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default SysMessagePage;
