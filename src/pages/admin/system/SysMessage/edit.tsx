import WeModal from "@/components/WeModal/WeModal";
import SysMessageApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import dayjs from "dayjs";

const layout = { row: 10, col: 24 };

const SysMessageEdit = (props: { title: any; children: any; onOk?: Function; onCancel?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const readSysMessage = async () => {
    const data = { id: props.data.id };
    await SysMessageApi.readSysMessage({ data });
  };

  const handleOpen = async () => {
    readSysMessage();
    form.resetFields();
    if (props.data) {
      const data = await SysMessageApi.getSysMessageInfo(props.data.id);
      data.startDate = data.startDate ? dayjs(data.startDate) : "";
      data.endDate = data.endDate ? dayjs(data.endDate) : "";
      data.readDate = data.readDate ? dayjs(data.readDate) : "";
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.startDate = dayjs(data.startDate).format("YYYY-MM-DD HH:mm:ss");
    data.endDate = dayjs(data.endDate).format("YYYY-MM-DD HH:mm:ss");
    data.readDate = dayjs(data.readDate).format("YYYY-MM-DD HH:mm:ss");
    if (data.id) {
      await SysMessageApi.putSysMessage({ data });
      message.success("修改消息待办成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit} onCancel={() => props.onCancel?.()} >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`title`} label="标题" rules={[{ required: false, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={12} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default SysMessageEdit;
