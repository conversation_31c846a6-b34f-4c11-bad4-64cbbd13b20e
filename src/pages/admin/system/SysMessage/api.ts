import { makeApi } from "@/utils/Api";

const SysMessageApi = {
    getSysMessage: makeApi("get", `/api/sysMessage`),
    getMyMessage: makeApi("get", `/api/sysMessage/query_my_list`),
    getSysMessageInfo: makeApi("get", `/api/sysMessage`, true),
    putSysMessage: makeApi("put", `/api/sysMessage`),
    delSysMessage: makeApi("delete", `/api/sysMessage`),
    readSysMessage: makeApi("post", `/api/sysMessage/read`),
};

export default SysMessageApi;
