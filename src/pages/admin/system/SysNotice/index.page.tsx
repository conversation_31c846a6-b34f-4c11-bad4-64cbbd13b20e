import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import SysNoticeApi from "./api";
import SysNoticeEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { Typography } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateMap } from "./types";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const type = 10;
const SysNoticePage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await SysNoticeApi.delSysNotice({ data: { ids: item.id } });
    message.success("删除公告成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ type: type }}//默认参数
        request={(p) => SysNoticeApi.getSysNotice({ params: p })}
        title={
          <SysNoticeEdit title={"新增公告"} onOk={handleReload}>
            <WeTable.AddBtn />
          </SysNoticeEdit>
        }
        search={[
          <Form.Item label="标题" name={`title`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="内容" name={`content`}>
            <Input placeholder="请输入内容" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "标题",
            dataIndex: "title",
            width: 500,
            render: (c) => (
              <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          {
            title: "内容",
            dataIndex: "content",
            width: 500,
            render: (c) => (
              <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          { title: "开始时间", dataIndex: "startDate", render: (c) => c ? fdate(c) : "--" },
          { title: "结束时间", dataIndex: "endDate", render: (c) => c ? fdate(c) : "--" },
          { title: "状态", dataIndex: "state",render: (c) => StateMap.find((i) => i.value === c)?.label },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <SysNoticeEdit title={`编辑公告`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </SysNoticeEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default SysNoticePage;
