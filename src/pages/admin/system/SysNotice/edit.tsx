import WeModal from "@/components/WeModal/WeModal";
import SysNoticeApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { StateMap } from "./types";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";

const layout = { row: 10, col: 12 };

const type = 10;
const SysNoticeEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {

      const fdate = (n: any) => (n ? dayjs(n) : undefined);

      const data = await SysNoticeApi.getSysNoticeInfo(props.data.id);
      // data.startDate = data.startDate ? dayjs(data.startDate) : "";
      // data.endDate = data.endDate ? dayjs(data.endDate) : "";

      data.Date = [fdate(data.startDate), fdate(data.endDate)];

      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    // data.startDate = dayjs(data.startDate).format("YYYY-MM-DD HH:mm:ss");
    // data.endDate = dayjs(data.endDate).format("YYYY-MM-DD HH:mm:ss");

    const Date = formatDateRange(data.Date);
    data.startDate = Date.start + " 00:00:00";
    data.endDate = Date.end + " 23:59:59";
    delete data.Date;

    data.type = type;
    if (!data.id) {
      await SysNoticeApi.addSysNotice({ data });
      message.success("添加公告成功");
    }
    if (data.id) {
      await SysNoticeApi.putSysNotice({ data });
      message.success("修改公告成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item name={`title`} label="标题" rules={[{ required: true, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: true, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={8} />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`startDate`} label="开始时间" rules={[{ required: false, message: "请选择开始时间" }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`endDate`} label="结束时间" rules={[{ required: false, message: "请选择结束时间" }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col> */}
          <Col span={layout.col} >
            <Form.Item name={`Date`} label="有效时间" rules={[{ required: true, message: "请选择有效时间" }]}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group
                options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default SysNoticeEdit;
