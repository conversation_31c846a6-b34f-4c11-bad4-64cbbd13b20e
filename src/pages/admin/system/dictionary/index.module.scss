.item {
  display: flex;
  font-size: 14px;
  padding: 8px 10px;
  // background: #f5f5f5;
  margin-bottom: 8px;
  border-radius: 4px;
  cursor: pointer;
  align-items: center;

  &:hover {
    background: rgba(0, 0, 0, 0.06);
  }

  &.on {
    background: #e6f4ff;
  }

  .dot {
    position: relative;
    top: -1px;
    margin-right: 5px;
  }

  .name {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  a {
    padding: 4px;
    font-size: 12px;
    // margin: 0 2px;
  }
}
