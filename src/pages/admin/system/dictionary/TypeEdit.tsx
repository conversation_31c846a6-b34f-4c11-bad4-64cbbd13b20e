import WeModal from "@/components/WeModal/WeModal";
import User<PERSON>pi from "@/services/UserApi";
import { Form, Input, InputNumber, Radio, Switch, message } from "antd";


const TypeEdit = (props: { children: any; data?: any; title: any; superMaster?: boolean, onOk: Function }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      // console.log(data);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.state = Number(data.state);
    if (data.id) {
      await UserApi.putDictType({ data });
    } else {
      await UserApi.addDictType({ data });
    }
    message.success("操作成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Form.Item name={`type`} hidden initialValue={2}>
          <Input />
        </Form.Item>
        <Form.Item label="类型名称" name={`name`} rules={[{ required: true, message: "类型名称" }]}>
          <Input placeholder="类型名称" />
        </Form.Item>
        <Form.Item label="英文编码" name={`enCode`} rules={[{ required: true, message: "请输入英文编码" }]}>
          <Input placeholder="请输入英文编码" />
        </Form.Item>
        <Form.Item label="排序" name={`sort`}>
          <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item label="状态" name={`state`} valuePropName="checked" initialValue={true}>
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>
        {props.superMaster && (
          <>
            <Form.Item label="系统参数" name={`systemTag`} initialValue={0}>
              <Radio.Group
                options={[
                  { label: '是', value: 1 },
                  { label: '否', value: 0 },
                ]}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </WeModal>
  );
};

export default TypeEdit;
