import UserApi from "@/services/UserApi";
import { Bad<PERSON>, Card, Col, Popconfirm, Row, Space, Table, Tag, Typography, message } from "antd";
import { useEffect } from "react";
import { useMount, useSetState } from "react-use";
import TypeEdit from "./TypeEdit";
import DataEdit from "./DataEdit";
import WeTable from "@/components/WeTable";
import Styles from "./index.module.scss";

const Dictionary = () => {
  const [state, setState] = useSetState({
    types: [] as any[],
    typeId: null as any,
    datas: [] as any[],
    superMaster: false as boolean, // 是否超级管理员
  });

  useMount(() => {
    fetchTypes();
    fetchUserInfo();
  });

  useEffect(() => {
    if (!state.typeId) return;
    fetchDatas();
  }, [state.typeId]);

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ superMaster: user?.property == 99 });
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await UserApi.getDictType({ params });
    const types: any[] = res?.list || [];
    const typeId = types?.[0]?.id || null;
    setState({ types, typeId });
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delDictType({ data });
    message.success("删除字典类型成功");
    fetchTypes();
  };

  const fetchDatas = async () => {
    const params = { dictionarytypeId: state.typeId, pageSize: 9999 };
    const res = await UserApi.getDictData({ params });
    // console.log(res);
    const datas = genTree(res?.list || []);
    setState({ datas });
  };

  const genTree = (list: any[]) => {
    const arr: any[] = [];
    const map: any = {};
    list.forEach((item) => {
      const rect = { ...item };
      map[item.id] = rect;
    });
    list.forEach((item) => {
      const rect = map[item.id];
      const parent = map[rect.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(rect);
      } else {
        arr.push(rect);
      }
    });

    return arr;
  };

  const handleDelData = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delDictData({ data });
    message.success("删除字典成功");
    fetchDatas();
  };

  return (
    <div>
      <Row gutter={10} wrap={false}>
        <Col flex={"300px"}>
          <Card
            title="字典类型"
            extra={
              <>
                {state.superMaster && (
                  <TypeEdit title={`添加字典类型`} superMaster={state.superMaster} onOk={() => fetchTypes()}>
                    <a>新增类型</a>
                  </TypeEdit>
                )}
              </>
            }
            style={{ minHeight: "100%", boxSizing: "border-box" }}
          >
            {state.types.map((item) => (
              <div
                className={`${Styles.item} ${item.id === state.typeId ? Styles.on : ""}`}
                key={item.id}
                onClick={() => setState({ typeId: item.id })}
              >
                <Badge className={Styles.dot} color={item.state == 1 ? "green" : "gray"} />
                <div className={Styles.name}>
                  <div>
                    {item?.systemTag === 1 && <Tag color="red" style={{ marginRight: 3 }}>系</Tag>}
                    {item.name}
                  </div>
                </div>
                <div className={Styles.extra} onClick={(e) => e.stopPropagation()}>
                  {state.superMaster && (
                    <>
                      <TypeEdit title={`编辑字典类型 - ${item.name}`} data={item} superMaster={state.superMaster} onOk={() => fetchTypes()}>
                        <a>编辑</a>
                      </TypeEdit>
                      <Popconfirm title={`确定要删除字典类型 ${item.name} 吗？`} onConfirm={() => handleDelType(item)}>
                        <a>删除</a>
                      </Popconfirm>
                    </>
                  )}
                </div>
              </div>
            ))}
          </Card>
        </Col>
        <Col flex={"auto"}>
          <Card
            bodyStyle={{ padding: 0 }}
            extra={
              <DataEdit
                title={`新增字典`}
                superMaster={state.superMaster}
                onOk={() => fetchDatas()}
                data={{ dictionarytypeId: state.typeId }}
                tree={state.datas}
              >
                <WeTable.AddBtn children="新增字典" />
              </DataEdit>
            }
            title={
              <>
                <span>{state.types.find((n) => n.id == state.typeId)?.name}</span>
                <span style={{ marginLeft: 10, color: "#999", fontSize: ".9em" }}>
                  {state.types.find((n) => n.id == state.typeId)?.enCode}
                </span>
              </>
            }
          >
            <Table
              rowKey={`id`}
              pagination={false}
              dataSource={state.datas}
              columns={[
                {
                  title: "名称",
                  dataIndex: "name",
                  render: (c, item) => (
                    <div className={Styles.name}>
                      {item?.systemTag === 1 && <Tag color="red" style={{ marginRight: 3 }}>系</Tag>}
                      {c}
                    </div>
                  ),
                },
                { title: "英文编码", dataIndex: "enCode" },
                { title: "排序", dataIndex: "sort" },
                {
                  title: "状态",
                  dataIndex: "state",
                  render: (c) => (
                    <>
                      {c == 1 && <Tag color="green">启用</Tag>}
                      {c == 0 && <Tag color="gray">禁用</Tag>}
                    </>
                  ),
                },
                {
                  title: "默认",
                  dataIndex: "defaultTag",
                  render: (c, item) => (
                    <div>
                      {c === 1 ? <b>当前默认</b> :
                        <>
                          [<Typography.Link
                            onClick={async () => {
                              await UserApi.defaultDictData({ data: { id: item.id } });
                              fetchDatas();
                            }}
                          >
                            设为默认
                          </Typography.Link>
                          ]
                        </>
                      }
                    </div>
                  ),
                },
                {
                  title: "操作",
                  render: (item) => {
                    return (
                      <Space>
                        {(state.superMaster || item?.systemTag === 0) ? (
                          <>
                            <DataEdit
                              title={`编辑字典`}
                              superMaster={state.superMaster}
                              onOk={() => fetchDatas()}
                              data={item}
                              tree={state.datas}
                            >
                              <a>编辑</a>
                            </DataEdit>
                            <Popconfirm
                              title={`确定要删除当前字典吗? - ${item.name}`}
                              onConfirm={() => handleDelData(item)}
                            >
                              <a>删除</a>
                            </Popconfirm>

                            {/* <DataEdit
                          title={`添加下级`}
                          onOk={() => fetchDatas()}
                          data={{ dictionarytypeId: state.typeId, parentId: item.id }}
                          tree={state.datas}
                        >
                          <a>添加下级</a>
                        </DataEdit> */}
                          </>
                        ) : (
                          <span style={{ color: "#999" }}>系统</span>
                        )}
                      </Space>
                    );
                  },
                },
              ]}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dictionary;
