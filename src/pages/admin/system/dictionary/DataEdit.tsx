import WeModal from "@/components/WeModal/WeModal";
import UserApi from "@/services/UserApi";
import { Form, Input, InputNumber, Radio, Switch,  message } from "antd";

const DataEdit = (props: { children: any; data?: any; title: any; superMaster?: boolean, onOk: Function; tree: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      data.parentId = data.parentId || -1;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.state = Number(data.state);

    if (data.parentId === -1) data.parentId = undefined;

    if (data.id) {
      await UserApi.putDictData({ data });
    } else {
      await UserApi.addDictData({ data });
    }
    message.success("操作成功");
    props.onOk();
  };

  // const tree = useMemo(() => {
  //   return [{ id: -1, name: "顶级节点", children: props.tree }];
  // }, [props.tree]);

  // const isEdit = !!props.data?.id;

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 16 }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Form.Item label="字典类型" hidden name={`dictionarytypeId`}>
          <Input />
        </Form.Item>
        {/* <Form.Item
          label="上级目录"
          name={`parentId`}
          rules={[{ required: true, message: "请选择上级目录" }]}
          initialValue={-1}
        >
          <TreeSelect
            placeholder="请选择上级目录"
            treeData={tree}
            fieldNames={{ label: "name", value: "id" }}
            disabled={isEdit}
            treeDefaultExpandedKeys={[-1]}
          />
        </Form.Item> */}
        <Form.Item label="字典名称" name={`name`} rules={[{ required: true, message: "请输入字典名称" }]}>
          <Input placeholder="请输入字典名称" />
        </Form.Item>
        <Form.Item label="英文编码" name={`enCode`} rules={[{ required: true, message: "请输入英文编码" }]}>
          <Input placeholder="请输入英文编码" />
        </Form.Item>
        <Form.Item label="排序" name={`sort`}>
          <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
        </Form.Item>
        <Form.Item label="状态" name={`state`} valuePropName="checked" initialValue={true}>
          <Switch checkedChildren="启用" unCheckedChildren="禁用" />
        </Form.Item>
        {props.superMaster && (
          <>
            <Form.Item label="系统参数" name={`systemTag`} initialValue={0}>
              <Radio.Group
                options={[
                  { label: '是', value: 1 },
                  { label: '否', value: 0 },
                ]}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </WeModal>
  );
};

export default DataEdit;
