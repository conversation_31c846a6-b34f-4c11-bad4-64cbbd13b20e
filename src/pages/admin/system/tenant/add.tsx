import WeModal from "@/components/WeModal/WeModal";
import SysTenantApi from "./api";
import { Col, Divider, Form, Input, Radio, Row, Select, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import { StateMap } from "./types";
import { useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import AreaPicker from "@/components/AreaPicker";
import HangyePicker from "@/components/IndustryPicker";

const layout = { row: 10, col: 12 };

const SysTenantAdd = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    ctypes: [] as any[],
  });

  const fetchTypes = async () => {
    const params = {};
    const res = await UserApi.getCompanyType({ params });
    setState({ ctypes: res.list || [] });
  };


  const handleOpen = async () => {
    form.resetFields();
    fetchTypes();
    if (props.data) {
      const data = await SysTenantApi.getSysTenantInfo(props.data.id);
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      data.Date = [fdate(data.startDate), fdate(data.endDate)];
      data.area = AreaPicker.serializer(data.data),
        data.industryIds = data.data?.industryIds?.split(","),
        form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const data = {
      ...val,
      ...AreaPicker.deserializer(val.area),
      industryIds: val.industryIds?.join(",") || val.industryIds,
    };

    const date = formatDateRange(data.Date, "YYYY-MM-DD HH:mm:ss");
    data.startDate = date.start;
    data.endDate = date.end;
    delete data.Date;
    if (!data.id) {
      await SysTenantApi.addSysTenant({ data });
      message.success("添加租户成功");
    }
    if (data.id) {
      await SysTenantApi.putSysTenant({ data });
      message.success("修改租户成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Divider orientation="left">租户信息</Divider>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="租户名称" rules={[{ required: true, message: "请输入租户名称" }]}>
              <Input placeholder="请输入租户名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: true, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`accountPrefix`} label="管理账号" rules={[{ required: true, message: "前缀，20位字符以内" }]} >
              <Input placeholder="前缀，20位字符以内" maxLength={20} addonAfter="@" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`manageAccount`} noStyle rules={[{ required: true, message: "账号只能包含字母、数字和下划线" }]}>
              <Input placeholder="账号只能包含字母、数字和下划线" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`contactName`} label="联系人" rules={[{ required: true, message: "请输入联系人" }]}>
              <Input placeholder="请输入联系人" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`contactPhone`} label="联系电话" rules={[{ required: true, message: "请输入联系电话" }]}>
              <Input placeholder="请输入联系电话" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item
              label="有效期限"
              name={`Date`}
              initialValue={[]}
              rules={[{ required: true, message: "请选择期限" }]}
            >
              <DatePicker.RangePicker
                presets={DatePresetRanges}
                style={{ width: "100%" }}
                showTime
              />
            </Form.Item>
          </Col>
          <Divider orientation="left">企业信息</Divider>
          <Col span={layout.col}>
            <Form.Item
              label="公司类型"
              name={`enterprisetypeId`}
              rules={[{ required: true, message: "请选择公司类型" }]}
            >
              <Select
                placeholder="请选择公司类型"
                options={state.ctypes}
                fieldNames={{ label: "name", value: "id" }}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="行业"
              name={`industryIds`}
              rules={[{ required: true, message: "请选择行业" }]}
              initialValue={[]}
            >
              <HangyePicker />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="省市区" name={`area`} rules={[{ required: true, message: "请选择省市区" }]}>
              <AreaPicker />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="街道门牌" name={`street`} rules={[{ required: true, message: "请填写街道门牌" }]}>
              <Input placeholder="请填写街道门牌" />
            </Form.Item>
          </Col>
          {/*   <Col span={layout.col}>
            <Form.Item
              label="管理账号"
              name={`manageAccount`}
              rules={[{ required: true, message: "请填写管理账号" }]}
            >
              <Input placeholder="请填写管理账号" />
            </Form.Item>
          </Col> */}
          <Divider orientation="left">其他信息</Divider>
          <Col span={layout.col * 2}>
            <Form.Item name={`description`} label="备注" rules={[{ required: false, message: "请输入备注" }]}>
              <Input.TextArea placeholder="请输入备注" rows={3} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default SysTenantAdd;
