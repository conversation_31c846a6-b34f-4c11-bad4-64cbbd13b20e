import { makeApi } from "@/utils/Api";

const SysTenantApi = {
    getSysTenant: makeApi("get", `/api/sysTenant`),
    getSysTenantInfo: makeApi("get", `/api/sysTenant`, true),
    addSysTenant: makeApi("post", `/api/sysTenant`),
    putSysTenant: makeApi("put", `/api/sysTenant`),
    delSysTenant: makeApi("delete", `/api/sysTenant`),
    defaultSysTenant: makeApi("post", `/api/sysTenant/set_default`),
    refreshSysTenant: makeApi("post", `/api/sysTenant/refresh`),
    initTenantData: makeApi("post", `/api/sysTenant/init_data`),
    getCurrentTenant: makeApi("get", `/api/sysTenant/query_current_tenant`),
    getTenantMenu: makeApi("get", "/api/sysTenantMenuR/tenant_menu_list"),
    modifyTenantMenu: makeApi("post", "/api/sysTenantMenuR/modify_tenant_menu"),
};

export default SysTenantApi;
