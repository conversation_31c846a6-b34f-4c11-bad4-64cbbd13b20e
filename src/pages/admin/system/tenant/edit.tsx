import WeModal from "@/components/WeModal/WeModal";
import SysTenantApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import { StateMap } from "./types";

const layout = { row: 10, col: 12 };

const SysTenantEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await SysTenantApi.getSysTenantInfo(props.data.id);
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      data.Date = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    const date = formatDateRange(data.Date, "YYYY-MM-DD HH:mm:ss");
    data.startDate = date.start;
    data.endDate = date.end;
    delete data.Date;
    if (!data.id) {
      await SysTenantApi.addSysTenant({ data });
      message.success("添加租户成功");
    }
    if (data.id) {
      await SysTenantApi.putSysTenant({ data });
      message.success("修改租户成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="租户名称" rules={[{ required: true, message: "请输入租户名称" }]}>
              <Input placeholder="请输入租户名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: true, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`contactName`} label="联系人" rules={[{ required: true, message: "请输入联系人" }]}>
              <Input placeholder="请输入联系人" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`contactPhone`} label="联系电话" rules={[{ required: true, message: "请输入联系电话" }]}>
              <Input placeholder="请输入联系电话" />
            </Form.Item>
          </Col>
          <Col span={layout.col*2}>
            <Form.Item
              label="有效期限"
              name={`Date`}
              initialValue={[]}
              rules={[{ required: true, message: "请选择期限" }]}
            >
              <DatePicker.RangePicker
                presets={DatePresetRanges}
                style={{ width: "100%" }}
                showTime
              />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`description`} label="备注" rules={[{ required: false, message: "请输入备注" }]}>
              <Input.TextArea placeholder="请输入备注" rows={3} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={0}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default SysTenantEdit;
