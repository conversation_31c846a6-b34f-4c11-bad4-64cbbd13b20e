import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import SysTenantApi from "./api";
import SysTenantEdit from "./edit";
import { Form, Input, Space, Select, message, Typography, Popconfirm, Button, Tag } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateMap } from "./types";
import SysTenantAdd from "./add";
import { CheckCircleOutlined } from "@ant-design/icons";
import { useSetState } from "react-use";
import { EditAuth } from "./EditAuth";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const SysTenantPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    authOpen: false,
    authData: null as any,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleRefresh = async () => {
    await SysTenantApi.refreshSysTenant();
    message.success("刷新成功");
  };

  const handleInitData = async (item: any) => {
    await SysTenantApi.initTenantData({ data: { id: item.id } });
    message.success("初始化配置成功");
    handleReload();
  };

  // const handleDel = async (item: any) => {
  //   await SysTenantApi.delSysTenant({ data: { ids: item.id } });
  //   message.success("删除租户成功");
  //   handleReload();
  // };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => SysTenantApi.getSysTenant({ params: p })}
        title={
          <Space>
            <SysTenantAdd title={"新增租户"} onOk={handleReload}>
              <WeTable.AddBtn />
            </SysTenantAdd>
            <Popconfirm title={`确定要应用租户信息？`} onConfirm={() => handleRefresh()}>
              <Button type="primary" icon={<CheckCircleOutlined />}>
                应用租户
              </Button>
            </Popconfirm>
          </Space>
        }
        search={[
          <Form.Item label="租户名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="英文编码" name={`enCode`}>
            <Input placeholder="请输入英文编码" />
          </Form.Item>,
          <Form.Item label="联系人" name={`contactName`}>
            <Input placeholder="请输入联系人" />
          </Form.Item>,
          <Form.Item label="联系电话" name={`contactPhone`}>
            <Input placeholder="请输入联系电话" />
          </Form.Item>,
          <Form.Item label="备注" name={`description`}>
            <Input placeholder="请输入备注" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "租户ID",
            dataIndex: "id",
          },
          {
            title: "租户名称",
            dataIndex: "name",
            render: (c, item) => (
              <div>
                {item?.property === 1 && (
                  <Tag color="red" style={{ marginRight: 3 }}>
                    系
                  </Tag>
                )}
                {c}
              </div>
            ),
          },
          { title: "英文编码", dataIndex: "enCode", render: (c) => (c ? c : "--") },
          { title: "管理账号", dataIndex: "manageAccount", render: (c) => (c ? c : "--") },
          { title: "联系人", dataIndex: "contactName", render: (c) => (c ? c : "--") },
          { title: "联系电话", dataIndex: "contactPhone", render: (c) => (c ? c : "--") },
          { title: "开始时间", dataIndex: "startDate", render: (c) => (c ? fdate(c) : "--") },
          { title: "结束时间", dataIndex: "endDate", render: (c) => (c ? fdate(c) : "--") },
          { title: "备注", dataIndex: "description", render: (c) => (c ? c : "--") },
          {
            title: "默认",
            dataIndex: "defaultTag",
            render: (c, item) => (
              <div>
                {c === 1 ? <b>当前默认</b> :
                  <>
                    [
                    <Typography.Link
                      onClick={async () => {
                        await SysTenantApi.defaultSysTenant({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      设为默认
                    </Typography.Link>
                    ]
                  </>
                }
              </div>
            ),
          },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => (c ? StateMap.find((item) => item.value === c)?.label : "--") },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  {item.property === 2 && (
                    <>
                      <SysTenantEdit title={`编辑租户`} data={item} onOk={handleReload}>
                        <a>编辑</a>
                      </SysTenantEdit>
                      <a onClick={() => setState({ authOpen: true, authData: item })}>权限管理</a>
                      <Popconfirm title={`确定要初始化数据？`} onConfirm={() => handleInitData(item)}>
                        <a>初始化数据</a>
                      </Popconfirm>
                      {/* <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                        <a>删除</a>
                      </Popconfirm> */}
                    </>
                  )}
                </Space>
              );
            },
          },
        ]}
      />

      <EditAuth data={state.authData} open={state.authOpen} onClose={() => setState({ authOpen: false })} />
    </div>
  );
};

export default SysTenantPage;
