import { arr2str } from "@/utils/Tools";
import { Checkbox, message, Modal, Spin, Tabs } from "antd";
import { useEffect, useMemo } from "react";
import { useSetState } from "react-use";
import SysTenantApi from "./api";

// const id = `1074380995297316864`;

// 为了满足特殊需求的自定义组件
export const EditAuth = (props: { data: any; open?: boolean; onClose?: any }) => {
  const [state, setState] = useSetState({
    map: {} as any,
    tree: [] as any[],
    ids: [] as any[],
    loading: false,
    busy: false,
  });

  // useMount(() => {
  //   fetchRoleMenu();
  // });

  useEffect(() => {
    if (!props.open) return;

    setState({ map: {}, tree: [], ids: [] });
    fetchRoleMenu();
  }, [props.open]);

  const fetchRoleMenu = async () => {
    setState({ busy: true });
    const params = { sysTenantId: props.data?.id ?? "" };
    const res: any[] = await SysTenantApi.getTenantMenu({ params }).finally(() => setState({ busy: false }));

    const { tree, map, ids } = genTree(res);

    setState({ tree, ids, map });
  };

  const genTree = (list: any[]) => {
    const moduleMap: any = {};
    const nodeMap: any = {};
    const ids: any[] = [];

    list.sort((a, b) => a.sort - b.sort);
    list = list.map((n) => ({ ...n, children: [], subs: [] }));

    list.forEach((item) => {
      nodeMap[item.id] = item;

      if (item.checked) {
        ids.push(item.id);
      }

      const mid = item.module?.id;
      if (mid && !moduleMap[mid]) {
        moduleMap[mid] = { ...item.module, children: [] };
      }
    });

    list.forEach((item) => {
      const parent = nodeMap[item.parentId];
      if (parent) {
        // parent.children.push(item);
        // {1: "目录", 2: "页面", 3: "权限"}
        if (item.type == 3) {
          parent.subs.push(item);
        } else {
          parent.children.push(item);
        }
      } else {
        const md = moduleMap[item.module?.id];
        if (md) md.children.push(item);
      }
    });

    const tree: any[] = Object.values(moduleMap);
    tree.sort((a, b) => a.sort - b.sort);

    return { tree: tree, map: nodeMap, ids: ids };
  };

  const half = useMemo(() => {
    const arr: any[] = [];

    const check = (node: any) => {
      if (!node.children?.length) return state.ids.includes(node.id) ? "full" : "none";

      const child: any[] = node.children.map(check);

      const full = child.every((n) => n == "full");
      const none = child.every((n) => n == "none");

      if (full) {
        return "full";
      } else if (none) {
        return "none";
      } else {
        arr.push(node.id);
        return "half";
      }
    };

    state.tree.forEach(check);

    return arr;
  }, [state.tree, state.ids]);

  const updateNode = (id: string, bool: boolean, list: any[]) => {
    list = list.filter((n) => n != id);
    if (bool) list.push(id);
    return list;
  };

  const onNodeCheck = (node: any) => {
    const checked = !state.ids.includes(node.id);
    let ids = [...state.ids];

    const updateParent = (node: any) => {
      const parent = state.map[node.parentId];
      if (!parent) return;

      const children: any[] = parent.children;

      const all = children.every((n) => ids.includes(n.id));

      ids = updateNode(parent.id, all, ids);

      updateParent(parent);
    };

    const updateChildren = (node: any) => {
      const children: any[] = node.children;

      children.forEach((n) => {
        ids = updateNode(n.id, checked, ids);
        updateChildren(n);
      });

      // false 关闭所有 sub
      if (!checked) {
        (node.subs as any[]).forEach((n) => {
          ids = updateNode(n.id, checked, ids);
        });
      }
    };

    ids = updateNode(node.id, checked, ids);
    updateParent(node);
    updateChildren(node);

    setState({ ids });
  };

  const renderTree = (tree: any[]) => {
    return (
      <div>
        {tree.map((node) => {
          const checked = state.ids.includes(node.id);

          return (
            <div className="my-1" key={node.id}>
              <div className="flex my-1">
                <div className="min-w-30">
                  <Checkbox className="text-#666 fw-bold" indeterminate={half.includes(node.id)} checked={checked} onClick={() => onNodeCheck(node)}>
                    {node.name}
                  </Checkbox>
                </div>
                <div className="ml-a w-640px grid cols-4 gap-1">
                  {node.subs.map((sub: any) => {
                    const ck = state.ids.includes(sub.id);
                    return (
                      <Checkbox
                        key={sub.id}
                        className="text-brand"
                        disabled={!checked}
                        checked={ck}
                        onClick={() => {
                          const ck = !state.ids.includes(sub.id);
                          let ids = updateNode(sub.id, ck, state.ids);
                          setState({ ids });
                        }}
                      >
                        {sub.name}
                      </Checkbox>
                    );
                  })}
                </div>
              </div>
              <div className="pl-8 my-1">{renderTree(node.children)}</div>
            </div>
          );
        })}
      </div>
    );
  };

  const onOk = async () => {
    setState({ loading: true });
    const data = { sysTenantId: props.data?.id, menuIds: arr2str(state.ids) };
    await SysTenantApi.modifyTenantMenu({ data }).finally(() => setState({ loading: false }));
    message.success("修改成功");
    props.onClose?.();
  };

  return (
    <Modal open={props.open} onCancel={props.onClose} width={1000} title={`权限分配 - ` + props.data?.name} confirmLoading={state.loading} onOk={onOk}>
      <Spin spinning={state.busy} delay={200}>
        <div className="flex pt-2 min-h-300px">
          <Tabs
            className="w-full"
            tabPosition="left"
            items={state.tree.map((md) => ({
              key: md.id,
              label: md.name,
              children: renderTree(md.children),
            }))}
          />
        </div>
      </Spin>
    </Modal>
  );
};
