import WeTable, { WeTableRef } from "@/components/WeTable";
import { Button, Form, Input, message, Popconfirm, Space, Typography } from "antd";
import { useRef } from "react";
import OptionAdd from "./eidt";
import { applyOption, getOptionList } from "@/services/OptionApi";
import { CheckCircleOutlined } from "@ant-design/icons";

const OptionList = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchOptionList = (params: any) => {
    return getOptionList({ params });
  };

  const applyConfig = async () => {
    await applyOption();
    message.success("应用签到配置成功");
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        size={100}
        request={fetchOptionList}
        title={
          <Popconfirm title={`确定要应用配置信息？`} onConfirm={() => applyConfig()}>
            <Button type="primary" icon={<CheckCircleOutlined />}>应用配置</Button>
          </Popconfirm>
        }
        search={[
          <Form.Item label="编码" name={`code`}>
            <Input placeholder="请输入编码" />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>
        ]}
        columns={[
          /*  { title: "编码", dataIndex: "code" }, */
          { title: "名称", dataIndex: "name" },
          {
            title: "值",
            dataIndex: "value",
            render: (c) => (
              <Typography.Text style={{ width: 400 }} ellipsis={{ tooltip: true }}>
                {c}
              </Typography.Text>
            ),
          },
          /*  { title: "备注", dataIndex: "description" }, */
          {
            title: "操作",
            render: (item) => (
              <Space>
                <OptionAdd title={`编辑配置`} data={item} onOk={handleReload}>
                  <a>编辑</a>
                </OptionAdd>
              </Space>
            ),
          },
        ]}
      />
    </div>
  );
};

export default OptionList;
