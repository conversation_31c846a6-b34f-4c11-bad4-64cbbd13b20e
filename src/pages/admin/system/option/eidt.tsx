import { putOption } from "@/services/OptionApi";
import { Alert, Col, Form, Input, Modal, Row, message } from "antd";
import { cloneElement } from "react";
import { useSetState } from "react-use";

const layout = {
  col: 24
}

const OptionAdd = (props: {
  children: any;
  title: string;
  data?: any;
  onOk?: Function;
}) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
  });

  const handleOpen = () => {
    const data = { ...props.data };

    form.resetFields();
    form.setFieldsValue(data);
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();

    const data = {
      ...val,
    };

    if (data.id) {
      await putOption({ data });
      message.success("修改成功");
    }

    handleClose();
    props.onOk?.();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: handleOpen })}

      <Modal
        width={800}
        title={props.title}
        open={state.open}
        onCancel={handleClose}
        onOk={handleSubmit}
      >
        <Form form={form} layout="vertical" >
          <Row gutter={10}>
            <Col span={layout.col} hidden>
              <Form.Item name={`id`}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="">
                <div className="grid gap-4">
                  <Alert
                    message={form.getFieldValue('description') || '暂无提示信息'}
                    type="info"
                  />
                </div>
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item
                label="值"
                name={`value`}
                rules={[{ required: true, message: "请输入值" }]}
              >
                <Input.TextArea autoSize={{ minRows: 3 }} placeholder="请输入值" />
              </Form.Item>
            </Col>

          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default OptionAdd;
