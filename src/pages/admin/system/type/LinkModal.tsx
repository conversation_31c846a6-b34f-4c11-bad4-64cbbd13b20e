import WeModal from "@/components/WeModal/WeModal";
import UserApi from "@/services/UserApi";
import { Form, Select, message } from "antd";
import { useSetState } from "react-use";

const LinkModal = (props: { children: any; data: any; onOk?: Function }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    opts: [] as any[],
  });

  const handleOpen = () => {
    form.resetFields();
    const manageTypeIds = (props.data.manageTypeIds || "").split(",").filter((n: any) => n);
    form.setFieldsValue({ manageTypeIds });

    fetchOpts();
  };

  const fetchOpts = async () => {
    const params = { pageSize: 9999 };
    const res = await UserApi.getAppRoleList({ params });
    setState({ opts: res?.list || [] });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const data = { id: props.data?.id, manageTypeIds: val.manageTypeIds.join(",") };
    await UserApi.putAppRole({ data });
    message.success("修改成功");
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title="管理类型" width={600} onOpen={handleOpen} onOk={handleSubmit}>
      <div style={{ height: 20 }}></div>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item label="关联列表" name={`manageTypeIds`} initialValue={[]}>
          <Select
            options={state.opts}
            placeholder="请选择管理类型"
            mode="multiple"
            fieldNames={{ label: "name", value: "id" }}
            allowClear
          />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default LinkModal;
