import { Form, Input, Popconfirm, Select, Space, TableColumnsType, Tag, message } from "antd";
import { useRef } from "react";
import WeTable from "@/components/WeTable";
import UserApi from "@/services/UserApi";
import AppRoleEdit from "./AppRoleEdit";
import LinkModal from "./LinkModal";
import { useSetState } from "react-use";
import { EditAuth } from "./EditAuth";
import { StateMap } from "./types";

const AppRole = () => {
  const tableRef = useRef<any>();
  const [state, setState] = useSetState({
    authOpen: false,
    authData: null as any,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await UserApi.delAppRole({ data });
    message.success("删除成功");
    handleReload();
  };

  const fetchList = (params: any) => {
    params = { ...params };
    return UserApi.getAppRoleList({ params });
  };

  const columns: TableColumnsType<any> = [
    { title: "名称", dataIndex: "name" },
    { title: "英文编码", dataIndex: "enCode" },
    {
      title: "状态",
      dataIndex: "state",
      render: (c) => (c === 1 ? <Tag color="success">启用</Tag> : <Tag color="default">禁用</Tag>),
    },
    {
      title: "操作",
      render: (item) => {
        return (
          <Space>
            <a onClick={() => setState({ authOpen: true, authData: item })}>权限管理</a>
            <LinkModal data={item} onOk={() => handleReload()}>
              <a>管理类型</a>
            </LinkModal>

            <AppRoleEdit onOk={handleReload} data={item} title={"编辑企业类型"}>
              <a>编辑类型</a>
            </AppRoleEdit>
            <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
              <a>删除类型</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <WeTable
        ref={tableRef}
        title={
          <AppRoleEdit onOk={handleReload} title={"添加企业类型"}>
            <WeTable.AddBtn />
          </AppRoleEdit>
        }
        request={fetchList}
        columns={columns}
        search={[
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
        ]}
      />
      <EditAuth data={state.authData} open={state.authOpen} onClose={() => setState({ authOpen: false })} />
    </>
  );
};

export default AppRole;
