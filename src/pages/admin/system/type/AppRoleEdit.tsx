import UserApi from "@/services/UserApi";
import { Col, Form, Input, InputNumber, Modal, Row, Switch, message } from "antd";
import { cloneElement } from "react";
import { useSetState } from "react-use";

interface Props {
  onOk?: any;
  data?: any;
  title: any;
  children: any;
}

const AppRoleEdit = (props: Props) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    open: false,
    checked: [] as string[],
  });

  const handleOpen = () => {
    form.resetFields();

    if (props.data?.id) {
      form.setFieldsValue(props.data);
    }

    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const data = { ...val, state: Number(val.state) };

    if (data.id) {
      await UserApi.putAppRole({ data });
      message.success("编辑成功");
    } else {
      await UserApi.addAppRole({ data });
      message.success("添加成功");
    }

    props.onOk?.();
    handleClose();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: handleOpen })}
      <Modal title={props.title} width={600} open={state.open} onCancel={handleClose} onOk={handleSubmit}>
        <Form form={form} labelCol={{ span: 8 }} layout="vertical">
          <Row gutter={15}>
            <Col span={12} hidden>
              <Form.Item label="名称" name={`id`}>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="英文编码" name={`enCode`} rules={[{ required: true, message: "请输入英文编码" }]}>
                <Input placeholder="请输入英文编码" />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="排序" name={`sort`}>
                <InputNumber />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="状态" name={`state`} valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default AppRoleEdit;
