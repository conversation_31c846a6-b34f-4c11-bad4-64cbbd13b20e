import { makeApi } from "@/utils/Api";

const PlatformPageLinkApi = {
    getPlatformPageLink: makeApi("get", `/api/platformPageLink`),
    getPlatformPageLinkInfo: makeApi("get", `/api/platformPageLink`, true),
    addPlatformPageLink: makeApi("post", `/api/platformPageLink`),
    putPlatformPageLink: makeApi("put", `/api/platformPageLink`),
    delPlatformPageLink: makeApi("delete", `/api/platformPageLink`),
    selectPlatformPageLink: makeApi("get", `/api/platformPageLink/select_list`),
};

export default PlatformPageLinkApi;
