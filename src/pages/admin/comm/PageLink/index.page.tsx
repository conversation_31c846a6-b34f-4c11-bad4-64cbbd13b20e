import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import PlatformPageLinkApi from "./api";
import PlatformPageLinkEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateMap, TypeMap } from "./types";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const PlatformPageLinkPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformPageLinkApi.delPlatformPageLink({ data: { ids: item.id } });
    message.success("删除页面链接成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => PlatformPageLinkApi.getPlatformPageLink({ params: p })}
        title={
          <Space>
            <PlatformPageLinkEdit title={"新增页面链接"} onOk={handleReload}>
              <WeTable.AddBtn />
            </PlatformPageLinkEdit>
          </Space>
        }
        search={[
          <Form.Item label="类型" name={`type`}>
            <Select options={TypeMap} placeholder="请选择类型" allowClear />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="链接地址" name={`linkUrl`}>
            <Input placeholder="请输入链接地址" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "类型", dataIndex: "type", render: (c) => <>{TypeMap.find((item) => item.value === c)?.label || "--"}</> },
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "链接地址", dataIndex: "linkUrl", render: (c) => c ? c : "--" },
          { title: "说明", dataIndex: "description", render: (c) => c ? c : "--" },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => <>{StateMap.find((item) => item.value === c)?.label || "--"}</> },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <PlatformPageLinkEdit title={`编辑页面链接`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </PlatformPageLinkEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformPageLinkPage;
