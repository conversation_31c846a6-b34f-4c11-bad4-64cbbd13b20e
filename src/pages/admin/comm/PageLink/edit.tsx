import WeModal from "@/components/WeModal/WeModal";
import PlatformPageLinkApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { InputNumber } from "antd";
import { StateMap, TypeMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformPageLinkEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformPageLinkApi.getPlatformPageLinkInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await PlatformPageLinkApi.addPlatformPageLink({ data });
      message.success("添加页面链接成功");
    }
    if (data.id) {
      await PlatformPageLinkApi.putPlatformPageLink({ data });
      message.success("修改页面链接成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col / 2}>
            <Form.Item name={`type`} label="类型" rules={[{ required: false, message: "请选择类型" }]} initialValue={1}>
              <Radio.Group options={TypeMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="页面名称" rules={[{ required: false, message: "请输入页面名称" }]}>
              <Input placeholder="请输入页面名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`linkUrl`} label="链接地址" rules={[{ required: false, message: "请输入链接地址" }]}>
              <Input placeholder="请输入链接地址" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`description`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input.TextArea placeholder="请输入说明" rows={4} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformPageLinkEdit;
