import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Input, Radio, Row, Space, message } from "antd";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { useState } from "react";

const layout = { row: 10, col: 12 };

const AccountComboPage = () => {

  const [form] = Form.useForm();

  form.resetFields();

  const [form2] = Form.useForm();

  form2.resetFields();


  const [showRecoveryCard, setShowRecoveryCard] = useState(false);

  const handleSubmit = async () => {
    const params = await form.validateFields();
    await MallApi.vipAccountCombo({ params });
    message.success("操作成功");
    form.resetFields();
  };

  const handleSubmit2 = async () => {
    const params = await form2.validateFields();
    await MallApi.vipAccountRecovery({ params });
    message.success("操作成功");
    form.resetFields();
  };

  return (
    <div>
      <Card title='账号合并' style={{ width: 700 }}>
        <Form form={form} labelCol={{ flex: "150px" }} onFinish={handleSubmit}>
          <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
            <Row gutter={layout.row}>
              <Col span={layout.col * 2}>
                <Form.Item label="">
                  <div className="grid gap-4">
                    <Alert
                      message={
                        <>
                          <p style={{ fontWeight: 'bold' }}>
                            ⚠️ 注意：账号合并功能适用于同一用户拥有多个会员账号的情况。
                          </p>
                          <p>
                            该操作会将两个账号的资料与余额进行合并，且 <span style={{ color: 'red' }}>合并后无法撤销</span>，请务必谨慎操作！
                          </p>
                          <p>
                            操作前请确认以下关键信息：
                          </p>
                          <ul>
                            <li><strong>旧手机号：</strong>指两个账号中注册时间较早的那个手机号。</li>
                            <li><strong>新手机号：</strong>指两个账号中注册时间较晚的那个手机号。</li>
                            <li><strong>基本资料保留：</strong>选择保留哪个账号的信息(如姓名、性别、生日、会员号、来源、建档时间)。</li>
                            <li><strong>最终使用号码：</strong>选择合并后继续使用的手机号码。</li>
                          </ul>
                          <p>
                            确保信息无误后再提交操作。
                          </p>
                        </>
                      }
                      type="error"
                    />
                  </div>
                </Form.Item>
              </Col>
              <Col span={layout.col * 2}>
                <Form.Item label={`旧手机号`} name={`mobileA`} rules={[{ required: true, message: '请输入手机号' }]}>
                  <Input placeholder="两个账号中注册时间较早的那个手机号" />
                </Form.Item>
              </Col>
              <Col span={layout.col * 2}>
                <Form.Item label={`新手机号`} name={`mobileB`} rules={[{ required: true, message: '请输入手机号' }]}>
                  <Input placeholder="两个账号中注册时间较晚的那个手机号" />
                </Form.Item>
              </Col>
              <Col span={layout.col * 2}>
                <Form.Item label={`基本资料保留`} name={`baseInfoRetainFor`} rules={[{ required: true, message: '请选择基本资料保留方式' },]} initialValue={"A"}>
                  <Radio.Group
                    options={[
                      { label: '保留旧手机号', value: "A" },
                      { label: '保留新手机号', value: "B" },
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col * 2}>
                <Form.Item label={`最终使用号码`} name={`mobileRetainFor`} rules={[{ required: true, message: '请选择最终使用号码' },]} initialValue={"B"}>
                  <Radio.Group
                    options={[
                      { label: '使用旧手机号', value: "A" },
                      { label: '使用新手机号', value: "B" },
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col}>
                <Form.Item style={{ marginTop: 20 }}>
                  <Button size="large" htmlType="reset" block >
                    清空
                  </Button>
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item style={{ marginTop: 20 }}>
                  <Button type="primary" size="large" htmlType="submit" block >
                    提交
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Space>
        </Form>
      </Card>
      <div style={{ height: 20 }} />

      <a
        className="ml-10px"
        onClick={() => setShowRecoveryCard(!showRecoveryCard)}
        style={{ color: 'grey' }}
      >
        {showRecoveryCard ? '隐藏账号恢复' : '操作失误？点击立即抢救'}
      </a>

      <div style={{ height: 20 }} />
      {showRecoveryCard && (
        <Card title='账号恢复' style={{ width: 700 }}>
          <Form form={form2} labelCol={{ flex: "150px" }} onFinish={handleSubmit2}>
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
              <Row gutter={layout.row}>
                <Col span={layout.col * 2}>
                  <Form.Item label="">
                    <div className="grid gap-4">
                      <Alert
                        message={
                          <>
                            <p style={{ fontWeight: 'bold' }}>
                              ⚠️ 注意：该功能仅用于恢复因账号合并而被删除的账号。
                            </p>
                            <p >
                              <ul>
                                <li><div style={{ color: 'red' }}>仅能恢复基本资料（如姓名、性别、生日、会员号、来源、建档时间等）。</div></li>
                                <li><div style={{ color: 'red' }}>余额、订单等业务数据将无法恢复。</div></li>
                              </ul>
                            </p>
                          </>
                        }
                        type="error"
                      />
                    </div>
                  </Form.Item>
                </Col>

                <Col span={layout.col * 2}>
                  <Form.Item label={`恢复手机号`} name={`recoveryMobile`} rules={[{ required: true, message: '请输入需要进行恢复的手机号' }]}>
                    <Input placeholder="请输入需要进行恢复的手机号" />
                  </Form.Item>
                </Col>

                <Col span={layout.col}>
                  <Form.Item style={{ marginTop: 20 }}>
                    <Button size="large" htmlType="reset" block >
                      清空
                    </Button>
                  </Form.Item>
                </Col>
                <Col span={layout.col}>
                  <Form.Item style={{ marginTop: 20 }}>
                    <Button type="primary" size="large" htmlType="submit" block >
                      提交
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Space>
          </Form>
        </Card>
      )}

      <div style={{ height: 50 }} />
    </div>
  );
};

export default AccountComboPage;
