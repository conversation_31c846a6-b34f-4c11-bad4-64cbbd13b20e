import WeModal from "@/components/WeModal/WeModal";
import PlatformIndexComponentTypeApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { InputNumber } from "antd";
import { useSetState } from "react-use";
import PlatformIndexComponentCategoryApi from "../IndexComponentCategory/api";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformIndexComponentTypeEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    categorys: [] as any[],
  });

  const handleOpen = async () => {
    form.resetFields();
    fetchCategorys();
    if (props.data) {
      const data = await PlatformIndexComponentTypeApi.getPlatformIndexComponentTypeInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await PlatformIndexComponentTypeApi.addPlatformIndexComponentType({ data });
      message.success("添加首页组件类型成功");
    }
    if (data.id) {
      await PlatformIndexComponentTypeApi.putPlatformIndexComponentType({ data });
      message.success("修改首页组件类型成功");
    }
    props.onOk?.();
  };

  const fetchCategorys = async () => {
    const params = { pageSize: 9999 };
    const res = await PlatformIndexComponentCategoryApi.getPlatformIndexComponentCategory({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item?.name,
      value: item?.id,
    }));
    setState({ categorys: list });;
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>

          <Col span={layout.col}>
            <Form.Item label="分类" name={`categoryId`} rules={[{ required: true, message: "请选择分类" }]}>
              <Radio.Group options={state.categorys} />
            </Form.Item>
          </Col>

          <Col span={layout.col / 2}>
            <Form.Item name={`name`} label="组件名称" rules={[{ required: true, message: "请输入组件名称" }]}>
              <Input placeholder="请输入组件名称" />
            </Form.Item>
          </Col>

          <Col span={layout.col / 2}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: true, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`description`} label="组件说明" rules={[{ required: false, message: "请输入组件说明" }]}>
              <Input.TextArea placeholder="请输入组件说明" rows={2} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`templateData`} label="数据模板" rules={[{ required: false, message: "请输入数据模板" }]}>
              <Input.TextArea placeholder="请输入数据模板" rows={8} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformIndexComponentTypeEdit;
