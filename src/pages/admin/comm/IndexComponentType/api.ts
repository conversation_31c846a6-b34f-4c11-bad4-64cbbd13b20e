import { makeApi } from "@/utils/Api";

const PlatformIndexComponentTypeApi = {
    getPlatformIndexComponentType: makeApi("get", `/api/platformIndexComponentType`),
    getPlatformIndexComponentTypeTree: makeApi("get", `/api/platformIndexComponentType/query_category_component_data`),
    getPlatformIndexComponentTypeInfo: makeApi("get", `/api/platformIndexComponentType`, true),
    addPlatformIndexComponentType: makeApi("post", `/api/platformIndexComponentType`),
    putPlatformIndexComponentType: makeApi("put", `/api/platformIndexComponentType`),
    delPlatformIndexComponentType: makeApi("delete", `/api/platformIndexComponentType`),
};

export default PlatformIndexComponentTypeApi;
