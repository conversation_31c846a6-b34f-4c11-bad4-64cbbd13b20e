import { Card, Col, Form, Input, Popconfirm, Radio, Row, Space, Tooltip, Tree, Typography, message } from "antd";
import Style from "./index.module.scss";
import WeTable, { WeTableRef } from "@/components/WeTable";
import { useMount, useSetState } from "react-use";
import { DeleteOutlined, FormOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useRef } from "react";
import PlatformIndexComponentCategoryApi from "../IndexComponentCategory/api";
import PlatformIndexComponentTypeApi from "./api";
import PlatformIndexComponentTypeEdit from "./edit";
import { StateMap } from "./types";
import PlatformIndexComponentCategoryEdit from "../IndexComponentCategory/edit";

const IndexComponentTypePage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    categorys: [] as any[],
    categoryId: null as any,
  });

  useMount(() => {
    fetchCategorys();
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchCategorys = async () => {
    const params = { pageSize: 9999 };
    const res = await PlatformIndexComponentCategoryApi.getPlatformIndexComponentCategory({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ categorys: types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item?.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await PlatformIndexComponentCategoryApi.delPlatformIndexComponentCategory({ data });
    message.success("删除分类成功");
    fetchCategorys();
  };

  const handleDelItem = async (item: any) => {
    const data = { ids: item.id };
    await PlatformIndexComponentTypeApi.delPlatformIndexComponentType({ data });
    message.success("删除组件成功");
    handleReload();
  };

  return (
    <Row wrap={false} gutter={10}>
      <Col flex={`300px`} style={{ minWidth: 0 }}>
        <Card
          bodyStyle={{ padding: 10 }}
          title={`组件分类`}
          style={{ height: "100%" }}
          extra={
            <PlatformIndexComponentCategoryEdit title={`添加分类`} onOk={() => fetchCategorys()} >
              <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
            </PlatformIndexComponentCategoryEdit>
          }
        >
          <Tree.DirectoryTree
            className={Style.tree}
            treeData={state.categorys}
            showIcon={false}
            selectedKeys={[state.categoryId]}
            onSelect={(e) => {
              const key = e[0];
              if (state.categoryId === key) {
                setState({ categoryId: "" });
              } else {
                setState({ categoryId: key });
              }
            }}
            titleRender={(item) => {
              return (
                <div className={Style.row} key={item.key}>
                  <div className={Style.title}>{item.title}</div>
                  <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                    <PlatformIndexComponentCategoryEdit title={`编辑分类`} onOk={() => fetchCategorys()} data={item}>
                      <Tooltip title="编辑分类">
                        <FormOutlined className={Style.ico} />
                      </Tooltip>
                    </PlatformIndexComponentCategoryEdit>

                    <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                      <Tooltip title="删除分类">
                        <DeleteOutlined className={Style.ico} />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                </div>
              );
            }}
          />
        </Card>
      </Col>
      <Col flex={"auto"}>
        <WeTable
          ref={tableRef}
          title={
            <PlatformIndexComponentTypeEdit title={`新增组件`} onOk={handleReload}>
              <WeTable.AddBtn children="新增组件" />
            </PlatformIndexComponentTypeEdit>
          }
          request={(params) => PlatformIndexComponentTypeApi.getPlatformIndexComponentType({ params })}
          params={{ categoryId: state.categoryId }}
          search={[
            <Form.Item label="组件名称" name={`name`}>
              <Input placeholder="请输入组件名称" />
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Radio.Group
                options={StateMap}
              />
            </Form.Item>,
          ]}
          columns={[
            { title: "组件名称", dataIndex: "name", render: (c) => c ? c : "--" },
            { title: "英文编码", dataIndex: "enCode", render: (c) => c ? c : "--" },
            { title: "说明", dataIndex: "description", render: (c) => c ? c : "--" },
            { title: "排序", dataIndex: "sort" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
            { title: "分类", dataIndex: "categoryName" },
            {
              title: "操作",
              render: (item) => (
                <Space>
                  <PlatformIndexComponentTypeEdit title={`编辑组件`} onOk={handleReload} data={item}>
                    <Typography.Link>编辑</Typography.Link>
                  </PlatformIndexComponentTypeEdit>

                  <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDelItem(item)}>
                    <Typography.Link>删除</Typography.Link>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
        />
      </Col>
    </Row>
  );
};

export default IndexComponentTypePage;
