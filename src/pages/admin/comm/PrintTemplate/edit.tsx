import WeModal from "@/components/WeModal/WeModal";
import PlatformPrintTemplateApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import RichText from "@/components/RichText";
import { useSetState } from "react-use";
import copy from "copy-to-clipboard";
import { TYPES } from "./types";

const layout = { row: 10, col: 12 };

const PlatformPrintTemplateEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  hideSubmit?: any;
}) => {
  const [form] = Form.useForm();
  // const type = Form.useWatch("type", form);
  const [state, setState] = useSetState({
    types: [] as any[],
    open: 0,
  });

  const isEdit = !!props.data?.id;

  const fetchType = async (type = 10) => {
    const params = { type };
    const res = await PlatformPrintTemplateApi.getPlatformPrintParam({ params });
    let types: any[] = res?.list || [];
    setState({ types });
  };

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformPrintTemplateApi.getPlatformPrintTemplateInfo(props.data.id);
      form.setFieldsValue(data);
      fetchType(data.type);
    } else {
      fetchType(10);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await PlatformPrintTemplateApi.addPlatformPrintTemplate({ data });
      message.success("添加打印模板成功");
    }
    if (data.id) {
      await PlatformPrintTemplateApi.putPlatformPrintTemplate({ data });
      message.success("修改打印模板成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={1400}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={24}>
            <Form.Item
              name={`type`}
              label="类型"
              rules={[{ required: true, message: "请选择类型 " }]}
              initialValue={10}
            >
              <Radio.Group
                options={TYPES.map((n) => ({ label: n.name, value: n.id }))}
                onChange={(e) => fetchType(e.target.value)}
                disabled={isEdit}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <RichText />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="参数">
              {!!state.types.length && (
                <div className="grid cols-8 gap-1px bg-#ebebeb p-1px">
                  {state.types.map((item) => (
                    <>
                      <div className="cursor-copy p-5px px-10px bg-#fafafa" onClick={() => copy(item.name)}>
                        {item.name}
                      </div>
                      <div className="cursor-copy p-5px px-10px bg-#fff" onClick={() => copy(item.value)}>
                        {item.value}
                      </div>
                    </>
                  ))}
                </div>
              )}
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformPrintTemplateEdit;
