import { makeApi } from "@/utils/Api";

const PlatformPrintTemplateApi = {
  getPlatformPrintTemplate: makeApi("get", `/api/platformPrintTemplate`),
  getPlatformPrintTemplateInfo: makeApi("get", `/api/platformPrintTemplate`, true),
  addPlatformPrintTemplate: makeApi("post", `/api/platformPrintTemplate`),
  putPlatformPrintTemplate: makeApi("put", `/api/platformPrintTemplate`),
  delPlatformPrintTemplate: makeApi("delete", `/api/platformPrintTemplate`),

  getPlatformPrintParam: makeApi("get", `/api/platformPrintParam`),
};

export default PlatformPrintTemplateApi;
