import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import PlatformPrintTemplateApi from "./api";
import PlatformPrintTemplateEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { TYPES } from "./types";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const PlatformPrintTemplatePage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformPrintTemplateApi.delPlatformPrintTemplate({ data: { ids: item.id } });
    message.success("删除打印模板成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => PlatformPrintTemplateApi.getPlatformPrintTemplate({ params: p })}
        title={
          <PlatformPrintTemplateEdit title={"新增打印模板"} onOk={handleReload}>
            <WeTable.AddBtn />
          </PlatformPrintTemplateEdit>
        }
        search={[
          <Form.Item label="类型" name={`type`}>
            <Select placeholder="请选择类型"
              options={TYPES}
              fieldNames={{ label: "name", value: "id" }}
               allowClear
            />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "类型",
            dataIndex: "type",
            render: (c) => TYPES.find((n) => n.id == c)?.name ?? "--",
          },
          { title: "名称", dataIndex: "name", render: (c) => (c ? c : "--") },
          // {
          //   title: "内容",
          //   dataIndex: "content",
          //   width: 200,
          //   render: (c) => (
          //     <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
          //       {c ? c : "--"}
          //     </Typography.Text>
          //   ),
          // },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <PlatformPrintTemplateEdit title={`编辑打印模板`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </PlatformPrintTemplateEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformPrintTemplatePage;
