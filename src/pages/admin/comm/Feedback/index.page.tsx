import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppFeedbackApi from "./api";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { Typography } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateMap } from "./types";
import AppFeedbackEdit from "./edit";

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const AppFeedbackPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  // const handleDel = async (item: any) => {
  //   await AppFeedbackApi.delAppFeedback({ data: { ids: item.id } });
  //   message.success("删除反馈成功");
  //   handleReload();
  // };
  const handleState = async (item: any, state: number) => {
    await AppFeedbackApi.putAppFeedback({ data: { id: item.id, state: state } });
    message.success("修改反馈成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppFeedbackApi.getAppFeedback({ params: p })}
        search={[
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} fieldNames={{ label: "name", value: "id" }} allowClear placeholder="请选择状态" />
          </Form.Item>,
          <Form.Item label="提交者" name={`mobile`}>
            <Input placeholder="请输入提交者" />
          </Form.Item>,
          <Form.Item label="内容" name={`content`}>
            <Input placeholder="请输入内容" />
          </Form.Item>,
          <Form.Item label="提交日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          //{ title: "用户id", dataIndex: "appUserId", render: (c) => c ? c : "--" },
          { title: "提交者", dataIndex: "mobile", render: (c) => c ? c : "--" },
          {
            title: "内容",
            dataIndex: "content",
            width: 800,
            render: (c) => (
              <Typography.Text style={{ width: 800 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          // {
          //   title: "图片",
          //   dataIndex: "image",
          //   width: 200,
          //   render: (c) => (
          //     <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
          //       {c ? c : "--"}
          //     </Typography.Text>
          //   ),
          // },
          { title: "提交日期", dataIndex: "createDate", render: (c) => fdate(c) },
          { title: "阅读日期", dataIndex: "readDate", render: (c) => fdate(c) },
          { title: "状态", dataIndex: "state", render: (c) => renderTag(StateMap, c), },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppFeedbackEdit title={`查看反馈`} data={item} onOk={handleReload} hideSubmit={true}>
                    <a>查看</a>
                  </AppFeedbackEdit>
                  <Popconfirm title={`设为已读`} onConfirm={() => handleState(item, 1)}>
                    <Typography.Link disabled={![0].includes(item.state)}>
                      设为已读
                    </Typography.Link>
                  </Popconfirm>
                  {/* <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm> */}
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppFeedbackPage;
