import WeModal from "@/components/WeModal/WeModal";
import AppFeedbackApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 24 };

const AppFeedbackEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?:any;}) => {

  const [form] = Form.useForm();

  const handleOpen = async() => {
    form.resetFields();
    if (props.data) {
      const data = await AppFeedbackApi.getAppFeedbackInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.image = ImageUpload.deserializer(data.image);
    if (data.id) {
      await AppFeedbackApi.putAppFeedback({ data });
      message.success("修改反馈成功");
    }
    props.onOk?.();
  };

return (
  <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps ={{hidden:props.hideSubmit}} onOk={handleSubmit}>
    <Form form={form} labelCol={{ flex: "120px" }}>
      <Form.Item name={`id`} hidden>
        <Input />
      </Form.Item>
      <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`mobile`} label="手机号" rules={[{ required: false, message: "请输入手机号" }]}>
              <Input placeholder="请输入手机号" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={6} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`image`} label="图片" rules={[{ required: false, message: "请上传图片" }]}  valuePropName="fileList">
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
      </Row>
    </Form>
  </WeModal>
  );
};

export default AppFeedbackEdit;
