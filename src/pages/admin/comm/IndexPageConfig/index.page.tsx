import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import PlatformIndexPageConfigApi from "./api";
import PlatformIndexPageConfigEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, Tooltip, Typography, message } from "antd";
import dayjs from "dayjs";
import { DefaultTagMap, StateMap } from "./types";
import { QuestionCircleOutlined } from "@ant-design/icons";
import VisualEditorIndex from "../IndexPageComponent/VisualEditor.index";
import PlatformIndexPageConfigCopy from "./copy";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const PlatformIndexPageConfigPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformIndexPageConfigApi.delPlatformIndexPageConfig({ data: { ids: item.id } });
    message.success("删除首页页面配置成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => PlatformIndexPageConfigApi.getPlatformIndexPageConfig({ params: p })}
        title={
          <Space>
            <PlatformIndexPageConfigEdit title={"新增首页页面配置"} onOk={handleReload}>
              <WeTable.AddBtn />
            </PlatformIndexPageConfigEdit>
          </Space>
        }
        search={[
          <Form.Item label="页面标题" name={`name`}>
            <Input placeholder="请输入页面标题" />
          </Form.Item>,
          <Form.Item label="默认" name={`defaultTag`}>
            <Select options={DefaultTagMap} placeholder="请选择默认" allowClear />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
        ]}
        columns={[
          { title: "页面标题", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "英文编码", dataIndex: "enCode", render: (c) => c ? c : "--" },
          {
            title: "有效期",
            render: (c) => (
              <>
                {c.timeLimit == 0 && "永久"}
                {c.timeLimit == 1 &&
                  `${dayjs(c.startDate).format("YYYY-MM-DD")} 至 ${dayjs(c.endDate).format(
                    "YYYY-MM-DD"
                  )}`}
              </>
            ),
          },
          {
            title: (
              <>
                排序
                <Tooltip title="页面按照排序优先级进行展示，排序数越小。优先级越高">
                  <QuestionCircleOutlined
                    style={{
                      cursor: "pointer",
                      marginLeft: 4,
                    }}
                  />
                </Tooltip>
              </>
            ),
            dataIndex: "sort"
          },
          { title: "状态", dataIndex: "state", render: (c) => (StateMap.find((t) => t.value === c)?.label || "--") },
          {
            title: (
              <>
                兜底页面
                <Tooltip title="当无有效的配置时，将使用兜底配置">
                  <QuestionCircleOutlined
                    style={{
                      cursor: "pointer",
                      marginLeft: 4,
                    }}
                  />
                </Tooltip>
              </>
            ),
            dataIndex: "defaultTag",
            render: (c, item) => (
              <div>
                {c === 1 ? <b>✔ 兜底配置</b> :
                  <>
                    ✖ [<Typography.Link
                      onClick={async () => {
                        await PlatformIndexPageConfigApi.defaultSet({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      设为兜底
                    </Typography.Link>
                    ]
                  </>
                }
              </div>
            ),
          },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <VisualEditorIndex title={`页面装修-${item.name}`} pageConfigId={item.id} >
                    <a>页面装修</a>
                  </VisualEditorIndex>
                  <PlatformIndexPageConfigCopy title={`复制页面配置`} data={item} onOk={handleReload}>
                    <a>复制页面</a>
                  </PlatformIndexPageConfigCopy>
                  <PlatformIndexPageConfigEdit title={`编辑页面配置`} data={item} onOk={handleReload}>
                    <a>编辑页面</a>
                  </PlatformIndexPageConfigEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除页面</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformIndexPageConfigPage;
