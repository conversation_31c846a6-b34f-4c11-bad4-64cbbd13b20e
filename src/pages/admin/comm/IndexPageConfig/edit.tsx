import WeModal from "@/components/WeModal/WeModal";
import PlatformIndexPageConfigApi from "./api";
import { Col, ColorPicker, Divider, Form, Input, Radio, Row, Switch, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { StateMap } from "./types";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";

const layout = { row: 10, col: 24 };

const PlatformIndexPageConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const data = await PlatformIndexPageConfigApi.getPlatformIndexPageConfigInfo(props.data.id);
      data.limitDate = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    data.timeLimit = Number(data.timeLimit);
    const limitDate = formatDateRange(data.limitDate, "YYYY-MM-DD HH:mm:ss");
    data.startDate = limitDate.start;
    data.endDate = limitDate.end;
    delete data.limitDate;

    //处理颜色
    if (data?.configDataJson?.background_color) {
      data.configDataJson.background_color = data.configDataJson.background_color.toHexString();
    }

    if (!data.id) {
      await PlatformIndexPageConfigApi.addPlatformIndexPageConfig({ data });
      message.success("添加首页页面配置成功");
    }
    if (data.id) {
      await PlatformIndexPageConfigApi.putPlatformIndexPageConfig({ data });
      message.success("修改首页页面配置成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col / 2}>
            <Form.Item name={`name`} label="页面标题" rules={[{ required: true, message: "请输入页面标题" }]}>
              <Input placeholder="请输入页面标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: true, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: true, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="时间限制">
              <div style={{ display: "flex", alignItems: "center" }}>
                <div style={{ marginRight: 20 }}>
                  <Form.Item noStyle name={`timeLimit`} valuePropName="checked" initialValue={false}>
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </div>
                <div style={{ flex: 1 }}>
                  <Form.Item noStyle dependencies={["timeLimit"]}>
                    {(form) => {
                      const value = form.getFieldValue("timeLimit");
                      return (
                        !!value && (
                          <Form.Item
                            noStyle
                            name={`limitDate`}
                            initialValue={[]}
                            rules={[
                              {
                                required: true,
                                message: "请选择限时时间",
                              },
                            ]}
                          >
                            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} showTime />
                          </Form.Item>
                        )
                      );
                    }}
                  </Form.Item>
                </div>
              </div>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`description`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input.TextArea placeholder="请输入说明" rows={4} />
            </Form.Item>
          </Col>

          <Divider orientation="left">页面配置项</Divider>

          <Col span={layout.col / 2}>
            <Form.Item
              label="背景颜色"
              name={["configDataJson", "background_color"]}
              required
            >
              <ColorPicker showText />
            </Form.Item>
          </Col>

        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformIndexPageConfigEdit;
