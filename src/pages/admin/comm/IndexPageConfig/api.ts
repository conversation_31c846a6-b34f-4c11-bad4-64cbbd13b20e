import { makeApi } from "@/utils/Api";

const PlatformIndexPageConfigApi = {
    getPlatformIndexPageConfig: makeApi("get", `/api/platformIndexPageConfig`),
    getPlatformIndexPageConfigInfo: makeApi("get", `/api/platformIndexPageConfig`, true),
    addPlatformIndexPageConfig: makeApi("post", `/api/platformIndexPageConfig`),
    putPlatformIndexPageConfig: makeApi("put", `/api/platformIndexPageConfig`),
    delPlatformIndexPageConfig: makeApi("delete", `/api/platformIndexPageConfig`),
    defaultSet: makeApi("post", `/api/platformIndexPageConfig/set_default`),
    copyPageConfig: makeApi("post", `/api/platformIndexPageConfig/copy`),
};

export default PlatformIndexPageConfigApi;
