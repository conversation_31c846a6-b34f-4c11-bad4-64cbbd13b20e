import WeModal from "@/components/WeModal/WeModal";
import PlatformIndexComponentCategoryApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { InputNumber } from "antd";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformIndexComponentCategoryEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformIndexComponentCategoryApi.getPlatformIndexComponentCategoryInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await PlatformIndexComponentCategoryApi.addPlatformIndexComponentCategory({ data });
      message.success("添加首页组件分类成功");
    }
    if (data.id) {
      await PlatformIndexComponentCategoryApi.putPlatformIndexComponentCategory({ data });
      message.success("修改首页组件分类成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="分类名称" rules={[{ required: false, message: "请输入分类名称" }]}>
              <Input placeholder="请输入分类名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`enCode`} label="英文编码" rules={[{ required: false, message: "请输入英文编码" }]}>
              <Input placeholder="请输入英文编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformIndexComponentCategoryEdit;
