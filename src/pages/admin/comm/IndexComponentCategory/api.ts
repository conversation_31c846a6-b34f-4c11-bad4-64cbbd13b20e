import { makeApi } from "@/utils/Api";

const PlatformIndexComponentCategoryApi = {
    getPlatformIndexComponentCategory: makeApi("get", `/api/platformIndexComponentCategory`),
    getPlatformIndexComponentCategoryInfo: makeApi("get", `/api/platformIndexComponentCategory`, true),
    addPlatformIndexComponentCategory: makeApi("post", `/api/platformIndexComponentCategory`),
    putPlatformIndexComponentCategory: makeApi("put", `/api/platformIndexComponentCategory`),
    delPlatformIndexComponentCategory: makeApi("delete", `/api/platformIndexComponentCategory`),
};

export default PlatformIndexComponentCategoryApi;
