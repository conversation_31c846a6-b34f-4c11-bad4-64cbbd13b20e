import { Col, DatePicker, Form, Input, Radio, Row, Select, message } from "antd";
import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import DictPicker from "@/components/Units/DictPicker";
import { useSetState } from "react-use";
import dayjs from "dayjs";
import UploadFile from "@/components/ImageUpload/UploadFIle";

const layout = { row: 10, col: 12 };

export const PayType = [
  { id: 2, name: "微信" },
  { id: 3, name: "支付宝" },
  { id: 10, name: "现金" },
  { id: 11, name: "刷卡" },
  { id: 12, name: "扫码" },
  { id: 99, name: "其他" },
];

const CashBookEdit = (props: { children: any; title: any; data: { id?: any }; onOk?: Function }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    user: null as any,
    users: [] as any[],
  });

  
  const getUser = async () => {
    const params = { pageSize: 9999 };
    const res = await Mall<PERSON>pi.getCashBookUserSelect({ params });
    // console.log(res);
    let users: any[] = res?.list || [];
    users = users.map((n) => ({ value: n.id, label: n.name }));
    setState({ users });
  };

  const handleOpen = async () => {
    form.resetFields();
    setState({ user: null });
    getUser();
    if (props.data?.id) {
      const { ...data } = await MallApi.getCashBookItem(props.data.id);
      data.reimbursementImage = ImageUpload.serializer(data.reimbursementImage);
      data.reimbursementFile = UploadFile.toArr(data.reimbursementFile);
      data.billDate = data.billDate ? dayjs(data.billDate) : "";
      //console.log(data);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.reimbursementImage = ImageUpload.deserializer(data.reimbursementImage);
    data.reimbursementFile = UploadFile.toStr(data.reimbursementFile);
    data.billDate = dayjs(data.billDate).startOf("day").format("YYYY-MM-DD HH:mm:ss");

    // console.log(data);
    // return false;

    if (data.id) {
      await MallApi.putCashBook({ data });
      message.success("编辑账单成功");
    } else {
      await MallApi.addCashBook({ data });
      message.success("添加账单成功");
    }

    props.onOk?.();
  };



  return (
    <WeModal trigger={props.children} title={props.title} width={700} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="报销日期" name={`billDate`} rules={[{ required: true, message: "请选择报销日期" }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          
          <Col span={layout.col}>
            <Form.Item
              label="报销类型"
              name={`reimbursementTypeId`}
              rules={[{ required: true, message: "请选择报销类型" }]}
            >
              <DictPicker type={`reimbursementType`} placeholder="请选择报销类型类型" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="项目内容" name={`content`} rules={[{ required: true, message: "请输入项目内容" }]}>
              <Input.TextArea
                autoSize={{ minRows: 3 }}
                maxLength={300}
                showCount
                allowClear
                placeholder="请输入项目内容"
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="金额" name={`money`} rules={[{ required: true, message: "请输入金额" }]}>
              <Input placeholder="请输入金额" suffix="元" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="报销人" name={`reimbursementUserId`}>
              <Select options={state.users} placeholder="请选择报销人" allowClear optionFilterProp="label" />
            </Form.Item>
          </Col>
          <Col span={layout.col}></Col>
          <Col span={layout.col * 2}>
            <Form.Item label="支付方式" name={`payType`} rules={[{ required: true, message: "请选择支付方式" }]}>
              <Radio.Group options={PayType.map((n) => ({ label: n.name, value: n.id }))} />
            </Form.Item>
          </Col>
          
          
          <Col span={layout.col}></Col>
          <Col span={24}>
            <Form.Item label="报销单" name={`reimbursementImage`} valuePropName="fileList">
              <ImageUpload maxCount={5} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="附件" name={`reimbursementFile`}>
              <UploadFile maxCount={5} />
            </Form.Item>
          </Col>
          <Col span={layout.col}></Col>
          <Col span={layout.col * 2}>
            <Form.Item label="备注" name={`notes`}>
              <Input.TextArea autoSize={{ minRows: 3 }} maxLength={300} showCount allowClear placeholder="请输入备注" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default CashBookEdit;
