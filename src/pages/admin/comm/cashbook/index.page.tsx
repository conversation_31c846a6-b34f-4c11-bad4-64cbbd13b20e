import Mall<PERSON>pi from "@/services/MallApi";
import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import { Button, DatePicker, Form, Image, Input, Popconfirm, Select, Space, Typography, message } from "antd";
import DictPicker from "@/components/Units/DictPicker";
import ConsultLogEdit from "./CashBookEdit";
import { DatePresetRanges, downloadExcel } from "@/utils/Tools";
import dayjs from "dayjs";
import { useSetState } from "react-use";
import { DownloadOutlined } from "@ant-design/icons";

export const PayType = [
  { id: 2, name: "微信" },
  { id: 3, name: "支付宝" },
  { id: 10, name: "现金" },
  { id: 11, name: "刷卡" },
  { id: 12, name: "扫码" },
  { id: 99, name: "其他" },
];


const CashBookList = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    imgs: [] as any[],
    imgIdx: 0,
    imgShow: false,
    exdata: [] as any[],
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delCashBook({ data });
    message.success("删除成功");
    handleReload();
  };

  const fetchStat = async (params: any) => {
    const exdata = await MallApi.getCashBookStat({ params });
    setState({ exdata });
  };

  const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

  return (
    <>
      <Image.PreviewGroup
        preview={{
          visible: state.imgShow,
          current: 0,
          onVisibleChange: (val) => setState({ imgShow: val }),
        }}
      >
        <div style={{ display: "none" }}>
          {state.imgs.map((n, i) => (
            <Image src={n} key={i} />
          ))}
        </div>
      </Image.PreviewGroup>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        request={(params) => {
          fetchStat(params);
          return MallApi.getCashBook({ params });
        }}
        title={
          <>
            <ConsultLogEdit title={`添加账单`} onOk={handleReload} data={{}}>
              <WeTable.AddBtn />
            </ConsultLogEdit>
            <Button
              icon={<DownloadOutlined />}
              type="primary"
              onClick={() => {
                downloadExcel(`/api/platformCashbook/exportExcel`, {
                  ...tableRef.current?.getParams(),
                });
              }}
            >
              导出
            </Button>
          </>
        }
        extra={
          <>
            <div style={{ display: "flex" }}>
              {state.exdata?.map?.((item) => (
                <div style={{ margin: "0 5px", fontWeight: "bold" }}>{item.reimbursementTypeName}: {item.money} 元</div>
              ))}
            </div>
          </>
        }
        search={[
          <Form.Item label="报销日期" name={`BillDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
          <Form.Item label="报销类型" name={`reimbursementTypeId`}>
            <DictPicker type="reimbursementType" placeholder="请选择报销类型" />
          </Form.Item>,
          <Form.Item label="项目内容" name={`content`}>
            <Input placeholder="请输入项目内容" />
          </Form.Item>,
          <Form.Item label="支付方式" name={`payType`}>
            <Select
              options={PayType}
              fieldNames={{ label: "name", value: "id" }}
              placeholder="请选择支付方式"
              allowClear
            />
          </Form.Item>,
          <Form.Item label="报销人" name={`reimbursementUserName`}>
            <Input placeholder="请输入报销人" />
          </Form.Item>,
        ]}
        columns={[
          { title: "报销日期", dataIndex: "billDate", render: (c) => fdate(c) },
          { title: "报销类型", dataIndex: "reimbursementTypeName" },
          {
            title: "项目内容",
            dataIndex: "content",
            render: (c) => (
              <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                {c}
              </Typography.Text>
            ),
          },
          { title: "金额(元)", dataIndex: "money" },
          { title: "报销人", dataIndex: "reimbursementUserName" },
          {
            title: "支付方式", dataIndex: "payType", render: (c) => {
              const crt = PayType.find((n) => n.id === c);
              if (crt) return crt.name;
            }
          },
          {
            title: "报销单",
            dataIndex: "reimbursementImage",
            render: (c) => {
              const imgs: any[] = ((c || "") as string).split(",").filter((n) => n);

              return (
                <Space>
                  {imgs.map((n, i) => (
                    <a
                      href={n}
                      target="_blank"
                      key={i}
                      onClick={(e) => {
                        e.preventDefault();
                        setState({ imgs, imgIdx: i, imgShow: true });
                      }}
                    >
                      报销单{i + 1}
                    </a>
                  ))}
                </Space>
              );
            },
          },
          {
            title: "附件",
            dataIndex: "reimbursementFile",
            render: (c: string) => {
              return (
                <Space>
                  {c
                    ?.split(",")
                    .filter((n) => n)
                    .map((n, i) => (
                      <a href={n} target="_blank" key={i}>
                        附件{i + 1}
                      </a>
                    ))}
                </Space>
              );
            },
          },
          {
            title: "备注说明",
            dataIndex: "notes",
            render: (c) => (
              <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                {c}
              </Typography.Text>
            ),
          },
          { title: "登记人", dataIndex: "employeeName" },
          { title: "登记日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => (
              <Space>
                <ConsultLogEdit title={`修改账单`} data={{ id: item.id }} onOk={handleReload}>
                  <Typography.Link>修改</Typography.Link>
                </ConsultLogEdit>
                <Popconfirm title={`确定要删除这条记录吗？`} onConfirm={() => handleDel(item)}>
                  <Typography.Link>删除</Typography.Link>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
      />
    </>
  );
};

export default CashBookList;
