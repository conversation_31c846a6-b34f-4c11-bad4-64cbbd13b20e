import WeModal from "@/components/WeModal/WeModal";
import PlatformPrintParamApi from "./api";
import { Col, Form, Input, Row, Select, message } from "antd";
import { TypeMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformPrintParamEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  hideSubmit?: any;
}) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformPrintParamApi.getPlatformPrintParamInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await PlatformPrintParamApi.addPlatformPrintParam({ data });
      message.success("添加打印参数成功");
    }
    if (data.id) {
      await PlatformPrintParamApi.putPlatformPrintParam({ data });
      message.success("修改打印参数成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={600}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`type`} label="类型" rules={[{ required: false, message: "请选择类型" }]}>
              <Select placeholder="请选择类型" options={TypeMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="参数名称" rules={[{ required: false, message: "请输入参数名称" }]}>
              <Input placeholder="请输入参数名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`value`} label="参数值" rules={[{ required: false, message: "请输入参数值" }]}>
              <Input placeholder="请输入参数值" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformPrintParamEdit;
