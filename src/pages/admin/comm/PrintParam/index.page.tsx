import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import PlatformPrintParamApi from "./api";
import PlatformPrintParamEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import dayjs from "dayjs";
import { TypeMap } from "./types";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const PlatformPrintParamPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformPrintParamApi.delPlatformPrintParam({ data: { ids: item.id } });
    message.success("删除打印参数成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => PlatformPrintParamApi.getPlatformPrintParam({ params: p })}
        title={
          <PlatformPrintParamEdit title={"新增打印参数"} onOk={handleReload}>
            <WeTable.AddBtn />
          </PlatformPrintParamEdit>
        }
        search={[
          <Form.Item label="类型" name={`type`}>
            <Select placeholder="请选择类型" options={TypeMap} allowClear />
          </Form.Item>,
          <Form.Item label="参数名称" name={`name`}>
            <Input placeholder="请输入参数名称" />
          </Form.Item>,
        ]}
        columns={[
          { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((i) => i.value === c)?.label },
          { title: "参数名称", dataIndex: "name", render: (c) => (c ? c : "--") },
          { title: "参数值", dataIndex: "value" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <PlatformPrintParamEdit title={`编辑打印参数`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </PlatformPrintParamEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformPrintParamPage;
