import { makeApi } from "@/utils/Api";

const PlatformPrintParamApi = {
    getPlatformPrintParam: makeApi("get", `/api/platformPrintParam`),
    getPlatformPrintParamInfo: makeApi("get", `/api/platformPrintParam`, true),
    addPlatformPrintParam: makeApi("post", `/api/platformPrintParam`),
    putPlatformPrintParam: makeApi("put", `/api/platformPrintParam`),
    delPlatformPrintParam: makeApi("delete", `/api/platformPrintParam`),
};

export default PlatformPrintParamApi;
