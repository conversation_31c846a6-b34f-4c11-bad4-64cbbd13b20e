// BannerConfig.tsx
import React from 'react';
import { Col, Divider, Form, Input, InputNumber, Select } from 'antd';
import { PlusOutlined } from "@ant-design/icons";
import LinkImage from "../LinkImage";
import { Button } from 'antd';
import { layout } from '../VisualEditor.main';
import { BannerPointPositionOptions, BannerStyleOptions, MarginLeftRightOptions } from '../types';

interface BannerConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const BannerConfig: React.FC<BannerConfigProps> = ({ form, formChange }) => {
  return (
    <>
      <Divider orientation="left">轮播图配置项</Divider>


      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择轮播图边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择轮播图边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="图片比例"
          name={["configDataJson", "ratio"]}
          //rules={[{ required: true, message: "请选择轮播图比例" }]}
          required
        >
          <Input placeholder="请输入图片比例 如 16:9" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="轮播间隔"
          name={["configDataJson", "interval"]}
          //rules={[{ required: true, message: "请输入轮播图间隔" }]}
          required
        >
          <InputNumber min={0} placeholder="请输入轮播图间隔" style={{ width: "100%" }} addonAfter="ms" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="指示器样式"
          name={["configDataJson", "point_style"]}
          //rules={[{ required: true, message: "请选择轮播样式" }]}
          //initialValue={"default"}
          required
        >
          <Select options={BannerStyleOptions} placeholder="请选择轮播样式" allowClear />
        </Form.Item>
      </Col>

      {/* 新增轮播指示器位置配置 */}
      <Col span={layout.col / 2}>
        <Form.Item
          label="指示器位置"
          name={["configDataJson", "point_position"]}
          required
        >
          <Select
            options={BannerPointPositionOptions}
            placeholder="请选择指示器位置"
            allowClear
          />
        </Form.Item>
      </Col>

      {/* 新增轮播指示器上下偏移位置配置 */}
      <Col span={layout.col / 2}>
        <Form.Item
          label="指示器偏移"
          name={["configDataJson", "point_offset"]}
          required
        >
          <InputNumber
            min={-1000}
            placeholder="正数向上偏移，负数向下偏移"
            style={{ width: "100%" }}
            addonAfter="px"
          />
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item label="轮播图项" name={["configDataJson", "items"]}
          //</Col>rules={[{ required: true, message: "请输入轮播图项" }]}
          required
        >
          <Form.List name={["configDataJson", "items"]}>
            {(fields, { add, remove, move }) => (
              <>
                {fields.map((field, index) => {
                  return <Form.Item key={field.key} noStyle>
                    <LinkImage field={field} remove={remove} form={form} formChange={formChange} index={index} move={move} total={fields.length} />
                  </Form.Item>
                })}
                <Button type="dashed" block icon={<PlusOutlined />} onClick={() => add()} >
                  添加轮播图项
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>
      </Col>
    </>
  );
};

export default BannerConfig;