// 新增文件：DividerConfig.tsx
import React from 'react';
import { Col, ColorPicker, Divider, Form, InputNumber, Select } from 'antd';
import { layout } from '../VisualEditor.main';
import { DividerStyleOptions, LineWidthOptions, MarginLeftRightOptions } from '../types';

interface DividerConfigProps {
  form?: any;
}

const DividerConfig: React.FC<DividerConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">分割线配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择边距" }]}
          required
        //initialValue={"side"}
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="颜色"
          name={["configDataJson", "color"]}
          //rules={[{ required: true, message: "请输入颜色" }]}
          required
        //initialValue={"#e0e0e0"}
        >
          <ColorPicker size="small" showText />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="样式"
          name={["configDataJson", "style"]}
          //rules={[{ required: true, message: "请选择样式" }]}
          required
        //initialValue={"solid"}
        >
          <Select options={DividerStyleOptions} placeholder="请选择样式" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="线条粗细"
          name={["configDataJson", "line_width"]}
          required
        >
          <Select options={LineWidthOptions} placeholder="请选择线条粗细" allowClear />
        </Form.Item>
      </Col>
    </>
  );
};

export default DividerConfig;