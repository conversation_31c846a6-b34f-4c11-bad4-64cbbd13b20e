// 新增文件：ChopConfig.tsx
import React from 'react';
import { Col, Divider, Form, Input, InputNumber, Select } from 'antd';
import { layout } from '../VisualEditor.main';
import { LayoutOptions, MarginLeftRightOptions } from '../types';

interface ChopConfigProps {
  form?: any;
}

const ChopConfig: React.FC<ChopConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">砍价配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="标题"
          name={["configDataJson", "title"]}
          rules={[{ required: true, message: "请输入标题" }]}
        >
          <Input placeholder="请输入标题" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="布局方式"
          name={["configDataJson", "layout"]}
          //rules={[{ required: true, message: "请选择布局方式" }]}   
          required
        //initialValue={"scroll"}
        >
          <Select options={LayoutOptions} placeholder="请选择布局方式" allowClear />
        </Form.Item>
      </Col>



      {/* <Col span={layout.col}>
        <Form.Item
          label="活动ID"
          name={["configDataJson", "activity_ids"]}
          rules={[{ required: true, message: "请输入活动ID" }]}
        >
          <Select
            mode="tags"
            placeholder="请输入砍价活动ID，按回车确认"
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Col> */}
    </>
  );
};

export default ChopConfig;