// 新增文件：SearchBarConfig.tsx
import React from 'react';
import { Col, ColorPicker, Divider, Form, Input, InputNumber, Select } from 'antd';
import { layout } from '../VisualEditor.main';
import { BorderRadiusOptions, MarginLeftRightOptions } from '../types';

interface SearchBarConfigProps {
  form?: any;
}

const SearchBarConfig: React.FC<SearchBarConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">搜索框配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="占位文本"
          name={["configDataJson", "placeholder"]}
          //rules={[{ required: true, message: "请输入占位文本" }]}
          required
        //initialValue={"请输入关键词"}
        >
          <Input placeholder="请输入占位文本" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="背景颜色"
          name={["configDataJson", "bg_color"]}
          //rules={[{ required: true, message: "请输入背景颜色" }]}
          required
        //initialValue={"#f5f5f5"}
        >
          <ColorPicker size="small" showText />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="文字颜色"
          name={["configDataJson", "text_color"]}
          //rules={[{ required: true, message: "请输入文字颜色" }]}
          required
        //initialValue={"#999"}
        >
          <ColorPicker size="small" showText />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="边框圆角"
          name={["configDataJson", "border_radius"]}
          required
        >
          <Select options={BorderRadiusOptions} placeholder="请选择边框圆角" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="整体高度"
          name={["configDataJson", "container_height"]}
          required
        >
          <InputNumber
            min={10}
            step={0.5}
            placeholder="请输入整体高度"
            style={{ width: "100%" }}
            addonAfter="px"
            precision={2}
          />
        </Form.Item>
      </Col>
    </>
  );
};

export default SearchBarConfig;