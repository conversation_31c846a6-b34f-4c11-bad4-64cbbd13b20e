import React, { useState } from 'react';
import { Col, Divider, Form, InputNumber, Select, ColorPicker } from 'antd';
import { PlusOutlined } from "@ant-design/icons";
import LinkIcon from "../LinkIcon";
import { Button } from 'antd';
import { layout } from '../VisualEditor.main';
import {
  BorderRadiusOptions,
  BorderStyleOptions,
  IconRadiusOptions,
  ItemsPerRowOptions,
  MarginLeftRightOptions,
  NavigationLayoutOptions,
  NavigationStylePresets
} from '../types';

interface NavigationConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const NavigationConfig: React.FC<NavigationConfigProps> = ({ form, formChange }) => {
  // 添加状态管理高级设置显示/隐藏
  const [showAdvancedSettings, setShowAdvancedSettings] = useState<boolean>(false);

  const handlePresetChange = (value: string) => {
    if (value && form) {
      const preset = NavigationStylePresets.find(p => p.value === value);
      if (preset) {
        // 更新表单中的相关字段
        Object.entries(preset.config).forEach(([key, val]) => {
          form.setFieldsValue({
            configDataJson: {
              ...form.getFieldValue(['configDataJson']),
              [key]: val
            }
          });
        });
        // 触发表单变化
        formChange({
          configDataJson: {
            ...form.getFieldValue(['configDataJson']),
            ...preset.config
          }
        });
      }
    }
  };

  return (
    <>
      <Divider orientation="left">导航配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          required
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="排列方式"
          name={["configDataJson", "layout"]}
          required
        >
          <Select options={NavigationLayoutOptions} placeholder="请选择布局方式" allowClear />
        </Form.Item>
      </Col>
      <Col span={layout.col / 2}>
        <Form.Item
          label="每行数量"
          name={["configDataJson", "items_per_row"]}
          required
        >
          <Select options={ItemsPerRowOptions} placeholder="请选择每行显示数量" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <div style={{ display: 'flex' }}>
          <div className='flex-1 '>
            <Form.Item
              label="风格"
              name={["configDataJson", "style_preset"]}
              required
            >
              <Select
                style={{ width: "100%" }}
                options={NavigationStylePresets.map(preset => ({
                  label: preset.label,
                  value: preset.value
                }))}
                placeholder="请选择导航风格"
                allowClear
                onChange={handlePresetChange}
              />
            </Form.Item>
          </div>
        </div>
      </Col>

      <Col span={layout.col} className='mb-4'>
        {/* 修改: 将onClick事件从Divider移到包装的div上 */}
        <div style={{ cursor: 'pointer' }} onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}>
          <Divider orientation="left">
            <span className='text-(14px #999)'>高级设置 {showAdvancedSettings ? " (点击收起)" : " (点击展开)"}</span>
          </Divider>
        </div>
        <Form.Item noStyle>
          <Col span={layout.col}>
            <div>
              <Col span={layout.col / 2}>
                <Form.Item
                  label="边框样式"
                  name={["configDataJson", "border_style"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <Select options={BorderStyleOptions} placeholder="请选择边框样式" allowClear />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="边框颜色"
                  name={["configDataJson", "border_color"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <ColorPicker showText />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="边框圆角"
                  name={["configDataJson", "border_radius"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <Select options={BorderRadiusOptions} placeholder="请选择边框圆角" allowClear />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="背景颜色"
                  name={["configDataJson", "background_color"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <ColorPicker showText />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="单行高度"
                  name={["configDataJson", "line_height"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <InputNumber
                    min={20}
                    step={0.5}
                    placeholder="请输入单行高度"
                    style={{ width: "100%" }}
                    addonAfter="px"
                    precision={2}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="图标圆形"
                  name={["configDataJson", "icon_radius"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <Select options={IconRadiusOptions} placeholder="请选择图标圆形" allowClear />
                </Form.Item>
              </Col>


              <Col span={layout.col / 2}>
                <Form.Item
                  label="图标宽度"
                  name={["configDataJson", "icon_width"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <InputNumber
                    min={10}
                    step={1}
                    placeholder="请输入图标宽度"
                    style={{ width: "100%" }}
                    addonAfter="px"
                    precision={0}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="图标高度"
                  name={["configDataJson", "icon_height"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <InputNumber
                    min={10}
                    step={1}
                    placeholder="请输入图标高度"
                    style={{ width: "100%" }}
                    addonAfter="px"
                    precision={0}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="字体颜色"
                  name={["configDataJson", "text_color"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <ColorPicker showText />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="字体大小"
                  name={["configDataJson", "font_size"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <InputNumber
                    min={10}
                    max={30}
                    step={1}
                    placeholder="请输入字体大小"
                    style={{ width: "100%" }}
                    addonAfter="px"
                    precision={0}
                  />
                </Form.Item>
              </Col>

              <Col span={layout.col / 2}>
                <Form.Item
                  label="图标文字间距"
                  name={["configDataJson", "icon_text_gap"]}
                  required
                  hidden={!showAdvancedSettings}
                >
                  <InputNumber
                    min={0}
                    max={50}
                    step={1}
                    placeholder="请输入图标和文字间距"
                    style={{ width: "100%" }}
                    addonAfter="px"
                    precision={0}
                  />
                </Form.Item>
              </Col>
            </div>
          </Col>
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item label="导航项" name={["configDataJson", "items"]}
          required
        >
          <Form.List name={["configDataJson", "items"]}>
            {(fields, { add, remove, move }) => (
              <>
                {fields.map((field, index) => {
                  return <Form.Item key={field.key} noStyle>
                    <LinkIcon field={field} remove={remove} form={form} formChange={formChange} index={index} move={move} total={fields.length} />
                  </Form.Item>
                })}
                <Button type="dashed" block icon={<PlusOutlined />} onClick={() => add()} >
                  添加导航项
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>
      </Col>
    </>
  );
};

export default NavigationConfig;