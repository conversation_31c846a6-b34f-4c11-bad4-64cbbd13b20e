// 新增文件：PhoneCallConfig.tsx
import React from 'react';
import { Col, Divider, Form, Input, InputNumber, Select } from 'antd';
import { layout } from '../VisualEditor.main';
import { UploadV2 } from '@/components/ImageUpload/UploadV2';
import { BorderRadiusOptions, PositionOptions } from '../types';
import { useWatch } from 'antd/es/form/Form';

interface PhoneCallConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const PhoneCallConfig: React.FC<PhoneCallConfigProps> = ({ form, formChange }) => {

  // 如果没有传入form，则使用Form.useForm创建
  const [innerForm] = Form.useForm();
  const usedForm = form || innerForm;


  const onValuesChange = (changedValues: any) => {
    if (!changedValues) return;

    const configData = form.getFieldValue(['configDataJson']) || {};

    // 清空所有位置相关的值
    const updatedConfig = {
      ...configData,
      position_top: 0,
      position_bottom: 0,
      position_left: 0,
      position_right: 0
    };

    // 根据选择的位置设置默认值
    switch (changedValues) {
      case "leftTop":
        updatedConfig.position_top = 50;
        updatedConfig.position_left = 10;
        break;
      case "rightTop":
        updatedConfig.position_top = 50;
        updatedConfig.position_right = 10;
        break;
      case "leftBottom":
        updatedConfig.position_bottom = 50;
        updatedConfig.position_left = 10;
        break;
      case "rightBottom":
        updatedConfig.position_bottom = 50;
        updatedConfig.position_right = 10;
        break;
      default:
        break;
    }

    // 更新表单值
    usedForm.setFieldsValue({
      configDataJson: updatedConfig
    });

    // 创建更新后的完整表单值
    const fieldValues = usedForm.getFieldsValue();
    const fieldValues2 = {
      ...fieldValues,
      configDataJson: {
        ...fieldValues.configDataJson,
        ...updatedConfig
      }
    };

    formChange(fieldValues2);
  }


  // 修改: 将监听的字段路径调整为正确的嵌套路径
  const position = useWatch(["configDataJson", "position"], usedForm);

  return (
    <>
      <Divider orientation="left">电话配置项</Divider>

      <Col span={layout.col}>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.configDataJson?.style !== curValues.configDataJson?.style
          }
        >
          {({ getFieldValue }) => {
            const style = getFieldValue(['configDataJson', 'style']);
            if (style === 'pureText') return null;

            return (
              <Form.Item
                label="图标"
                name={["configDataJson", "icon"]}
              >
                <UploadV2 compress={false} />
              </Form.Item>
            );
          }}
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="显示位置"
          name={["configDataJson", "position"]}
          initialValue={"rightBottom"}
        >
          <Select options={PositionOptions} placeholder="请选择位置" onChange={onValuesChange} />
        </Form.Item>
      </Col>

      {position === "leftTop" && (
        <>
          <Col span={layout.col / 2} key="top">
            <Form.Item
              // 修改字段名称为 position_top
              label="靠上距离"
              name={["configDataJson", "position_top"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离顶部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="left">
            <Form.Item
              // 修改字段名称为 position_left
              label="靠左距离"
              name={["configDataJson", "position_left"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离左侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "rightTop" && (
        <>
          <Col span={layout.col / 2} key="top">
            <Form.Item
              label="靠上距离"
              name={["configDataJson", "position_top"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离顶部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="right">
            <Form.Item
              label="靠右距离"
              name={["configDataJson", "position_right"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离右侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "leftBottom" && (
        <>
          <Col span={layout.col / 2} key="bottom">
            <Form.Item
              label="靠下距离"
              name={["configDataJson", "position_bottom"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离底部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="left">
            <Form.Item
              label="靠左距离"
              name={["configDataJson", "position_left"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离左侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "rightBottom" && (
        <>
          <Col span={layout.col / 2} key="bottom">
            <Form.Item
              label="靠下距离"
              name={["configDataJson", "position_bottom"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离底部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="right">
            <Form.Item
              label="靠右距离"
              name={["configDataJson", "position_right"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离右侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      <Col span={layout.col / 2}>
        <Form.Item
          label="容器宽度"
          name={["configDataJson", "container_width"]}
          required
        >
          <InputNumber addonAfter="px" style={{ width: '100%' }} min={1} />
        </Form.Item>
      </Col>
      <Col span={layout.col / 2}>
        <Form.Item
          label="容器高度"
          name={["configDataJson", "container_height"]}
          required
        >
          <InputNumber addonAfter="px" style={{ width: '100%' }} min={1} />
        </Form.Item>
      </Col>


      <Col span={layout.col / 2}>
        <Form.Item
          label="电话号码"
          name={["configDataJson", "phone"]}
          //rules={[{ required: true, message: "请输入电话号码" }]}
          required
        >
          <Input placeholder="请输入电话号码" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="边框圆角"
          name={["configDataJson", "border_radius"]}
          required
        >
          <Select options={BorderRadiusOptions} placeholder="请选择边框圆角" allowClear />
        </Form.Item>
      </Col>

    </>
  );
};

export default PhoneCallConfig;