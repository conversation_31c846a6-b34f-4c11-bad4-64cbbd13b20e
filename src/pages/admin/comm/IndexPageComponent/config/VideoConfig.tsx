// 新增文件：VideoConfig.tsx
import React from 'react';
import { Col, Divider, Form, InputNumber, Radio, Select } from 'antd';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { layout } from '../VisualEditor.main';
import { MarginLeftRightOptions } from '../types';

interface VideoConfigProps {
  form?: any;
}

const VideoConfig: React.FC<VideoConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">视频配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item
          label="视频地址"
          name={["configDataJson", "video_url"]}
          //rules={[{ required: true, message: "请输入视频地址" }]}
          required
        >
          <UploadV2 mode="video" compress={false} />
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item
          label="封面图片"
          name={["configDataJson", "poster"]}
          //rules={[{ required: true, message: "请上传封面图片" }]}
          required
        >
          <UploadV2 compress={false} />
        </Form.Item>
      </Col>

      <Col span={layout.col / 3}>
        <Form.Item
          label="自动播放"
          name={["configDataJson", "autoplay"]}
          required
        >
          <Radio.Group
            options={[
              { label: "是", value: true },
              { label: "否", value: false },
            ]} />
        </Form.Item>
      </Col>
    </>
  );
};

export default VideoConfig;