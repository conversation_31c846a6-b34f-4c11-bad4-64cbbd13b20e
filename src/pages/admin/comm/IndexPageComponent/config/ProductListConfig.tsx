// 新增文件：ProductListConfig.tsx
import React from 'react';
import { Col, Divider, Form, Input, InputNumber, Select } from 'antd';
import { layout } from '../VisualEditor.main';
import { MarginLeftRightOptions, ProductListLayoutOptions } from '../types';

interface ProductListConfigProps {
  form?: any;
}

const ProductListConfig: React.FC<ProductListConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">商品列表配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="标题"
          name={["configDataJson", "title"]}
          //rules={[{ required: true, message: "请输入标题" }]}
          required
        >
          <Input placeholder="请输入标题" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="布局方式"
          name={["configDataJson", "layout"]}
          //rules={[{ required: true, message: "请选择布局方式" }]}
          required
        //initialValue={"large_single"}
        >
          <Select options={ProductListLayoutOptions} placeholder="请选择布局方式" allowClear />
        </Form.Item>
      </Col>

      {/* <Col span={layout.col / 2}>
        <Form.Item
          label="商品来源"
          name={["configDataJson", "source"]}
          rules={[{ required: true, message: "请选择商品来源" }]}
          //initialValue={"manual"}
        >
          <Select placeholder="请选择商品来源">
            <Select.Option value="manual">手动选择</Select.Option>
            <Select.Option value="tag">标签筛选</Select.Option>
            <Select.Option value="category">分类筛选</Select.Option>
          </Select>
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item
          label="商品ID"
          name={["configDataJson", "goods_ids"]}
          rules={[{ required: true, message: "请输入商品ID" }]}
        >
          <Select
            mode="tags"
            placeholder="请输入商品ID，按回车确认"
            tokenSeparators={[',']}
          />
        </Form.Item>
      </Col> */}
    </>
  );
};

export default ProductListConfig;