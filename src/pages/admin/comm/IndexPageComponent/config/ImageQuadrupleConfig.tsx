// 新增文件：ImageQuadrupleConfig.tsx
import React from 'react';
import { Col, Divider, Form, InputNumber, Select, Typography } from 'antd';
import LinkImage from "../LinkImage";
import { layout } from '../VisualEditor.main';
import { getImageQuadrupleHeight, getImageQuadrupleUnitHeight, ImageQuadrupleStyleOptions, ImageRadiusOptions, MarginLeftRightOptions } from '../types';

interface ImageQuadrupleConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const ImageQuadrupleConfig: React.FC<ImageQuadrupleConfigProps> = ({ form, formChange }) => {

  // 如果没有传入form，则使用Form.useForm创建
  const [innerForm] = Form.useForm();
  const usedForm = form || innerForm;

  const handleValuesChange = () => {
    const fieldValues = usedForm.getFieldsValue();

    if (fieldValues.configDataJson) {
      const configData = form.getFieldValue(['configDataJson']) || {};
      const mergedConfig = {
        ...configData,
        ...fieldValues.configDataJson
      };

      const margin_x = mergedConfig.margin_x;
      const style = mergedConfig.style;

      if (margin_x !== undefined || style !== undefined) {
        // 计算并设置整体高度
        const height = getImageQuadrupleHeight(style, margin_x);
        if (height > 0) {
          usedForm.setFieldsValue({
            configDataJson: {
              ...configData,
              container_height: height
            }
          });
        }

        // 触发表单的 onValuesChange 事件
        const fieldValues2 = usedForm.getFieldsValue();
        formChange(fieldValues2);
      }
    }
  };

  return (
    <>
      <Divider orientation="left">四图配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select
            options={MarginLeftRightOptions}
            placeholder="请选择左右边距"
            allowClear
            onChange={handleValuesChange}
          />
        </Form.Item>
      </Col>
      <Col span={layout.col / 2}>
        <Form.Item
          label="布局方式"
          name={["configDataJson", "style"]}
          //rules={[{ required: true, message: "请选择布局方式" }]}
          required
        //initialValue={"top2_bottom2"}
        >
          <Select
            options={ImageQuadrupleStyleOptions}
            placeholder="请选择布局方式"
            allowClear
            onChange={handleValuesChange}
          />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="整体高度"
          name={["configDataJson", "container_height"]}
          required
        >
          <InputNumber
            min={50}
            step={0.5}
            placeholder="请输入整体高度"
            style={{ width: "100%" }}
            addonAfter="px"
            precision={2}
          />
        </Form.Item>
      </Col>


      <Col span={layout.col / 2}>
        <Form.Item
          label="图片圆角"
          name={["configDataJson", "image_radius"]}
          required
        >
          <Select options={ImageRadiusOptions} placeholder="请选择图片圆角" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item label="图片项" name={["configDataJson", "items"]}
          //rules={[{ required: true, message: "请输入图片项" }]}
          required
        >
          <Form.List name={["configDataJson", "items"]}>
            {(fields, { move, remove }) => (
              <>
                {Array.from({ length: 4 }).map((_, index) => {
                  const configData = form?.getFieldValue(['configDataJson']);
                  if (!configData || !configData.style || !configData.margin_x) return null;

                  const { style, container_height } = configData;
                  const size = getImageQuadrupleUnitHeight(style, container_height, index + 1);

                  return (
                    <Form.Item key={index} noStyle>
                      <Col span={layout.col}>
                        <Typography.Text type="secondary">
                          图片{index + 1}：建议尺寸 ( {size} )
                        </Typography.Text>
                      </Col>
                      <LinkImage field={{ key: index, name: index, restField: {} }} form={form} formChange={formChange} move={move} total={fields.length} index={index} remove={remove} />
                    </Form.Item>
                  );
                })}
              </>
            )}
          </Form.List>
        </Form.Item>
      </Col>
    </>
  );
};

export default ImageQuadrupleConfig;