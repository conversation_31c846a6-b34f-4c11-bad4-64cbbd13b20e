// 新增文件：NoticeConfig.tsx
import React from 'react';
import { Col, ColorPicker, Divider, Form, Input, InputNumber, Select } from 'antd';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { layout } from '../VisualEditor.main';
import { MarginLeftRightOptions } from '../types';

interface NoticeConfigProps {
  form?: any;
}

const NoticeConfig: React.FC<NoticeConfigProps> = () => {
  return (
    <>
      <Divider orientation="left">通知配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col}>
        <Form.Item
          label="通知消息"
          name={["configDataJson", "messages"]}
          //rules={[{ required: true, message: "请输入通知消息" }]}
          required
        >
          <Input placeholder="请输入通知消息" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="背景颜色"
          name={["configDataJson", "bg_color"]}
          //rules={[{ required: true, message: "请输入背景颜色" }]}
          required
        //initialValue={"#fff8e6"}
        >
          <ColorPicker size="small" showText />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="文字颜色"
          name={["configDataJson", "text_color"]}
          //rules={[{ required: true, message: "请输入文字颜色" }]}
          required
        //initialValue={"#ff6b00"}
        >
          <ColorPicker size="small" showText />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="滚动间隔"
          name={["configDataJson", "interval"]}
          //rules={[{ required: true, message: "请输入滚动间隔" }]}
          required
        >
          <InputNumber min={0} placeholder="请输入滚动间隔" style={{ width: "100%" }} addonAfter="ms" />
        </Form.Item>
      </Col>


      <Col span={layout.col}>
        <Form.Item
          label="图标"
          name={["configDataJson", "icon"]}
          //rules={[{ required: true, message: "请输入图标" }]}
          required
        >
          <UploadV2 compress={false} />
        </Form.Item>
      </Col>
    </>
  );
};

export default NoticeConfig;