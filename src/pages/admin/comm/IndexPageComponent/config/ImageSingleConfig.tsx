// 新增文件：ImageSingleConfig.tsx
import React from 'react';
import { Col, Divider, Form, InputNumber, Select } from 'antd';
import LinkImage from "../LinkImage";
import { layout } from '../VisualEditor.main';
import { ImageRadiusOptions, MarginLeftRightOptions } from '../types';

interface ImageSingleConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const ImageSingleConfig: React.FC<ImageSingleConfigProps> = ({ form, formChange }) => {
  return (
    <>
      <Divider orientation="left">单图配置项</Divider>

      <Col span={layout.col / 2}>
        <Form.Item
          label="上边距"
          name={["configDataJson", "margin_top"]}
          //rules={[{ required: true, message: "请输入上边距 单位PX" }]}
          required
        //initialValue={10}
        >
          <InputNumber min={-1000} placeholder="请输入上边距 单位PX" style={{ width: "100%" }} addonAfter="px" />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="左右边距"
          name={["configDataJson", "margin_x"]}
          //rules={[{ required: true, message: "请选择左右边距" }]}
          required
        >
          <Select options={MarginLeftRightOptions} placeholder="请选择左右边距" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="图片圆角"
          name={["configDataJson", "image_radius"]}
          required
        >
          <Select options={ImageRadiusOptions} placeholder="请选择图片圆角" allowClear />
        </Form.Item>
      </Col>

      <Form.Item label="图片项" name={["configDataJson", "items"]}
        //</> rules={[{ required: true, message: "请输入图片项" }]}
        required
      >
        <Form.List name={["configDataJson", "items"]}>
          {() => (
            <>
              {Array.from({ length: 1 }).map((_, index) => (
                <Form.Item key={index} noStyle>
                  <LinkImage field={{ key: index, name: index, restField: {} }} form={form} formChange={formChange} />
                </Form.Item>
              ))}
            </>
          )}
        </Form.List>
      </Form.Item>
    </>
  );
};

export default ImageSingleConfig;