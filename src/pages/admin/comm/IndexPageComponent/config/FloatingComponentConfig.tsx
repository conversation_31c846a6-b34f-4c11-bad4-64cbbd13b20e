// 新增文件：FloatingComponentConfig.tsx
import React, { useState, useEffect } from 'react';
import { Col, Divider, Form, Select, InputNumber, Input } from 'antd';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { layout } from '../VisualEditor.main';
import { BorderRadiusOptions, LinkTypeOptions, PositionOptions } from '../types';
import { useWatch } from 'antd/es/form/Form';
import LinkSelectorModal from '../LinkSelectorModal';

interface FloatingComponentConfigProps {
  form?: any;
  formChange: (changedValues: any) => void;
}

const FloatingComponentConfig: React.FC<FloatingComponentConfigProps> = ({ form, formChange }) => {

  // 如果没有传入form，则使用Form.useForm创建
  const [innerForm] = Form.useForm();
  const usedForm = form || innerForm;

  // 添加状态管理链接类型和链接选择面板
  const [linkType, setLinkType] = useState<string | undefined>(undefined);
  const [_, setLinkName] = useState<string | undefined>(undefined); // 添加链接名称状态
  const [linkValue, setLinkValue] = useState<string | undefined>(undefined); // 添加链接值状态
  const [isModalVisible, setIsModalVisible] = useState(false);

  // 添加useEffect来初始化linkType值
  useEffect(() => {
    const initLinkType = async () => {
      try {
        const currentLinkType = usedForm.getFieldValue(['configDataJson', 'link_type']);
        const currentLinkName = usedForm.getFieldValue(['configDataJson', 'link_name']); // 获取链接名称
        const currentLinkValue = usedForm.getFieldValue(['configDataJson', 'link_value']); // 获取链接值

        if (currentLinkType !== undefined) {
          setLinkType(currentLinkType);
          setLinkName(currentLinkName); // 设置链接名称
          setLinkValue(currentLinkValue); // 设置链接值
        }
      } catch (error) {
        console.error('Failed to get initial link type:', error);
      }
    };

    initLinkType();
  }, [usedForm]);

  const onValuesChange = (changedValues: any) => {
    if (!changedValues) return;

    const configData = form.getFieldValue(['configDataJson']) || {};

    // 清空所有位置相关的值
    const updatedConfig = {
      ...configData,
      position_top: 'auto',
      position_bottom: 'auto',
      position_left: 'auto',
      position_right: 'auto'
    };

    // 根据选择的位置设置默认值
    switch (changedValues) {
      case "leftTop":
        updatedConfig.position_top = 50;
        updatedConfig.position_left = 10;
        break;
      case "rightTop":
        updatedConfig.position_top = 50;
        updatedConfig.position_right = 10;
        break;
      case "leftBottom":
        updatedConfig.position_bottom = 50;
        updatedConfig.position_left = 10;
        break;
      case "rightBottom":
        updatedConfig.position_bottom = 50;
        updatedConfig.position_right = 10;
        break;
      default:
        break;
    }

    // 更新表单值
    usedForm.setFieldsValue({
      configDataJson: updatedConfig
    });

    // 创建更新后的完整表单值
    const fieldValues = usedForm.getFieldsValue();
    const fieldValues2 = {
      ...fieldValues,
      configDataJson: {
        ...fieldValues.configDataJson,
        ...updatedConfig
      }
    };

    formChange(fieldValues2);
  }

  // 修改: 将监听的字段路径调整为正确的嵌套路径
  const position = useWatch(["configDataJson", "position"], usedForm);

  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleOk = (selectedLink: string) => {
    // 解析链接名称和链接值（假设格式为 "名称|链接"）
    const [linkNameTemp, linkValueTemp] = selectedLink.split('|');

    usedForm.setFieldsValue({
      configDataJson: {
        ...usedForm.getFieldValue(['configDataJson']),
        link_value: linkValueTemp || selectedLink,
        link_name: linkNameTemp || '', // 设置链接名称
        phone_number: '' // 清空电话号码
      }
    });

    setLinkName(linkNameTemp || ''); // 更新链接名称状态
    setLinkValue(linkValueTemp || ''); // 更新链接值状态

    // 触发表单的 onValuesChange 事件
    const fieldValues = usedForm.getFieldsValue();
    formChange(fieldValues);

    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <>
      <Divider orientation="left">悬浮组件配置项</Divider>

      <Col span={layout.col}>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, curValues) =>
            prevValues.configDataJson?.style !== curValues.configDataJson?.style
          }
        >
          {({ getFieldValue }) => {
            const style = getFieldValue(['configDataJson', 'style']);
            if (style === 'pureText') return null;

            return (
              <Form.Item
                label="图标"
                name={["configDataJson", "icon"]}
                required
              >
                <UploadV2 compress={false} />
              </Form.Item>
            );
          }}
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="显示位置"
          name={["configDataJson", "position"]}
          initialValue={"rightBottom"}
          required
        >
          <Select options={PositionOptions} placeholder="请选择位置" onChange={onValuesChange} />
        </Form.Item>
      </Col>

      {position === "leftTop" && (
        <>
          <Col span={layout.col / 2} key="top">
            <Form.Item
              // 修改字段名称为 position_top
              label="靠上距离"
              name={["configDataJson", "position_top"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离顶部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="left">
            <Form.Item
              // 修改字段名称为 position_left
              label="靠左距离"
              name={["configDataJson", "position_left"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离左侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "rightTop" && (
        <>
          <Col span={layout.col / 2} key="top">
            <Form.Item
              label="靠上距离"
              name={["configDataJson", "position_top"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离顶部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="right">
            <Form.Item
              label="靠右距离"
              name={["configDataJson", "position_right"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离右侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "leftBottom" && (
        <>
          <Col span={layout.col / 2} key="bottom">
            <Form.Item
              label="靠下距离"
              name={["configDataJson", "position_bottom"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离底部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="left">
            <Form.Item
              label="靠左距离"
              name={["configDataJson", "position_left"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离左侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      {position === "rightBottom" && (
        <>
          <Col span={layout.col / 2} key="bottom">
            <Form.Item
              label="靠下距离"
              name={["configDataJson", "position_bottom"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离底部距离" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2} key="right">
            <Form.Item
              label="靠右距离"
              name={["configDataJson", "position_right"]}
              required
            >
              <InputNumber addonAfter="px" style={{ width: '100%' }} min={0} placeholder="请输入距离右侧距离" />
            </Form.Item>
          </Col>
        </>
      )}

      <Col span={layout.col / 2}>
        <Form.Item
          label="容器宽度"
          name={["configDataJson", "container_width"]}
          required
        >
          <InputNumber addonAfter="px" style={{ width: '100%' }} min={1} />
        </Form.Item>
      </Col>
      <Col span={layout.col / 2}>
        <Form.Item
          label="容器高度"
          name={["configDataJson", "container_height"]}
          required
        >
          <InputNumber addonAfter="px" style={{ width: '100%' }} min={1} />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="边框圆角"
          name={["configDataJson", "border_radius"]}
          required
        >
          <Select options={BorderRadiusOptions} placeholder="请选择边框圆角" allowClear />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="链接类型"
          name={["configDataJson", "link_type"]}
          initialValue="none"
        >
          <Select
            placeholder="请选择链接类型"
            onChange={(value) => {
              setLinkType(value as string);
              setLinkName('');
              setLinkValue('');
              // 清空链接名称和链接值
              usedForm.setFieldsValue({
                configDataJson: {
                  ...usedForm.getFieldValue(['configDataJson']),
                  link_name: '',
                  link_value: '',
                  phone_number: ''
                }
              });
              // 触发表单的 onValuesChange 事件
              const fieldValues = usedForm.getFieldsValue();
              formChange(fieldValues);
            }}
            options={LinkTypeOptions}
          />
        </Form.Item>
      </Col>

      <Col span={layout.col / 2}>
        <Form.Item
          label="链接名称"
          name={["configDataJson", "link_name"]}
        >
          <Input
            placeholder="请选择链接获取名称"
            disabled
            addonAfter={
              <a
                onClick={() => linkType !== 'none' && showModal()}
                style={{ color: linkType === 'none' ? '#ccc' : undefined, cursor: linkType === 'none' ? 'not-allowed' : 'pointer' }}
              >
                选择
              </a>
            }
          />
        </Form.Item>
      </Col>
      <Col span={layout.col}>
        <Form.Item
          label="链接值"
          name={["configDataJson", "link_value"]}
          hidden
        >
          <Input
            placeholder={linkType === 'none' ? '无需填写' : "请输入链接值"}
            disabled={linkType === 'none'}
          />
        </Form.Item>
      </Col>

      {linkValue && linkValue.startsWith('CallPhone') && (
        <Col span={layout.col / 2}>
          <Form.Item
            label="电话号码"
            name={["configDataJson", "phone_number"]}
            rules={[{ required: true, message: '请输入电话号码' }]}
          >
            <Input placeholder="请输入电话号码" />
          </Form.Item>
        </Col>
      )}

      <LinkSelectorModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onOk={handleOk}
        linkType={linkType} // 传递linkType参数
      />
    </>
  );
};

export default FloatingComponentConfig;