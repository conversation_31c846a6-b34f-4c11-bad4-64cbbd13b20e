import React from 'react';

interface VideoVisualProps {
  configDataJson: any;
}

const VideoVisual: React.FC<VideoVisualProps> = ({ configDataJson }) => {
  const { video_url, poster, autoplay } = configDataJson || {};


  const videoUrls = video_url || "";
  const posters = poster || "";

  return (
    <div style={{ position: 'relative', width: '100%', height: '200px' }}>
      {videoUrls.length > 0 ? (
        <video
          src={videoUrls}
          poster={posters}
          autoPlay={autoplay}
          controls
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      ) : posters.length > 0 ? (
        <img
          src={posters}
          alt=""
          style={{ width: '100%', height: '100%', objectFit: 'cover' }}
        />
      ) : (
        <div style={{
          width: '100%',
          height: '100%',
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          请添加视频
        </div>
      )}
    </div>
  );
};

export default VideoVisual;