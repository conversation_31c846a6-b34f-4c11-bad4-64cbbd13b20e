import React from 'react';
import { Input } from 'antd';

interface SearchBarVisualProps {
  configDataJson: any;
}

const SearchBarVisual: React.FC<SearchBarVisualProps> = ({ configDataJson }) => {
  const { container_height, placeholder, bg_color, text_color, border_radius } = configDataJson || {};

  const height = container_height || 40;

  return (
    <div style={{ overflow: 'hidden' }}>
      <Input
        style={{
          height: `${height}px`,
          backgroundColor: bg_color || '#f5f5f5',
          color: text_color || '#999',
          borderRadius: border_radius === 'round' ? '5px' : '0px',
          border: 'none'
        }}
        placeholder={placeholder || "请输入关键词"}
      />
    </div>
  );
};

export default SearchBarVisual;