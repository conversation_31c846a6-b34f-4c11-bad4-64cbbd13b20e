import React, { useState, useRef } from 'react';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { Row, Col, Button } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';

interface NavigationVisualProps {
  configDataJson: any;
}

const NavigationVisual: React.FC<NavigationVisualProps> = ({ configDataJson }) => {
  const { 
    layout, 
    items_per_row, 
    margin_top, 
    items = [], 
    border_style, 
    border_color, 
    border_radius, 
    background_color, 
    line_height,
    icon_radius,
    icon_width,
    icon_height,
    text_color,
    font_size,
    icon_text_gap
  } = configDataJson || {};
  
  const [currentPage, setCurrentPage] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [translateX, setTranslateX] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const getContainerStyle = () => {

    const baseStyle: React.CSSProperties = {
      //marginTop: `${margin_top || 0}px`,
      border: border_style !== 'none' ? `1px ${border_style || 'solid'} ${border_color || '#e0f0f0'}` : 'none',
      borderRadius: border_radius === 'round' ? '8px' : '0px',
      // 当边距为负数时，允许覆盖其他组件
      position: (margin_top < 0) ? 'relative' : 'static',
      zIndex: (margin_top < 0) ? 1 : 'auto',
      // 背景透明设置
      backgroundColor: background_color || '#ffffff',
      overflow: 'hidden'
    };

    return baseStyle;
  };

  // 修改: 正确计算总页数，确保当items.length等于items_per_row时也计算为1页
  const totalPages = Math.ceil(items.length / items_per_row);
  // 添加: 判断是否需要显示分页控件
  const needPagination = layout === 'single' && items.length > items_per_row;

  const handlePrev = () => {
    setCurrentPage(prev => (prev === 0 ? totalPages - 1 : prev - 1));
  };

  const handleNext = () => {
    setCurrentPage(prev => (prev === totalPages - 1 ? 0 : prev + 1));
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true);
    setStartX(e.touches[0].clientX - translateX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return;
    const currentX = e.touches[0].clientX;
    const newTranslateX = currentX - startX;
    setTranslateX(newTranslateX);
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    setIsDragging(false);
    
    // 判断滑动距离决定是否切换页面
    if (Math.abs(translateX) > 50) {
      if (translateX > 0) {
        handlePrev();
      } else {
        handleNext();
      }
    }
    setTranslateX(0);
  };

  // 获取导航项的样式，包括高度设置和垂直居中
  const getItemStyle = () => {
    return {
      display: 'flex',
      flexDirection: 'column' as const,
      alignItems: 'center',
      justifyContent: 'center', // 添加垂直居中
      padding: '10px 0',
      height: line_height ? `${line_height}px` : 'auto'
    };
  };

  // 获取图片样式
  const getIconStyle = () => {
    return {
      width: icon_width ? `${icon_width}px` : '40px',
      height: icon_height ? `${icon_height}px` : '40px',
      marginBottom: icon_text_gap !== undefined ? `${icon_text_gap}px` : '5px',
      objectFit: 'contain' as const,
      borderRadius: icon_radius === 'round' ? '50%' : '0px'
    };
  };

  // 获取文字样式
  const getTextStyle = () => {
    return {
      fontSize: font_size ? `${font_size}px` : '12px',
      color: text_color || 'inherit'
    };
  };

  return (
    <div style={getContainerStyle()}>
      {items && items.length > 0 ? (
        <div style={{ position: 'relative' }}>
          {needPagination && (
            <>
              <Button 
                type="text" 
                icon={<LeftOutlined />} 
                onClick={handlePrev}
                style={{ 
                  position: 'absolute', 
                  left: '10px', 
                  top: '50%', 
                  transform: 'translateY(-50%)', 
                  zIndex: 2,
                  width: '30px',
                  height: '30px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
              <Button 
                type="text" 
                icon={<RightOutlined />} 
                onClick={handleNext}
                style={{ 
                  position: 'absolute', 
                  right: '10px', 
                  top: '50%', 
                  transform: 'translateY(-50%)', 
                  zIndex: 2,
                  width: '30px',
                  height: '30px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              />
            </>
          )}
          <div 
            ref={containerRef}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={{ 
              transform: `translateX(${translateX}px)`,
              transition: isDragging ? 'none' : 'transform 0.3s ease',
              display: 'flex',
              // 修改: 使用needPagination变量来判断是否需要留出按钮空间
              paddingLeft: needPagination ? '40px' : '0',
              paddingRight: needPagination ? '40px' : '0'
            }}
          >
            <Row gutter={8} style={{ flex: 1 }}>
              {layout === 'single' ? (
                // 单行显示 - 跑马灯轮播
                <>
                  {(needPagination 
                    ? items.slice(currentPage * items_per_row, (currentPage + 1) * items_per_row) 
                    : items
                  ).map((item: any, index: number) => {
                    const icon = item?.icon ? UploadV2.str2arr(item.icon) : [];
                    return (
                      <Col span={24 / items_per_row} key={index}>
                        <div style={getItemStyle()}>
                          {icon.length > 0 ? (
                            <img
                              src={icon[0]?.url}
                              alt=""
                              style={getIconStyle()}
                            />
                          ) : (
                            <div style={{
                              ...getIconStyle(),
                              background: '#f5f5f5',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: '#999',
                              fontSize: '12px'
                            }}>
                              图标
                            </div>
                          )}
                          <span style={getTextStyle()}>{item?.text || '导航项'}</span>
                        </div>
                      </Col>
                    );
                  })}
                </>
              ) : (
                // 多行显示 - 换行显示
                <>
                  {items.map((item: any, index: number) => {
                    const icon = item?.icon ? UploadV2.str2arr(item.icon) : [];
                    return (
                      <Col span={24 / items_per_row} key={index}>
                        <div style={getItemStyle()}>
                          {icon.length > 0 ? (
                            <img
                              src={icon[0]?.url}
                              alt=""
                              style={getIconStyle()}
                            />
                          ) : (
                            <div style={{
                              ...getIconStyle(),
                              background: '#f5f5f5',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: '#999',
                              fontSize: '12px'
                            }}>
                              图标
                            </div>
                          )}
                          <span style={getTextStyle()}>{item?.text || '导航项'}</span>
                        </div>
                      </Col>
                    );
                  })}
                </>
              )}
            </Row>
          </div>
        </div>
      ) : (
        <div style={{
          height: line_height ? `${line_height}px` : '80px',
          background: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#999',
          fontSize: '12px'
        }}>
          请添加导航项
        </div>
      )}
    </div>
  );
};

export default NavigationVisual;