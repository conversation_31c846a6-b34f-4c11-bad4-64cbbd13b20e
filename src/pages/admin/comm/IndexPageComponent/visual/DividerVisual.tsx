import React from 'react';

interface DividerVisualProps {
  configDataJson: any;
}

const DividerVisual: React.FC<DividerVisualProps> = ({ configDataJson }) => {
  const { color, style, line_width } = configDataJson || {};

  const lineWidth = line_width || 1;

  return (
    <div>
      <div style={
        {
          borderTop: `${lineWidth || 1}px ${style || 'solid'} ${color || '#e0e0e0'}`,
        }
      } />
    </div>
  );
};

export default DividerVisual;