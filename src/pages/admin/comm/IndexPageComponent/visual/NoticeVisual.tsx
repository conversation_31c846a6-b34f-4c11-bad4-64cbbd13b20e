import React from 'react';
interface NoticeVisualProps {
  configDataJson: any;
}

const NoticeVisual: React.FC<NoticeVisualProps> = ({ configDataJson }) => {
  const { bg_color, text_color, icon, messages } = configDataJson || {};

  const icons = icon || "";

  const getContainerStyle = () => {
    const baseStyle: React.CSSProperties = {
      background: bg_color || '#fff8e6',
      padding: '10px',
      display: 'flex',
      alignItems: 'center',
    };

    return baseStyle;
  };

  const textStyle = {
    color: text_color || '#ff6b00',
    fontSize: '14px',
    marginLeft: '5px'
  };

  return (
    <div style={getContainerStyle()}>
      {icons.length > 0 ? (
        <img
          src={icons}
          alt=""
          style={{ width: '16px', height: '16px' }}
        />
      ) : (
        <div style={{
          width: '16px',
          height: '16px',
          background: '#f5f5f5'
        }} />
      )}
      <span style={textStyle}>{messages || '通知消息'}</span>
    </div>
  );
};

export default NoticeVisual;