import React from 'react';

interface CustomerServiceVisualProps {
  configDataJson: any;
}
const CustomerServiceVisual: React.FC<CustomerServiceVisualProps> = ({ configDataJson }) => {
  const { icon, container_width, container_height, border_radius } = configDataJson || {};

  const icons = icon || "";

  const getBorderRadiusStyle = () => {
    return border_radius === 'round' ? { borderRadius: '5px', overflow: 'hidden' } : {};
  };
  // 根据样式类型渲染不同组件
  const renderContent = () => {
    // 使用配置的容器宽高，如果没有配置则使用默认值
    const containerWidth = container_width ? `${container_width}px` : '50px';
    const containerHeight = container_height ? `${container_height}px` : '50px';

    // 默认样式（纯图）
    if (icons.length > 0) {
      return (
        <div className='w-full h-full bg-contain bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${icons})` }} />
      );
    } else {
      return (
        <div
          style={{
            width: containerWidth,
            height: containerHeight,
            backgroundImage: `url(${icons})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            textAlign: 'center',
          }}
        >
          <div>联系客服</div>
        </div>
      );
    }
  };

  // 添加容器样式，应用宽高设置
  const containerStyle: React.CSSProperties = {
    width: container_width ? `${container_width}px` : '50px',
    height: container_height ? `${container_height}px` : '50px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  };

  return (
    <div style={containerStyle}>
      {renderContent()}
    </div>
  );
};

export default CustomerServiceVisual;
