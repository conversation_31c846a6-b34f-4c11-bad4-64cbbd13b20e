import React from 'react';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

interface ImageDoubleVisualProps {
  configDataJson: any;
}

const ImageDoubleVisual: React.FC<ImageDoubleVisualProps> = ({ configDataJson }) => {
  const { style, items = [], image_radius, container_height } = configDataJson || {};
  const item1 = items[0] || {};
  const item2 = items[1] || {};

  const image1 = item1?.image ? UploadV2.str2arr(item1.image) : [];
  const image2 = item2?.image ? UploadV2.str2arr(item2.image) : [];


  const getBorderRadiusStyle = () => {
    return image_radius === 'round' ? { borderRadius: '10px', overflow: 'hidden' } : {};
  };

  // 添加容器高度样式
  const getContainerStyle = () => {
    return container_height ? { height: `${container_height}px` } : {};
  };

  const renderImagePlaceholder = (text: string) => (
    <div className='flex-1 size-full bg-cover bg-center flex items-center justify-center' 
         style={{ ...getBorderRadiusStyle(), backgroundColor: "#f5f5f5" }}>
      <span>{text}</span>
    </div>
  );

  const renderLayout = () => {
    switch (style) {
      case 'horizontal':
        return (
          <div className='flex gap-10px' style={{ ...getContainerStyle() }}>
            {image1.length === 0 ? (
              renderImagePlaceholder('图1')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
            )}
            {image2.length === 0 ? (
              renderImagePlaceholder('图2')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
            )}
          </div>
        );

      case 'vertical':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            {image1.length === 0 ? (
              renderImagePlaceholder('图1')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
            )}
            {image2.length === 0 ? (
              renderImagePlaceholder('图2')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{
      width: '100%',
      height: '100%',
      //backgroundColor: '#fff',
      overflow: 'hidden'
    }}>
      {renderLayout()}
    </div>
  );
};

export default ImageDoubleVisual;
