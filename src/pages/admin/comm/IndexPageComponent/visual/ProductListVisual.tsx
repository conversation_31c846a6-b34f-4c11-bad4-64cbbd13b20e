import React from 'react';
import { Row, Col, Card } from 'antd';

interface ProductListVisualProps {
  configDataJson: any;
}

const ProductListVisual: React.FC<ProductListVisualProps> = ({ configDataJson }) => {
  const { title, layout, } = configDataJson || {};

  // 根据布局方式确定列数
  const getColumnSpan = () => {
    switch (layout) {
      case 'large_single':
        return 24;
      case 'small_double':
        return 12;
      case 'detail_list':
        return 24;
      default:
        return 12;
    }
  };

  const span = getColumnSpan();

  return (
    <div style={{ padding: '10px' }}>
      {title && (
        <div style={{
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '10px'
        }}>
          {title}
        </div>
      )}
      <Row gutter={8}>
        {[1, 2, 3, 4].map((item) => (
          <Col span={span} key={item} style={{ marginBottom: '8px' }}>
            {layout === 'detail_list' ? (
              <Card size="small">
                <Row>
                  <Col span={8}>
                    <div style={{
                      height: '80px',
                      background: '#f5f5f5'
                    }} />
                  </Col>
                  <Col span={16} style={{ paddingLeft: '10px' }}>
                    <div style={{
                      height: '20px',
                      background: '#f5f5f5',
                      marginBottom: '10px'
                    }} />
                    <div style={{
                      height: '20px',
                      background: '#f5f5f5',
                      width: '60%'
                    }} />
                  </Col>
                </Row>
              </Card>
            ) : (
              <Card size="small" cover={
                <div style={{
                  height: layout === 'large_single' ? '150px' : '100px',
                  background: '#f5f5f5'
                }} />
              }>
                <Card.Meta
                  title={<div style={{
                    height: '20px',
                    background: '#f5f5f5'
                  }} />}
                  description={<div style={{
                    height: '20px',
                    background: '#f5f5f5',
                    width: '60%',
                    marginTop: '5px'
                  }} />}
                />
              </Card>
            )}
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default ProductListVisual;