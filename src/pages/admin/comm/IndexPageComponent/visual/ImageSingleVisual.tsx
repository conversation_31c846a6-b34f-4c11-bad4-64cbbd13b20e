import React from 'react';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

interface ImageSingleVisualProps {
  configDataJson: any;
}

const ImageSingleVisual: React.FC<ImageSingleVisualProps> = ({ configDataJson }) => {
  const { items = [], image_radius } = configDataJson || {};
  const item1 = items[0] || {};

  const image1 = item1?.image ? UploadV2.str2arr(item1.image) : [];

  const renderImagePlaceholder = (height?: any, index?: number) => (
    <div style={{
      height: height || '100px',
      background: '#f5f5f5',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }}>
      <span>请添加图 {index}</span>
    </div>
  );

  const getBorderRadiusStyle = () => {
    return image_radius === 'round' ? { borderRadius: '10px', overflow: 'hidden' } : {};
  };

  const renderLayout = () => {
    return (
      <div style={{
        width: '100%',
        height: '100%',
      }}>
        <div style={{ ...getBorderRadiusStyle() }}>
          {image1.length > 0 ? (
            <img src={image1[0]?.url} alt="first" style={{ width: '100%', height: '100%', objectFit: 'cover', ...getBorderRadiusStyle() }} />
          ) : renderImagePlaceholder(null, 1)}
        </div>
      </div>
    );
  };

  return (
    <div style={{
      width: '100%',
      height: '100%',
      //backgroundColor: '#fff',
      overflow: 'hidden'
    }}>
      {renderLayout()}
    </div>
  );
};

export default ImageSingleVisual;