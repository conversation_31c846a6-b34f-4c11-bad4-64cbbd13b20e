import React from 'react';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

interface ImageQuadrupleVisualProps {
  configDataJson: any;
}

const ImageQuadrupleVisual: React.FC<ImageQuadrupleVisualProps> = ({ configDataJson }) => {
  const { style, items = [], image_radius, container_height } = configDataJson || {};
  const item1 = items[0] || {};
  const item2 = items[1] || {};
  const item3 = items[2] || {};
  const item4 = items[3] || {};

  const image1 = item1?.image ? UploadV2.str2arr(item1.image) : [];
  const image2 = item2?.image ? UploadV2.str2arr(item2.image) : [];
  const image3 = item3?.image ? UploadV2.str2arr(item3.image) : [];
  const image4 = item4?.image ? UploadV2.str2arr(item4.image) : [];

  const renderImagePlaceholder = (text: string) => (
    <div className='flex-1 size-full bg-cover bg-center flex items-center justify-center' 
         style={{ backgroundColor: "#f5f5f5" }}>
      <span>{text}</span>
    </div>
  );

  const getBorderRadiusStyle = () => {
    return image_radius === 'round' ? { borderRadius: '10px', overflow: 'hidden' } : {};
  };

  // 添加容器高度样式
  const getContainerStyle = () => {
    return container_height ? { height: `${container_height}px` } : {};
  };

  const renderLayout = () => {
    switch (style) {
      case 'top2_bottom2':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1 flex gap-10px'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1 flex gap-10px'>
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'top2_bottom22':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1 flex gap-10px'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1 flex gap-10px'>
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'top1_bottom3':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='size-full bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1 flex gap-10px'>
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'top3_bottom1':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1 flex gap-10px'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1'>
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='size-full bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'left1_right3':
        return (
          <div className='flex gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='size-full bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1 flex flex-col gap-10px'>
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'left3_right1':
        return (
          <div className='flex gap-10px' style={{ ...getContainerStyle() }}>
            <div className='flex-1 flex flex-col gap-10px'>
              {image1.length === 0 ? (
                renderImagePlaceholder('图1')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
              )}
              {image2.length === 0 ? (
                renderImagePlaceholder('图2')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
              )}
              {image3.length === 0 ? (
                renderImagePlaceholder('图3')
              ) : (
                <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
              )}
            </div>
            <div className='flex-1'>
              {image4.length === 0 ? (
                renderImagePlaceholder('图4')
              ) : (
                <div className='size-full bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
              )}
            </div>
          </div>
        );

      case 'horizontal':
        return (
          <div className='flex gap-10px' style={{ ...getContainerStyle() }}>
            {image1.length === 0 ? (
              renderImagePlaceholder('图1')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
            )}
            {image2.length === 0 ? (
              renderImagePlaceholder('图2')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
            )}
            {image3.length === 0 ? (
              renderImagePlaceholder('图3')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
            )}
            {image4.length === 0 ? (
              renderImagePlaceholder('图4')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
            )}
          </div>
        );

      case 'vertical':
        return (
          <div className='flex flex-col gap-10px' style={{ ...getContainerStyle() }}>
            {image1.length === 0 ? (
              renderImagePlaceholder('图1')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image1?.[0]?.url})` }} />
            )}
            {image2.length === 0 ? (
              renderImagePlaceholder('图2')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image2?.[0]?.url})` }} />
            )}
            {image3.length === 0 ? (
              renderImagePlaceholder('图3')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image3?.[0]?.url})` }} />
            )}
            {image4.length === 0 ? (
              renderImagePlaceholder('图4')
            ) : (
              <div className='flex-1 bg-cover bg-center' style={{ ...getBorderRadiusStyle(), backgroundImage: `url(${image4?.[0]?.url})` }} />
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{
      width: '100%',
      height: '100%',
      //backgroundColor: '#fff',
      overflow: 'hidden'
    }}>
      {renderLayout()}
    </div>
  );
};

export default ImageQuadrupleVisual;