import React from 'react';
import { Row, Col, Card } from 'antd';

interface GroupVisualProps {
  configDataJson: any;
}

const GroupVisual: React.FC<GroupVisualProps> = ({ configDataJson }) => {
  const { title, layout } = configDataJson || {};

  return (
    <div style={{ padding: '10px' }}>
      {title && (
        <div style={{
          fontSize: '16px',
          fontWeight: 'bold',
          marginBottom: '10px'
        }}>
          {title}
        </div>
      )}
      <Row gutter={layout === 'scroll' ? 8 : 0}>
        {[1, 2, 3, 4].map((item) => (
          <Col
            span={layout === 'grid' ? 12 : undefined}
            key={item}
            style={{
              marginBottom: '8px',
              ...(layout === 'scroll' ? { minWidth: '120px' } : {})
            }}
          >
            <Card size="small" cover={
              <div style={{
                height: '100px',
                background: '#f5f5f5'
              }} />
            }>
              <Card.Meta
                title={<div style={{
                  height: '15px',
                  background: '#f5f5f5'
                }} />}
                description={<div style={{
                  height: '15px',
                  background: '#f5f5f5',
                  width: '80%',
                  marginTop: '5px'
                }} />}
              />
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default GroupVisual;