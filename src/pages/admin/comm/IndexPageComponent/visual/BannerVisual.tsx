import React from 'react';
import { Carousel } from 'antd';
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import type { CarouselProps } from 'antd';

interface BannerVisualProps {
  configDataJson: any;
}

const BannerVisual: React.FC<BannerVisualProps> = ({ configDataJson }) => {
  const { point_style, ratio, interval, items = [], point_position = 'bottom-center', point_offset = 0 } = configDataJson || {};

  // 计算图片比例
  const getRatioPadding = () => {
    // 如果是 auto 模式，返回 0 表示自适应高度
    if (ratio === 'auto') {
      return 0;
    }

    if (!ratio) return 40; // 默认高度
    const [width, height] = ratio.split(':').map(Number);
    return (height / width) * 100;
  };

  // 轮播配置
  const carouselSettings: CarouselProps = {
    autoplay: true,
    autoplaySpeed: interval ? parseInt(interval, 10) : 3000,
    dots: false // 删除: 隐藏默认的dots
  };

  // 判断是否为自适应高度模式
  const isAutoHeight = ratio === 'auto';

  // 获取轮播项的样式
  const getSlideStyle = () => {
    return {
      position: 'relative' as const,
      width: '100%'
    };
  };

  // 获取图片容器样式（统一高度）
  const getImageContainerStyle = () => {
    if (isAutoHeight) {
      return {
        width: '100%',
        position: 'relative' as const
      };
    }

    return {
      width: '100%',
      paddingBottom: `${getRatioPadding()}%`,
      position: 'relative' as const
    };
  };

  // 获取图片样式
  const getImageStyle = () => {
    if (isAutoHeight) {
      return {
        width: '100%',
        height: 'auto',
        display: 'block'
      };
    }

    return {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const
    };
  };

  // 统一占位符样式
  const getPlaceholderStyle = () => {
    if (isAutoHeight) {
      return {
        width: '100%',
        minHeight: '120px',
        background: '#f5f5f5',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#999',
        fontSize: '12px'
      };
    }

    return {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
      background: '#f5f5f5',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      color: '#999',
      fontSize: '12px'
    };
  };

  // 获取指示器位置样式
  const getDotsPositionStyle = () => {
    let justifyContent = 'center';
    switch (point_position) {
      case 'bottom_left':
        justifyContent = 'flex-start';
        break;
      case 'bottom_right':
        justifyContent = 'flex-end';
        break;
      default:
        justifyContent = 'center';
    }

    return {
      display: 'flex',
      justifyContent,
      position: 'absolute' as const,
      bottom: `${10 + (point_offset || 0)}px`,
      left: 0,
      right: 0,
      padding: '0 10px'
    };
  };

  // 获取指示器项样式
  const getDotStyle = (isActive: boolean) => {
    const baseStyle = {
      margin: '0 2px',
      cursor: 'pointer',
      transition: 'all 0.3s',
      border: 'none',
      background: isActive ? '#c48053' : '#E2E2E2'
    };

    if (point_style === 'square') {
      return {
        ...baseStyle,
        width: '10px',
        height: '3px',
        borderRadius: '0'
      };
    } else {
      return {
        ...baseStyle,
        width: '5px',
        height: '5px',
        borderRadius: '50%'
      };
    }
  };

  // 轮播引用
  const carouselRef = React.useRef<any>(null);
  const [currentSlide, setCurrentSlide] = React.useState(0);

  // 轮播切换回调
  const handleBeforeChange = (_: number, to: number) => {
    setCurrentSlide(to);
  };

  return (
    <div style={{ overflow: 'hidden', position: 'relative' }}>
      {items && items.length > 0 ? (
        <>
          <Carousel 
            ref={carouselRef} 
            {...carouselSettings} 
            beforeChange={handleBeforeChange}
          >
            {items.map((item: any, index: number) => {
              const imageUrl = item?.image ? UploadV2.str2arr(item.image)[0]?.url : null;
              return (
                <div key={index} style={getSlideStyle()}>
                  <div style={getImageContainerStyle()}>
                    {imageUrl ? (
                      <img
                        src={imageUrl}
                        alt=""
                        style={getImageStyle()}
                      />
                    ) : (
                      <div style={getPlaceholderStyle()}>
                        请添加图片
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </Carousel>
          {/* 自定义指示器 */}
          {items.length > 1 && (
            <div style={getDotsPositionStyle()}>
              {items.map((_: any, index: number) => (
                <div
                  key={index}
                  style={getDotStyle(index === currentSlide)}
                  onClick={() => carouselRef.current?.goTo(index)}
                />
              ))}
            </div>
          )}
        </>
      ) : (
        <div style={{
          width: '100%',
          paddingBottom: isAutoHeight ? '0' : `${getRatioPadding()}%`,
          position: 'relative'
        }}>
          <div style={isAutoHeight ? {
            width: '100%',
            minHeight: '120px',
            background: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999',
            fontSize: '12px'
          } : {
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999',
            fontSize: '12px'
          }}>
            请添加轮播图项
          </div>
        </div>
      )}
    </div>
  );
};

export default BannerVisual;





