import { makeApi } from "@/utils/Api";

const PlatformIndexPageComponentApi = {
    getPlatformIndexPageComponent: makeApi("get", `/api/platformIndexPageComponent`),
    getPlatformIndexPageComponentInfo: makeApi("get", `/api/platformIndexPageComponent`, true),
    addPlatformIndexPageComponent: makeApi("post", `/api/platformIndexPageComponent`),
    putPlatformIndexPageComponent: makeApi("put", `/api/platformIndexPageComponent`),
    delPlatformIndexPageComponent: makeApi("delete", `/api/platformIndexPageComponent`),
    savePlatformIndexPageComponent: makeApi("post", `/api/platformIndexPageComponent/save_component_data`),
};

export default PlatformIndexPageComponentApi;
