// src/pages/admin/comm/IndexPageComponent/VisualEditor.page.tsx
import React, { cloneElement } from "react";
import { Drawer } from "antd";
import VisualEditorMain from "./VisualEditor.main";
import { useSetState } from "react-use";

interface VisualEditorPageProps {
  children: React.ReactElement;
  title?: string;
  pageConfigId?: string;
}

const VisualEditorIndex: React.FC<VisualEditorPageProps> = ({
  children,
  title = "可视化装修",
  pageConfigId,
}) => {
  const [state, setState] = useSetState({
    open: false,
  });

  const handleOpen = () => {
    setState({ open: true });
  };

  const handleClose = () => {
    setState({ open: false });
  };

  return (
    <div>
      {cloneElement(children, { onClick: handleOpen })}
      <Drawer
        open={state.open}
        width={"100%"}
        onClose={handleClose}
        title={title}
        keyboard={false}
        destroyOnClose
      >
        {state.open && pageConfigId && (
          <VisualEditorMain pageConfigId={pageConfigId} />
        )}
      </Drawer>
    </div>
  );
};

export default VisualEditorIndex;