import dayjs from "dayjs";

// 基础常量
export const ScreenWidth = 375;
export const MarginX = 15;
export const ImageMargin = 10;

// 状态选项
export const StateMap = [
  { value: 1, label: "启用" },
  { value: 0, label: "禁用" },
];

// 组件类型选项
export const IndexPageComponentTypeMap = [
  { value: "banner", label: "banner" },
  { value: "navigation", label: "导航" },
  { value: "image_single", label: "单图" },
  { value: "image_double", label: "双图" },
  { value: "image_triple", label: "三图" },
  { value: "image_quadruple", label: "四图" },
  { value: "video", label: "视频" },
  { value: "search_bar", label: "搜索栏" },
  { value: "divider", label: "分割线" },
  { value: "notice", label: "通知" },
  { value: "product_list", label: "商品列表" },
  { value: "group", label: "拼团" },
  { value: "seckill", label: "秒杀" },
  { value: "chop", label: "砍价" },
  { value: "customer_service", label: "客服" },
  { value: "phone_call", label: "电话" },
  { value: "floating_component", label: "悬浮组件" },
];

// 链接类型选项
export const LinkTypeOptions = [
  { value: "none", label: "不跳转" },
  { value: "page", label: "页面" },
  { value: "action", label: "事件" },
];

// 边距选项
export const MarginLeftRightOptions = [
  { label: "无边距", value: "none" },
  { label: "有边距", value: "side" },
];

// 图片圆角选项
export const ImageRadiusOptions = [
  { label: "直角", value: "sharp" },
  { label: "圆角", value: "round" },
];

// 图标圆形选项
export const IconRadiusOptions = [
  { label: "方形", value: "sharp" },
  { label: "圆形", value: "round" },
];

// Banner样式选项
export const BannerStyleOptions = [
  { label: "圆点样式", value: "dot" },
  { label: "长条样式", value: "square" },
];

// 布局选项
export const LayoutOptions = [
  { label: "横向滚动", value: "scroll" },
  { label: "网格布局", value: "grid" },
];

// 导航布局选项
export const NavigationLayoutOptions = [
  { label: "单行显示", value: "single" },
  { label: "多行显示", value: "multi" },
];

// 每行项目数选项
export const ItemsPerRowOptions = [
  { label: "1个", value: 1 },
  { label: "2个", value: 2 },
  { label: "3个", value: 3 },
  { label: "4个", value: 4 },
  { label: "5个", value: 5 },
];

// 新增：预设导航风格
export const NavigationStylePresets = [
  {
    value: "default",
    label: "默认风格",
    config: {
      border_style: "solid",
      border_color: "#c2c2c2ff",
      border_radius: "round",
      background_color: "#ffffff",
      line_height: 80,
      icon_radius: "round",
      icon_width: 40,
      icon_height: 40,
      text_color: "#333333",
      font_size: 12,
      icon_text_gap: 5
    }
  },
  {
    value: "modern",
    label: "现代风格",
    config: {
      border_style: "solid",
      border_color: "#e8e8e8",
      border_radius: "sharp",
      background_color: "#f9f9f9",
      line_height: 70,
      icon_radius: "sharp",
      icon_width: 35,
      icon_height: 35,
      text_color: "#666666",
      font_size: 11,
      icon_text_gap: 5
    }
  },
  {
    value: "minimal",
    label: "极简风格",
    config: {
      border_style: "none",
      border_color: "#ffffff",
      border_radius: "sharp",
      background_color: "#ffffff",
      line_height: 70,
      icon_radius: "sharp",
      icon_width: 35,
      icon_height: 35,
      text_color: "#999999",
      font_size: 11,
      icon_text_gap: 5
    }
  }
];

// 边框样式选项
export const BorderStyleOptions = [
  { label: "无边框", value: "none" },
  { label: "实线", value: "solid" },
  { label: "虚线", value: "dashed" },
  { label: "点线", value: "dotted" },
];

// 边框圆角选项
export const BorderRadiusOptions = [
  { label: "直角", value: "sharp" },
  { label: "圆角", value: "round" },
];

// 位置选项
export const PositionOptions = [
  { label: '左上', value: 'leftTop' },
  { label: '右上', value: 'rightTop' },
  { label: '左下', value: 'leftBottom' },
  { label: '右下', value: 'rightBottom' }
];

// Banner点位置选项
export const BannerPointPositionOptions = [
  { label: '底部居左', value: 'bottom_left' },
  { label: '底部居中', value: 'bottom_center' },
  { label: '底部居右', value: 'bottom_right' }
];

// 双图样式选项
export const ImageDoubleStyleOptions = [
  { label: "水平排列", value: "horizontal", height: 87.5 },
  { label: "垂直排列", value: "vertical", height: 250 },
];

// 三图样式选项
export const ImageTripleStyleOptions = [
  { label: "上1下2", value: "top1_bottom2", height: 285 },
  { label: "上2下1", value: "top2_bottom1", height: 285 },
  { label: "左1右2", value: "left1_right2", height: 250 },
  { label: "左2右1", value: "left2_right1", height: 250 },
  { label: "水平排列", value: "horizontal", height: 220 },
  { label: "垂直排列", value: "vertical", height: 375 },
];

// 四图样式选项
export const ImageQuadrupleStyleOptions = [
  { label: "上2下2", value: "top2_bottom2", height: 250 },
  { label: "上1下3", value: "top1_bottom3", height: 250 },
  { label: "上3下1", value: "top3_bottom1", height: 250 },
  { label: "左1右3", value: "left1_right3", height: 285 },
  { label: "左3右1", value: "left3_right1", height: 285 },
  { label: "水平排列", value: "horizontal", height: 160 },
  { label: "垂直排列", value: "vertical", height: 500 },
];

// 商品列表布局选项
export const ProductListLayoutOptions = [
  { label: "大图单列", value: "large_single" },
  { label: "小图双列", value: "small_double" },
  { label: "详情列表", value: "detail_list" },
];

// 分割线样式选项
export const DividerStyleOptions = [
  { label: "实线", value: "solid" },
  { label: "虚线", value: "dashed" },
  { label: "点线", value: "dotted" },
];

// 线宽选项
export const LineWidthOptions = [
  { label: "样式1", value: 1 },
  { label: "样式2", value: 2 },
  { label: "样式3", value: 3 },
  { label: "样式4", value: 4 },
  { label: "样式5", value: 5 },
];

// 背景类型选项
export const BackgroundTypeOptions = [
  { value: "solid", label: "纯色" },
  { value: "gradient", label: "渐变色" },
  { value: "image", label: "背景图" },
];

// 渐变类型选项
export const GradientTypeOptions = [
  { value: "linear", label: "线性渐变" },
  { value: "radial", label: "径向渐变" },
];

// 背景图片模式选项
export const BackgroundImageModeOptions = [
  { value: "stretch", label: "拉伸(stretch)" },
  { value: "tile", label: "平铺(tile)" },
  { value: "fit", label: "自适应(fit)" },
  { value: "cover", label: "覆盖(cover)" },
  { value: "contain", label: "包含(contain)" },
  { value: "center", label: "居中(center)" },
  { value: "top", label: "顶部(top)" },
  { value: "bottom", label: "底部(bottom)" },
  { value: "left", label: "左侧(left)" },
  { value: "right", label: "右侧(right)" },
];

// 组件分类类型
export interface ComponentCategory {
  categoryId: string;
  categoryName: string;
  categoryIdEnCode: string;
  typeList: ComponentType[];
}

// 组件类型
export interface ComponentType {
  id: number;
  name: string;
  enCode: string;
  description?: string;
  state: number;
}

// 页面组件实例
export interface PageComponent {
  id?: number;
  componentTypeId: number;
  componentTypeName: string;
  componentTypeEnCode: string;
  state: number;
  sort: number;
  timeLimit: number;
  startDate?: string;
  endDate?: string;
  configDataJson: any;
}

// 提交数据类型
export interface ComponentSubmitData {
  componentTypeId: number;
  timeLimit: number;
  startDate?: string;
  endDate?: string;
  state: number;
  configDataJson: any;
}

// 表单组件实例（包含额外的表单字段）
export interface FormPageComponent extends PageComponent {
  limitDate?: [dayjs.Dayjs, dayjs.Dayjs];
}

// 页面配置的类型
export interface PageConfig {
  background_type?: 'solid' | 'gradient' | 'image';
  background_color?: string;
  background_gradient?: string;
  // 渐变色相关配置
  gradient_type?: 'linear' | 'radial';
  gradient_direction?: string;
  gradient_colors?: string[];
  gradient_angle?: number;
  background_image?: string;
  background_image_arr?: any[];
  background_image_mode?: 'stretch' | 'tile' | 'fit' | 'cover' | 'contain' | 'center' | 'top' | 'bottom' | 'left' | 'right';
  // 可以在这里添加更多页面配置属性
  [key: string]: any;
}

// 定义各组件的初始值配置
export const getComponentInitialValues = (enCode: string) => {
  const initialMap: Record<string, any> = {
    banner: {
      margin_top: 18,
      margin_x: "side",
      ratio: "5:4",
      interval: 3000,
      point_style: "dot",
      point_position: "bottom-center",
      point_offset: 0,
      items: []
    },
    navigation: {
      margin_top: 18,
      margin_x: "side",
      border_style: "none",
      layout: 2,
      items_per_row: 4,
      border_radius: "round",
      border_color: "#e0e0e0",
      background_color: "#ffffff",
      line_height: 80,
      icon_radius: "round",
      icon_width: 40,
      icon_height: 40,
      text_color: "#333333",
      font_size: 12,
      items: []
    },
    image_single: {
      margin_top: 18,
      margin_x: "side",
      image_radius: "round",
      items: []
    },
    image_double: {
      margin_top: 18,
      margin_x: "side",
      style: "horizontal",
      image_radius: "round",
      container_height: 87.5,
      items: []
    },
    image_triple: {
      margin_top: 18,
      margin_x: "side",
      style: "top1_bottom2",
      image_radius: "round",
      container_height: 285,
      items: []
    },
    image_quadruple: {
      margin_top: 18,
      margin_x: "side",
      style: "top2_bottom2",
      image_radius: "round",
      container_height: 250,
      items: []
    },
    video: {
      margin_top: 18,
      margin_x: "side",
      autoplay: false,
      video_url: [],
      poster: [],
    },
    search_bar: {
      margin_top: 18,
      margin_x: "side",
      container_height: 40,
      placeholder: "请输入关键词",
      bg_color: "#f5f5f5",
      text_color: "#999",
      margin: "side"
    },
    divider: {
      margin_top: 18,
      margin_x: "side",
      color: "#e0e0e0",
      style: "solid",
      line_width: 1
    },
    notice: {
      margin_top: 18,
      margin_x: "side",
      bg_color: "#fff8e6",
      text_color: "#ff6b00",
      interval: 3000,
      icon: []
    },
    product_list: {
      margin_top: 18,
      margin_x: "side",
      layout: "large_single",
      source: "manual",
      title: "商品列表"
    },
    group: {
      margin_top: 18,
      margin_x: "side",
      layout: "scroll",
      title: "拼团列表"
    },
    seckill: {
      margin_top: 18,
      margin_x: "side",
      layout: "scroll",
      title: "秒杀列表"
    },
    chop: {
      margin_top: 18,
      margin_x: "side",
      layout: "scroll",
      title: "砍价列表"
    },
    floating_component: {
      position: "rightBottom",
      position_bottom: 50,
      position_right: 10,
      container_width: 40,
      container_height: 40,
      border_radius: "sharp",
      link_type: "none",
      link_name: "",
      link_value: "",
      icon: []
    },
  };

  return initialMap[enCode] || {};
};

// 工具函数
export const getImageReferenceSize = (width: number, height: number) => {
  width = parseFloat(width.toFixed(2));
  height = parseFloat(height.toFixed(2));
  return width + "*" + height;
};

export const getImageDoubleHeight = (style: string, margin_x: string) => {
  const option = ImageDoubleStyleOptions.find((item) => item.value === style);
  let newWidth = ScreenWidth;
  let newHeight = option ? option.height : 0;
  if (margin_x === "side") {
    newWidth = newWidth - (ImageMargin * 2);
    newHeight = (newWidth / ScreenWidth) * newHeight;
  }
  return parseFloat(newHeight.toFixed(2));
};

export const getImageDoubleUnitHeight = (style: string, container_height: number, image_index: number) => {
  let newWidth = ScreenWidth;
  let newHeight = container_height;

  if (style === "horizontal") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
  }
  if (style === "vertical") {
    if (image_index === 1) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2));
    }
  }
  return "";
};

export const getImageTripleHeight = (style: string, margin_x: string) => {
  const option = ImageTripleStyleOptions.find((item) => item.value === style);
  let newWidth = ScreenWidth;
  let newHeight = option ? option.height : 0;
  if (margin_x === "side") {
    newWidth = newWidth - (ImageMargin * 2);
    newHeight = (newWidth / ScreenWidth) * newHeight;
  }
  return newHeight;
};

export const getImageTripleUnitHeight = (style: string, container_height: number, image_index: number) => {
  let newWidth = ScreenWidth;
  let newHeight = container_height;

  if (style === "top1_bottom2") {
    if (image_index === 1) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2))
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "top2_bottom1") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "left1_right2") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "left2_right1") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
  }

  if (style === "horizontal") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 3), newHeight);
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 3), newHeight);
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 3), newHeight);
    }
  }

  if (style === "vertical") {

    if (image_index === 1) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 3));
    }
    if (image_index === 2) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 3));
    }
    if (image_index === 3) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 3));
    }
  }
  return "";
};

export const getImageQuadrupleHeight = (style: string, margin_x: string) => {
  const option = ImageQuadrupleStyleOptions.find((item) => item.value === style);
  let newWidth = ScreenWidth;
  let newHeight = option ? option.height : 0;
  if (margin_x === "side") {
    newWidth = newWidth - (ImageMargin * 2);
    newHeight = (newWidth / ScreenWidth) * newHeight;
  }
  return newHeight;
};

export const getImageQuadrupleUnitHeight = (style: string, container_height: number, image_index: number) => {
  let newWidth = ScreenWidth;
  let newHeight = container_height;
  if (style === "top2_bottom2") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 4) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "top1_bottom3") {
    if (image_index === 1) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 4) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "top3_bottom1") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 4) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 2)) / 3), ((newHeight - ImageMargin) / 2));
    }
    if (image_index === 4) {
      return getImageReferenceSize(newWidth, ((newHeight - ImageMargin) / 2));
    }
  }
  if (style === "left1_right3") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
    if (image_index === 4) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
  }
  if (style === "left3_right1") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), ((newHeight - (ImageMargin * 2)) / 3));
    }
    if (image_index === 4) {
      return getImageReferenceSize(((newWidth - ImageMargin) / 2), newHeight);
    }
  }

  if (style === "horizontal") {
    if (image_index === 1) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 3)) / 4), newHeight);
    }
    if (image_index === 2) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 3)) / 4), newHeight);
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 3)) / 4), newHeight);
    }
    if (image_index === 3) {
      return getImageReferenceSize(((newWidth - (ImageMargin * 3)) / 4), newHeight);
    }
  }
  if (style === "vertical") {
    if (image_index === 1) {
      return getImageReferenceSize(newWidth, ((newHeight - (ImageMargin * 3)) / 4));
    }
    if (image_index === 2) {
      return getImageReferenceSize(newWidth, ((newHeight - (ImageMargin * 3)) / 4));
    }
    if (image_index === 3) {
      return getImageReferenceSize(newWidth, ((newHeight - (ImageMargin * 3)) / 4));
    }
    if (image_index === 4) {
      return getImageReferenceSize(newWidth, ((newHeight - (ImageMargin * 3)) / 4));
    }
  }
  return "";
};