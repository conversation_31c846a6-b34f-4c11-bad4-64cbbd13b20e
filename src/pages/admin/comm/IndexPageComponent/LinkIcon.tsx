// LinkIcon.tsx
import { Col, Form, Input, Row, Select } from 'antd';
import { MinusCircleOutlined, UpOutlined, DownOutlined } from "@ant-design/icons";
import { UploadV2 } from '@/components/ImageUpload/UploadV2';
import React, { useState, useEffect } from 'react';
import { LinkTypeOptions } from './types';
import LinkSelectorModal from './LinkSelectorModal';

interface LinkIconProps {
  field: {
    key: React.Key;
    name: number;
    [key: string]: any;
  };
  remove: (name: number) => void;
  form?: any;
  formChange: (changedValues: any) => void;
  index?: number;
  move?: (from: number, to: number) => void;
  total?: number;
}

const LinkIcon: React.FC<LinkIconProps> = ({ field, remove, form, formChange, index, move, total }) => {
  const { key, name, ...restField } = field;


  // 构建字段路径
  const fieldName = [name];
  const linkTypeFieldName = [...fieldName, 'link_type'];
  const linkValueFieldName = [...fieldName, 'link_value'];
  const linkNameFieldName = [...fieldName, 'link_name']; // 添加链接名称字段路径

  // 添加状态管理链接类型和链接选择面板
  const [linkType, setLinkType] = useState<string | undefined>(undefined);
  const [_, setLinkName] = useState<string | undefined>(undefined);
  const [linkValue, setLinkValue] = useState<string | undefined>(undefined);
  const [isModalVisible, setIsModalVisible] = useState(false);
  // 如果没有传入form，则使用Form.useForm创建
  const [innerForm] = Form.useForm();
  const usedForm = form || innerForm;

  // 添加useEffect来初始化linkType值
  useEffect(() => {
    const initLinkType = async () => {
      try {
        const currentLinkType = usedForm.getFieldValue(['configDataJson', 'items', name, 'link_type']);
        const currentLinkName = usedForm.getFieldValue(['configDataJson', 'items', name, 'link_name']);
        const currentLinkValue = usedForm.getFieldValue(['configDataJson', 'items', name, 'link_value']);

        if (currentLinkType !== undefined) {
          setLinkType(currentLinkType);
          setLinkName(currentLinkName);
          setLinkValue(currentLinkValue);
        }
      } catch (error) {
        console.error('Failed to get initial link type:', error);
      }
    };

    initLinkType();
  }, [usedForm, name]);

  const showModal = async () => {
    setIsModalVisible(true);
  };

  const handleOk = (selectedLink: string) => {
    // 解析链接名称和链接值（假设格式为 "名称|链接"）
    const [linkNameTemp, linkValueTemp] = selectedLink.split('|');

    usedForm.setFieldsValue({
      configDataJson: {
        items: {
          [name]: {
            link_name: linkNameTemp || '', // 设置链接名称
            link_value: linkValueTemp || selectedLink
          }
        }
      }
    });

    setLinkName(linkNameTemp || '');
    setLinkValue(linkValueTemp || '');

    // 触发表单的 onValuesChange 事件
    const fieldValues = usedForm.getFieldsValue();
    formChange(fieldValues);

    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <div key={key} style={{ border: '1px solid #f0f0f0', padding: '16px', marginBottom: '16px', position: 'relative' }}>
      <Row gutter={16}>
        <Col span={5}>
          <Form.Item
            {...restField}
            name={[...fieldName, 'icon']}
            label={`图${name + 1}`}
            rules={[{ required: true, message: '请上传图标' }]}
          >
            <UploadV2 compress={false} />
          </Form.Item>
        </Col>
        <Col span={15}>
          <Row gutter={[16, 0]}>
            <Col span={24}>
              <Form.Item
                {...restField}
                name={[...fieldName, 'text']}
                label="文本"
                labelCol={{ flex: '100px' }}
                required
              >
                <Input placeholder="请输入文本" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                {...restField}
                name={linkTypeFieldName}
                label="链接类型"
                labelCol={{ flex: '100px' }}
                required
              >
                <Select
                  placeholder="请选择链接类型"
                  onChange={(value) => {
                    setLinkType(value as string);
                    setLinkName('');
                    setLinkValue('');
                    // 清空链接名称和链接值
                    usedForm.setFieldsValue({
                      configDataJson: {
                        items: {
                          [name]: {
                            link_name: '',
                            link_value: '',
                            phone_number: ''
                          }
                        }
                      }
                    });
                    // 触发表单的 onValuesChange 事件
                    const fieldValues = usedForm.getFieldsValue();
                    formChange(fieldValues);
                  }}
                  options={LinkTypeOptions}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                {...restField}
                name={linkNameFieldName}
                label="链接名称"
                labelCol={{ flex: '100px' }}
              >
                <Input
                  placeholder="请选择链接获取名称"
                  disabled
                  addonAfter={
                    <a
                      onClick={() => linkType !== 'none' && showModal()}
                      style={{ color: linkType === 'none' ? '#ccc' : undefined, cursor: linkType === 'none' ? 'not-allowed' : 'pointer' }}
                    >
                      选择
                    </a>
                  } />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                {...restField}
                name={linkValueFieldName}
                label="链接值"
                labelCol={{ flex: '100px' }}
                hidden
              >
                <Input
                  placeholder={linkType === 'none' ? '无需填写' : "请输入链接值"}
                  disabled={linkType === 'none'}
                />
              </Form.Item>
            </Col>
            {linkValue && linkValue.startsWith('CallPhone') && (
              <Col span={24}>
                <Form.Item
                  {...restField}
                  name={[...fieldName, 'phone_number']}
                  label="电话号码"
                  labelCol={{ flex: '100px' }}
                  rules={[{ required: true, message: '请输入电话号码' }]}
                >
                  <Input placeholder="请输入电话号码" />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Col>
      </Row>
      <div style={{ position: 'absolute', top: '16px', right: '16px', display: 'flex', gap: '8px' }}>
        {move && index !== undefined && total !== undefined && (
          <>
            <UpOutlined
              style={{ fontSize: '16px', color: '#1890ff', cursor: index === 0 ? 'not-allowed' : 'pointer', opacity: index === 0 ? 0.5 : 1 }}
              onClick={() => index > 0 && move(index, index - 1)}
            />
            <DownOutlined
              style={{ fontSize: '16px', color: '#1890ff', cursor: index === total - 1 ? 'not-allowed' : 'pointer', opacity: index === total - 1 ? 0.5 : 1 }}
              onClick={() => index < total - 1 && move(index, index + 1)}
            />
          </>
        )}
        <MinusCircleOutlined
          onClick={() => remove(name)}
          style={{ fontSize: '16px', color: '#ff4d4f' }}
        />
      </div>
      <LinkSelectorModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onOk={handleOk}
        linkType={linkType} // 传递linkType参数
      />
    </div>
  );
};

export default LinkIcon;