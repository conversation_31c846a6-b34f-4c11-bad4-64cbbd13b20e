import React, { useState, useEffect } from 'react';
import { Modal, List } from 'antd';
import PlatformPageLinkApi from '../PageLink/api';

interface LinkOption {
  name: string;
  linkUrl: string;
}

interface LinkSelectorModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (selectedLink: string) => void;
  linkType?: string; // 新增linkType参数
}

const LinkSelectorModal: React.FC<LinkSelectorModalProps> = ({ visible, onCancel, onOk, linkType }) => {
  const [linkOptions, setLinkOptions] = useState<LinkOption[]>([]);

  const fetchLinkOptions = async () => {
    try {
      // 调用接口时传递linkType参数
      const params = { type: linkType };
      const response = await PlatformPageLinkApi.selectPlatformPageLink({params});
      setLinkOptions(response?.list || []);
    } catch (error) {
      console.error('获取链接列表失败:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchLinkOptions();
    }
  }, [visible, linkType]); // 添加linkType到依赖数组

  // 修改handleSelect函数，将名称和链接值一起返回
  const handleSelect = (item: LinkOption) => {
    // 将名称和链接值组合成 "名称|链接" 格式返回
    const combinedValue = `${item.name}|${item.linkUrl}`;
    onOk(combinedValue);
  };

  return (
    <Modal
      title="选择链接"
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <List
        dataSource={linkOptions}
        renderItem={item => (
          <List.Item
            // 修改onClick事件，传递完整对象
            onClick={() => handleSelect(item)}
            style={{ cursor: 'pointer' }}
          >
            <List.Item.Meta
              title={item.name}
              description={item.linkUrl}
            />
          </List.Item>
        )}
        style={{ maxHeight: '700px', overflowY: 'auto' }}
      />
    </Modal>
  );
};

export default LinkSelectorModal;