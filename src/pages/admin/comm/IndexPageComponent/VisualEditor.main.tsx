// src/pages/admin/comm/IndexPageComponent/VisualEditor.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { Row, Col, Card, List, Button, message, Empty, Collapse } from 'antd';
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd';
import PlatformIndexComponentTypeApi from '../IndexComponentType/api';
import PlatformIndexPageComponentApi from './api';
import BannerConfig from './config/BannerConfig';
import NavigationConfig from './config/NavigationConfig';
import ImageSingleConfig from './config/ImageSingleConfig';
import ImageDoubleConfig from './config/ImageDoubleConfig';
import ImageTripleConfig from './config/ImageTripleConfig';
import ImageQuadrupleConfig from './config/ImageQuadrupleConfig';
import VideoConfig from './config/VideoConfig';
import SearchBarConfig from './config/SearchBarConfig';
import DividerConfig from './config/DividerConfig';
import NoticeConfig from './config/NoticeConfig';
import ProductListConfig from './config/ProductListConfig';
import GroupConfig from './config/GroupConfig';
import SeckillConfig from './config/SeckillConfig';
import ChopConfig from './config/ChopConfig';
import { BackgroundImageModeOptions, BackgroundTypeOptions, getComponentInitialValues, GradientTypeOptions, MarginX, PageConfig, StateMap } from './types';
import { Form, Input, Radio, Switch, DatePicker, ColorPicker, Select, Slider } from 'antd';
import dayjs from "dayjs";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { DatePresetRanges, formatDateRange } from '@/utils/Tools';
// 引入可视化组件
import BannerVisual from './visual/BannerVisual';
import NavigationVisual from './visual/NavigationVisual';
import ImageSingleVisual from './visual/ImageSingleVisual';
import ImageDoubleVisual from './visual/ImageDoubleVisual';
import ImageTripleVisual from './visual/ImageTripleVisual';
import ImageQuadrupleVisual from './visual/ImageQuadrupleVisual';
import VideoVisual from './visual/VideoVisual';
import SearchBarVisual from './visual/SearchBarVisual';
import DividerVisual from './visual/DividerVisual';
import NoticeVisual from './visual/NoticeVisual';
import ProductListVisual from './visual/ProductListVisual';
import GroupVisual from './visual/GroupVisual';
import SeckillVisual from './visual/SeckillVisual';
import ChopVisual from './visual/ChopVisual';
import { CloseOutlined } from '@ant-design/icons';
// 引入类型定义
import { ComponentCategory, ComponentType, PageComponent, ComponentSubmitData, FormPageComponent } from './types';
import FloatingComponentConfig from './config/FloatingComponentConfig';
import FloatingComponentVisual from './visual/FloatingComponentVisual';
// 引入页面配置API
import PlatformIndexPageConfigApi from '../IndexPageConfig/api';

// 布局常量
export const layout = { row: 10, col: 24 };

const { Panel } = Collapse;

// ==================== 工具函数 ====================
// 自定义防抖函数
function debounce<F extends (...args: any[]) => any>(func: F, wait: number): (...args: Parameters<F>) => void {
  let timeout: NodeJS.Timeout;
  return function (...args: Parameters<F>) {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// ==================== 子组件 ====================
// 页面背景预览组件
const PageBackgroundPreview: React.FC<{ pageConfig: PageConfig }> = ({ pageConfig }) => {
  if (pageConfig.background_type === 'solid') {
    return (
      <div style={{
        height: 406,
        width: 188,
        background: pageConfig.background_color || '#ffffff',
        borderRadius: 4,
        border: '1px solid #d9d9d9'
      }} />
    );
  }

  if (pageConfig.background_type === 'gradient') {
    return (
      <div style={{
        height: 406,
        width: 188,
        background: pageConfig.gradient_type === 'linear'
          ? `linear-gradient(${pageConfig.gradient_angle}deg, ${pageConfig.gradient_colors?.join(', ')})`
          : `radial-gradient(${pageConfig.gradient_colors?.join(', ')})`,
        borderRadius: 4,
        border: '1px solid #d9d9d9'
      }} />
    );
  }

  if (pageConfig.background_type === 'image') {
    return (
      <div style={{
        height: 406,
        width: 188,
        backgroundImage: pageConfig.background_image ? `url("${pageConfig.background_image}")` : 'none',
        backgroundSize: pageConfig.background_image_mode === 'stretch'
          ? '100% 100%'
          : pageConfig.background_image_mode === 'fit'
            ? 'contain'
            : pageConfig.background_image_mode === 'cover'
              ? 'cover'
              : pageConfig.background_image_mode === 'contain'
                ? 'contain'
                : 'auto',
        backgroundRepeat: pageConfig.background_image_mode === 'tile' ? 'repeat' : 'no-repeat',
        backgroundPosition: pageConfig.background_image_mode === 'center' ? 'center' :
          pageConfig.background_image_mode === 'top' ? 'top' :
            pageConfig.background_image_mode === 'bottom' ? 'bottom' :
              pageConfig.background_image_mode === 'left' ? 'left' :
                pageConfig.background_image_mode === 'right' ? 'right' : 'center',
        borderRadius: 4,
        border: '1px solid #d9d9d9'
      }} />
    );
  }

  return null;
};

// 手机预览容器组件
const PhonePreviewContainer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div
      className="phone-preview-container"
      style={{
        width: '375px',        // iPhone X 宽度
        height: '812px',       // iPhone X 高度
        border: '10px solid #000',
        borderRadius: '40px',  // iPhone X 圆角
        margin: '0 auto',
        position: 'relative',
        boxShadow: '0 0 20px rgba(0,0,0,0.4)',
        overflow: 'hidden',
        backgroundColor: '#000' // 边框颜色
      }}>
      {/* 手机听筒 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '50%',
        transform: 'translateX(-50%)',
        width: '140px',
        height: '16px',
        background: '#222',
        borderRadius: '8px',
        zIndex: 10
      }}></div>

      {/* 手机摄像头区域 */}
      <div style={{
        position: 'absolute',
        top: '8px',
        right: '25px',
        width: '100px',
        height: '24px',
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'center',
        zIndex: 10
      }}>
      </div>

      {/* 手机Home键区域 - iPhone X 使用手势操作，用细线表示 */}
      {/* <div style={{
        position: 'absolute',
        bottom: '8px',
        left: '50%',
        transform: 'translateX(-50%)',
        width: '130px',
        height: '5px',
        background: '#fff',
        borderRadius: '3px',
        zIndex: 10
      }}></div> */}

      {children}
    </div>
  );
};

// 底部导航栏组件
const BottomNavigationBar: React.FC = () => {
  return (
    <div style={{
      position: 'absolute',
      bottom: '0px',     // 与手机预览容器底部对齐
      left: '0px',       // 与手机预览容器左边框对齐
      right: '0px',      // 与手机预览容器右边框对齐
      height: '50px',
      backgroundColor: '#fff',
      display: 'flex',
      borderTop: '1px solid #eee',
      zIndex: 25,         // 确保在内容之上但在悬浮组件之下
      boxShadow: '0 -2px 10px rgba(0,0,0,0.1)'
    }}>
      {[
        { name: '首页', icon: 'https://oss.gaomei168.com/file-release/20250816/1406304273146769408_200_200.png' },
        { name: '商城', icon: 'https://oss.gaomei168.com/file-release/20250816/1406304603636953088_200_200.png' },
        { name: '搞美星球', icon: 'https://oss.gaomei168.com/file-release/20250816/1406304685048393728_200_200.png' },
        { name: '我的', icon: 'https://oss.gaomei168.com/file-release/20250816/1406304771035820032_200_200.png' }
      ].map((item, index) => (
        <div
          key={index}
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            color: index === 0 ? '#8E4E36' : '#999'
          }}
        >
          <img
            src={item.icon}
            style={{
              width: '24px',
              height: '24px',
              marginBottom: '0px',
              objectFit: 'contain'
            }}
            alt={item.name}
          />
          <div>{item.name}</div>
        </div>
      ))}
    </div>
  );
};

// 悬浮组件容器
const FloatingComponentsContainer: React.FC<{
  pageComponents: PageComponent[],
  renderFloatingComponentPreview: (component: PageComponent) => React.ReactNode
}> = ({ pageComponents, renderFloatingComponentPreview }) => {
  return (
    <div style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 5,
      pointerEvents: 'none' // 防止影响其他交互
    }}>
      {pageComponents
        .filter(component =>
          component.componentTypeEnCode === 'floating_component'
        )
        .map((component, _) => (
          <div
            key={`${component.componentTypeId}-${component.sort}`}
            style={{ pointerEvents: 'auto' }} // 恢复悬浮组件的交互
          >
            {renderFloatingComponentPreview(component)}
          </div>
        ))
      }
    </div>
  );
};

// 主要内容预览区域
const MainPreviewArea: React.FC<{
  pageConfig: PageConfig;
  pageComponents: PageComponent[];
  renderComponentPreview: (component: PageComponent) => React.ReactNode;
  handleDragEnd: (result: DropResult) => void;
}> = ({ pageConfig, pageComponents, renderComponentPreview, handleDragEnd }) => {
  return (
    <div style={{
      width: '100%',
      height: '100%',
      overflowY: 'hidden',
      backgroundColor: pageConfig.background_color || 'transparent',
      marginTop: '30px',
      marginBottom: '0',
      paddingBottom: '80px',
      scrollbarWidth: 'none',
      // 根据背景类型设置背景样式
      backgroundImage: pageConfig.background_type === 'image' && pageConfig.background_image
        ? `url("${pageConfig.background_image}")`
        : pageConfig.background_type === 'gradient'
          ? (pageConfig.background_gradient ||
            (pageConfig.gradient_type === 'linear'
              ? `linear-gradient(${pageConfig.gradient_angle}deg, ${pageConfig.gradient_colors?.join(', ')})`
              : `radial-gradient(${pageConfig.gradient_colors?.join(', ')})`))
          : 'none',
      backgroundSize: pageConfig.background_type === 'image'
        ? (pageConfig.background_image_mode === 'stretch'
          ? '100% 100%'
          : pageConfig.background_image_mode === 'fit'
            ? 'contain'
            : pageConfig.background_image_mode === 'cover'
              ? 'cover'
              : pageConfig.background_image_mode === 'contain'
                ? 'contain'
                : 'auto')
        : 'auto',
      backgroundRepeat: pageConfig.background_type === 'image'
        ? (pageConfig.background_image_mode === 'tile' ? 'repeat' : 'no-repeat')
        : 'repeat',
      backgroundPosition: pageConfig.background_type === 'image'
        ? (pageConfig.background_image_mode === 'center' ? 'center' :
          pageConfig.background_image_mode === 'top' ? 'top' :
            pageConfig.background_image_mode === 'bottom' ? 'bottom' :
              pageConfig.background_image_mode === 'left' ? 'left' :
                pageConfig.background_image_mode === 'right' ? 'right' : 'center')
        : 'center'
    }}>
      {/* 添加鼠标滚轮支持的容器 */}
      <div style={{
        width: '100%',
        height: '100%',
        overflowY: 'auto',
        paddingRight: '17px',    // 隐藏滚动条的宽度
        boxSizing: 'content-box',
        scrollbarWidth: 'none'   // Firefox 隐藏滚动条
      }}>
        {/* 针对 Webkit 浏览器隐藏滚动条 */}
        <style>{`
          ::-webkit-scrollbar {
            display: none;
          }
        `}</style>

        {pageComponents.length > 0 ? (
          <>
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="components">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {pageComponents
                      .filter(component =>
                        component.componentTypeEnCode !== 'floating_component'
                      )
                      .map((component, index) => (
                        <Draggable
                          key={`${component.componentTypeId}-${component.sort}`}
                          draggableId={`${component.componentTypeId}-${component.sort}`}
                          index={index}
                        >
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{ ...provided.draggableProps.style }}
                            >
                              {renderComponentPreview(component)}
                            </div>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
            <div style={{ height: '20px' }} />
          </>
        ) : (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Empty description="暂无组件，请从左侧添加" />
          </div>
        )}
      </div>
    </div>
  );
};

// 左侧组件列表区域
const ComponentListArea: React.FC<{
  componentCategories: ComponentCategory[];
  handleAddComponent: (componentType: ComponentType) => void;
}> = ({ componentCategories, handleAddComponent }) => {
  const renderComponentList = () => {
    return (
      <Collapse defaultActiveKey={componentCategories.map((_, index) => index.toString())} ghost>
        {componentCategories.map((category, categoryIndex) => (
          <Panel header={category.categoryName} key={categoryIndex.toString()}>
            <List
              split={false}
              grid={{ column: 2, gutter: 8 }}
              dataSource={category.typeList}
              renderItem={item => (
                <List.Item>
                  <List.Item style={{ padding: '4px 0' }}>
                    <div style={{ width: '100%' }}>
                      <Button
                        block
                        onClick={() => handleAddComponent(item)}
                        title={item.description}
                        size="small"
                        disabled={item.state === 0} // 添加禁用状态判断
                        style={{
                          height: '50px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center',
                          alignItems: 'center',
                          // 当组件被禁用时添加视觉提示
                          opacity: item.state === 0 ? 0.5 : 1,
                          cursor: item.state === 0 ? 'not-allowed' : 'pointer'
                        }}
                      >
                        {item.name}
                        {/*  {item.state === 0 && (
                          <span style={{ fontSize: '10px', color: '#ff4d4f' }}>(未开放)</span>
                        )} */
                        }
                      </Button>
                    </div>
                  </List.Item>
                </List.Item>
              )}
            />
          </Panel>
        ))}
      </Collapse>
    );
  };

  return (
    <div style={{ flex: 1, overflow: 'auto' }}>
      {componentCategories.length > 0 ? (
        renderComponentList()
      ) : (
        <Empty description="暂无组件分类" />
      )}
    </div>
  );
};

// 页面属性编辑区域
const PagePropertyEditor: React.FC<{
  pageConfig: PageConfig;
  setPageConfig: React.Dispatch<React.SetStateAction<PageConfig>>;
  selectedComponentKey: string | null;
}> = ({ pageConfig, setPageConfig, selectedComponentKey }) => {
  return (
    <Form
      key={selectedComponentKey}
      labelCol={{ flex: "100px" }}
      wrapperCol={{ span: 24 }}
    >
      <Row gutter={10}>
        <Col span={layout.col / 2}>
          <Form.Item label="背景类型">
            <Select
              value={pageConfig.background_type || 'solid'}
              options={BackgroundTypeOptions}
              onChange={(value) => setPageConfig(prev => {
                // 根据新的背景类型初始化相关配置
                const newType = value as 'solid' | 'gradient' | 'image';
                const baseConfig = {
                  background_type: newType
                };

                // 根据新类型添加默认配置
                switch (newType) {
                  case 'solid':
                    return {
                      ...baseConfig,
                      background_color: prev.background_color || '#ffffff'
                    };
                  case 'gradient':
                    return {
                      ...baseConfig,
                      gradient_type: prev.gradient_type || 'linear',
                      gradient_colors: prev.gradient_colors || ['#ffffff', '#000000'],
                      ...(prev.gradient_type !== 'radial' && { gradient_angle: prev.gradient_angle || 45 })
                    };
                  case 'image':
                    return {
                      ...baseConfig,
                      background_color: '#ffffff',
                      background_image: prev.background_image || '',
                      background_image_arr: prev.background_image_arr || [],
                      background_image_mode: prev.background_image_mode || 'fit'
                    };
                  default:
                    return baseConfig;
                }
              })}
            />
          </Form.Item>
        </Col>
        <Col span={layout.col / 2} />
        {pageConfig.background_type === 'solid' && (
          <Col span={layout.col / 2}>
            <Form.Item label="背景颜色">
              <ColorPicker
                value={pageConfig.background_color || '#ffffff'}
                onChange={(color) => setPageConfig(prev => ({
                  ...prev,
                  background_color: color.toHexString()
                }))}
                showText
              />
            </Form.Item>
          </Col>
        )}
        {pageConfig.background_type === 'solid' && (
          <Col span={layout.col}>
            <Form.Item label="效果预览">
              <PageBackgroundPreview pageConfig={pageConfig} />
            </Form.Item>
          </Col>
        )}
        {pageConfig.background_type === 'gradient' && (
          <>
            <Col span={layout.col / 2}>
              <Form.Item label="渐变类型">
                <Select
                  value={pageConfig.gradient_type || 'linear'}
                  options={GradientTypeOptions}
                  onChange={(value) => setPageConfig(prev => ({
                    ...prev,
                    gradient_type: value as 'linear' | 'radial'
                  }))}
                />
              </Form.Item>
            </Col>
            <Col />
            {pageConfig.gradient_type === 'linear' && (
              <Col span={layout.col / 2}>
                <Form.Item label="角度">
                  <Slider
                    min={0}
                    max={360}
                    value={pageConfig.gradient_angle || 45}
                    onChange={(value) => setPageConfig(prev => ({
                      ...prev,
                      gradient_angle: value
                    }))}
                  />
                </Form.Item>
              </Col>
            )}
            <Col span={layout.col / 2}>
              <Form.Item label="渐变颜色">
                <div style={{ display: 'flex', gap: 8 }}>
                  <ColorPicker
                    value={pageConfig.gradient_colors?.[0] || '#ffffff'}
                    onChange={(color) => setPageConfig(prev => ({
                      ...prev,
                      gradient_colors: [
                        color.toHexString(),
                        ...(prev.gradient_colors?.slice(1) || ['#000000'])
                      ]
                    }))}
                    showText
                  />
                  <ColorPicker
                    value={pageConfig.gradient_colors?.[1] || '#000000'}
                    onChange={(color) => setPageConfig(prev => ({
                      ...prev,
                      gradient_colors: [
                        ...(prev.gradient_colors?.slice(0, 1) || ['#ffffff']),
                        color.toHexString()
                      ]
                    }))}
                    showText
                  />
                </div>
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="效果预览">
                <PageBackgroundPreview pageConfig={pageConfig} />
              </Form.Item>
            </Col>
          </>
        )}
        {pageConfig.background_type === 'image' && (
          <Col span={layout.col / 2}>
            <>
              <Form.Item label="背景图片">
                <UploadV2
                  value={pageConfig.background_image_arr || []}
                  onChange={(value) => setPageConfig(prev => ({
                    ...prev,
                    background_image_arr: Array.isArray(value) ? value : [value],
                    background_image: UploadV2.arr2str(Array.isArray(value) ? value : [value])
                  }))}
                  maxCount={1}
                />
              </Form.Item>
              <Form.Item label="图片模式">
                <Select
                  value={pageConfig.background_image_mode || 'fit'}
                  options={BackgroundImageModeOptions}
                  onChange={(value) => setPageConfig(prev => ({
                    ...prev,
                    background_image_mode: value as 'stretch' | 'tile' | 'fit' | 'cover' | 'contain' | 'center' | 'top' | 'bottom' | 'left' | 'right'
                  }))}
                />
              </Form.Item>
            </>
          </Col>
        )}
        {pageConfig.background_type === 'image' && (
          <Col span={layout.col}>
            <Form.Item label="效果预览">
              <PageBackgroundPreview pageConfig={pageConfig} />
            </Form.Item>
          </Col>
        )}
      </Row>
    </Form>
  );
};

// 组件属性编辑区域
const ComponentPropertyEditor: React.FC<{
  selectedComponent: PageComponent | null;
  selectedComponentKey: string | null;
  form: any;
  autoSaveComponent: (values: any) => void;
  pageComponents: PageComponent[];
  handleDeleteComponent: (index: number) => void;
  renderComponentConfig: () => React.ReactNode;
}> = ({ selectedComponent, selectedComponentKey, form, autoSaveComponent, renderComponentConfig }) => {
  if (!selectedComponent) {
    return <Empty description="请选择组件进行编辑" />;
  }

  return (
    <Form
      key={selectedComponentKey}
      form={form}
      labelCol={{ flex: "100px" }}
      wrapperCol={{ span: 24 }}
      onValuesChange={(_, allValues) => {
        autoSaveComponent(allValues);
      }}
    >
      <Form.Item name="componentTypeId" label="组件类型" hidden>
        <Input />
      </Form.Item>

      <Row gutter={10}>
        <Col span={layout.col / 2}>
          <Form.Item name={"state"} label="状态" rules={[{ required: true, message: "请输入状态" }]} initialValue={1}>
            <Radio.Group options={StateMap} />
          </Form.Item>
        </Col>
        <Col span={layout.col}>
          <Form.Item label="时限" required>
            <div style={{ display: "flex", alignItems: "center" }}>
              <div style={{ marginRight: 20 }}>
                <Form.Item noStyle name={"timeLimit"} valuePropName="checked" initialValue={false} >
                  <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                </Form.Item>
              </div>
              <div style={{ flex: 1 }}>
                <Form.Item noStyle dependencies={["timeLimit"]}>
                  {(form) => {
                    const value = form.getFieldValue("timeLimit");
                    return (
                      !!value && (
                        <Form.Item
                          noStyle
                          name={"limitDate"}
                          initialValue={[]}
                          rules={[
                            {
                              required: true,
                              message: "请选择限时时间",
                            },
                          ]}
                        >
                          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} showTime />
                        </Form.Item>
                      )
                    );
                  }}
                </Form.Item>
              </div>
            </div>
          </Form.Item>
        </Col>
      </Row>

      {renderComponentConfig()}
    </Form>
  );
};

// ==================== 主组件 ====================
const VisualEditorMain: React.FC<{ pageConfigId: string }> = ({ pageConfigId }) => {
  // ==================== 状态管理 ====================
  const [componentCategories, setComponentCategories] = useState<ComponentCategory[]>([]);
  const [pageComponents, setPageComponents] = useState<PageComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<PageComponent | null>(null);
  const [selectedComponentKey, setSelectedComponentKey] = useState<string | null>(null);
  const [form] = Form.useForm();
  const [isEditing, setIsEditing] = useState(false);
  // 页面配置状态（包含背景色和其他可能的属性）
  const [pageConfig, setPageConfig] = useState<PageConfig>({
    background_type: 'solid',
    background_color: '#ffffff',
    background_gradient: '',
    gradient_type: 'linear',
    gradient_direction: 'to right',
    gradient_colors: ['#ffffff', '#000000'],
    gradient_angle: 45,
    background_image: '',
    background_image_mode: 'fit'
  });

  // ==================== 数据获取函数 ====================
  const fetchComponentTypes = useCallback(async () => {
    try {
      const res = await PlatformIndexComponentTypeApi.getPlatformIndexComponentTypeTree();
      setComponentCategories(res?.list || []);
    } catch (error) {
      message.error('获取组件类型失败');
    }
  }, []);

  const fetchPageComponents = useCallback(async () => {
    try {
      const res = await PlatformIndexPageComponentApi.getPlatformIndexPageComponent({
        params: { pageConfigId, pageSize: 100 }
      });
      setPageComponents(res?.list || []);
    } catch (error) {
      message.error('获取页面组件失败');
    }
  }, [pageConfigId]);

  const fetchPageConfigInfo = useCallback(async () => {
    try {
      const res = await PlatformIndexPageConfigApi.getPlatformIndexPageConfigInfo(pageConfigId);
      if (res?.configDataJson) {
        // 根据背景类型过滤相关配置项
        const configData = res.configDataJson;
        let filteredConfig: Partial<PageConfig> = {
          background_type: configData.background_type || 'solid'
        };

        // 根据背景类型保留相关配置项
        switch (configData.background_type) {
          case 'solid':
            filteredConfig = {
              ...filteredConfig,
              background_color: configData.background_color || '#ffffff'
            };
            break;
          case 'gradient':
            filteredConfig = {
              ...filteredConfig,
              gradient_type: configData.gradient_type || 'linear',
              gradient_colors: configData.gradient_colors || ['#ffffff', '#000000'],
              ...(configData.gradient_type !== 'radial' && { gradient_angle: configData.gradient_angle || 45 })
            };
            break;
          case 'image':
            filteredConfig = {
              ...filteredConfig,
              background_color: configData.background_color || '#ffffff', // 背景图模式下也保留背景色
              background_image: configData.background_image || '',
              background_image_arr: configData.background_image ? UploadV2.str2arr(configData.background_image) : [],
              background_image_mode: configData.background_image_mode || 'fit'
            };
            break;
        }

        setPageConfig({
          // 默认值
          background_type: 'solid',
          background_color: '#ffffff',
          background_gradient: '',
          gradient_type: 'linear',
          gradient_direction: 'to right',
          gradient_colors: ['#ffffff', '#000000'],
          gradient_angle: 45,
          background_image: '',
          background_image_arr: [],
          background_image_mode: 'fit',
          ...filteredConfig
        });
      }
    } catch (error) {
      message.error('获取页面配置信息失败');
    }
  }, [pageConfigId]);

  // ==================== 数据处理函数 ====================
  // 处理配置数据中的图片和颜色（正向处理：表单数据 -> 存储数据）
  const processConfigData = useCallback((obj: any): any => {
    if (!obj) return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => processConfigData(item));
    }

    if (typeof obj === 'object') {
      const processedObj: any = {};

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];

          if (key === 'image' && Array.isArray(value)) {
            processedObj[key] = UploadV2.arr2str(value);
          } else if (key === 'icon' && Array.isArray(value)) {
            processedObj[key] = UploadV2.arr2str(value);
          } else if (key === 'video_url' && Array.isArray(value)) {
            processedObj[key] = UploadV2.arr2str(value);
          } else if (key === 'poster' && Array.isArray(value)) {
            processedObj[key] = UploadV2.arr2str(value);
          } else if (key === 'color' && value && typeof value.toHexString === 'function') {
            processedObj[key] = value.toHexString();
          } else if (key === 'bg_color' && value && typeof value.toHexString === 'function') {
            processedObj[key] = value.toHexString();
          } else if (key === 'text_color' && value && typeof value.toHexString === 'function') {
            processedObj[key] = value.toHexString();
          } else if (key === 'border_color' && value && typeof value.toHexString === 'function') {
            processedObj[key] = value.toHexString();
          } else if (key === 'background_color' && value && typeof value.toHexString === 'function') {
            processedObj[key] = value.toHexString();
          } else if (typeof value === 'object' && value !== null) {
            processedObj[key] = processConfigData(value);
          } else {
            processedObj[key] = value;
          }
        }
      }

      return processedObj;
    }

    return obj;
  }, []);

  // 处理配置数据中的图片和颜色（反向处理：存储数据 -> 表单数据）
  const processConfigDataReverse = (obj: any): any => {
    if (!obj) return obj;

    if (Array.isArray(obj)) {
      return obj.map(item => processConfigDataReverse(item));
    }

    if (typeof obj === 'object') {
      const processedObj: any = {};

      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          const value = obj[key];

          if (key === 'image' && typeof value === 'string') {
            processedObj[key] = UploadV2.str2arr(value);
          } else if (key === 'icon' && typeof value === 'string') {
            processedObj[key] = UploadV2.str2arr(value);
          } else if (key === 'video_url' && typeof value === 'string') {
            processedObj[key] = UploadV2.str2arr(value);
          } else if (key === 'poster' && typeof value === 'string') {
            processedObj[key] = UploadV2.str2arr(value);
          } else if (key === 'color' && typeof value === 'string') {
            processedObj[key] = value;
          } else if (key === 'bg_color' && typeof value === 'string') {
            processedObj[key] = value;
          } else if (key === 'text_color' && typeof value === 'string') {
            processedObj[key] = value;
          } else if (key === 'border_color' && typeof value === 'string') {
            processedObj[key] = value;
          } else if (key === 'background_color' && typeof value === 'string') {
            processedObj[key] = value;
          } else if (typeof value === 'object' && value !== null) {
            processedObj[key] = processConfigDataReverse(value);
          } else {
            processedObj[key] = value;
          }
        }
      }

      return processedObj;
    }

    return obj;
  };

  // 统一的数据处理函数
  const processComponentDataForForm = (component: PageComponent): FormPageComponent => {
    const formComponent: FormPageComponent = { ...component };
    if (component.timeLimit === 1 && component.startDate && component.endDate) {
      formComponent.limitDate = [dayjs(component.startDate), dayjs(component.endDate)];
    }
    formComponent.configDataJson = processConfigDataReverse(formComponent.configDataJson);
    return formComponent;
  };

  // ==================== 组件操作函数 ====================
  // 添加组件到页面
  const handleAddComponent = (componentType: ComponentType) => {

    setIsEditing(false);

    const newComponent: PageComponent = {
      componentTypeId: componentType.id,
      componentTypeName: componentType.name,
      componentTypeEnCode: componentType.enCode,
      state: 1,
      sort: pageComponents.length + 1,
      timeLimit: 0,
      configDataJson: {}
    };

    // 设置组件特定的初始值
    const componentInitialValues = getComponentInitialValues(componentType.enCode);

    if (componentInitialValues) {
      newComponent.configDataJson = componentInitialValues;
    }

    setPageComponents(prev => [...prev, newComponent]);
    // 生成一个唯一的key来强制刷新表单
    const componentKey = `${newComponent.componentTypeId}-${newComponent.sort}-${Date.now()}`;
    setSelectedComponentKey(componentKey);
    setSelectedComponent(newComponent);
    setIsEditing(true);

    // 设置表单初始值
    const initialValues: any = {
      ...newComponent,
      limitDate: []
    };

    // 处理表单初始值
    const formInitialValues = processComponentDataForForm(initialValues);
    form.setFieldsValue(formInitialValues);
  };

  // 选择组件进行编辑
  const handleSelectComponent = (component: PageComponent) => {
    // 生成一个唯一的key来强制刷新表单
    const componentKey = `${component.componentTypeId}-${component.sort}-${Date.now()}`;
    setSelectedComponentKey(componentKey);
    setSelectedComponent(component);
    setIsEditing(true);

    // 先设置初始值（清空表单）
    form.resetFields();

    // 处理表单初始值
    const initialValues = processComponentDataForForm(component);
    form.setFieldsValue(initialValues);
  };

  // 删除组件
  const handleDeleteComponent = (index: number) => {
    const updatedComponents = [...pageComponents];
    updatedComponents.splice(index, 1);

    // 重新排序
    const reindexedComponents = updatedComponents.map((item, idx) => ({
      ...item,
      sort: idx + 1
    }));

    setPageComponents(reindexedComponents);
    if (selectedComponent === pageComponents[index]) {
      setSelectedComponent(null);
      setIsEditing(false);
    }
  };

  // ==================== 事件处理函数 ====================
  // 处理拖拽结束事件
  const handleDragEnd = (result: DropResult) => {
    if (!result.destination) return;

    // 只对非悬浮组件进行排序
    const nonFloatingComponents = pageComponents.filter(component =>
      component.componentTypeEnCode !== 'floating_component'
    );

    const floatingComponents = pageComponents.filter(component =>
      component.componentTypeEnCode === 'floating_component'
    );

    if (result.source.index < nonFloatingComponents.length &&
      result.destination.index < nonFloatingComponents.length) {
      // 只有当拖拽的是非悬浮组件时才进行排序
      const items = Array.from(nonFloatingComponents);
      const [reorderedItem] = items.splice(result.source.index, 1);
      items.splice(result.destination.index, 0, reorderedItem);

      // 重新分配sort值
      const updatedNonFloatingComponents = items.map((item, index) => ({
        ...item,
        sort: index + 1
      }));

      // 合并所有组件（悬浮组件保持原样）
      //   .sort((a, b) => a.sort - b.sort);
      const updatedComponents = [...updatedNonFloatingComponents, ...floatingComponents]
        .sort((a, b) => a.sort - b.sort);

      setPageComponents(updatedComponents);

      // 更新选中组件
      if (selectedComponent) {
        const updatedSelectedComponent = updatedComponents.find(
          item => item.componentTypeId === selectedComponent.componentTypeId &&
            item.sort === selectedComponent.sort
        );
        if (updatedSelectedComponent) {
          setSelectedComponent(updatedSelectedComponent);
        }
      }
    }
  };

  // ==================== 表单处理函数 ====================
  // 自动保存组件配置（防抖处理）
  const autoSaveComponent = useCallback(
    debounce(async (values: any) => {
      if (!selectedComponent) return;

      console.log('自动保存:', values);

      try {
        // 深拷贝 values 以避免修改原始对象
        const processedValues = { ...values };

        processedValues.timeLimit = Number(processedValues.timeLimit);
        if (processedValues.timeLimit === 1 && processedValues.limitDate) {
          const limitDate = formatDateRange(processedValues.limitDate, "YYYY-MM-DD HH:mm:ss");
          processedValues.startDate = limitDate.start;
          processedValues.endDate = limitDate.end;
        }
        delete processedValues.limitDate;

        // 处理配置数据中的图片和颜色
        if (processedValues.configDataJson) {
          processedValues.configDataJson = processConfigData(processedValues.configDataJson);
        }

        // 更新页面组件列表 - 使用 componentTypeId 和 sort 来定位组件
        const updatedComponents = pageComponents.map(item => {
          if (item.componentTypeId === selectedComponent.componentTypeId &&
            item.sort === selectedComponent.sort) {
            return { ...item, ...processedValues };
          }
          return item;
        });

        setPageComponents(updatedComponents);
        // 更新选中的组件
        setSelectedComponent(prev => {
          if (!prev) return null;
          return { ...prev, ...processedValues };
        });
      } catch (error) {
        message.error('自动保存失败');
      }
    }, 1000),
    [pageComponents, selectedComponent, processConfigData]
  );

  // ==================== 提交处理函数 ====================
  // 提交所有组件
  const handleSubmitAll = async () => {
    try {

      //非悬浮组件
      const nonFloatingComponents = pageComponents.filter(component =>
        component.componentTypeEnCode !== 'floating_component'
      );

      //悬浮组件
      const floatingComponents = pageComponents.filter(component =>
        component.componentTypeEnCode === 'floating_component'
      );

      // 创建新的组件列表，先放非悬浮组件，再放悬浮组件
      let reorderedComponents = [...nonFloatingComponents, ...floatingComponents];

      // 重新分配sort值
      const finalComponents = reorderedComponents.map((item, index) => ({
        ...item,
        sort: index + 1
      }));


      const params = {
        pageConfigId: pageConfigId,
        componentList: finalComponents.map(component => ({
          componentTypeId: component.componentTypeId,
          timeLimit: component.timeLimit,
          startDate: component.startDate,
          endDate: component.endDate,
          state: component.state,
          configDataJson: component.configDataJson,
        })) as ComponentSubmitData[]
      };

      //去除params中的null节点
      const removeNullNodes = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return undefined;
        }
        if (Array.isArray(obj)) {
          return obj.map(item => removeNullNodes(item)).filter(item => item !== undefined);
        }
        if (typeof obj === 'object') {
          const cleanedObj: any = {};
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              const cleanedValue = removeNullNodes(obj[key]);
              if (cleanedValue !== undefined && cleanedValue != null) {
                cleanedObj[key] = cleanedValue;
              }
            }
          }
          return Object.keys(cleanedObj).length > 0 ? cleanedObj : undefined;
        }
        return obj;
      };

      // 清理参数中的null和undefined值
      const cleanedParams = removeNullNodes(params);
      console.log(cleanedParams);

      // 保存组件配置
      await PlatformIndexPageComponentApi.savePlatformIndexPageComponent({ data: cleanedParams });

      // 改为根据背景类型选择性传递配置项：
      let filteredPageConfig: Partial<PageConfig> = {
        background_type: pageConfig.background_type
      };

      if (pageConfig.background_type === 'solid') {
        filteredPageConfig = {
          background_type: pageConfig.background_type,
          background_color: pageConfig.background_color
        };
      } else if (pageConfig.background_type === 'gradient') {
        filteredPageConfig = {
          background_type: pageConfig.background_type,
          gradient_type: pageConfig.gradient_type,
          gradient_colors: pageConfig.gradient_colors,
          ...(pageConfig.gradient_type === 'linear' && { gradient_angle: pageConfig.gradient_angle })
        };
      } else if (pageConfig.background_type === 'image') {
        filteredPageConfig = {
          background_color: pageConfig.background_color,
          background_type: pageConfig.background_type,
          background_image: pageConfig.background_image,
          background_image_mode: pageConfig.background_image_mode
        };
      }

      // 同时保存页面配置（包括所有页面配置属性）
      await PlatformIndexPageConfigApi.putPlatformIndexPageConfig({
        data: {
          id: pageConfigId,
          configDataJson: filteredPageConfig
        }
      });

      message.success('保存成功');
    } catch (error) {
      message.error('保存失败: ' + (error as Error).message);
    }
  };

  // ==================== 渲染函数 ====================
  // 根据组件类型渲染配置组件
  const renderComponentConfig = () => {
    if (!selectedComponent) return null;

    switch (selectedComponent.componentTypeEnCode) {
      case "banner":
        return <BannerConfig form={form} formChange={autoSaveComponent} />;
      case "navigation":
        return <NavigationConfig form={form} formChange={autoSaveComponent} />;
      case "image_single":
        return <ImageSingleConfig form={form} formChange={autoSaveComponent} />;
      case "image_double":
        return <ImageDoubleConfig form={form} formChange={autoSaveComponent} />;
      case "image_triple":
        return <ImageTripleConfig form={form} formChange={autoSaveComponent} />;
      case "image_quadruple":
        return <ImageQuadrupleConfig form={form} formChange={autoSaveComponent} />;
      case "video":
        return <VideoConfig form={form} />;
      case "search_bar":
        return <SearchBarConfig form={form} />;
      case "divider":
        return <DividerConfig form={form} />;
      case "notice":
        return <NoticeConfig form={form} />;
      case "product_list":
        return <ProductListConfig form={form} />;
      case "group":
        return <GroupConfig form={form} />;
      case "seckill":
        return <SeckillConfig form={form} />;
      case "chop":
        return <ChopConfig form={form} />;
      case "floating_component":
        return <FloatingComponentConfig form={form} formChange={autoSaveComponent} />;
      default:
        return <div>未知组件类型</div>;
    }
  };

  // 根据组件类型渲染可视化预览组件
  const renderComponentVisual = (component: PageComponent) => {
    switch (component.componentTypeEnCode) {
      case "banner":
        return <BannerVisual configDataJson={component.configDataJson} />;
      case "navigation":
        return <NavigationVisual configDataJson={component.configDataJson} />;
      case "image_single":
        return <ImageSingleVisual configDataJson={component.configDataJson} />;
      case "image_double":
        return <ImageDoubleVisual configDataJson={component.configDataJson} />;
      case "image_triple":
        return <ImageTripleVisual configDataJson={component.configDataJson} />;
      case "image_quadruple":
        return <ImageQuadrupleVisual configDataJson={component.configDataJson} />;
      case "video":
        return <VideoVisual configDataJson={component.configDataJson} />;
      case "search_bar":
        return <SearchBarVisual configDataJson={component.configDataJson} />;
      case "divider":
        return <DividerVisual configDataJson={component.configDataJson} />;
      case "notice":
        return <NoticeVisual configDataJson={component.configDataJson} />;
      case "product_list":
        return <ProductListVisual configDataJson={component.configDataJson} />;
      case "group":
        return <GroupVisual configDataJson={component.configDataJson} />;
      case "seckill":
        return <SeckillVisual configDataJson={component.configDataJson} />;
      case "chop":
        return <ChopVisual configDataJson={component.configDataJson} />;
      case "floating_component":
        return <FloatingComponentVisual configDataJson={component.configDataJson} />;
      default:
        return <div>{component.componentTypeName} 预览区域</div>;
    }
  };

  // 渲染组件预览
  const renderComponentPreview = (component: PageComponent) => {
    const isSelected = selectedComponent &&
      selectedComponent.componentTypeId === component.componentTypeId &&
      selectedComponent.sort === component.sort;

    // 根据state值确定选中颜色
    const selectedColor = component?.state === 0 ? '#ff4d4f' : '#1890ff';

    return (
      <div
        onClick={() => handleSelectComponent(component)}
        style={{
          cursor: 'pointer',
          position: 'relative',
          border: '2px solid transparent', // 预留边框空间，避免选中时尺寸变化
          borderRadius: '4px',
          boxSizing: 'border-box',
          marginLeft: component.configDataJson?.margin_x === "side" ? `${MarginX}px` : '0px',
          marginRight: component.configDataJson?.margin_x === "side" ? `${MarginX}px` : '0px',
          marginTop: component.configDataJson?.margin_top ? `${component.configDataJson.margin_top}px` : '0px'
        }}
      >
        {/* 选中标识 */}
        {isSelected && (
          <>
            <div style={{
              position: 'absolute',
              left: '0',
              backgroundColor: selectedColor,
              color: 'white',
              fontSize: '12px',
              padding: '0px 6px',
              borderRadius: '2px',
              zIndex: 100
            }}>
              {component.componentTypeName}
            </div>
            {/* 删除图标 */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                const index = pageComponents.findIndex(item =>
                  item.componentTypeId === component.componentTypeId &&
                  item.sort === component.sort
                );
                if (index !== -1) {
                  handleDeleteComponent(index);
                }
              }}
              style={{
                position: 'absolute',
                right: '-8px', // 调整位置以适应新的边框空间
                top: '-8px', // 调整位置以适应新的边框空间
                width: '15px',
                height: '15px',
                backgroundColor: '#ff4d4f',
                borderRadius: '50%',

                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                zIndex: 101,
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            >
              <CloseOutlined style={{ color: 'white', fontSize: '12px' }} />
            </div>
            {/* 选中状态边框 */}
            <div style={{
              position: 'absolute',
              top: '-2px',
              left: '-2px',
              right: '-2px',
              bottom: '-2px',
              border: `1px dashed ${selectedColor}`,
              borderRadius: '4px',
              zIndex: 99,
              pointerEvents: 'none' // 防止影响点击事件
            }} />
          </>
        )}

        {/* 组件可视化内容 */}
        <div style={{
          minHeight: 1,
        }}>
          {renderComponentVisual(component)}
        </div>
      </div>
    );
  };

  // 渲染悬浮组件预览（仅限图标区域可点击）
  const renderFloatingComponentPreview = (component: PageComponent) => {
    const isSelected = selectedComponent &&
      selectedComponent.componentTypeId === component.componentTypeId &&
      selectedComponent.sort === component.sort;

    // 根据state值确定选中颜色
    const selectedColor = component?.state === 0 ? '#ff4d4f' : '#1890ff';

    // 计算位置 - 使用更通用的类型
    let positionStyle: React.CSSProperties; // 使用更通用的CSS样式类型

    // 获取组件配置的宽高
    const containerWidth = component.configDataJson?.container_width || 60;
    const containerHeight = component.configDataJson?.container_height || 60;

    if (component.componentTypeEnCode === 'floating_component') {
      const position = component.configDataJson?.position || 'rightBottom';

      // 根据位置设置样式
      switch (position) {
        case 'leftTop':
          positionStyle = {
            left: component.configDataJson?.position_left !== undefined ? `${component.configDataJson.position_left}px` : '25px',
            top: component.configDataJson?.position_top !== undefined ? `${component.configDataJson.position_top}px` : '25px'
          };
          break;
        case 'rightTop':
          positionStyle = {
            right: component.configDataJson?.position_right !== undefined ? `${component.configDataJson.position_right}px` : '25px',
            top: component.configDataJson?.position_top !== undefined ? `${component.configDataJson.position_top}px` : '25px'
          };
          break;
        case 'leftBottom':
          positionStyle = {
            left: component.configDataJson?.position_left !== undefined ? `${component.configDataJson.position_left}px` : '25px',
            bottom: component.configDataJson?.position_bottom !== undefined ? `${component.configDataJson.position_bottom + 60}px` : '110px'
          };
          break;
        case 'rightBottom':
        default:
          positionStyle = {
            right: component.configDataJson?.position_right !== undefined ? `${component.configDataJson.position_right}px` : '25px',
            bottom: component.configDataJson?.position_bottom !== undefined ? `${component.configDataJson.position_bottom + 60}px` : '110px'
          };
          break;
      }
    } else {
      // 默认情况
      positionStyle = { right: '10px', bottom: '110px' };
    }

    return (
      <div
        onClick={(e) => {
          e.stopPropagation(); // 阻止事件冒泡
          handleSelectComponent(component);
        }}
        style={{
          position: 'absolute',
          ...positionStyle,
          cursor: 'pointer',
          display: 'inline-block',
          zIndex: 100, // 确保在最上层
          // 使用组件配置的宽高
          width: `${containerWidth}px`,
          height: `${containerHeight}px`,
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {/* 选中状态 - 参考普通组件的样式 */}
        {isSelected && (
          <>
            {/* 与普通组件相同的边框和阴影效果 */}
            <div style={{
              position: 'absolute',
              top: '-2px',
              left: '-2px',
              right: '-2px',
              bottom: '-2px',
              border: `1px dashed ${selectedColor}`,
              borderRadius: '4px',
              zIndex: 99,
            }} />
            {/* 选中标识文字 */}
            <div>
              {renderComponentVisual(component)}
            </div>
            {/* 选中标识 */}
            <div style={{
              position: 'absolute',
              left: '0',
              backgroundColor: selectedColor,
              color: 'white',
              fontSize: '12px',
              padding: '0px 6px',
              borderRadius: '2px',
              zIndex: 100
            }}>
              {/*  {component.componentTypeName} */

              }
            </div>
            {/* 删除图标 */}
            <div
              onClick={(e) => {
                e.stopPropagation();
                const index = pageComponents.findIndex(item =>
                  item.componentTypeId === component.componentTypeId &&
                  item.sort === component.sort
                );
                if (index !== -1) {
                  handleDeleteComponent(index);
                }
              }}
              style={{
                position: 'absolute',
                right: '-8px',
                top: '-8px',
                width: '15px',
                height: '15px',
                backgroundColor: '#ff4d4f',
                borderRadius: '50%',

                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
                zIndex: 101,
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}
            >
              <CloseOutlined style={{ color: 'white', fontSize: '12px' }} />
            </div>
          </>
        )}

        {/* 悬浮组件本身 */}
        {!isSelected && (
          <div>
            {renderComponentVisual(component)}
          </div>
        )}
      </div>
    );
  };

  // ==================== 副作用钩子 ====================
  // 初始化数据
  useEffect(() => {
    fetchComponentTypes();
    fetchPageComponents();
    fetchPageConfigInfo();
  }, [fetchComponentTypes, fetchPageComponents, fetchPageConfigInfo]);

  // 添加键盘事件监听，用于删除选中的组件
  // 在 useEffect 中修改键盘事件处理函数
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 检查是否有输入框获得焦点
      const activeElement = document.activeElement;
      const isInputFocused = activeElement?.tagName === 'INPUT' ||
        activeElement?.tagName === 'TEXTAREA' ||
        activeElement?.hasAttribute('contenteditable');

      // 只有在没有输入框获得焦点且有选中组件时才执行删除操作
      if (e.key === 'Delete' && selectedComponent && !isInputFocused) {
        const index = pageComponents.findIndex(item =>
          item.componentTypeId === selectedComponent.componentTypeId &&
          item.sort === selectedComponent.sort
        );
        if (index !== -1) {
          handleDeleteComponent(index);
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown);

    // 清理事件监听器
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedComponent, pageComponents, handleDeleteComponent]);

  // 点击手机界面外侧取消选中状态
  // 修改现有的点击监听 useEffect 钩子
  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      // 检查点击是否在中间预览区域但不在手机预览区域内
      const middlePreviewArea = document.querySelector('.ant-col-10 .ant-card-body'); // 中间预览区域
      const phonePreview = document.querySelector('.phone-preview-container'); // 手机预览区域

      if (middlePreviewArea &&
        middlePreviewArea.contains(e.target as Node) &&
        phonePreview &&
        !phonePreview.contains(e.target as Node) &&
        selectedComponent) {
        setSelectedComponent(null);
        setIsEditing(false);
      }
    };

    document.addEventListener('click', handleDocumentClick);

    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [selectedComponent]);

  // ==================== 主渲染 ====================
  return (
    <div style={{ padding: 0 }}>
      <Row gutter={16} style={{ height: 'calc(100vh - 120px)', overflow: 'hidden' }}>
        {/* 左侧组件列表（按分类分组）*/}

        <Col span={4} style={{ height: '100%', overflow: 'hidden' }}>

          <Card title="组件列表" size="small" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, overflow: 'auto' }}>

            <ComponentListArea
              componentCategories={componentCategories}
              handleAddComponent={handleAddComponent}
            />
          </Card>
        </Col>

        {/* 中间预览区域 */}

        <Col span={10} style={{ height: '100%', overflow: 'hidden' }}>

          <Card
            title="首页预览"
            size="small"
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, overflow: 'hidden', padding: 10 }}
            extra={
              <Button type="primary" onClick={handleSubmitAll}>
                保存所有组件
              </Button>
            }
          >
            <PhonePreviewContainer>
              {/* 底部导航栏 - 固定在手机预览容器底部 */}
              <BottomNavigationBar />

              {/* 悬浮组件容器（客服和电话）- 固定定位 */}
              <FloatingComponentsContainer
                pageComponents={pageComponents}
                renderFloatingComponentPreview={renderFloatingComponentPreview}
              />

              <MainPreviewArea
                pageConfig={pageConfig}
                pageComponents={pageComponents}
                renderComponentPreview={renderComponentPreview}
                handleDragEnd={handleDragEnd}
              />
            </PhonePreviewContainer>
          </Card>
        </Col>

        {/* 右侧属性编辑区域 */}

        <Col span={10} style={{ height: '100%', overflow: 'hidden' }}>

          <Card
            title={selectedComponent ? `${selectedComponent.componentTypeName} 属性` : "页面 属性"}
            size="small"
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, overflow: 'auto' }}
            extra={
              isEditing && selectedComponent ? (
                <Button
                  danger
                  onClick={() => {
                    const index = pageComponents.findIndex(item =>
                      item.componentTypeId === selectedComponent.componentTypeId &&
                      item.sort === selectedComponent.sort
                    );
                    if (index !== -1) {
                      handleDeleteComponent(index);
                    }
                  }}
                >
                  删除
                </Button>
              ) : null
            }
          >
            <div>
              {/* 页面配置设置 - 仅在未选中组件时显示 */}
              {!selectedComponent && (
                <PagePropertyEditor
                  pageConfig={pageConfig}
                  setPageConfig={setPageConfig}
                  selectedComponentKey={selectedComponentKey}
                />
              )}

              {isEditing && selectedComponent ? (
                <ComponentPropertyEditor
                  selectedComponent={selectedComponent}
                  selectedComponentKey={selectedComponentKey}
                  form={form}
                  autoSaveComponent={autoSaveComponent}
                  pageComponents={pageComponents}
                  handleDeleteComponent={handleDeleteComponent}
                  renderComponentConfig={renderComponentConfig}
                />
              ) : selectedComponent ? (
                <Empty description="请选择组件进行编辑" />
              ) : null}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default VisualEditorMain;