import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { Col, Form, Input, Radio, Row, message } from "antd";

const layout = { row: 10, col: 24 };

const ModalUnCheck = (props: { children: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.id = props.data.id;
    data.dealPaper = ImageUpload.deserializer(data.dealPaper);

    await MallApi.refuseWithdraw({ data });
    message.success("拒绝操作成功");
    props.onOk();
  };

  return (
    <WeModal
      trigger={props.children}
      title={"拒绝申请"}
      onOpen={handleOpen}
      onOk={handleSubmit}
      width={400}
      okText={"确认拒绝"}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label={`拒绝备注`} name={`dealRemark`}>
              <Input.TextArea placeholder={`请输入拒绝备注`} autoSize={{ minRows: 2, maxRows: 4 }} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="退回资金" name={`moneyBackTag`} initialValue={1}>
              <Radio.Group
                options={[
                  { label: "退回", value: 1 },
                  { label: "不退回", value: 0 },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalUnCheck;
