import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { renderTag } from "@/utils/Tools";
import { Button, DatePicker, Form, Image, Input, Select, Space, Typography } from "antd";
import dayjs from "dayjs";
import { useRef } from "react";
import ModalCheck from "./ModalCheck";
import { useSetState } from "react-use";
import ModalUnCheck from "./ModalUnCheck";
import { DatePresetRanges } from "@/utils/Tools";
import UserManager from "@/components/UserManager";
import { Rule } from "./Rule";
import { PaperClipOutlined } from "@ant-design/icons";

const StateMap = [
  { id: 10, name: "申请中", color: "cyan" },
  { id: 20, name: "已打款", color: "green" },
  { id: 30, name: "拒绝", color: "red" },
  { id: 40, name: "取消", color: "gray" },
];

const MoneyDestinationMap = [
  { id: 2, name: "微信" },
  { id: 3, name: "支付宝" },
  { id: 10, name: "现金" },
  { id: 11, name: "银行卡" },
];

const SourceMap = [
  { id: 10, name: "用户余额" },
  { id: 11, name: "M币" },
  { id: 20, name: "门店余额" },
  { id: 30, name: "公司余额" },
];

const WithdrawLog = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    imgs: [] as any[],
    imgShow: false,
    total: {} as any,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };


  const fetchTotal = async (p: any) => {
    const res = await MallApi.getWithdrawTotal({ params: p });
    console.log(res);
    setState({ total: res });
    // console.log(res);
  };


  return (
    <>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        request={(p) => {
          fetchTotal(p);
          return MallApi.getWithdrawList({ params: p });
        }}
        title={
          <>
            <Rule onOk={handleReload}>
              <Button type="primary" icon={<PaperClipOutlined />}>提现规则</Button>
            </Rule>
          </>
        }
        extra={
          <>
            <div className="text-(14px #000/80) fw-bold">合计：{state.total?.totalMoney ?? "--"}元</div>
          </>
        }
        search={[
          <Form.Item label="申请时间" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
          <Form.Item label="申请方名称" name={`applyName`}>
            <Input placeholder="请输入申请方名称" />
          </Form.Item>,
          <Form.Item label="申请方手机号" name={`applyMobile`}>
            <Input placeholder="请输入申请方手机号" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select
              options={StateMap}
              fieldNames={{ label: "name", value: "id" }}
              placeholder="请选择状态"
              allowClear
            />
          </Form.Item>,
          <Form.Item label="资金去向" name={`moneyDestination`}>
            <Select
              options={MoneyDestinationMap}
              allowClear
              fieldNames={{ label: "name", value: "id" }}
              placeholder="请选择资金去向"
            />
          </Form.Item>,
        ]}
        columns={[
          {
            fixed: "left",
            title: "申请方",
            render: (item) => (
              <div className="flex items-center gap-1">
                <UserManager userId={item?.applyId}>
                  <a>{item.applyName}</a>
                </UserManager>
              </div>
            ),
          },
          { title: "申请手机号", dataIndex: "applyMobile" },

          { title: "申请来源", dataIndex: "source", render: (c) => SourceMap.find((n) => n.id == c)?.name },
          { title: "申请金额", dataIndex: "applyMoney" },
          { title: "到账金额", dataIndex: "incomeMoney" },
          {
            title: "资金去向",
            dataIndex: "moneyDestination",
            render: (c) => MoneyDestinationMap.find((n) => n.id == c)?.name,
          },
          {
            title: "申请时间",
            dataIndex: "applyTime",
            render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
          },
          { title: "处理人", dataIndex: "dealUserName", render: (c) => c || "--" },
          {
            title: "处理时间",
            dataIndex: "dealTime",
            render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
          },
          {
            title: "处理备注",
            dataIndex: "dealRemark",
            render: (c) => (
              c ?
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
                : "--"
            ),
          },
          {
            title: "打款凭证",
            dataIndex: "dealPaper",
            render: (c) => {
              const arr: any[] = (c || "").split(",").filter((n: any) => n);
              return arr.length ? (
                <a
                  onClick={() => {
                    setState({ imgs: arr, imgShow: true });
                  }}
                >
                  点击查看({arr.length}张)
                </a>
              ) : (
                "--"
              );
            },
          },
          { fixed: "right", title: "状态", dataIndex: "state", render: (c) => renderTag(StateMap, c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => (
              item.dealMode == 2 && (
                <Space>
                  <ModalCheck onOk={handleReload} data={item}>
                    <Typography.Link disabled={!([10].includes(item.state))}>通过</Typography.Link>
                  </ModalCheck>

                  <ModalUnCheck onOk={handleReload} data={item}>
                    <Typography.Link disabled={!([10].includes(item.state))}>拒绝</Typography.Link>
                  </ModalUnCheck>
                </Space>
              )
            ),
          },
        ]}
      />
      <Image.PreviewGroup
        preview={{
          visible: state.imgShow,
          current: 0,
          onVisibleChange: (val) => setState({ imgShow: val }),
        }}
      >
        <div style={{ display: "none" }}>
          {state.imgs.map((n) => (
            <Image src={n} />
          ))}
        </div>
      </Image.PreviewGroup>
    </>
  );
};

export default WithdrawLog;
