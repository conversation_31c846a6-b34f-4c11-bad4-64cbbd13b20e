import WeModal from "@/components/WeModal/WeModal";
import { Checkbox, Col, Divider, Form, Input, InputNumber, message, Row } from "antd";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { DayMap } from "./types";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    dayLast: [] as any[]
  });

  const onOk = async () => {
    const data = await form.validateFields();
    if (data.integralWithdrawDates) {
      if (Array.isArray(data.integralWithdrawDates)) {
        data.integralWithdrawDates = data.integralWithdrawDates.join(",");
      }
    }
    await MallApi.putWithdrawRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await MallApi.getWithdrawRule();
    data.integralWithdrawDates = data.integralWithdrawDates ? data.integralWithdrawDates.split(",") : [];
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="提现规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "150px" }}>
        <Row gutter={layout.row}>
          <Divider orientation="left">搞美币提现</Divider>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawMin`} label="最低提现"  >
              <InputNumber placeholder="请输入最低提现M币 " addonAfter="M币" min={0.1} step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawMax`} label="最高提现" >
              <InputNumber placeholder="请输入最高提现M币 " addonAfter="M币" min={0.1} step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawDaytime`} label="每日最多提现次数" >
              <InputNumber placeholder="请输入每日最多提现次数 " addonAfter="次" min={0} step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawInterval`} label="距前一次提现间隔时间" >
              <InputNumber placeholder="请输入间隔时间 " addonAfter="分钟" min={1} step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawDates`} label="提现号数">
              <Checkbox.Group
                options={DayMap}
                onChange={value => {
                  let dayLast = [] as any[];
                  if (value.includes("L") && value.length > 1) {
                    let _dayLast;
                    if (state.dayLast.includes("L")) {
                      _dayLast = value.filter(item => item != "L");
                    } else {
                      _dayLast = value.filter(item => item == "L");
                    }
                    dayLast = _dayLast;
                  } else {
                    dayLast = value;
                  }
                  form.setFieldsValue({ integralWithdrawDates: dayLast });
                  setState({ dayLast: dayLast });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integralWithdrawDateWarning`} label="非时段提现提示">
              <Input placeholder="请输入非时段提现提示 " />
            </Form.Item>
          </Col>

        </Row>
      </Form>
    </WeModal>
  );
};
