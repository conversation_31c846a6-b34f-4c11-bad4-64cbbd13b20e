import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { Col, Form, Input, InputNumber, Row, message } from "antd";

const layout = { row: 10, col: 24 };

const ModalCheck = (props: { children: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.id = props.data.id;
    data.dealPaper = ImageUpload.deserializer(data.dealPaper);

    await MallApi.agreeWithdraw({ data });
    message.success("通过操作成功");
    props.onOk();
  };

  return (
    <WeModal
      trigger={props.children}
      title={"通过申请"}
      onOpen={handleOpen}
      onOk={handleSubmit}
      width={400}
      okText={"确认通过"}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label={`到账金额`} name={`incomeMoney`}>
              <InputNumber placeholder={`请输入到账金额`} min={0} addonAfter="元" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label={`通过备注`} name={`dealRemark`}>
              <Input.TextArea placeholder={`请输入通过备注`} autoSize={{ minRows: 2, maxRows: 4 }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="打款凭证" name={`dealPaper`}>
              <ImageUpload maxCount={4} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalCheck;
