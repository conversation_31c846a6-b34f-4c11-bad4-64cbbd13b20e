import { Button, Descriptions, Input, message, Modal } from "antd";
import { useSetState } from "react-use";
import { Api } from "./api";
import dayjs from "dayjs";
import { cloneElement } from "react";

const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const HeXiao = (props: { children: any; onOk?: any }) => {
  const [state, setState] = useSetState({
    open: false,
    id: "",
    order: null as any,
  });

  const onSearch = async () => {
    // "9417806289";

    try {
      const res = await Api.getVerifyOrde({ params: { verifyCode: state.id } });
      setState({ order: res });
    } catch (err) {
      setState({ order: null });
    }
  };

  const onPost = async () => {
    await Api.postVerifyOrde({ data: { verifyCode: state.id } });
    message.success("核销成功");
    props.onOk?.();
    setState({ open: false });
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Modal
        open={state.open}
        onCancel={() => setState({ open: false })}
        maskClosable={false}
        keyboard={false}
        title="订单核销"
        width={600}
        footer={null}
      >
        <div className="p-4">
          <div className="flex items-center justify-center pb-4 gap-4">
            <Input
              className="w-50"
              placeholder="请输入核销码"
              value={state.id}
              onInput={(e) => setState({ id: e.currentTarget.value })}
            />
            <Button onClick={onSearch}>查询</Button>
          </div>

          <Descriptions
            size="small"
            column={1}
            className="[&_.ant-descriptions-item-label]:w-30"
            items={[
              { label: "订单来源", children: state.order?.sourceName },
              { label: "订单编号", children: state.order?.serialNo },
              {
                label: "商品信息",
                children: (
                  <div>
                    {state.order?.itemList?.map((item: any) => (
                      <div key={item.id} className="flex">
                        <div>{item.productName}</div>
                        <div className="ml-4">x{item.totalCount}</div>
                      </div>
                    ))}
                  </div>
                ),
              },
              { label: "支付金额", children: state.order?.payMoney },
              { label: "客户名称", children: state.order?.vipUser?.name },
              { label: "客户手机", children: state.order?.vipUser?.mobile },
              { label: "下单时间", children: fdate(state.order?.createDate) },
              { label: "下单备注", children: state.order?.orderRemark || "--" },
              { label: "核销截止", children: fdate(state.order?.verifyEndDate) },
            ]}
          />

          <Button type="primary" className="mt-4 mx-a block" disabled={!state.order} onClick={onPost}>
            确认核销
          </Button>
        </div>
      </Modal>
    </>
  );
};
