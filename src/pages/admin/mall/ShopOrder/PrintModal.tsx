import MallApi from "@/services/MallApi";
import { cloneElement, useRef } from "react";
import { useSetState } from "react-use";
import "react-to-print";
import { useReactToPrint } from "react-to-print";
import { FileTextOutlined } from "@ant-design/icons";
import { Button, Checkbox, message, Modal, Typography } from "antd";
import { formatDate } from "@/utils/Tools";
import { OrderSourceSelectFull } from "./types";

export const PrintModal = (props: any) => {
  const ref = useRef<any>(null);
  const reactToPrintFn = useReactToPrint({ contentRef: ref });
  const [state, setState] = useSetState({
    html: "",
  });

  const onOpen = async () => {
    const data = { orderId: props.data.id };
    const res = await MallApi.printOrder({ data });
    setState({ html: res?.content || "" });
    setTimeout(() => reactToPrintFn(), 100);
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => onOpen() })}
      <div className="hidden">
        <div ref={ref} className="p-10px" dangerouslySetInnerHTML={{ __html: state.html }} />
      </div>
    </>
  );
};

export const PrintButton = (props: any) => {
  const data = props.data;

  const ref = useRef<any>(null);
  const reactToPrintFn = useReactToPrint({ contentRef: ref });

  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    ids: [] as any[],
    open: false,
    html: "",
  });

  const print = async (id: any) => {
    const data = { orderId: id };
    const res = await MallApi.printOrder({ data });
    setState({ html: res?.content || "" });
    setTimeout(() => reactToPrintFn(), 100);
  };

  return (
    <>
      <Typography.Link
        disabled={![10, 40].includes(data.orderState)}
        onClick={async () => {
          if (state.loading) return;

          setState({ loading: true });
          try {
            const params = {
              pageSize: 9999,
              enrollTag: 1,
              source: OrderSourceSelectFull,
              queryType: props.userId ? 9 : 1,
              shopVipUserId: data?.vipUser?.id,
              startEnrollDate: formatDate(data?.enrollDate, "YYYY-MM-DD"),
              endEnrollDate: formatDate(data?.enrollDate, "YYYY-MM-DD"),
            };
            const res = await MallApi.getOrderListNew({ params });
            const list: any[] = res?.list || [];

            if (list.length < 0) {
              return message.error("未找到订单");
            } else if (list.length <= 1) {
              print(list?.[0]?.id);
            } else {
              setState({ open: true, list, ids: list.map((n) => n.id) });
            }
          } finally {
            setState({ loading: false });
          }
        }}
      >
        打印
      </Typography.Link>

      <div className="hidden">
        <div ref={ref} className="p-10px" dangerouslySetInnerHTML={{ __html: state.html }} />
      </div>

      <Modal open={state.open} onCancel={() => setState({ open: false })} title={`今日订单 - ${data?.vipUser?.name} - ${formatDate(data?.enrollDate, "YYYY-MM-DD")}`} footer={false}>
        <div className="grid gap-4 mt-4">
          {state.list.map((item) => {
            const isChecked = state.ids.includes(item.id);
            return (
              <div
                key={item.id}
                className="flex gap-2 items-center b-(1 solid #eee) rounded-2 p-2 hover:(bg-blue-1/50 cus) cursor-pointer "
                onClick={() => {
                  const ids = isChecked ? state.ids.filter((n) => n !== item.id) : [...state.ids, item.id];
                  setState({ ids });
                }}
              >
                <Checkbox checked={state.ids.includes(item.id)} />
                <FileTextOutlined className="text-(blue 26px)" />
                <div className="">{item.productName}</div>
                <div className="text-(xs #999)">{item.itemList.map((n: any) => n.productName).join(",")}</div>
                <div className="ml-a text-(12px red)">&yen;{item.payMoney}</div>
              </div>
            );
          })}
        </div>
        <div className="flex justify-center items-center mt-4">
          <Button
            size="small"
            onClick={() => {
              setState({ ids: state.ids.length == state.list.length ? [] : state.list.map((n) => n.id) });
            }}
          >
            全选
          </Button>
          <Button
            type="primary"
            className="ml-a"
            onClick={() => {
              const ids = state.ids.join(",");
              print(ids);
            }}
          >
            打印
          </Button>
        </div>
      </Modal>
    </>
  );
};
