import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Divider, Form, Input, InputNumber, Radio, Row, Space, message, Table } from "antd";
import { CancelTypes } from "./types";

const layout = { row: 10, col: 24 };

const RefundOrder = (props: { children: any; title: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
    // 初始化退款次数字段
    const initialValues: any = {};
    props.data.itemList?.forEach((item: any, index: number) => {
      if (item.projectList?.length > 0) {
        item.projectList.forEach((project: any, projectIndex: number) => {
          initialValues[`projectRefundNum_${index}_${projectIndex}`] = project.lessNum;
        });
      }
    });
    form.setFieldsValue(initialValues);
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.orderId = props.data.id;
    data.cancelImage = ImageUpload.deserializer(data.cancelImage);

    // 处理项目退款次数数据
    const projectRefundList: any[] = [];
    console.log(props.data.itemList);
    props.data.itemList?.forEach((item: any, index: number) => {
      if (item.projectList?.length > 0) {
        item.projectList.forEach((project: any, projectIndex: number) => {
          const refundNum = data[`projectRefundNum_${index}_${projectIndex}`];
          if (refundNum > 0) {
            projectRefundList.push({
              mallOrderProjectId: project.id,
              refundNum: refundNum
            });
          }
        });
      }
    });
    data.refundProjects = projectRefundList;

    await MallApi.refundOrderNew({ data });
    message.success("退款成功");
    props.onOk();
  };

  // 构建退款项目表格数据
  const getRefundItems = () => {
    const items: any[] = [];
    props.data.itemList?.forEach((item: any, index: number) => {
      if (item.productModel == 1 && item.projectList?.length > 0) {
        // 普通商品
        items.push({
          key: item.id,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: item.projectList?.length > 0 ? `${item.projectList[0].lessNum} / ${item.projectList[0].includeNum}` : '--',
          actual: item.projectList?.length > 0 ? (
            <Form.Item
              name={`projectRefundNum_${index}_0`}
              noStyle
              rules={[{ required: true, message: "请输入扣减次数" }]}
            >
              <InputNumber
                min={0}
                max={item.projectList?.[0]?.lessNum || 0}
                defaultValue={item.projectList?.[0]?.lessNum || 0}
                style={{ width: 150 }}
                addonAfter="次"
              />
            </Form.Item>
          ) : null,
          isParent: false
        });
      } else if (item.productModel == 2 && item.projectList?.length > 0) {
        // 组合商品
        items.push({
          key: `parent_${item.id}`,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: null,
          actual: null,
          isParent: true
        });

        item.projectList.forEach((sub: any, projectIndex: number) => {
          items.push({
            key: sub.id,
            name: (
              <div style={{ paddingLeft: 20 }}>
                <span className="p-2 text-gray-400">-</span>
                {sub?.productName}
                {sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName && (
                  <span className="text-gray-400 ml-2px">
                    {" - " + sub.productSpecificationName}
                  </span>
                )}
              </div>
            ),
            refundable: `${sub.lessNum} / ${sub.includeNum}`,
            actual: (
              <Form.Item
                name={`projectRefundNum_${index}_${projectIndex}`}
                noStyle
                rules={[{ required: true, message: "请输入扣减次数" }]}
              >
                <InputNumber
                  min={0}
                  max={sub.lessNum}
                  defaultValue={sub.lessNum}
                  style={{ width: 150 }}
                  addonAfter="次"
                />
              </Form.Item>
            ),
            isParent: false
          });
        });
      } else {
        // 普通商品
        items.push({
          key: item.id,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: "--",
          actual: "--",
          isParent: false
        });
      }
    });
    return items;
  };

  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '次数',
      dataIndex: 'refundable',
      key: 'refundable',
      width: 120,
      align: 'center' as const
    },
    {
      title: '实扣',
      dataIndex: 'actual',
      key: 'actual',
      width: 200,
      align: 'center' as const
    }
  ];

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={[layout.row, layout.row]}>
          <Col span={layout.col}>
            <Form.Item label="实付金额">
              <Input value={props.data.payMoney + "元"} bordered={false} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              label="退款金额"
              name={`refundApplyMoney`}
              initialValue={props.data.payMoney}
              rules={[{ required: true, message: "请输入退款金额" }]}
            >
              <InputNumber min={0} max={props.data.payMoney} addonAfter="元" style={{ width: '100%' }} />
            </Form.Item>
          </Col>

          {/* 添加退款项目 */}
          <Col span={24}>
            <Form.Item label="退款项目" required>
              <Table
                size="small"
                dataSource={getRefundItems()}
                columns={columns}
                pagination={false}
                showHeader={true}
                bordered
              />
            </Form.Item>
          </Col>

          <Divider orientation="left">退款原因</Divider>

          <Col span={layout.col}>
            <Form.Item label="退款原因" name={`cancelType`} rules={[{ required: true, message: "请选择退款原因" }]}>
              <Radio.Group>
                <Space direction="vertical">
                  {CancelTypes.map((item) => (
                    <Radio value={item.value} key={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="原因说明" name={`cancelDesc`} rules={[{ required: true, message: "请输入原因说明" }]}>
              <Input.TextArea
                placeholder="请输入原因说明"
                maxLength={200}
                showCount
                allowClear
                autoSize={{ minRows: 2 }}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="图片描述" name={`cancelImage`} valuePropName="fileList">
              <ImageUpload maxCount={3} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default RefundOrder;