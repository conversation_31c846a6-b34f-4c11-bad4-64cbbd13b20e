import { Tabs } from "antd";
import { useSetState } from "react-use";
import OrderListPage from "./OrderList";
import OrderRefundPage from "./OrderRefundList";

const tabItems = [
  { key: "OrderList", label: "订单列表" },
  { key: "RefundList", label: "退款列表" },
];
const ShopOrderPage = () => {

  const [state, setState] = useSetState({
    show: false,
    user: {} as any,
    tabKey: tabItems[0].key,
  });


  return (
    <div>
      <Tabs
        items={tabItems}
        type="card"
        onChange={(e) => {
          setState({ tabKey: e });
        }}
        activeKey={state.tabKey}
        className="h-12"
      />
      <div>
        {state.tabKey === "OrderList" && <OrderListPage />}
        {state.tabKey === "RefundList" && <OrderRefundPage />}
      </div>
    </div>
  );
};

export default ShopOrderPage;


