import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useRef } from "react";
import AppUserVoteLogApi from "./api";
import { Drawer, Form, } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const AppUserVoteLogPage = (props: { params?: any; children?: any; title: string; voteId?: string }) => {

  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });


  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} width={"70%"} title={props.title} onClose={() => setState({ open: false })}>
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ voteId: props.voteId }}//默认参数
          request={(p) => AppUserVoteLogApi.getAppUserVoteLog({ params: p })}
          search={[
            <Form.Item label="投票日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "投票用户", render: (c) => c?.appUser?.name },
            { title: "投票日期", dataIndex: "createDate", render: (c) => fdate(c) },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default AppUserVoteLogPage;
