import WeModal from "@/components/WeModal/WeModal";
import AppUserVoteLogApi from "./api";
import { Col, Form, Input, Row, } from "antd";

const layout = { row: 10, col: 24 };

const AppUserVoteLogEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserVoteLogApi.getAppUserVoteLogInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`appUserId`} label="投票用户id" rules={[{ required: false, message: "请输入投票用户id" }]}>
              <Input placeholder="请输入投票用户id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`appUserData`} label="投票用户信息" rules={[{ required: false, message: "请输入投票用户信息" }]}>
              <Input.TextArea placeholder="请输入投票用户信息" rows={4} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`voteId`} label="投票id" rules={[{ required: false, message: "请输入投票id" }]}>
              <Input placeholder="请输入投票id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`voteOptionId`} label="投票项id" rules={[{ required: false, message: "请输入投票项id" }]}>
              <Input placeholder="请输入投票项id" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserVoteLogEdit;
