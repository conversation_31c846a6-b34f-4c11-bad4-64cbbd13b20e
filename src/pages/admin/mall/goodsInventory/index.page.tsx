import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { <PERSON><PERSON>, <PERSON>ton, Card, Col, Divider, Form, Input, Row, Select, Space, Switch, Tree, Typography } from "antd";
import { useRef } from "react";
import { useMount, useSetState } from "react-use";
import Style from "./index.module.scss";
import { InventoryState } from "./types";
import StockEdit from "./StockEdit";
import { DownloadOutlined, UploadOutlined } from "@ant-design/icons";
import StockDetail from "./StockDetail";
import StockLogs from "./StockLogs";

const GoodsInventory = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    types: [] as any[],
    typeId: null as any,
  });

  useMount(() => {
    fetchTypes();
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getGoodsType({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const TSwitch = (props: any) => {
    return <Switch checked={!!props.value} onChange={(e) => props.onChange(e ? 1 : "")} />;
  };

  return (
    <div>
      <Row gutter={10}>
        <Col flex={`300px`} style={{ minWidth: 0 }}>
          <Card bodyStyle={{ padding: 10 }} title={`物资分类`} style={{ height: "100%" }}>
            <Tree.DirectoryTree
              className={Style.tree}
              treeData={state.types}
              showIcon={false}
              selectedKeys={[state.typeId]}
              onSelect={(e) => {
                const key = e[0];
                if (state.typeId === key) {
                  setState({ typeId: "" });
                } else {
                  setState({ typeId: key });
                }
              }}
            />
          </Card>
        </Col>
        <Col flex={`auto`}>
          <WeTable
            ref={tableRef}
            title={
              <>
                <StockEdit title={`新增入库`} onOk={handleReload} operType={1}>
                  <Button type="primary" icon={<DownloadOutlined />}>
                    新增入库
                  </Button>
                </StockEdit>
                <StockEdit title={`新增出库`} onOk={handleReload} operType={2}>
                  <Button type="primary" icon={<UploadOutlined />}>
                    新增出库
                  </Button>
                </StockEdit>

                <Divider type="vertical" />

                <StockLogs operType={1} onClose={handleReload}>
                  <Button type="primary" icon={<DownloadOutlined />}>
                    入库明细
                  </Button>
                </StockLogs>

                <StockLogs operType={2} onClose={handleReload}>
                  <Button type="primary" icon={<UploadOutlined />}>
                    出库明细
                  </Button>
                </StockLogs>
              </>
            }
            request={(params) => MallApi.getGoodsStock({ params })}
            params={{ goodsCategoryId: state.typeId }}
            search={[
              <Form.Item label="名称" name={`name`}>
                <Input placeholder="请输入名称" />
              </Form.Item>,
              <Form.Item label="库存状态" name={`inventoryState`}>
                <Select
                  placeholder="请选择库存状态"
                  options={InventoryState}
                  fieldNames={{ label: "name", value: "id" }}
                  allowClear
                />
              </Form.Item>,
              <Form.Item label="库存大于0" name={`startInventory`} initialValue={""}>
                <TSwitch />
              </Form.Item>,
            ]}
            columns={[
              { title: "门店", dataIndex: "shopName" },
              { title: "编号", dataIndex: "serialNo" },
              { title: "名称", dataIndex: "name" },
              { title: "规格", dataIndex: "specs" },
              { title: "库存", dataIndex: "inventoryDesc" },
              {
                title: "状态",
                dataIndex: "inventoryState",
                render: (c) => (
                  <>
                    {c == 1 && <Badge status="success" text="正常" />}
                    {c == 2 && <Badge status="error" style={{ color: "#ff4d4f" }} text="库存低" />}
                  </>
                ),
              },
              {
                title: "操作",
                render: (item) => (
                  <Space>
                    <StockEdit title={`新增入库`} onOk={handleReload} operType={1} data={item}>
                      <Typography.Link>入库</Typography.Link>
                    </StockEdit>

                    <StockEdit title={`新增入库`} onOk={handleReload} operType={2} data={item}>
                      <Typography.Link>出库</Typography.Link>
                    </StockEdit>

                    <StockDetail params={{ goodsId: item.goodsId }}>
                      <Typography.Link>明细</Typography.Link>
                    </StockDetail>
                  </Space>
                ),
              },
            ]}
          />
        </Col>
      </Row>
    </div>
  );
};

export default GoodsInventory;
