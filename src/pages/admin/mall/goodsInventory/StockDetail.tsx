import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Drawer, Typography } from "antd";
import dayjs from "dayjs";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";

const StockDetail = (props: { children: any; params?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={`出入库明细`}
        keyboard={false}
      >
        <WeTable
          autoLoad={true}
          ref={tableRef}
          request={(params) => MallApi.getGoodsStockLogDetail({ params })}
          params={props.params}
          columns={[
            { title: "名称", dataIndex: "name" },
            { title: "单号", dataIndex: "serialNo" },
            { title: "操作人", dataIndex: "operUserName" },
            { title: "操作类型", dataIndex: "billTypeName" },
            { title: "操作数量", render: (c) => c?.operNum + c?.unit },
            { title: "结余数量", render: (c) => c?.surplusNum + c?.unit },
            {
              title: "备注",
              dataIndex: "operRemark",
              render: (c) => (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
              ),
            },
            {
              title: "出入库日期",
              dataIndex: "createDate",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
            },
          ]}
        />
      </Drawer>
    </>
  );
};

export default StockDetail;
