import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import {
  Button,
  DatePicker,
  Drawer,
  Form,
  Input,
  Spin,
  Table,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { cloneElement, useRef } from "react";
import { useSetState, useUpdateEffect } from "react-use";
import StockEdit from "./StockEdit";
import { DownloadOutlined, UploadOutlined } from "@ant-design/icons";
import { DatePresetRanges } from "@/utils/Tools";

const OperTypeMap = [
  { id: 1, name: "入库", type: "goodsInType" },
  { id: 2, name: "出库", type: "goodsOutType" },
];

const StockLogs = (props: {
  children: any;
  operType?: 1 | 2;
  onClose?: Function;
}) => {
  const tableRef = useRef<WeTableRef>(null);
  const operTypeName = OperTypeMap.find((n) => n.id === props.operType)?.name;
  const [state, setState] = useSetState({
    open: false,
    exdata: {} as any,
  });

  useUpdateEffect(() => {
    if (state.open) {
      handleReload();
    } else {
      props.onClose?.();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchSubData = async (billId: any) => {
    const params = { pageSize: 9999, billId };
    const res = await MallApi.getGoodsStockLogDetail({ params });
    const list = res?.list || [];
    setState((prev) => {
      let { ...exdata } = prev.exdata;
      exdata[billId] = list;
      return { exdata };
    });
  };

  return (
    <>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={operTypeName + "明细"}
        keyboard={false}
        extra={
          <>
            {props.operType == 1 && (
              <StockEdit title={`新增入库`} onOk={handleReload} operType={1}>
                <Button type="primary" icon={<DownloadOutlined />}>
                  新增入库
                </Button>
              </StockEdit>
            )}
            {props.operType == 2 && (
              <StockEdit title={`新增出库`} onOk={handleReload} operType={2}>
                <Button type="primary" icon={<UploadOutlined />}>
                  新增出库
                </Button>
              </StockEdit>
            )}
          </>
        }
      >
        <WeTable
          ref={tableRef}
          tableProps={{
            expandable: {
              onExpand: (bool, item) => {
                if (bool) fetchSubData(item.id);
              },
              expandedRowRender: (item) => {
                const data = state.exdata[item.id];

                if (!data)
                  return (
                    <Spin spinning={true} style={{ width: "100%" }}></Spin>
                  );

                return (
                  <Table
                    size="small"
                    style={{ paddingLeft: "10px", paddingBottom: "20px" }}
                    rowKey={`id`}
                    locale={{ emptyText: "暂无数据" }}
                    dataSource={data}
                    pagination={false}
                    columns={[
                      { title: "物资编号", dataIndex: "serialNo" },
                      { title: "物资名称", dataIndex: "name" },
                      { title: "规格", dataIndex: "specs" },
                      { title: "分类", dataIndex: "goodsCategoryName" },
                      {
                        title: `${operTypeName}数量`,
                        render: (c) => c?.operNum + c?.unit,
                      },
                      {
                        title: "结余数量",
                        render: (c) => c?.surplusNum + c?.unit,
                      },
                    ]}
                  />
                );
              },
            },
          }}
          request={(params) => MallApi.getGoodsStockLogs({ params })}
          params={{ operType: props.operType }}
          search={[
            <Form.Item label={`${operTypeName}单号`} name="serialNo">
              <Input placeholder={`请输入${operTypeName}单号`} />
            </Form.Item>,
            <Form.Item label={`${operTypeName}日期`} name="CreateDate">
              <DatePicker.RangePicker
                presets={DatePresetRanges}
                style={{ width: "100%" }}
              />
            </Form.Item>,
            <Form.Item label="项目名称" name={`projectName`}>
              <Input placeholder="请输入项目名称" />
            </Form.Item>,
            <Form.Item label="物资名称" name={`goodsName`}>
              <Input placeholder="请输入物资名称" />
            </Form.Item>,
            <Form.Item label="关联单号" name={`relateNo`}>
              <Input placeholder="请输入关联单号" />
            </Form.Item>,
            <Form.Item label="操作人" name="operUserName">
              <Input placeholder="请输入操作人" />
            </Form.Item>,
          ]}
          columns={[
            { title: `${operTypeName}单号`, dataIndex: "serialNo" },
            {
              title: `${operTypeName}日期`,
              dataIndex: "createDate",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
            },
            {
              title: `单据日期`,
              dataIndex: "billTime",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
            },
            { title: "类型", dataIndex: "billTypeName" },
            { title: "关联单号", dataIndex: "relateNo" },
            { title: "项目名称", dataIndex: "projectName" },
            { title: "操作人", dataIndex: "operUserName" },
            {
              title: "备注",
              dataIndex: "operRemark",
              render: (c) => (
                <Typography.Text
                  style={{ width: 150 }}
                  ellipsis={{ tooltip: true }}
                >
                  {c}
                </Typography.Text>
              ),
            },
          ]}
        />
      </Drawer>
    </>
  );
};

export default StockLogs;
