import MatterPicker from "@/components/MatterPicker";
import DictPicker from "@/components/Units/DictPicker";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { PlusOutlined } from "@ant-design/icons";
import { <PERSON>ton, Col, DatePicker, Divider, Form, Input, InputNumber, Row, Table, message } from "antd";
import dayjs from "dayjs";

const layout = { row: 10, col: 8 };
const OperTypeMap = [
  { id: 1, name: "入库", type: "goodsInType" },
  { id: 2, name: "出库", type: "goodsOutType" },
];
const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

const StockEdit = (props: { children: any; title: any; onOk?: Function; data?: any; operType: number }) => {
  const [form] = Form.useForm();
  const operTypeName = OperTypeMap.find((n) => n.id === props.operType)?.name;

  const handleOpen = () => {
    form.resetFields();
    form.setFieldValue("details", []);

    if (props.data) {
      const { ...data } = props.data;

      form.setFieldValue("details", [
        {
          inventoryDesc: data.inventoryDesc,
          serialNo: data.serialNo,
          goodsId: data.goodsId,
          name: data.name,
          operNum: 1,
          operRemark: "",
          unit: data.unit,
        },
      ]);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();

    const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");
    data.billTime = fdate(data.billTime);
    data.operType = props.operType;

    if (!data.details?.length) {
      message.error("请选择物资信息");
      return false;
    }

    await MallApi.putGoodsInOut({ data });

    message.success(`${operTypeName}操作成功`);
    props.onOk?.();
  };

  return (
    <WeModal width={1000} title={props.title} trigger={props.children} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Divider orientation="left">基础信息</Divider>
          <Group>
            <Col span={layout.col}>
              <Form.Item
                label={operTypeName + "类型"}
                name={`billTypeId`}
                rules={[{ required: true, message: "请选择操作类型" }]}
              >
                <DictPicker type={OperTypeMap.find((n) => n.id === props.operType)?.type} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="单据时间" name={`billTime`}>
                <DatePicker showTime style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="操作备注" name={`operRemark`}>
                <Input.TextArea
                  autoSize={{ minRows: 2 }}
                  maxLength={200}
                  showCount
                  allowClear
                  placeholder="请输入操作备注"
                />
              </Form.Item>
            </Col>
          </Group>
        </Row>

        <Divider orientation="left">物资信息</Divider>

        <Form.List name={`details`} initialValue={[]}>
          {(fields, action) => {
            return (
              <>
                <Table
                  size="small"
                  locale={{ emptyText: "暂无数据" }}
                  pagination={false}
                  dataSource={fields}
                  columns={[
                    {
                      title: "编号",
                      render: (c) => {
                        const name = form.getFieldValue(["details", c.name, "serialNo"]);
                        return name;
                      },
                    },
                    {
                      title: "名称",
                      render: (c) => {
                        const name = form.getFieldValue(["details", c.name, "name"]);
                        return name;
                      },
                    },
                    {
                      title: "库存",
                      render: (c) => {
                        const name = form.getFieldValue(["details", c.name, "inventoryDesc"]);
                        return name || "--";
                      },
                    },
                    {
                      title: "操作数量",
                      render: (c) => {
                        const unit = form.getFieldValue(["details", c.name, "unit"]);
                        return (
                          <Form.Item style={{ marginBottom: 0 }} name={[c.name, "operNum"]} initialValue={1}>
                            <InputNumber min={1} addonAfter={unit} />
                          </Form.Item>
                        );
                      },
                    },
                    // {
                    //   title: "备注",
                    //   render: (c) => {
                    //     return (
                    //       <Form.Item style={{ marginBottom: 0 }} name={[c.name, `operRemark`]}>
                    //         <Input.TextArea autoSize placeholder="请输入备注" />
                    //       </Form.Item>
                    //     );
                    //   },
                    // },
                    {
                      title: "操作",
                      render: (c) => {
                        if (props.data) return null;

                        return <a onClick={() => action.remove(c.name)}>删除</a>;
                      },
                    },
                  ]}
                />

                {!props.data && (
                  <>
                    <div style={{ height: 20 }}></div>
                    <MatterPicker
                      params={{ operType: props.operType }}
                      onSelect={(rows) => {
                        //console.log(rows);
                        const list: any[] = form.getFieldValue("details");
                        rows.forEach((item) => {
                          if (list.every((n) => n.goodsId !== item.id))
                            list.push({
                              inventoryDesc: item.inventoryDesc,
                              serialNo: item.serialNo,
                              goodsId: item.id,
                              name: item.name,
                              operNum: 1,
                              operRemark: "",
                              unit: item.unit,
                            });
                        });

                        form.setFieldValue("details", list);
                      }}
                    >
                      <Button type="dashed" block icon={<PlusOutlined />}>
                        添加
                      </Button>
                    </MatterPicker>
                  </>
                )}
              </>
            );
          }}
        </Form.List>
      </Form>
    </WeModal>
  );
};

export default StockEdit;
