import { Api } from "./Api";
import { <PERSON><PERSON>, Card, Col, Dropdown, Form, Input, message, Modal, Pagination, Popconfirm, Row, Select, Space, Table, Tooltip, Tree, Typography } from "antd";
import { Edit } from "./Edit";
import { Rule } from "./Rule";
import MarketEditType from "../marketproductcategory/MarketEditType";
import {
  ApartmentOutlined,
  ClearOutlined,
  DeleteOutlined,
  DiffOutlined,
  DownOutlined,
  FormOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  UpOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from "@ant-design/icons";
import { useMount, useSetState } from "react-use";
import { MarketCategoryApi } from "../marketproductcategory/MarketCategoryApi";
import Style from "../marketproductcategory/index.module.scss";
import AsyncSwitch from "@/components/AsyncSwitch";
import dayjs from "dayjs";
import { PropertyState } from "./types";
import OperateInventory from "../GroupProduct/OperateInventory";
import { useTable } from "@/components/WeTablev2/useTable";
import { useState } from "react";

const marketType = 60;
export default function MarketProductMall() {
  const [fold, setFold] = useState(true);
  const [state, setState] = useSetState({
    keys: [] as any[],

    types: [] as any[],
    typeId: null as any,
    exdata: {} as any,
  });

  const table = useTable({
    params: { marketType: marketType, categoryId: state.typeId },
    onFetch: (p) => {
      setState({ keys: [] });
      return Api.getMarketProduct({ params: p });
    },
  });

  useMount(() => {
    fetchTypes();
  });

  const fetchTypes = async () => {
    const params = { pageSize: 9999, marketType };
    const res = await MarketCategoryApi.getMarketProductTypeList({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await MarketCategoryApi.delMarketProductType({ data });
    message.success("删除分类成功");
    fetchTypes();
  };

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  return (
    <div>
      <Row wrap={false} gutter={10}>
        <Col flex={`300px`} style={{ minWidth: 0 }}>
          <Card
            classNames={{ body: "!p-2" }}
            title={`分类`}
            style={{ height: "100%" }}
            extra={
              <MarketEditType title={`添加分类`} marketType={marketType} onOk={() => fetchTypes()} tree={state.types}>
                <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
              </MarketEditType>
            }
          >
            <Tree.DirectoryTree
              className={Style.tree}
              treeData={state.types}
              showIcon={false}
              selectedKeys={[state.typeId]}
              onSelect={(e) => {
                const key = e[0];
                if (state.typeId === key) {
                  setState({ typeId: "" });
                } else {
                  setState({ typeId: key });
                }
              }}
              titleRender={(item) => {
                return (
                  <div className={Style.row} key={item.key}>
                    <div className={Style.title}>{item.title}</div>
                    <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                      <MarketEditType title={`编辑分类`} marketType={marketType} onOk={() => fetchTypes()} tree={state.types} data={item}>
                        <Tooltip title="编辑分类">
                          <FormOutlined className={Style.ico} />
                        </Tooltip>
                      </MarketEditType>

                      <MarketEditType title={`添加下级`} marketType={marketType} onOk={() => fetchTypes()} tree={state.types} data={{ parentId: item.id }}>
                        <Tooltip title="添加下级">
                          <ApartmentOutlined className={Style.ico} />
                        </Tooltip>
                      </MarketEditType>

                      <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                        <Tooltip title="删除分类">
                          <DeleteOutlined className={Style.ico} />
                        </Tooltip>
                      </Popconfirm>
                    </div>
                  </div>
                );
              }}
            />
          </Card>
        </Col>
        <Col flex={"auto"}>
          <Form form={table.form} onValuesChange={table.onFormChange}>
            <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
              <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
                <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
                  <Form.Item label="名称" name={`productName`}>
                    <Input placeholder="请输入" />
                  </Form.Item>
                  <Form.Item label="属性" name={`property`}>
                    <Select placeholder="请选择" options={PropertyState.map((n) => ({ label: n.name, value: n.id }))} allowClear />
                  </Form.Item>
                </div>
                <div className="flex gap-2 pl-3">
                  <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
                  <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                    搜索
                  </Button>
                  <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
                </div>
              </div>
            </Card>

            <Card
              classNames={{ body: "!p-0" }}
              title={
                <Space>
                  <Edit title={`添加商品`} onOk={handleReload}>
                    <Button type="primary" icon={<PlusOutlined />}>
                      添加商品
                    </Button>
                  </Edit>
                  <Rule onOk={handleReload}>
                    <Button type="primary" icon={<PaperClipOutlined />}>
                      秒杀规则
                    </Button>
                  </Rule>
                </Space>
              }
            >
              <Table
                rowKey={"id"}
                dataSource={table.state.list}
                columns={[
                  { title: "名称", dataIndex: "name" },
                  { title: "分类", dataIndex: "categoryName", render: (c) => c || "--" },
                  { title: "属性", dataIndex: "property", render: (c) => PropertyState.find((n) => n.id == c)?.name },
                  { title: "划线价", dataIndex: "oldPrice" },
                  { title: "秒杀价", dataIndex: "salePrice" },
                  { title: "累计库存", dataIndex: "totalInventoryCount" },
                  {
                    title: "剩余库存",
                    render: (c) => {
                      return (
                        <>
                          {!c.inventoryLimit && "不限"}
                          {!!c.inventoryLimit && (
                            <>
                              {c.inventoryCount} &nbsp;&nbsp;
                              <OperateInventory title={`增减库存`} onOk={handleReload} data={c}>
                                <Tooltip title="增减库存">
                                  <DiffOutlined />
                                </Tooltip>
                              </OperateInventory>
                            </>
                          )}
                        </>
                      );
                    },
                  },
                  {
                    title: "销量",
                    render: (c) => {
                      return (
                        <>
                          {c.soldCount}
                          {/* &nbsp;&nbsp;
                      <OperateSold title={`增减销量`} onOk={handleReload} data={c}>
                        <Tooltip title="增减销量">
                          <DiffOutlined />
                        </Tooltip>
                      </OperateSold> */}
                        </>
                      );
                    },
                  },
                  {
                    title: "时间限制",
                    width: 300,
                    render: (c) => {
                      return (
                        <>
                          {!c.saleTimeLimit && "不限制"}
                          {!!c.saleTimeLimit && `${dayjs(c.saleStartDate).format("YYYY-MM-DD HH:mm")} 至 ${dayjs(c.saleEndDate).format("YYYY-MM-DD HH:mm")}`}
                        </>
                      );
                    },
                  },
                  { title: "限购", render: (c) => (c.buyLimit ? c.buyMaxCount : "不限") },
                  {
                    title: "上下架",
                    // dataIndex: "saleIn",
                    render: (c) => (
                      <AsyncSwitch
                        onClick={async () => {
                          await Api.batchModifySaleIn({ data: { operType: 1, ids: c.id, saleIn: Number(!c.saleIn) } });
                          handleReload();
                        }}
                        value={!!c.saleIn}
                      />
                    ),
                  },
                  {
                    title: "操作",
                    render: (c) => (
                      <>
                        <Space>
                          <Edit title={`编辑商品`} onOk={handleReload} data={c}>
                            <a>编辑</a>
                          </Edit>

                          <Dropdown
                            menu={{
                              items: [
                                {
                                  key: "1",
                                  label: (
                                    <Popconfirm
                                      title="确定要删除这条数据吗？"
                                      onConfirm={async () => {
                                        await Api.delMarketProduct({ data: { ids: c.id } });
                                        handleReload();
                                      }}
                                    >
                                      <a>删除</a>
                                    </Popconfirm>
                                  ),
                                },
                                {
                                  key: "2",
                                  disabled: !!c.isSaleIn,
                                  label: (
                                    <Popconfirm
                                      title="确定要进行秒杀操作吗？"
                                      onConfirm={async () => {
                                        await Api.virtualSeckill({ data: { marketProductId: c.id } });
                                        message.success("秒杀成功");
                                      }}
                                    >
                                      <a>虚拟秒杀</a>
                                    </Popconfirm>
                                  ),
                                },
                              ],
                            }}
                          >
                            <Typography.Link>更多</Typography.Link>
                          </Dropdown>
                        </Space>
                      </>
                    ),
                  },
                ]}
                pagination={false}
                rowSelection={{
                  selectedRowKeys: state.keys,
                  onChange: (selectedRowKeys) => {
                    setState({ keys: selectedRowKeys });
                  },
                }}
              />
              <div className="p-4 flex items-center">
                <Space>
                  <Button
                    onClick={() => {
                      const len = state.keys.length;
                      if (len == table.state.list.length) {
                        setState({ keys: [] });
                      } else {
                        setState({ keys: table.state.list.map((n) => n.id) });
                      }
                    }}
                  >
                    全选
                  </Button>
                  <Dropdown
                    menu={{
                      items: [
                        { label: "批量上架", key: "up", icon: <VerticalAlignTopOutlined /> },
                        { label: "批量下架", key: "down", icon: <VerticalAlignBottomOutlined /> },
                        { label: "批量删除", key: "del", icon: <DeleteOutlined />, danger: true },
                      ],
                      onClick: (e) => {
                        if (!state.keys.length) return message.error("请先进行选择");

                        if (e.key == "up") {
                          Modal.confirm({
                            title: "确定要批量上架这些商品吗？",
                            onOk: async () => {
                              const data = { saleIn: 1, ids: state.keys.join(",") };
                              await Api.batchModifySaleIn({ data });
                              message.success("批量上架成功");
                              handleReload();
                            },
                          });
                        }

                        if (e.key == "down") {
                          Modal.confirm({
                            title: "确定要批量下架这些商品吗？",
                            onOk: async () => {
                              const data = { saleIn: 0, ids: state.keys.join(",") };
                              await Api.batchModifySaleIn({ data });
                              message.success("批量下架成功");
                              handleReload();
                            },
                          });
                        }

                        if (e.key == "del") {
                          Modal.confirm({
                            title: "确定要批量删除这些商品吗？",
                            onOk: async () => {
                              const data = { ids: state.keys.join(",") };
                              await Api.delMarketProduct({ data });
                              message.success("批量删除成功");
                              handleReload();
                            },
                          });
                        }
                      },
                    }}
                  >
                    <Button>
                      批量操作
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                  <div className="text-#666 text-xs">
                    {table.state.list.length}条数据(已选{state.keys.length}条)
                  </div>
                </Space>
                <Pagination
                  className="ml-a"
                  {...{
                    current: table.state.page,
                    pageSize: table.state.size,
                    total: table.state.total,
                    showSizeChanger: true,
                    showTotal: (total) => <div>共 {total} 条数据</div>,
                    pageSizeOptions: [5, 10, 20, 50, 100],
                    onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
                  }}
                />
              </div>
            </Card>
          </Form>
        </Col>
      </Row>
    </div>
  );
}
