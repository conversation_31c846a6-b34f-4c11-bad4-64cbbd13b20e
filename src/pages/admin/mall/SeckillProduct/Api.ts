import { makeApi } from "@/utils/Api";

export const Api = {
  getMarketProduct: makeApi("get", `/api/mallMarketProduct`),
  getMarketProductInfo: makeApi("get", `/api/mallMarketProduct`, true),
  addMarketProduct: makeApi("post", `/api/mallMarketProduct`),
  putMarketProduct: makeApi("put", `/api/mallMarketProduct`),
  delMarketProduct: makeApi("delete", `/api/mallMarketProduct`),
  batchModifySaleIn: makeApi("post", `/api/mallMarketProduct/batch_modify_sale_in`),
  virtualSeckill: makeApi("post", `/api/mallMarketProduct/virtual_seckill`),

  getRule: makeApi("get", `/api/mallMarketProduct/query_rule`),
  putRule: makeApi("post", `/api/mallMarketProduct/save_rule`),
};
