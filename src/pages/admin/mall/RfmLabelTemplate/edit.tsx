import WeModal from "@/components/WeModal/WeModal";
import MallRfmLabelTemplateApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";

const layout = { row: 10, col: 24 };

const MallRfmLabelTemplateEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?:any;}) => {

  const [form] = Form.useForm();

  const handleOpen = async() => {
    form.resetFields();
    if (props.data) {
      const data = await MallRfmLabelTemplateApi.getMallRfmLabelTemplateInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id)  {
      await MallRfmLabelTemplateApi.addMallRfmLabelTemplate({ data });
      message.success("添加RFM标签模板成功");
    }
    if (data.id) {
      await MallRfmLabelTemplateApi.putMallRfmLabelTemplate({ data });
      message.success("修改RFM标签模板成功");
    }
    props.onOk?.();
  };

return (
  <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps ={{hidden:props.hideSubmit}} onOk={handleSubmit}>
    <Form form={form} labelCol={{ flex: "100px" }}>
      <Form.Item name={`id`} hidden>
        <Input />
      </Form.Item>
      <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`companyId`} label="公司id" rules={[{ required: false, message: "请输入公司id" }]}>
              <Input placeholder="请输入公司id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`rfmLevel`} label="rfm层级(2~5)" rules={[{ required: false, message: "请输入rfm层级(2~5)" }]} initialValue={0}>
             <InputNumber min={0} placeholder="请输入rfm层级(2~5)" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: false, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`strategyContent`} label="运营策略" rules={[{ required: false, message: "请输入运营策略" }]}>
              <Input placeholder="请输入运营策略" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`rfmData`} label="rfm数据" rules={[{ required: false, message: "请输入rfm数据" }]}>
              <Input placeholder="请输入rfm数据" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
      </Row>
    </Form>
  </WeModal>
  );
};

export default MallRfmLabelTemplateEdit;
