import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallRfmLabelTemplateApi from "./api";
import MallRfmLabelTemplateEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, } from "@/utils/Tools";
import dayjs from "dayjs";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallRfmLabelTemplatePage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallRfmLabelTemplateApi.delMallRfmLabelTemplate({ data: { ids: item.id } });
    message.success("删除RFM标签模板成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallRfmLabelTemplateApi.getMallRfmLabelTemplate({ params: p })}
        title={
          <Space>
            <MallRfmLabelTemplateEdit title={"新增RFM标签模板"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallRfmLabelTemplateEdit>
          </Space>
        }
        search={[
          <Form.Item label="公司id" name={`companyId`}>
            <Input placeholder="请输入公司id" />
          </Form.Item>,
          <Form.Item label="rfm层级(2~5)" name={`rfmLevel`}>
            <Input placeholder="请输入rfm层级(2~5)" />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="运营策略" name={`strategyContent`}>
            <Input placeholder="请输入运营策略" />
          </Form.Item>,
          <Form.Item label="rfm数据" name={`rfmData`}>
            <Input placeholder="请输入rfm数据" />
          </Form.Item>,
          <Form.Item label="排序" name={`sort`}>
            <Input placeholder="请输入排序" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "公司id", dataIndex: "companyId", render: (c) => c ? c : "--" },
          { title: "rfm层级(2~5)", dataIndex: "rfmLevel" },
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "运营策略", dataIndex: "strategyContent", render: (c) => c ? c : "--" },
          { title: "rfm数据", dataIndex: "rfmData", render: (c) => c ? c : "--" },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallRfmLabelTemplateEdit title={`编辑RFM标签模板`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallRfmLabelTemplateEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallRfmLabelTemplatePage;
