import WeModal from "@/components/WeModal/WeModal";
import AppUserVoteOptionApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";
import { StateMap } from "./types";

const layout = { row: 10, col: 12 };

const AppUserVoteOptionEdit = (props: { title: any; children: any; onOk?: Function; data?: any; voteId?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserVoteOptionApi.getAppUserVoteOptionInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.image = ImageUpload.deserializer(data.image);
    data.voteId = props.voteId;
    if (!data.id) {
      await AppUserVoteOptionApi.addAppUserVoteOption({ data });
      message.success("添加投票选项成功");
    }
    if (data.id) {
      await AppUserVoteOptionApi.putAppUserVoteOption({ data });
      message.success("修改投票选项成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item name={`name`} label="名称" rules={[{ required: false, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={4} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`image`} label="图片" rules={[{ required: false, message: "请上传图片" }]} valuePropName="fileList">
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserVoteOptionEdit;
