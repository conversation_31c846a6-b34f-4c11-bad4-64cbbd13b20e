import { makeApi } from "@/utils/Api";

const AppUserVoteOptionApi = {
    getAppUserVoteOption: makeApi("get", `/api/appUserVoteOption`),
    getAppUserVoteOptionInfo: makeApi("get", `/api/appUserVoteOption`, true),
    addAppUserVoteOption: makeApi("post", `/api/appUserVoteOption`),
    putAppUserVoteOption: makeApi("put", `/api/appUserVoteOption`),
    delAppUserVoteOption: makeApi("delete", `/api/appUserVoteOption`),
};

export default AppUserVoteOptionApi;
