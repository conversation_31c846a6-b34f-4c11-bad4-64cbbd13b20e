import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useRef } from "react";
import AppUserVoteOptionApi from "./api";
import AppUserVoteOptionEdit from "./edit";
import { Drawer, Form, Input, Popconfirm, Select, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import { useSetState } from "react-use";
import { StateMap } from "./types";

const AppUserVoteOptionPage = (props: { params?: any; children?: any; title: string; voteId?: string }) => {

  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserVoteOptionApi.delAppUserVoteOption({ data: { ids: item.id } });
    message.success("删除投票选项成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} width={"70%"} title={props.title} onClose={() => setState({ open: false })}>
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ voteId: props.voteId }}//默认参数
          request={(p) => AppUserVoteOptionApi.getAppUserVoteOption({ params: p })}
          title={
            <AppUserVoteOptionEdit title={"新增投票选项"} voteId={props.voteId} onOk={handleReload}>
              <WeTable.AddBtn />
            </AppUserVoteOptionEdit>
          }
          search={[
            <Form.Item label="名称" name={`name`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Select options={StateMap} placeholder="请选择状态" allowClear />
            </Form.Item>,
            <Form.Item label="创建日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
            { title: "累计票数", dataIndex: "totalVoteNum" },
            { title: "排序", dataIndex: "sort" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <AppUserVoteOptionEdit title={`编辑投票选项`} data={item} voteId={props.voteId} onOk={handleReload}>
                      <a>编辑</a>
                    </AppUserVoteOptionEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default AppUserVoteOptionPage;
