import { Modal, Popover } from "antd";
import { Fragment, useMemo } from "react";
import { PreState } from "./types";
import clsx from "clsx";
import { PlusCircleOutlined } from "@ant-design/icons";
import { useSetState } from "react-use";
import { useDuePane } from "./due-pane-context";
import UserManager from "@/components/UserManager";

export const PreItemInfo = (props: { data: any }) => {
  const data = props.data || {};
  const state = PreState.find((n) => n.id == data.state);

  return (
    <div className="w-50 p-2 pointer-events-auto" style={{ "--bg1": state?.color } as any}>
      <div className="flex items-center b-b-(1 dotted #eee) pb-2 mb-1">
        <div className="flex-1 min-w-0">
          {data.shopVipUserId ? (
            <UserManager userId={data.shopVipUserId}>
              <a>
                <div className="line-clamp-1 text-(sm)">{data.preName}</div>
                <div className="line-clamp-1 text-(xs #999)">{data.preMobile}</div>
              </a>
            </UserManager>
          ) : (<>
            <div className="line-clamp-1 text-(sm #000)">{data.preName}</div>
            <div className="line-clamp-1 text-(xs #999)">{data.preMobile}</div>
          </>
          )}
        </div>
        <div className="text-(xs #fff) px-1 rounded bg-[var(--bg1)]">{state?.name}</div>
      </div>

      <div className="flex text-(13px #222) my-0.5">
        <div className="flex-shrink-0 text-#777">预约门店：</div>
        <div className="break-all">{data.shopName}</div>
      </div>
      <div className="flex text-(13px #222) my-0.5">
        <div className="flex-shrink-0 text-#777">预约日期：</div>
        <div className="break-all">
          {data.dateStr} {data.timeStr}
        </div>
      </div>
      <div className="flex text-(13px #222) my-0.5">
        <div className="flex-shrink-0 text-#777">预约类型：</div>
        <div className="break-all" style={{ color: data.typeColor }}>
          {data.typeName}
        </div>
      </div>
      <div className="flex text-(13px #222) my-0.5">
        <div className="flex-shrink-0 text-#777">预约备注：</div>
        <div className="break-all">{data.content}</div>
      </div>
    </div>
  );
};

export const PreItem = (props: { className?: string; data: any }) => {
  const data = props.data;
  const ctx = useDuePane();

  return (
    <Popover content={<PreItemInfo data={data} />} classNames={{ body: "!p-0" }} arrow={false} placement="rightTop" autoAdjustOverflow destroyOnHidden align={{ offset: [6, 30] }}>
      <div
        className={clsx("bg-[var(--bg1)] hover:(bg-[var(--bg2)]) transition-all flex items-center rounded m-1 py-1 px-2 text-(#fff sm) cursor-pointer", props.className)}
        style={{ "--bg1": PreState.find((n) => n.id == data.state)?.color, "--bg2": PreState.find((n) => n.id == data.state)?.hoverColor } as any}
        onClick={() => ctx?.openInfo(data)}
      >
        <div className="flex-1 min-w-0">
          [{data.typeName?.slice(0, 2) || "??"}] {data.preName}
        </div>
        <div className="text-xs">{data.timeStr}</div>
      </div>
    </Popover>
  );
};

export const PaneMonth = (props: { data: any[] }) => {
  const ctx = useDuePane();
  const [state, setState] = useSetState({
    exopen: false,
    exid: "",

    infoOpen: false,
    infoData: null,
  });

  const exdata = useMemo(() => props.data.find((n) => n.ymd == state.exid), [state.exid, props.data]);

  return (
    <>
      <div className="grid cols-7 gap-1px bg-#ddd b-(1 solid #ddd) overflow-y-scroll">
        {"一二三四五六日".split("").map((n) => (
          <div key={n} className="h-10 flex items-center justify-center bg-#fff/60">
            周{n}
          </div>
        ))}
      </div>
      <div className="flex-1 basis-0 overflow-y-scroll">
        <div className="grid cols-7 gap-1px bg-#ddd b-(1 solid #ddd) b-t-0">
          {props.data.map((item: any) => {
            return (
              <div key={item.ymd} className="min-h-25 bg-#fff group">
                <div className="flex items-center p-2">
                  <div className="text-lg text-#333 data-[month=false]:text-#000/25" data-month={item.sameMonth}>
                    {item.dd}
                  </div>

                  <div className="flex-1 flex items-center rounded pl-2 text-(#999 xs)">
                    {item.children.length > 0 && <div>共{item.children.length}位顾客</div>}
                    {item.children.length > 5 && (
                      <div
                        className="text-blue-5 ml-1 cursor-pointer transition-all hover:text-blue-7"
                        onClick={() => {
                          setState({ exopen: true, exid: item.ymd });
                        }}
                      >
                        [全部]
                      </div>
                    )}
                    {item.sameMonth && !item.isDisable && (
                      <div
                        className="ml-a cursor-pointer text-#999 text-sm transition-all opacity-0 group-hover:opacity-100 hover:text-#333"
                        title="新增预约"
                        onClick={() => ctx?.openAdd({ preTime: item.ymd + " 10:00" })}
                      >
                        <PlusCircleOutlined />
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  {item.children.slice(0, 5).map((sub: any) => {
                    return <PreItem key={sub.id} data={sub} />;
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      <Modal
        open={state.exopen}
        onCancel={() => setState({ exopen: false })}
        classNames={{ content: "!p-2" }}
        footer={null}
        title={
          <div>
            <div className="text-center">{exdata?.day?.format("YYYY年MM月DD日")}</div>
            <div className="ml-2 text-(sm #999 center) fw-400">共{exdata?.children?.length}位顾客</div>
          </div>
        }
        width={280}
      >
        <div
          key={exdata?.ymd}
          className="min-h-25 py-4 bg-#fff group"
        // onClick={() => setState({ exopen: false })}
        >
          <div>
            {exdata?.children?.map((sub: any) => {
              return <PreItem key={sub.id} data={sub} />;
            })}
          </div>
        </div>
      </Modal>
    </>
  );
};

export const PaneWeek = (props: { data: any[]; weeks: any[] }) => {
  const ctx = useDuePane();

  return (
    <>
      <div className="grid cols-[50px_repeat(7,1fr)] gap-1px bg-#ddd b-(1 solid #ddd) overflow-y-scroll">
        <div className="h-10 flex items-center justify-center bg-#fff/60">时间</div>
        {props.weeks.map((n: any) => (
          <div key={n.dateStr} className="h-10 flex items-center justify-center bg-#fff/60">
            {n.title}
          </div>
        ))}
      </div>
      <div className="flex-1 basis-0 overflow-y-scroll">
        <div className="grid cols-[50px_repeat(7,1fr)] gap-1px bg-#ddd b-(1 solid #ddd) b-t-0">
          {props.data.map((item, idx) => {
            return (
              <Fragment key={idx}>
                {idx % 7 == 0 && <div className="min-h-10 bg-#fff flex items-center justify-center">{item.time}</div>}
                <div className="min-h-10 bg-#fff group">
                  <div className="flex items-center px-2 h-7">
                    {item.children.length > 0 && <div className="text-(#999 xs)">共{item.children.length}位顾客</div>}
                    {!item.isDisable && (
                      <div
                        className="ml-a h-7 flex items-center pr-1 text-(base #999) cursor-pointer transition-all opacity-0 group-hover:opacity-100 hover:text-#333"
                        title="新增预约"
                        onClick={() => {
                          ctx?.openAdd({ preTime: item.day + " " + item.time });
                        }}
                      >
                        <PlusCircleOutlined />
                      </div>
                    )}
                  </div>
                  <div className="flex items-center">
                    <div className="flex-1">
                      {item.children.map((sub: any) => {
                        return <PreItem key={sub.id} data={sub} />;
                      })}
                    </div>
                  </div>
                </div>
              </Fragment>
            );
          })}
        </div>
      </div>
    </>
  );
};

export const PaneDay = (props: { data: any[] }) => {
  const ctx = useDuePane();
  return (
    <>
      <div className="grid cols-[50px_repeat(1,1fr)] gap-1px bg-#ddd b-(1 solid #ddd) overflow-y-scroll">
        <div className="h-10 flex items-center justify-center bg-#fff/60">时间</div>
        <div className="h-10 flex items-center px-2 bg-#fff/60">客户</div>
      </div>
      <div className="flex-1 basis-0 overflow-y-scroll">
        <div className="grid cols-[50px_repeat(1,1fr)] gap-1px bg-#ddd b-(1 solid #ddd) b-t-0">
          {props.data.map((item: any) => {
            return (
              <Fragment key={item.time}>
                <div className="min-h-10 bg-#fff flex items-center justify-center">{item.time}</div>
                <div className="min-h-10 bg-#fff group">
                  <div className="flex flex-wrap">
                    {item.children.map((sub: any) => {
                      return <PreItem key={sub.id} className="w-50" data={sub} />;
                    })}
                    <div
                      className="h-9 flex items-center px-2 text-(base #999) cursor-pointer transition-all opacity-0 group-hover:opacity-100 hover:text-#333"
                      title="新增预约"
                      onClick={() => {
                        ctx?.openAdd({ preTime: item.day + " " + item.time });
                      }}
                    >
                      <PlusCircleOutlined />
                    </div>
                  </div>
                </div>
              </Fragment>
            );
          })}
        </div>
      </div>
    </>
  );
};
