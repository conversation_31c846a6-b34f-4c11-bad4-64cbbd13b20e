import { Button, Checkbox, Divider, Form, Radio, Spin } from "antd";
import { useSetState } from "react-use";
import { DuePaneProvider } from "./due-pane-context";
import dayjs from "dayjs";
import * as Api from "./api";
import UserApi from "@/services/UserApi";
import { PreState } from "./types";
import { useEffect, useMemo } from "react";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { PaneDay, PaneMonth, PaneWeek } from "./due-pane-unit";
import { DueInfo } from "./due-info";
import { DueAdd } from "./due-add";
import { DueModal } from "./DueModal";

export const DuePane = (props: { onModeChange?: (m: any) => any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    ptype: "day" as "month" | "week" | "day",
    pdate: dayjs(),
    refresh: 0,

    states: [] as any[],
    types: [] as any[],
    typesOk: false,

    pane: [] as any[],
    paneType: "day" as "month" | "week" | "day",
    paneLoading: false,
    paneWeeks: [] as any[],

    infoOpen: false,
    infoData: null as any,

    addOpen: false,
    addData: null as any,

    editOpen: false,
    editData: null as any,
  });

  useEffect(() => {
    fetchTypes();
  }, []);

  useEffect(() => {
    if (!state.typesOk) return;
    fetchPane();
  }, [state.pdate, state.ptype, state.refresh, state.typesOk]);

  const calendarDays = useMemo(() => {
    const start = state.pdate.startOf("month").startOf("week");
    const days = Array(42)
      .fill(null)
      .map((_, i) => start.add(i, "day"))
      .map((n) => ({
        day: n,
        ymd: n.format("YYYY-MM-DD"),
        dd: n.format("DD"),
        sameMonth: state.pdate.isSame(n, "month"),
        select: state.pdate.isSame(n, "day"),
      }));
    return days;
  }, [state.pdate]);

  const paneTitle = useMemo(() => {
    if (state.ptype === "day") {
      return (
        <>
          <Button size="small" shape="circle" icon={<LeftOutlined />} onClick={() => setState({ pdate: state.pdate.subtract(1, "day") })} />
          <div className="text-(base #111) fw-bold px-2">{state.pdate.format("YYYY年MM月DD日")}</div>
          <Button size="small" shape="circle" icon={<RightOutlined />} onClick={() => setState({ pdate: state.pdate.add(1, "day") })} />
          <Button className="ml-4" onClick={() => setState({ pdate: dayjs() })}>
            回到今天
          </Button>
        </>
      );
    } else if (state.ptype === "week") {
      return (
        <>
          <Button size="small" shape="circle" icon={<LeftOutlined />} onClick={() => setState({ pdate: state.pdate.subtract(1, "week").startOf("week") })} />
          <div className="text-(base #111) fw-bold px-2">{state.pdate.startOf("week").format("YYYY年MM月DD日") + " - " + state.pdate.endOf("week").format("MM月DD日")}</div>
          <Button size="small" shape="circle" icon={<RightOutlined />} onClick={() => setState({ pdate: state.pdate.add(1, "week").startOf("week") })} />
          <Button className="ml-4" onClick={() => setState({ pdate: dayjs() })}>
            回到本周
          </Button>
        </>
      );
    } else if (state.ptype === "month") {
      return (
        <>
          <Button size="small" shape="circle" icon={<LeftOutlined />} onClick={() => setState({ pdate: state.pdate.subtract(1, "month").startOf("month") })} />
          <div className="text-(base #111) fw-bold px-2">{state.pdate.format("YYYY年MM月")}</div>
          <Button size="small" shape="circle" icon={<RightOutlined />} onClick={() => setState({ pdate: state.pdate.add(1, "month").startOf("month") })} />
          <Button className="ml-4" onClick={() => setState({ pdate: dayjs() })}>
            回到本月
          </Button>
        </>
      );
    }

    return null;
  }, [state.pdate, state.ptype]);

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params: params });
    //const colors = ["#f5222d", "#fa8c16", "#13c2c2", "#52c41a", "#1677ff", "#722ed1", "#fadb14"];
    const types: any[] = res?.map((n: any, _: number) => ({ id: n.id, name: n.name, color: "#444444", num: 0 })) || [];
    setState({ types, typesOk: true });
  };

  const fetchPane = async () => {
    try {
      const ptype = state.ptype;
      const pdate = state.pdate;

      let params: any = {};

      if (ptype == "month") {
        params.startPreTime = dayjs(pdate).startOf("month").format("YYYY-MM-DD");
        params.endPreTime = dayjs(pdate).endOf("month").format("YYYY-MM-DD");
      }

      if (ptype == "week") {
        params.startPreTime = dayjs(pdate).startOf("week").format("YYYY-MM-DD");
        params.endPreTime = dayjs(pdate).endOf("week").format("YYYY-MM-DD");
      }

      if (ptype == "day") {
        params.startPreTime = dayjs(pdate).format("YYYY-MM-DD");
        params.endPreTime = dayjs(pdate).format("YYYY-MM-DD");
      }

      const val = await form.validateFields();
      val.state = val.state?.join(",") || "-1";
      val.typeId = val.typeId?.join(",") || "-1";

      params = { ...params, ...val };
      // console.log("params", params);

      const res = await Api.getPane({ params });

      const list: any[] = [];
      const times: any[] = res?.list?.[0]?.timeList?.map((n: any) => n.timeStr) || [];
      const days: any[] = res?.list?.map((n: any) => n.dateStr);

      res?.list?.forEach((lv1: any) => {
        const dateStr = lv1.dateStr;
        lv1.timeList.forEach((lv2: any) => {
          const timeStr = lv2.timeStr;
          lv2.reservationList.forEach((lv3: any) => {
            list.push({ ...lv3, dateStr, timeStr });
          });
        });
      });

      const types = state.types.map((n) => ({ ...n, num: 0 }));
      const states = PreState.map((n) => ({ ...n, num: 0 }));

      list.forEach((item) => {
        const type = types.find((n) => n.id == item.typeId);
        if (type) type.num++;
        item.typeColor = type?.color;

        const state = states.find((n) => n.id == item.state);
        if (state) state.num++;
      });

      let pane: any[] = [];
      let weeks: any[] = [];

      if (ptype == "month") {
        const start = pdate.startOf("month").startOf("week");
        const end = pdate.endOf("month").endOf("week");
        const num = end.diff(start, "day");

        pane = Array(num + 1)
          .fill(null)
          .map((_, i) => {
            const day = start.add(i, "day");
            const ymd = day.format("YYYY-MM-DD");
            const dd = day.format("DD");
            const sameMonth = pdate.isSame(day, "month");
            const children = list.filter((n: any) => n.dateStr == ymd) || [];
            const isDisable = dayjs().isAfter(day, "day");
            return { day, ymd, dd, sameMonth, children, isDisable };
          });
      }

      if (ptype == "week") {
        weeks = days.map((n: any, i: any) => ({ dateStr: n, title: dayjs(n).format("MM/DD") + " 周" + "一二三四五六日".split("")[i] }));

        times.forEach((time: any) => {
          days.forEach((day: dayjs.Dayjs) => {
            const children = list?.filter((n: any) => n.timeStr == time && n.dateStr == day) || [];
            const isDisable = dayjs().isAfter(day, "day");
            const item = { time, day, children, isDisable };
            pane.push(item);
          });
        });
      }

      if (ptype == "day") {
        const day = state.pdate.format("YYYY-MM-DD");
        pane =
          times.map((time: any) => {
            const children = list?.filter((n: any) => n.timeStr == time) || [];
            return { day, time, children };
          }) || [];
      }

      // console.log("fetchPane", ptype, pane);
      setState({ types, states, pane, paneType: ptype, paneWeeks: weeks, paneLoading: false });
    } catch (err) {
      setState({ paneLoading: false });
      throw err;
    }
  };

  const handleReload = () => {
    setState({ refresh: state.refresh + 1 });
  };

  const openInfo = (data?: any) => {
    setState({ infoOpen: true, infoData: data });
  };

  const closeInfo = () => {
    setState({ infoOpen: false });
  };

  const openAdd = (data?: any) => {
    setState({ addOpen: true, addData: data });
  };

  const closeAdd = () => {
    setState({ addOpen: false });
  };

  const contextValue = {
    openInfo,
    openAdd,
  };

  return (
    <DuePaneProvider value={contextValue}>
      <div className="flex-1 flex flex-col min-w-0 p-2 overflow-hidden">
        <Form form={form}>
          <div className="mb-2 rounded bg-#fff flex flex-wrap items-center gap-2 p-2 [&_.ant-form-item]:mb-0">
            <div className="ws-nowrap">
              <Form.Item name={"state"} label="预约状态" initialValue={PreState.map((n) => n.id)}>
                <Checkbox.Group options={PreState.map((n) => ({ label: n.name, value: n.id }))} onChange={handleReload} />
              </Form.Item>
            </div>

            {state.typesOk && (
              <div className="ml-10 ws-nowrap">
                <Form.Item name={"typeId"} label="预约类型" initialValue={state.types.map((n) => n.id)}>
                  <Checkbox.Group options={state.types.map((n) => ({ label: n.name, value: n.id }))} onChange={handleReload} />
                </Form.Item>
              </div>
            )}

            <div className="ml-10">
              <Button
                size="small"
                type="link"
                onClick={() => {
                  form.resetFields();
                  handleReload();
                }}
              >
                重置
              </Button>
            </div>

            <div className="ml-a">
              <Radio.Group
                value={"pane"}
                options={[
                  { label: "列表", value: "list" },
                  { label: "日历", value: "pane" },
                ]}
                optionType="button"
                onChange={(e) => props.onModeChange?.(e.target.value)}
              />
            </div>
          </div>
        </Form>

        <div className="flex-1 min-h-0 flex rounded overflow-hidden pos-relative ">
          <div
            className="z-50 pos-absolute top-0 left-0 size-full bg-#fff/80 flex items-center justify-center transition-all invisible opacity-0 data-[on=ture]:(visible opacity-0)"
            data-on={state.paneLoading}
          >
            <Spin spinning size="large"></Spin>
          </div>

          <aside className="w-60 bg-#fff rounded mr-2 overflow-y-auto">
            <section>
              <header className="flex items-center p-2">
                <Button
                  className="text-#999"
                  type="text"
                  size="small"
                  shape="circle"
                  icon={<LeftOutlined className="text-inherit" />}
                  onClick={() => {
                    setState({ pdate: state.pdate.subtract(1, "month").startOf("month") });
                  }}
                />
                <div className="flex-1 text-(lg #333 center)">{state.pdate.format("YYYY年MM月")}</div>
                <Button
                  className="text-#999"
                  type="text"
                  size="small"
                  shape="circle"
                  icon={<RightOutlined className="text-inherit" />}
                  onClick={() => {
                    setState({ pdate: state.pdate.add(1, "month").startOf("month") });
                  }}
                />
              </header>

              <div className="grid cols-7 p-1">
                {"一二三四五六日".split("").map((n) => (
                  <div key={n} className="flex items-center justify-center h-8">
                    {n}
                  </div>
                ))}
              </div>
              <div className="grid cols-7 p-1">
                {calendarDays.map((day) => {
                  return (
                    <div
                      key={day.ymd}
                      className="flex items-center justify-center h-8 rounded cursor-pointer hover:bg-#f5f5f5 data-[on=true]:(bg-#c48053 text-#fff) data-[on=true]:hover:bg-#c48053 data-[month=false]:text-#000/25"
                      data-on={day.select}
                      data-month={day.sameMonth}
                      onClick={() => setState({ pdate: day.day })}
                    >
                      {day.dd}
                    </div>
                  );
                })}
              </div>
            </section>
            <div className="b-t-(1 solid #eee) px-2">
              <header className="py-2 flex items-center">
                <b className="w-1 h-4 bg-blue mr-2" />
                <div className="flex-1 text-(base #000) fw-bold">预约状态</div>
                <span className="text-(sm #999)">人数</span>
              </header>
              <div className="pb-3">
                {state.states.map((n) => (
                  <div key={n.id} className="flex items-center justify-between py-1">
                    <div className="flex items-center">
                      <div className="size-2 rounded-full mr-2" style={{ backgroundColor: n.color }}></div>
                      <div style={{ color: n.color }}>{n.name}</div>
                    </div>
                    <span className="text-(sm #111)">{n.num}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="b-t-(1 solid #eee) px-2">
              <header className="py-2 flex items-center">
                <b className="w-1 h-4 bg-blue mr-2" />
                <div className="flex-1 text-(base #000) fw-bold">预约类型</div>
                <span className="text-(sm #999)">人数</span>
              </header>
              <div className="pb-3">
                {state.types.map((n) => (
                  <div key={n.id} className="flex items-center justify-between py-1">
                    <div className="flex items-center">
                      <div className="size-2 rounded-full mr-2" style={{ backgroundColor: n.color }}></div>
                      <div style={{ color: n.color }}>{n.name}</div>
                    </div>
                    <span className="text-(sm #111)">{n.num}</span>
                  </div>
                ))}
              </div>
            </div>
          </aside>
          <div className="flex-1 min-w-0 flex flex-col bg-#fff rounded overflow-hidden">
            <header className="h-12 flex items-center px-2 bg-#fff">
              {paneTitle}
              <div className="flex-1"></div>
              <Radio.Group
                value={state.ptype}
                onChange={(e) => setState({ ptype: e.target.value })}
                options={[
                  { label: "日", value: "day" },
                  { label: "周", value: "week" },
                  { label: "月", value: "month" },
                ]}
                optionType="button"
              />
              <Divider type="vertical" />

              <Button className="" type="primary" onClick={() => openAdd()}>
                新增预约
              </Button>

              <DueModal onChange={handleReload}>
                <Button className="ml-2" type="primary">
                  放号管理
                </Button>
              </DueModal>
            </header>

            {state.paneType == "month" && <PaneMonth data={state.pane} />}
            {state.paneType == "week" && <PaneWeek data={state.pane} weeks={state.paneWeeks} />}
            {state.paneType == "day" && <PaneDay data={state.pane} />}
          </div>
        </div>
      </div>

      <DueInfo open={state.infoOpen} data={state.infoData} onClose={closeInfo} onChange={handleReload} />
      <DueAdd open={state.addOpen} data={state.addData} onClose={closeAdd} onOk={handleReload} />
      {/* <DueEdit open={state.editOpen} data={state.editData} onClose={closeEdit} onOk={handleReload} /> */}
    </DuePaneProvider>
  );
};
