import { Switch01 } from "@/components/Units/Switch01";
import User<PERSON><PERSON> from "@/services/UserApi";
import { DeleteOutlined, LeftCircleOutlined, RightCircleOutlined } from "@ant-design/icons";
import { Badge, Button, Calendar, DatePicker, Drawer, InputNumber, message, Radio } from "antd";
import { cloneElement, useEffect } from "react";
import { useMount, useSetState } from "react-use";
import * as <PERSON><PERSON><PERSON> from "./api";
import dayjs from "dayjs";

let id = 0;

export const DueModal = (props: any) => {
  const [state, setState] = useSetState({
    open: false,
    types: [] as any[],
    type: "",
    mkey: 1,

    date: dayjs(),
    dateList: [] as any[],
    list: [] as any[],
    preTag: 1 as any,
  });

  useMount(() => {
    fetchTypes();
  });

  useEffect(() => {
    if (!state.type) return;
    fetchData();
  }, [state.type, state.date]);

  useEffect(() => {
    const item = state.dateList.find((n) => n.preDate == state.date.format("YYYY-MM-DD"));
    let list: any[] = item?.timeList || [];
    setState({ list, preTag: item?.preTag ?? 1 });
  }, [state.date, state.dateList]);

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((n) => ({ label: n.name, value: n.id }));
    // list.unshift({ label: "全部", value: "" });
    setState({ types: list, type: list?.[0]?.value });
  };

  const fetchData = async () => {
    const start = state.date.startOf("month").format("YYYY-MM-DD");
    const end = state.date.endOf("month").format("YYYY-MM-DD");
    const params = { typeId: state.type, startPreDate: start, endPreDate: end };
    const res = await DueApi.getDate({ params });
    setState({ dateList: res?.list || [] });
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        onClose={() => setState({ open: false })}
        keyboard={false}
        width={"100vw"}
        title={
          <>
            <div className="flex items-center">
              <div>放号管理</div>
            </div>
          </>
        }
      >
        <div className="h-full flex flex-col">
          <Radio.Group className="ml-4" options={state.types} buttonStyle="solid" optionType="button" value={state.type} onChange={(e) => setState({ type: e.target.value })}></Radio.Group>

          <div className="flex-1 min-h-0 mt-4 flex">
            <Calendar
              value={state.date}
              onChange={(e) => setState({ date: e })}
              className="flex-1"
              headerRender={(e) => {
                return (
                  <div className="flex items-center justify-center py-4 gap-4 select-none">
                    <LeftCircleOutlined className="text-(2xl gray) cursor-pointer" onClick={() => setState({ date: state.date.subtract(1, "month") })} />
                    <div className="text-2xl">{e.value.format("YYYY年MM月")}</div>
                    <RightCircleOutlined className="text-(2xl gray) cursor-pointer" onClick={() => setState({ date: state.date.add(1, "month") })} />
                  </div>
                );
              }}
              cellRender={(date) => {
                const item = state.dateList.find((n) => n.preDate == date.format("YYYY-MM-DD"));
                const preNum = item?.preNum || 0;

                return (
                  <div className="flex justify-center pt-4">
                    <Badge count={preNum} />
                  </div>
                );
              }}
            />

            <div className="w-[400px] ml-4 b-l-(1 solid #eee) px-4 flex flex-col">
              <div className="grid gap-2 cols-[150px_100px_50px_auto] items-center py-2 b-b-(1 solid #eee)">
                <div>时间</div>
                <div>人数</div>
                <div>
                  <Switch01 value={state.preTag} onChange={(e: any) => setState({ preTag: e })}></Switch01>
                </div>
              </div>

              <div className="flex-1 min-h-0 overflow-y-auto py-4">
                <div className="grid cols-1 gap-1">
                  {!state.list.length && <div className="text-([14px] center #999) py-10">暂无数据</div>}

                  {state.list.map((item, idx) => (
                    <div className="grid gap-2 cols-[150px_100px_50px_auto] items-center" key={item.id}>
                      <DatePicker
                        picker="time"
                        format={"HH:mm"}
                        showNow={false}
                        minuteStep={10}
                        allowClear={false}
                        value={dayjs(dayjs().format("YYYY-MM-DD " + item.preTime + ":00"))}
                        onChange={(e) => {
                          const v = e.format("HH:mm");
                          const list = state.list;
                          list[idx].preTime = v;
                          setState({ list: [...list] });
                        }}
                      ></DatePicker>

                      <InputNumber
                        min={1}
                        addonAfter="人"
                        value={item.preNum}
                        onChange={(e) => {
                          const v = e;
                          const list = state.list;
                          list[idx].preNum = v;
                          setState({ list: [...list] });
                        }}
                      />
                      <div>
                        <Switch01
                          value={item.preTag}
                          onChange={(e: any) => {
                            const v = e;
                            const list = state.list;
                            list[idx].preTag = v;
                            setState({ list: [...list] });
                          }}
                        ></Switch01>
                      </div>
                      <div>
                        <DeleteOutlined
                          className="ml-2 text-red-4 cursor-pointer"
                          onClick={() => {
                            const list = state.list.filter((n) => n.id != item.id);
                            setState({ list });
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex gap-5 justify-end">
                <Button
                  type="primary"
                  onClick={() => {
                    const list = [...state.list];
                    list.push({ id: ++id, preTime: "", preNum: 1, preTag: 1 });
                    setState({ list });
                  }}
                >
                  添加
                </Button>
                <Button
                  type="primary"
                  onClick={async () => {
                    const data = {
                      typeId: state.type,
                      preDate: state.date.format("YYYY-MM-DD"),
                      preTag: state.preTag,
                      timeList: state.list,
                    };
                    await DueApi.saveDue({ data });
                    message.success("保存成功");
                    props.onChange?.();
                    fetchData();
                  }}
                >
                  保存
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Drawer>
    </>
  );
};
