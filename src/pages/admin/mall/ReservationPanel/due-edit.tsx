import UserPane from "@/components/Units/UserPane";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import User<PERSON><PERSON> from "@/services/UserApi";
import { App, Card, Col, Form, Input, Modal, Radio, Row } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";
import { PickDateTime } from "./PickDateTime";
import { formatDate } from "@/utils/Tools";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

export const DueEdit = (props: { open?: boolean; onClose?: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();
  const { message } = App.useApp();

  const [state, setState] = useSetState({
    user: null as any,
    types: [] as any[],
  });

  useEffect(() => {
    if (!props.open) return;

    form.resetFields();
    setState({ user: null });
    fetchTypes();

    if (props.data) {
      const data = { ...props.data };
      data.preTime = formatDate(data.preTime, "YYYY-MM-DD HH:mm");
      data.images = UploadV2.str2arr(data.images);
      form.setFieldsValue(data);
      if (data.shopVipUserId) {
        fetchUser();
      }
    }
  }, [props.open]);

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = (res || [])?.map((n: any) => ({ label: n.name, value: n.id }));
    setState({ types: list });
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.images = UploadV2.arr2str(data.images);

    await MallApi.putReservation({ data });
    message.success("修改预约成功");
    props.onClose?.();
    props.onOk?.(data);
  };

  return (
    <Modal
      zIndex={1002}
      open={props.open}
      title={
        <div className="flex gap-4 fw-normal">
          <div className="fw-bold">编辑预约</div>
        </div>
      }
      width={1000}
      onOk={onOk}
      onCancel={props.onClose}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <div className="h-4"></div>

        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>

        {!!props.data?.shopVipUserId && (
          <div className="mb-4 min-h-110px">
            <UserPane user={state.user} />
          </div>
        )}

        {!props.data?.shopVipUserId && (
          <Card className="mb-4" size="small" title="客户信息">
            <Row>
              <Col span={8}>
                <Form.Item label="客户姓名" name={`preName`} rules={[{ required: false, message: "请输入客户姓名" }]}>
                  <Input placeholder="请输入客户姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="客户手机" name={`preMobile`} rules={[{ required: false, message: "请输入客户手机" }]}>
                  <Input placeholder="请输入客户手机" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        <Card size="small" title="预约信息">
          <Form.Item label="预约类型" name={`typeId`} rules={[{ required: true, message: "请选择预约类型" }]}>
            <Radio.Group options={state.types} />
          </Form.Item>

          <Row>
            <Col span={8}>
              <Form.Item noStyle shouldUpdate={(p, c) => p["typeId"] !== c["typeId"]}>
                {(ins) => {
                  const typeId = ins.getFieldValue(`typeId`);
                  console.log("typeId", typeId);
                  return (
                    <Form.Item label="预约日期" name={`preTime`} rules={[{ required: true, message: "请选择预约日期" }]}>
                      <PickDateTime typeId={typeId} />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>

          <Row>
            <Col span={16}>
              <Form.Item label="预约备注" name={`content`} rules={[{ required: true, message: "请输入预约备注" }]}>
                <Input.TextArea autoSize={{ minRows: 2 }} placeholder="请输入预约备注" maxLength={400} showCount />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="附件" name={`images`}>
            <UploadV2 maxCount={20} />
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};
