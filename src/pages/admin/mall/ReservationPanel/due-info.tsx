import Mall<PERSON><PERSON> from "@/services/MallApi";
import { <PERSON><PERSON>, <PERSON><PERSON>, But<PERSON>, Descriptions, Image, Modal } from "antd";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useSetState } from "react-use";
import UserManager from "@/components/UserManager";
import { PreState } from "./types";
import DaoDian from "./DaoDian";
import { DueEdit } from "./due-edit";
import DataRelateNew from "./DataRelateNew";
import { str2arr } from "@/utils/Tools";

export const DueInfo = (props: { open?: boolean; onClose?: any; data?: any; onChange?: any }) => {
  const { modal, message } = App.useApp();
  const [state, setState] = useSetState({
    info: {} as any,

    editOpen: false,
    editData: null as any,
  });

  useEffect(() => {
    if (!props.data?.id) return;
    fetchInfo();
  }, [props.data?.id]);

  const fetchInfo = async () => {
    const res = await MallApi.getReservationInfo(props.data?.id);
    setState({ info: res });
  };

  const onDataChange = async () => {
    props.onClose?.();
    props.onChange?.();
  };

  const onConfirm = async () => {
    modal.confirm({
      title: (
        <div>
          确定要<b className="text-red-5">确认</b>当前预约吗？
        </div>
      ),
      content: (
        <div className="flex">
          <div className="text-(sm #333)">{state.info.preName}</div>
          <div className="text-(sm #999) ml-2">({state.info.preMobile})</div>
        </div>
      ),
      onOk: async () => {
        const data = { ids: state.info.id };
        await MallApi.confirmReservation({ data });
        message.success("确认成功");
        onDataChange();
      },
    });
  };

  const onCancel = async () => {
    modal.confirm({
      title: (
        <div>
          确定要<b className="text-red-5">取消</b>当前预约吗？
        </div>
      ),
      content: (
        <div className="flex">
          <div className="text-(sm #333)">{state.info.preName}</div>
          <div className="text-(sm #999) ml-2">({state.info.preMobile})</div>
        </div>
      ),
      onOk: async () => {
        const data = { ids: state.info.id };
        await MallApi.delReservation({ data });
        message.success("取消成功");
        onDataChange();
      },
    });
  };

  return (
    <>
      <Modal open={props.open} onCancel={props.onClose} width={500} title="预约详情" footer={null} zIndex={1001}>
        {!state.info.shopVipUserId && (
          <Alert
            message="当前客户未关联会员"
            type="warning"
            showIcon
            action={
              <DataRelateNew
                title={"关联会员"}
                data={state.info}
                onOk={() => {
                  fetchInfo();
                  props.onChange?.();
                }}
              >
                <Button type="link" size="small">
                  去关联
                </Button>
              </DataRelateNew>
            }
          />
        )}

        <div className="pt-4 pb-8">
          <Descriptions
            column={2}
            items={[
              {
                label: "预约姓名",
                span: "filled",
                children: (
                  <div className="flex items-center">
                    {state.info.shopVipUserId ? (
                      <UserManager userId={state.info.shopVipUserId}>
                        <a>
                          {state.info.preName} &nbsp;{" "}
                          {state.info?.preMobile ? (
                            <>
                              <span className="text-(xs #999)">{state.info.preMobile}</span>
                            </>
                          ) : (
                            ""
                          )}
                        </a>
                      </UserManager>
                    ) : (
                      <div className="flex items-center">
                        <span>
                          {state.info.preName} &nbsp;{" "}
                          {state.info?.preMobile ? (
                            <>
                              <span className="text-(xs #999)">{state.info.preMobile}</span>
                            </>
                          ) : (
                            ""
                          )}
                        </span>
                      </div>
                    )}

                    <div className="text-(xs #fff) fw-400 px-2  rounded ml-8" style={{ backgroundColor: PreState.find((n) => n.id == state.info?.state)?.color }}>
                      {PreState.find((n) => n.id == state.info?.state)?.name}
                    </div>
                  </div>
                ),
              },

              { label: "预约门店", span: "filled", children: state.info.shopName || "--" },

              { label: "预约日期", span: "filled", children: dayjs(state.info.preTime).format("YYYY-MM-DD HH:mm") || "--" },
              { label: "预约类型", children: state.info.typeName || "--" },

              { label: "到院类型", children: state.info.triageTypeName || "--" },
              { label: "预约医生", children: state.info.doctorName || "--" },
              { label: "预约项目", children: state.info.productName || "--" },

              { label: "预约备注", span: "filled", children: state.info.content || "--" },
              {
                label: "预约附件",
                span: "filled",
                children: (
                  <Image.PreviewGroup>
                    <div className="flex flex-wrap gap-2">
                      {str2arr(state.info.images).map((n: any, i: any) => {
                        return <Image key={i} src={n} width={80} height={80} />;
                      })}
                    </div>
                  </Image.PreviewGroup>
                ),
              },
            ]}
          />
        </div>

        <div className="flex gap-2">
          <Button type="primary" onClick={() => setState({ editOpen: true, editData: state.info })} disabled={![10, 20, 40].includes(state.info.state)}>
            编辑
          </Button>
          <Button danger onClick={onCancel} disabled={![10, 20, 40].includes(state.info.state)}>
            取消
          </Button>
          <Button onClick={onConfirm} disabled={![10].includes(state.info.state)}>
            确认
          </Button>
          <DaoDian title={`到院`} onOk={onDataChange} data={state.info}>
            <Button disabled={![10, 20, 40].includes(state.info.state) || !state.info.vipUser}>到院</Button>
          </DaoDian>
        </div>
      </Modal>
      <DueEdit
        open={state.editOpen}
        data={state.editData}
        onClose={() => setState({ editOpen: false })}
        onOk={() => {
          fetchInfo();
          props.onChange?.();
        }}
      />
    </>
  );
};
