import DictCheckbox from "@/components/Units/DictCheckbox";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, Select, Switch, message } from "antd";
import { useSetState } from "react-use";

const EditModal = (props: { children: any; title: any; onOk: Function; data?: any; shopId: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    users: [] as any[],
  });

  const getUser = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopUserSelect({ params });
    // console.log(res);
    let users: any[] = res?.list || [];
    users = users.map((n) => ({ value: n.id, label: n.organizeName + " - " + n.name }));
    setState({ users });
  };

  const handleOpen = () => {
    getUser();
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      data.state = !!data.state;
      data.sysUserId = [data.sysUserId];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.sysUserId = data.sysUserId.join(",");
    data.state = Number(data.state);
    data.shopId = props.shopId;

    if (data.id) {
      await MallApi.putShopStaff({ data });
    } else {
      await MallApi.addShopStaff({ data });
    }

    message.success("操作成功");
    props.onOk();
  };

  return (
    <WeModal title={props.title} trigger={props.children} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "60px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Form.Item label="员工" name={`sysUserId`} rules={[{ required: true, message: "请选择员工" }]}>
          <Select
            placeholder="请选择员工"
            options={state.users}
            showSearch
            allowClear
            mode="multiple"
            disabled={!!props.data?.id}
          />
        </Form.Item>
        <Form.Item label="职位" name={`positionId`}>
          <DictCheckbox type="EmployeePosition" />
        </Form.Item>
        <Form.Item label="状态" name={`state`} valuePropName="checked" initialValue={true}>
          <Switch />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default EditModal;
