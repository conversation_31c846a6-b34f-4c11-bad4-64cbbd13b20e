import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Card, Popconfirm, Space, Tag, message } from "antd";
import { useRef } from "react";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import EditModal from "./edit";

const ShopEmployee = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    shops: [] as any[],
    shopId: "",
    user: null as any,
  });

  useMount(() => {
    fetchUser();
    fetchShops();
  });

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user, shopId: user?.currentShopId || "" });
  };

  const fetchShops = async () => {
    const res = await MallApi.getShopListForManage();
    let list: any[] = res.list || [];
    list = list.map((n) => ({ tab: n.name, key: n.id }));
    setState({ shops: list });
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delShopStaff({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={state.shops}
        activeTabKey={state.shopId}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(shopId) => setState({ shopId })}
      />
      <WeTable
        autoLoad={false}
        ref={tableRef}
        title={
          <EditModal title={`新增员工`} shopId={state.shopId} onOk={handleReload}>
            <WeTable.AddBtn />
          </EditModal>
        }
        params={{ shopId: state.shopId }}
        request={(p) => MallApi.getShopStaff({ params: p })}
        columns={[
          { title: "门店", dataIndex: "shopName" },
          { title: "姓名", dataIndex: "user", render: (c) => c?.name },
          { title: "手机", dataIndex: "user", render: (c) => c?.mobile },
          { title: "职位", dataIndex: "position", render: (c) => <span>{c || "--"}</span> },
          {
            title: "状态",
            dataIndex: "state",
            render: (c) => (c ? <Tag color="green">启用</Tag> : <Tag color="gray">禁用</Tag>),
          },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <EditModal title={`编辑员工 - ${item.user.name}`} shopId={state.shopId} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </EditModal>
                  <Popconfirm title={`确定要删除 ${item.user.name}吗?`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default ShopEmployee;
