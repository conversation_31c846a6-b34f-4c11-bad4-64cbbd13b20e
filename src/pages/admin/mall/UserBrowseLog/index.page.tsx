import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserBrowseLogApi from "./api";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const AppUserBrowseLogPage = (props: { userId?: any }) => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserBrowseLogApi.delAppUserBrowseLog({ data: { ids: item.id } });
    message.success("删除页面浏览记录成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        size={props.userId ? 10 : undefined}
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ appUserId: props.userId }}
        request={(p) => AppUserBrowseLogApi.getAppUserBrowseLog({ params: p })}
        search={[
          !props.userId && (
            <Form.Item label="会员" name={`appUserId`}>
              <VipPickerInput />
            </Form.Item>
          ),
          <Form.Item label="微信页面路径" name={`wxPagePath`}>
            <Input placeholder="请输入微信页面路径" />
          </Form.Item>,
          <Form.Item label="标题" name={`title`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          {
            fixed: "left",
            title: "会员姓名",
            dataIndex: "vipUser",
            hide: props.userId,
            render: (c) => (
              c ? <UserManager userId={c?.id}>
                <a>{c?.name}</a>
              </UserManager>
                : "--"
            ),
          },
          { title: "微信页面路径", dataIndex: "wxPagePath", render: (c) => c ? c : "--" },
          { title: "标题", dataIndex: "title", render: (c) => c ? c : "--" },
          { title: "进入时间", dataIndex: "entryDate", render: (c) => c ? fdate(c) : "--" },
          { title: "离开时间", dataIndex: "exitDate", render: (c) => c ? fdate(c) : "--" },
          { title: "停留时长(秒)", dataIndex: "stayLength", render: (c) => c ? c : "--" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppUserBrowseLogPage;
