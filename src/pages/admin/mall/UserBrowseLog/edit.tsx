import WeModal from "@/components/WeModal/WeModal";
import AppUserBrowseLogApi from "./api";
import { Col, Form, Input, Row } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";

const layout = { row: 10, col: 24 };

const AppUserBrowseLogEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserBrowseLogApi.getAppUserBrowseLogInfo(props.data.id);
      data.entryDate = data.entryDate ? dayjs(data.entryDate) : "";
      data.exitDate = data.exitDate ? dayjs(data.exitDate) : "";
      form.setFieldsValue(data);
    }
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`appUserId`} label="用户id" rules={[{ required: false, message: "请输入用户id" }]}>
              <Input placeholder="请输入用户id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`wxAppId`} label="微信appId" rules={[{ required: false, message: "请输入微信appId" }]}>
              <Input placeholder="请输入微信appId" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`wxPagePath`} label="微信页面路径" rules={[{ required: false, message: "请输入微信页面路径" }]}>
              <Input placeholder="请输入微信页面路径" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`moduleName`} label="模块名称" rules={[{ required: false, message: "请输入模块名称" }]}>
              <Input placeholder="请输入模块名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`entryDate`} label="进入时间" rules={[{ required: false, message: "请选择进入时间" }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`exitDate`} label="离开时间" rules={[{ required: false, message: "请选择离开时间" }]}>
              <DatePicker style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`stayLength`} label="停留时长(秒)" rules={[{ required: false, message: "请输入停留时长(秒)" }]}>
              <Input placeholder="请输入停留时长(秒)" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserBrowseLogEdit;
