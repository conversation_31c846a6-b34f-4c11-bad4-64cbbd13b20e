import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, Row, message } from "antd";
import { Api } from "./Api";

const layout = { row: 10, col: 12 };

const OperateSold = (props: { children: any; title: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    await Api.operateSold({ data });
    message.success("操作成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="增减销量" name={`num`} initialValue={1}>
              <InputNumber placeholder="请输入增减销量" className="w-full" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default OperateSold;
