import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import RichText from "@/components/RichText";
import SelectShop from "@/components/Units/SelectShop";
import { arr2str, DatePresetRanges, formatDateRange, str2arr } from "@/utils/Tools";
import { Button, DatePicker, Divider, Form, Input, InputNumber, message, Radio, Select } from "antd";
import { PickType } from "./pick-type";
import { Api } from "../Api";
import { useMount, useSetState } from "react-use";
import { SwitchContent, SwitchOC } from "./unit";
import dayjs from "dayjs";
import MallCommissionConfigApi from "../../../operation/CommissionConfig/api";
import DictPicker from "@/components/Units/DictPicker";

const property = 2;
export const Step0 = (props: { pid?: string; goNext?: (id: string) => any; onClose?: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    commissionConfigList: [] as any[],
  });

  useMount(() => {
    fetchCommissionConfig();
    init();
  });

  const fetchCommissionConfig = async () => {
    const res = await MallCommissionConfigApi.selectMallCommissionConfig();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ commissionConfigList: list });
  };

  const init = async () => {
    const pid = props.pid;
    if (!pid) return;

    const res = await Api.getProduct(pid);
    const data = { ...res };
    data.bannerImages = UploadV2.str2arr(data.bannerImages);
    data.coverImage = UploadV2.str2arr(data.coverImage);
    data.shopCategoryId = str2arr(data.shopCategoryId);
    data.partId = str2arr(data.partId);
    data.labelId = str2arr(data.labelId);

    data.shopList = data.shopList?.map((n: any) => n.shopId);

    const fdate = (n: any) => (n ? dayjs(n) : undefined);
    data.saleDate = [fdate(data.saleStartDate), fdate(data.saleEndDate)];

    form.setFieldsValue(data);
  };

  const onSubmit = async (goNext = false) => {
    const pid = props.pid;
    const data = await form.validateFields();

    data.id = pid;

    data.bannerImages = UploadV2.arr2str(data.bannerImages);
    data.coverImage = UploadV2.arr2str(data.coverImage);
    data.shopCategoryId = arr2str(data.shopCategoryId);
    if (data.partId?.length > 5) {
      message.error("适用部位不能超过5个");
      return;
    }
    if (data.labelId?.length > 5) {
      message.error("商品标签不能超过5个");
      return;
    }
    data.partId = arr2str(data.partId);
    data.labelId = arr2str(data.labelId);


    data.shopList = data.shopList?.map((n: any) => ({ shopId: n }));

    const saleDate = formatDateRange(data.saleDate, "YYYY-MM-DD HH:mm:ss");
    data.saleStartDate = saleDate.start;
    data.saleEndDate = saleDate.end;
    delete data.saleDate;

    data.property = property;

    let res: any = null;

    if (data.id) {
      res = await Api.putPorduct({ data });
    } else {
      res = await Api.addProduct({ data });
    }

    if (goNext) {
      props.goNext?.(res.id);
    } else {
      message.success("保存成功");
    }
  };

  return (
    <Form form={form} labelCol={{ className: "min-w-80px" }}>
      <div>
        <Divider>基本信息</Divider>

        <Form.Item
          label="模式"
          name="productModel"
          rules={[{ required: true, message: "请选择类型" }]}
          initialValue={1}
        >
          <Radio.Group
            options={[
              { label: "项目", value: 1 },
              { label: "套餐", value: 2 },
            ]}
            disabled={!!props.pid}
          />
        </Form.Item>

        <Form.Item label="名称" name="name" rules={[{ required: true, message: "请输入名称" }]}>
          <Input placeholder="请输入" />
        </Form.Item>

        <Form.Item
          label="分类"
          name="shopCategoryId"
          initialValue={[]}
          rules={[{ required: true, message: "请选择分类" }]}
        >
          <PickType />
        </Form.Item>

        <div className="flex gap-4">
          <Form.Item label="佣金方案" name={`commissionConfigId`}>
            <Select
              options={state.commissionConfigList}
              allowClear
              showSearch
              optionFilterProp="label"
              placeholder="请选择佣金方案"
              style={{ width: 200 }}
            />
          </Form.Item>

          <Form.Item label="排序" name="sort">
            <InputNumber placeholder="请输入" min={1} max={999} style={{ width: 200 }} />
          </Form.Item>

          {/* <Form.Item label="上下架" name="saleIn" initialValue={1}>
            <SwitchOC checkedChildren="上架" unCheckedChildren="下架" />
          </Form.Item> */}
          <Form.Item label="隐藏价格" name="priceSensitiveTag">
            <SwitchOC />
          </Form.Item>
        </div>
        <Form.Item
          label="适用部位"
          name={`partId`}
          initialValue={[]}
          rules={[{ required: false, message: "请选择适用部位" }]}
        >
          <DictPicker type="ProductPart" placeholder="请选择适用部位" mode="multiple" className="w-full" />
        </Form.Item>
        <Form.Item
          label="商品标签"
          name={`labelId`}
          initialValue={[]}
          rules={[{ required: false, message: "请选择商品标签" }]}
        >
          <DictPicker type="ProductLabel" placeholder="请选择商品标签" mode="multiple" className="w-full" />
        </Form.Item>
      </div>

      <div>
        <Divider>约束限制</Divider>

        <Form.Item label="门店限制">
          <SwitchContent name="shopLimit" >
            <Form.Item noStyle name="shopList" initialValue={[]} rules={[{ required: true, message: "请选择门店" }]}>
              <SelectShop params={{ isAll: 1 }} mode="multiple" className="w-full" />
            </Form.Item>
          </SwitchContent>
        </Form.Item>

        <Form.Item label="限时售卖" className="w-600px">
          <SwitchContent name="saleTimeLimit">
            <Form.Item
              noStyle
              name="saleDate"
              initialValue={[]}
              rules={[{ required: true, message: "请选择限时售卖时间" }]}
            >
              <DatePicker.RangePicker presets={DatePresetRanges} className="w-full" showTime />
            </Form.Item>
          </SwitchContent>
        </Form.Item>

        <div className="flex cols-4 gap-4">
          <Form.Item label="到店服务" name="offlineConsumeTag" initialValue={1}>
            <SwitchOC />
          </Form.Item>

          <Form.Item label="送货到家" name="onlineDeliveryTag">
            <SwitchOC />
          </Form.Item>

        </div>

        <div className="flex cols-4 gap-4">
          <Form.Item label="推荐标识" name="recommendTag">
            <SwitchOC />
          </Form.Item>

          <Form.Item label="M币赠送" name="integralTag" initialValue={1}>
            <SwitchOC />
          </Form.Item>

          <Form.Item label="会员折扣" name="preferentialTag" initialValue={1}>
            <SwitchOC />
          </Form.Item>
        </div>


      </div>

      <div>
        <Divider>扩展信息</Divider>
        <Form.Item label="封面图" name="coverImage" rules={[{ required: true, message: "请上传封面图" }]}>
          <UploadV2 />
        </Form.Item>
        <Form.Item label="轮播图" name="bannerImages" rules={[{ required: true, message: "请上传轮播图" }]}>
          <UploadV2 maxCount={6} />
        </Form.Item>
        <Form.Item label="营销语" name="promotionWord">
          <Input placeholder="请输入" />
        </Form.Item>
        <Form.Item label="商品详情" name="content">
          <RichText />
        </Form.Item>
      </div>

      <div className="flex items-center justify-center gap-4">
        <Button type="default" onClick={props.onClose}>
          返回列表
        </Button>
        <Button type="primary" onClick={() => onSubmit(true)}>
          下一步
        </Button>
        {props.pid && (
          <Button type="primary" onClick={() => onSubmit(false)}>
            保存信息
          </Button>
        )}
      </div>
    </Form>
  );
};
