import Mall<PERSON><PERSON> from "@/services/MallApi";
import { TreeSelect, TreeSelectProps } from "antd";
import { useMount, useSetState } from "react-use";

const property = 2;

export const PickType = (props: TreeSelectProps) => {
  const [state, setState] = useSetState({
    types: [] as any[],
  });

  useMount(() => {
    fetchTypes();
  });

  const fetchTypes = async () => {
    const params = { pageSize: 9999, property };
    const res = await MallApi.getShopProductTypeForSelect({ params });
    const types = genTree(res?.list || [], (item) => ({
      key: item.id,
      title: item.name,
      ...item,
    }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  return (
    <TreeSelect
      multiple
      treeData={state.types}
      fieldNames={{ label: "name", value: "id" }}
      placeholder="请选择分类"
      allowClear
      showSearch
      treeNodeFilterProp="name"
      {...props}
    />
  );
};
