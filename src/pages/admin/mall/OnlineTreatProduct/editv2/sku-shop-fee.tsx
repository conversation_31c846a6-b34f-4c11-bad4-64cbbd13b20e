import ShopPicker from "@/components/ShopPicker";
import WeModal from "@/components/WeModal/WeModal";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { Button, Form, InputNumber, Table, Tooltip } from "antd";
import { Api } from "../Api";

const PlainVlaue = (p: any) => p.value;

export const SkuShopFee = (props: any) => {
  const [form] = Form.useForm();

  const onOpen = async () => {
    form.resetFields();
    const res = await Api.getSkuInfo(props.skuId);
    form.setFieldsValue(res);
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.id = props.skuId;
    const res = await Api.putSku({ data });
    props.onOk?.(res);
  };

  return (
    <WeModal trigger={props.children} title={
      <>
        门店门诊费(选填)
        <Tooltip title="若门店需要收取额外的门诊费，请在此填写，不收取可不填">
          <QuestionCircleOutlined
            style={{
              cursor: "pointer",
              marginLeft: 2,
            }}
          />
        </Tooltip>
      </>
    } width={800} onOpen={onOpen} onOk={onOk}>
      <Form form={form} labelCol={{ className: "min-w-80px" }}>
        <div className="pb-8">
          <Form.List name="outpatientFeeList" initialValue={[]}>
            {(fields, action) => {
              return (
                <div>
                  <Table
                    className="[&_.ant-form-item]:m-0"
                    size="small"
                    locale={{ emptyText: "暂无数据" }}
                    pagination={false}
                    dataSource={fields}
                    columns={[
                      {
                        title: "门店",
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "shopName"]}>
                              <PlainVlaue />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "门诊费",
                        width: 250,
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "outpatientFee"]} rules={[{ required: true }]} help={false}>
                              <InputNumber min={0} addonAfter="元" placeholder="请输入" className="w-full" />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "操作",
                        width: "100px",
                        render: (c) => <a onClick={() => action.remove(c.name)}>删除</a>,
                      },
                    ]}
                  />
                  <ShopPicker
                    params={{ queryType: 9 }}
                    onSelect={(rows) => {
                      const list: any[] = form.getFieldValue("outpatientFeeList") || [];
                      rows.forEach((item) => {
                        if (list.every((n) => !(n.shopId === item.id))) {
                          list.push({
                            shopId: item.id,
                            shopName: item.name,
                            outpatientFee: undefined,
                          });
                        }
                      });

                      form.setFieldValue("outpatientFeeList", list);
                    }}
                  >
                    <Button block type="dashed" icon={<PlusOutlined />} className="mt-4">
                      添加门店
                    </Button>
                  </ShopPicker>
                </div>
              );
            }}
          </Form.List>
        </div>
      </Form>
    </WeModal>
  );
};
