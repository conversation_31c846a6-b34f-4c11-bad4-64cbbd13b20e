import { makeApi } from "@/utils/Api";

export const Api = {
  getProductList: makeApi("get", "/api/v2/mallProduct"),
  getProduct: makeApi("get", `/api/v2/mallProduct`, true),
  addProduct: makeApi("post", `/api/v2/mallProduct/online_product`),
  putPorduct: makeApi("put", `/api/v2/mallProduct/online_product`),
  delProduct: makeApi("delete", "/api/v2/mallProduct/online_product"),
  batchModifySaleIn: makeApi("post", `/api/v2/mallProduct/batch_modify_sale_in`),
  batchModifyRecommendTag: makeApi("post", `/api/v2/mallProduct/batch_modify_recommend_tag`),
  modifySold: makeApi("post", `/api/v2/mallProduct/modify_sold_count`),
  operateSold: makeApi("post", `/api/v2/mallProduct/operate_sold_count`),
  operateInventory: makeApi("post", `/api/v2/mallProduct/operate_inventory_count`),

  getSku: makeApi("get", `/api/mallProductSpecification`),
  putSku: makeApi("put", `/api/mallProductSpecification`),
  getSkuInfo: makeApi("get", `/api/mallProductSpecification`, true),
  batchModifySkuSaleIn: makeApi("post", `/api/mallProductSpecification/batch_modify_sale_in`),
  batchModifySkuRecommendTag: makeApi("post", `/api/mallProductSpecification/batch_modify_recommend_tag`),
};
