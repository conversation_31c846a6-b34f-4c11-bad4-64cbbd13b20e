import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON>pi from "@/services/MallApi";
import { Form, Input, InputNumber, message } from "antd";

const EditModal = (props: { title: any; children: any; onOk?: Function; data?: any; shopId: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const data = { ...props.data };
      data.investorRate = (data.investorRate * 100).toFixed(2);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    data.investorRate = (data.investorRate / 100).toFixed(4);
    data.shopId = props.shopId;

    if (data.id) {
      await MallApi.putShopInvestor({ data });
      message.success("修改投资人成功");
    } else {
      await MallApi.addShopInvestor({ data });
      message.success("添加投资人成功");
    }

    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={450} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Form.Item label="姓名" name={`investorUserName`} rules={[{ required: true, message: "请输入投资人姓名" }]}>
          <Input placeholder="请输入投资人姓名" />
        </Form.Item>
        <Form.Item label="手机号" name={`investorUserMobile`} rules={[{ required: true, message: "请输入手机号" }]}>
          <Input placeholder="请输入手机号" />
        </Form.Item>
        <Form.Item label="投资比例(%)" name={`investorRate`} rules={[{ required: true, message: "请输入投资比例" }]}>
          <InputNumber
            placeholder="请输入投资比例"
            style={{ width: "100%" }}
            min={0}
            max={100}
            step={1}
            addonAfter="百分比"
          />
        </Form.Item>
        <Form.Item label="身份证号" name={`identityCard`}>
          <Input placeholder="请输入身份证号" />
        </Form.Item>
        <Form.Item label="地址" name={`address`}>
          <Input placeholder="请输入地址" />
        </Form.Item>
        <Form.Item label="备注" name={`content`}>
          <Input.TextArea autoSize={{ minRows: 2 }} allowClear maxLength={500} showCount placeholder="请输入备注" />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default EditModal;
