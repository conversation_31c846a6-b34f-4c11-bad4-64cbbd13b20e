import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import EditModal from "./Edit1";
import { Card, Popconfirm, Space, message } from "antd";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";

const InvestorPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    shops: [] as any[],
    shopId: "",
    user: null as any,
  });

  useMount(() => {
    fetchUser();
    fetchShops();
  });

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user, shopId: user?.currentShopId || "" });
  };

  const fetchShops = async () => {
    const res = await Mall<PERSON>pi.getShopListForManage();
    let list: any[] = res.list || [];
    list = list.map((n) => ({ tab: n.name, key: n.id }));
    setState({ shops: list });
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallApi.delShopInvestor({ data: { ids: item.id } });
    message.success("删除投资人成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={state.shops}
        activeTabKey={state.shopId}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(shopId) => setState({ shopId })}
      />
      <WeTable
        autoLoad={false}
        ref={tableRef}
        params={{ shopId: state.shopId }}
        request={(p) => MallApi.getShopInvestor({ params: p })}
        title={
          <EditModal title={"新增投资人"} shopId={state.shopId} onOk={handleReload}>
            <WeTable.AddBtn />
          </EditModal>
        }
        columns={[
          { title: "门店", dataIndex: "shopName" },
          { title: "姓名", dataIndex: "investorUserName" },
          { title: "手机号", dataIndex: "investorUserMobile" },
          { title: "投资比例(%)", dataIndex: "investorRate", render: (c) => <span>{(c * 100).toFixed(2)}%</span> },
          { title: "身份证号", dataIndex: "identityCard", render: (c) => <span>{c || "--"}</span> },
          { title: "地址", dataIndex: "address", render: (c) => <span>{c || "--"}</span> },
          { title: "备注", dataIndex: "content", render: (c) => <span>{c || "--"}</span> },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <EditModal title={`编辑投资人 - ${item.name}`} shopId={state.shopId} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </EditModal>
                  <Popconfirm title={`确定要删除 - ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default InvestorPage;
