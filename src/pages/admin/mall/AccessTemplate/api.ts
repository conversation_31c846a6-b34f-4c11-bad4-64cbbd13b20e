import { makeApi } from "@/utils/Api";

const AppUserAccessTemplateApi = {
  getAppUserAccessTemplate: makeApi("get", `/api/appUserAccessTemplate`),
  getAppUserAccessTemplateInfo: makeApi("get", `/api/appUserAccessTemplate`, true),
  addAppUserAccessTemplate: makeApi("post", `/api/appUserAccessTemplate`),
  putAppUserAccessTemplate: makeApi("put", `/api/appUserAccessTemplate`),
  delAppUserAccessTemplate: makeApi("delete", `/api/appUserAccessTemplate`),
};

export default AppUserAccessTemplateApi;
