import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserAccessTemplateApi from "./api";
import AppUserAccessTemplateEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import DictPicker from "@/components/Units/DictPicker";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppUserAccessTemplatePage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserAccessTemplateApi.delAppUserAccessTemplate({ data: { ids: item.id } });
    message.success("删除回访模板成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => AppUserAccessTemplateApi.getAppUserAccessTemplate({ params: p })}
        title={
          <AppUserAccessTemplateEdit title={"新增回访模板"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppUserAccessTemplateEdit>
        }
        search={[
          <Form.Item label="回访类型" name={`typeId`}>
            <DictPicker type="accessType" />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "回访类型", dataIndex: "typeName", render: (c) => (c ? c : "--") },
          { title: "名称", dataIndex: "name", render: (c) => (c ? c : "--") },
          { title: "备注", dataIndex: "content", render: (c) => (c ? c : "--") },

          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppUserAccessTemplateEdit title={`编辑回访模板`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppUserAccessTemplateEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppUserAccessTemplatePage;
