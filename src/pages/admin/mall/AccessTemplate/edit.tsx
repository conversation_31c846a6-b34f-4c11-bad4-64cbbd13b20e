import WeModal from "@/components/WeModal/WeModal";
import AppUserAccessTemplateApi from "./api";
import {
  Button,
  Col,
  Divider,
  Form,
  Input,
  InputNumber,
  Radio,
  Row,
  Select,
  Space,
  Switch,
  Table,
  message,
} from "antd";
import DictPicker from "@/components/Units/DictPicker";
import GoodsPicker from "@/components/GoodsPicker";
import { PlusOutlined } from "@ant-design/icons";
import { useSetState } from "react-use";
import { GoodsTypePicker } from "@/components/GoodsPicker/GoodsTypePicker";
import { ProductModelType, ProductPropertyType } from "@/components/GoodsPicker/types";

const layout = { row: 10, col: 12 };

const AppUserAccessTemplateEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  hideSubmit?: any;
}) => {
  const [form] = Form.useForm();
  const model = Form.useWatch("model", form);

  const [state, setState] = useSetState({
    goods: [] as any[],
  });

  const handleOpen = async () => {
    setState({ goods: [] });
    form.resetFields();
    if (props.data) {
      const data = await AppUserAccessTemplateApi.getAppUserAccessTemplateInfo(props.data.id);
      data.categoryIds = data.categoryList?.map((n: any) => n.id);
      data.state = !!data.state;
      setState({ goods: data.productList });
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    data.productIds = state.goods?.map((n) => n.id).join(",");
    data.categoryIds = data.categoryIds?.join(",");
    data.state = Number(data.state);

    if (data.model == 2 && !data.productIds) {
      message.error("请选择商品");
      return false;
    }

    if (!data.planDataList?.length) {
      message.error("请添加回访计划");
      return false;
    }

    if (!data.id) {
      await AppUserAccessTemplateApi.addAppUserAccessTemplate({ data });
      message.success("添加回访模板成功");
    }
    if (data.id) {
      await AppUserAccessTemplateApi.putAppUserAccessTemplate({ data });
      message.success("修改回访模板成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={1000}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`typeId`} label="回访类型" rules={[{ required: true, message: "请选择回访类型" }]}>
              <DictPicker type="accessType" />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item name={`content`} label="备注" rules={[{ required: false, message: "请输入备注" }]}>
              <Input.TextArea autoSize={{ minRows: 3 }} allowClear placeholder="请输入备注" />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              name={`state`}
              label="状态"
              rules={[{ required: false, message: "请输入备注" }]}
              initialValue={true}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>

          <Divider></Divider>

          <Col span={24}>
            <Form.Item
              name={`model`}
              label="适用模式"
              rules={[{ required: false, message: "请选择" }]}
              initialValue={1}
            >
              <Radio.Group
                optionType="button"
                buttonStyle="solid"
                options={[
                  { label: "适用分类", value: 1 },
                  { label: "适用商品", value: 2 },
                ]}
              />
            </Form.Item>
          </Col>

          {model == 1 && (
            <Col span={24}>
              <Form.Item name={`categoryIds`} label="适用分类" rules={[{ required: true, message: "请选择分类" }]}>
                <GoodsTypePicker />
              </Form.Item>
            </Col>
          )}
          {model == 2 && (
            <Col span={23}>
              <Form.Item
                label="适用商品"
                rules={[{ required: false, message: "请输入适用商品ids" }]}
              >
                <div>
                  <Table
                    rowKey={`id`}
                    size="small"
                    dataSource={state.goods}
                    pagination={false}
                    locale={{ emptyText: "暂无商品" }}
                    columns={[
                      { title: "名称", dataIndex: "name" },
                      {
                        title: "属性",
                        dataIndex: "property",
                        render: (c) => ProductPropertyType.find((n) => n.id == c)?.name,
                      },
                      {
                        title: "模式",
                        dataIndex: "productModel",
                        render: (c) => ProductModelType.find((n) => n.id == c)?.name,
                      },
                      //{ title: "分类", dataIndex: "shopCategoryName" },
                      {
                        title: "操作",
                        render: (item) => (
                          <Space>
                            <a
                              onClick={() => {
                                const goods = state.goods.filter((crt) => crt.id !== item.id);
                                setState({ goods });
                              }}
                            >
                              删除
                            </a>
                          </Space>
                        ),
                      },
                    ]}
                  />
                  <div className="h-10px"></div>
                  <GoodsPicker
                    params={{ productModel: 1, isAll: 1, saleInAll: 1 }}
                    onSelect={(rows) => {
                      const newList: any[] = rows.filter((item) => {
                        return state.goods.every((n) => n.id !== item.id);
                      });
                      const fulllist = [...state.goods, ...newList];
                      setState({ goods: fulllist });
                    }}
                  >
                    <Button block type="dashed" icon={<PlusOutlined />}>
                      添加商品
                    </Button>
                  </GoodsPicker>
                </div>
              </Form.Item>
            </Col>
          )}

          <Divider></Divider>

          <Col span={24}>
            <Form.Item label="回访计划">
              <div>
                <Form.List name={`planDataList`} initialValue={[]}>
                  {(fields, operation) => {
                    return (
                      <>
                        <Table
                          className="[&_.ant-form-item-explain]:(text-12px pos-absolute top-1/1) "
                          // size="small"
                          locale={{ emptyText: "暂无数据" }}
                          pagination={false}
                          dataSource={fields}
                          columns={[
                            {
                              title: "推送条件",
                              width: 240,
                              render: (item) => {
                                return (
                                  <Form.Item
                                    className="m-0"
                                    name={[item.name, "accessAfterDay"]}
                                    rules={[{ required: true, message: "请输入" }]}
                                  >
                                    <InputNumber
                                      className="w-full"
                                      min={0}
                                      addonBefore="回访在"
                                      addonAfter="天后"
                                      placeholder="请输入"
                                    />
                                  </Form.Item>
                                );
                              },
                            },
                            {
                              title: "回访推送人",
                              width: 200,
                              render: (item) => {
                                return (
                                  <Form.Item
                                    className="m-0"
                                    name={[item.name, "accessRole"]}
                                    rules={[{ required: true, message: "请选择" }]}
                                  >
                                    <Select
                                      placeholder="请选择"
                                      allowClear
                                      options={[
                                        { value: 10, label: "所属开发" },
                                        { value: 20, label: "所属客服" },
                                        { value: 30, label: "执行医生" },
                                        { value: 40, label: "执行治疗师" },
                                      ]}
                                    />
                                  </Form.Item>
                                );
                              },
                            },
                            {
                              title: "回访主题",
                              render: (item) => {
                                return (
                                  <Form.Item
                                    className="m-0"
                                    name={[item.name, "accessTitle"]}
                                    rules={[{ required: true, message: "请输入" }]}
                                  >
                                    <Input placeholder="请输入回访主题" />
                                  </Form.Item>
                                );
                              },
                            },
                            {
                              title: "操作",
                              render: (item) => (
                                <Space>
                                  {/* <a>修改</a> */}
                                  <a onClick={() => operation.remove(item.name)}>删除</a>
                                </Space>
                              ),
                            },
                          ]}
                        />

                        <div className="h-10px"></div>

                        <Button block type="dashed" icon={<PlusOutlined />} onClick={() => operation.add()}>
                          添加计划
                        </Button>
                      </>
                    );
                  }}
                </Form.List>
              </div>
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item
              name={`sort`}
              label="排序"
              rules={[{ required: false, message: "请输入排序" }]}
              initialValue={999}
              tooltip="数字越小越靠前"
            >
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserAccessTemplateEdit;
