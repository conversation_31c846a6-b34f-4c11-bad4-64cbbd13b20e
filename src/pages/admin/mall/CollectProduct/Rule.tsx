import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, message, Row, Switch } from "antd";
import { Api } from "./Api";
import RichText from "@/components/RichText";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.collectMallOpenTag = Number(data.collectMallOpenTag);
    await Api.putRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getRule();
    data.collectMallOpenTag = !!data.collectMallOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="商城规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`collectMallOpenTag`} label="是否开启兑换" valuePropName="checked">
              <Switch></Switch>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`collectMallRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
