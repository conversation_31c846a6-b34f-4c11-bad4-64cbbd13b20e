import WeModal from "@/components/WeModal/WeModal";
import { Button, Col, Divider, Form, Input, InputNumber, message, Row, Switch, Table, TreeSelect } from "antd";
import { Api } from "./Api";
import { useSetState } from "react-use";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import { arr2str, str2arr } from "@/utils/Tools";
import { MarketCategoryApi } from "../marketproductcategory/MarketCategoryApi";
import { GoodsSkuPicker } from "@/components/GoodsSkuPicker";

const layout = { row: 10, col: 24 };

const marketType = 20;

export const Edit = (props: { children: any; title: any; onOk: any; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    product: null as any,
    types: [] as any[],
  });

  const fetchTypes = async () => {
    const params = { pageSize: 9999, marketType };
    const res = await MarketCategoryApi.getMarketProductTypeList({ params });
    const types = genTree(res?.list || [], (item) => ({
      key: item.id,
      title: item.name,
      ...item,
    }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.marketType = marketType;
    data.inventoryLimit = Number(data.inventoryLimit);
    data.buyLimit = Number(data.buyLimit);
    data.productId = state.product?.productId;
    data.productSpecificationId = state.product?.productSpecificationId;
    data.coverImage = UploadV2.arr2str(data.coverImage);
    data.saleIn = Number(data.saleIn);
    data.property = 10;

    if (data.categoryId?.length > 5) {
      message.error("分类不能大于5个");
      return false;
    }

    data.categoryId = arr2str(data.categoryId);
    if (data.id) {
      await Api.putMarketProduct({ data });
    } else {
      await Api.addMarketProduct({ data });
    }
    message.success("操作成功");
    props.onOk();
  };

  const handleOpen = async () => {
    fetchTypes();
    form.resetFields();
    const data = { ...props.data };
    data.inventoryLimit = !!data.inventoryLimit;
    data.buyLimit = !!data.buyLimit;
    data.coverImage = UploadV2.str2arr(data.coverImage);
    data.categoryId = str2arr(data.categoryId);
    form.setFieldsValue(data);

    let product = data.product;

    if (data.product) {
      product = { ...data.product };
      product.productId = data.product?.id;
      product.productName = data.product?.name;
      product.productSpecificationId = data.productSpecification?.id;
      product.productSpecificationName = data.productSpecification?.name;
      product.oldPrice = data.productSpecification?.oldPrice;
      product.salePrice = data.productSpecification?.salePrice;
      product.includeNum = data.productSpecification?.includeNum;
    }

    setState({ product });
  };

  return (
    <WeModal trigger={props.children} width={1000} title={props.title} onOk={handleSubmit} onOpen={handleOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="项目">
              <div>
                <GoodsSkuPicker
                  max={1}
                  params={{ property: 2, isAll: 1}}
                  onOk={(e) => {
                    const item = e?.[0];
                    setState({ product: item });
                    form.setFieldsValue({ name: item?.productName + "(" + item?.productSpecificationName + ")", coverImage: UploadV2.str2arr(item?.coverImage) });
                  }}
                >
                  <Button>选择项目</Button>
                </GoodsSkuPicker>
              </div>
              {!!state.product && (
                <div className="mt-10px">
                  <Table
                    size="small"
                    rowKey="id"
                    pagination={false}
                    dataSource={[state.product]}
                    columns={[
                      { title: "名称", dataIndex: "productName" },
                      { title: "规格", dataIndex: "productSpecificationName" },
                      { title: "分类", dataIndex: "shopCategoryName" },
                      {
                        title: "模式",
                        dataIndex: "productModel",
                        render: (c) => {
                          if (c == 1) {
                            return "项目单品";
                          } else if (c == 2) {
                            return "项目套餐";
                          } else if (c == 11) {
                            return "配送商品";
                          } else if (c == 12) {
                            return "服务商品";
                          }
                        },
                      },
                      {
                        title: "次数",
                        dataIndex: "includeNum",
                        render: (c) => (c ? c + "次" : "--"),
                      },
                      {
                        title: "划线价",
                        dataIndex: "oldPrice",
                        render: (c) => (c ?? "--") + "元",
                      },
                      {
                        title: "售价",
                        dataIndex: "salePrice",
                        render: (c) => (c ?? "--") + "元",
                      },
                      {
                        title: "库存",
                        render: (c) => (c.inventoryLimit == 1 ? c.inventoryCount : "无限"),
                      },
                    ]}
                  ></Table>
                </div>
              )}
            </Form.Item>
          </Col>

          <Col span={layout.col / 2}>
            <Form.Item label="名称" name={"name"} rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item
              label="分类"
              name={`categoryId`}
              rules={[{ required: true, message: "请选择分类" }]}
              initialValue={[]}
            >
              <TreeSelect
                multiple
                treeData={state.types}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择分类"
                allowClear
                showSearch
                treeNodeFilterProp="name"
              />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="封面" name={"coverImage"} rules={[{ required: true, message: "请选择封面" }]}>
              <UploadV2 />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item label="上下架" name={`saleIn`} valuePropName="checked" initialValue={true}>
              <Switch checkedChildren="上架" unCheckedChildren="下架" />
            </Form.Item>
          </Col>

          <Col span={layout.col / 2}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber style={{ width: "100%" }} min={0} placeholder="请输入排序，范围1~999" />
            </Form.Item>
          </Col>

          <Divider orientation="left">约束限制</Divider>

          <Col span={layout.col}>
            <Form.Item label="库存限制">
              <div className="flex items-center">
                <div className="mr-20px">
                  <Form.Item noStyle name={`inventoryLimit`} valuePropName="checked" initialValue={false}>
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </div>

                <Form.Item noStyle dependencies={["inventoryLimit"]}>
                  {(form) => {
                    const value = form.getFieldValue("inventoryLimit");
                    return (
                      !!value && (
                        <div className="w-300px">
                          <Form.Item
                            noStyle
                            name={`inventoryCount`}
                            rules={[{ required: true, message: "请输入库存数量" }]}
                          >
                            <InputNumber
                              placeholder="请输入库存数量"
                              min={0}
                              style={{ width: "100%" }}
                            // addonAfter="张"
                            />
                          </Form.Item>
                        </div>
                      )
                    );
                  }}
                </Form.Item>
              </div>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="限兑数量" tooltip="限制每位顾客兑换此商品的数量，关闭表示可以无限兑换">
              <div className="flex items-center">
                <div className="mr-20px">
                  <Form.Item noStyle name={`buyLimit`} valuePropName="checked" initialValue={false}>
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </div>

                <Form.Item noStyle dependencies={["buyLimit"]}>
                  {(form) => {
                    const value = form.getFieldValue("buyLimit");
                    return (
                      !!value && (
                        <div className="w-300px">
                          <Form.Item
                            noStyle
                            name={`buyMaxCount`}
                            rules={[{ required: true, message: "请输入限兑数量" }]}
                          >
                            <InputNumber
                              placeholder="请输入限兑数量"
                              min={0}
                              style={{ width: "100%" }}
                            // addonAfter="张"
                            />
                          </Form.Item>
                        </div>
                      )
                    );
                  }}
                </Form.Item>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
