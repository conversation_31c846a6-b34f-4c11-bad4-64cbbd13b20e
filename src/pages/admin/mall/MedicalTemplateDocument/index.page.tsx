import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallMedicalTemplateDocumentApi from "./api";
import MallMedicalTemplateDocumentEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallMedicalTemplateDocumentPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallMedicalTemplateDocumentApi.delMallMedicalTemplateDocument({ data: { ids: item.id } });
    message.success("删除病历模板文档关联成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallMedicalTemplateDocumentApi.getMallMedicalTemplateDocument({ params: p })}
        title={
          <Space>
            <MallMedicalTemplateDocumentEdit title={"新增病历模板文档关联"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallMedicalTemplateDocumentEdit>
          </Space>
        }
        search={[
          <Form.Item label="公司id" name={`companyId`}>
            <Input placeholder="请输入公司id" />
          </Form.Item>,
          <Form.Item label="模板id" name={`templateId`}>
            <Input placeholder="请输入模板id" />
          </Form.Item>,
          <Form.Item label="文档id" name={`documentId`}>
            <Input placeholder="请输入文档id" />
          </Form.Item>,
          <Form.Item label="状态 1.启用 0.禁用" name={`state`}>
            <Input placeholder="请输入状态 1.启用 0.禁用" />
          </Form.Item>,
          <Form.Item label="排序" name={`sort`}>
            <Input placeholder="请输入排序" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "公司id", dataIndex: "companyId", render: (c) => c ? c : "--" },
          { title: "模板id", dataIndex: "templateId", render: (c) => c ? c : "--" },
          { title: "文档id", dataIndex: "documentId", render: (c) => c ? c : "--" },
          { title: "状态 1.启用 0.禁用", dataIndex: "state" },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallMedicalTemplateDocumentEdit title={`编辑病历模板文档关联`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallMedicalTemplateDocumentEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallMedicalTemplateDocumentPage;
