import { makeApi } from "@/utils/Api";

const MallShopVipAchieveRewardApi = {
    getMallShopVipAchieveReward: makeApi("get", `/api/mallShopVipAchieveReward`),
    getMallShopVipAchieveRewardInfo: makeApi("get", `/api/mallShopVipAchieveReward`, true),
    addMallShopVipAchieveReward: makeApi("post", `/api/mallShopVipAchieveReward`),
    putMallShopVipAchieveReward: makeApi("put", `/api/mallShopVipAchieveReward`),
    delMallShopVipAchieveReward: makeApi("delete", `/api/mallShopVipAchieveReward`),
};

export default MallShopVipAchieveRewardApi;
