import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallShopVipAchieveRewardApi from "./api";
import MallShopVipAchieveRewardEdit from "./edit";
import { Drawer, Popconfirm, Space, message } from "antd";
import { useSetState } from "react-use";
import { ModeMap, RegisterMap, StateMap, TypeMap } from "./types";

const MallShopVipAchieveRewardPage = (props: { children: any; title: any, vipLevelId?: any }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
  });


  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallShopVipAchieveRewardApi.delMallShopVipAchieveReward({ data: { ids: item.id } });
    message.success("删除达成奖励成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ vipLevelId: props.vipLevelId }}
          request={(p) => MallShopVipAchieveRewardApi.getMallShopVipAchieveReward({ params: p })}
          title={
            <MallShopVipAchieveRewardEdit title={"新增达成奖励"} onOk={handleReload} vipLevelId={props.vipLevelId}>
              <WeTable.AddBtn />
            </MallShopVipAchieveRewardEdit>
          }
          columns={[
            { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((i) => i.value === c)?.label },
            { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
            { title: "有效天数", dataIndex: "effectiveDays", render: (c, item) => [10, 20].includes(item.type) ? c : "--" },
            { title: "包含数量", dataIndex: "includeNum"},
            { title: "奖励模式", dataIndex: "mode", render: (c) => ModeMap.find((i) => i.value === c)?.label },
            { title: "自己获得", dataIndex: "selfObtainTag", render: (c) => c ? "是" : "--" },
            { title: "上级获得", dataIndex: "parentObtainTag", render: (c) => c ? "是" : "--" },
            { title: "注册限定", dataIndex: "registerLimit", render: (c) => RegisterMap.find((i) => i.value === c)?.label },
            { title: "排序", dataIndex: "sort" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((i) => i.value === c)?.label },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallShopVipAchieveRewardEdit title={`编辑达成奖励`} data={item} vipLevelId={props.vipLevelId} onOk={handleReload}>
                      <a>编辑</a>
                    </MallShopVipAchieveRewardEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallShopVipAchieveRewardPage;
