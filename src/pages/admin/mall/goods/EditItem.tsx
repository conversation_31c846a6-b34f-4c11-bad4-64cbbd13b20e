import DictPicker from "@/components/Units/DictPicker";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Input, InputNumber, Row, Space, Switch, TreeSelect, message } from "antd";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };

//const RSwitch = (props: any) => <Switch checked={!!props.value} onChange={(e) => props.onChange(Number(e))} />;

const EditItem = (props: { children: any; title: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    types: [] as any[],
  });

  const handleOpen = () => {
    form.resetFields();
    fetchTypes();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    // console.log(data);

    data.saleIn = Number(data.saleIn);

    if (data.id) {
      await MallApi.putGoods({ data });
      message.success("编辑物资成功");
    } else {
      await MallApi.addGoods({ data });
      message.success("新增物资成功");
    }

    props.onOk();

    // return false;
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getGoodsType({ params });
    const types = genTree(res?.list || [], (item) => ({ title: item.name, value: item.id, ...item }));
    setState({ types });
    //console.log(types);
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "110px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="分类" name={`goodsCategoryId`} rules={[{ required: true, message: "请输入分类" }]}>
              <TreeSelect
                treeData={state.types}
                placeholder="请选择分类"
                showSearch
                treeNodeFilterProp="title"
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="规格" name={`specs`} rules={[{ required: true, message: "请输入规格" }]}>
              <Input placeholder="请输入规格" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="最小使用单位"
              name={`unit`}
              labelCol={{ flex: "none" }}
              rules={[{ required: true, message: "请选择最小使用单位" }]}
            >
              <DictPicker
                type="unitType"
                placeholder="请选择最小使用单位"
                formater={(n) => n.map((o) => ({ label: o.name, value: o.name }))}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="库房单位">
              <Space.Compact style={{ width: "100%" }}>
                <Input disabled value="1" style={{ textAlign: "center" }} />
                <Form.Item noStyle name={`storeUnit`} rules={[{ required: true, message: "请选择库房单位" }]}>
                  <DictPicker
                    type="unitType"
                    placeholder="库房单位"
                    formater={(n) => n.map((o) => ({ label: o.name, value: o.name }))}
                    style={{ minWidth: 100 }}
                  />
                </Form.Item>
                <Input disabled value="=" style={{ textAlign: "center" }} />
                <Form.Item noStyle name={`eachStoreUnitInclude`} rules={[{ required: true, message: "请输入数量" }]}>
                  <InputNumber placeholder="数量" min={1} style={{ minWidth: 80 }} />
                </Form.Item>
                <Input disabled value="最小单位" style={{ minWidth: 80, textAlign: "center" }} />
              </Space.Compact>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="库存预警" name={`inventoryWarningNum`} initialValue={0}>
              <InputNumber
                min={0}
                placeholder="数量"
                addonBefore="少于"
                addonAfter="(最小单位)触发预警"
                style={{ width: "100%" }}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="上架" name={`saleIn`} valuePropName="checked" initialValue={true}>
              <Switch checkedChildren="上架" unCheckedChildren="下架" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditItem;
