import { Card, Col, Form, Input, Popconfirm, Row, Select, Space, Tag, Tooltip, Tree, Typography, message } from "antd";
import Style from "./index.module.scss";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallApi from "@/services/MallApi";
import { useMount, useSetState } from "react-use";
import EditType from "./EditType";
import { ApartmentOutlined, DeleteOutlined, FormOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useRef } from "react";
import EditItem from "./EditItem";

const GoodsPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    types: [] as any[],
    typeId: null as any,
  });

  useMount(() => {
    fetchTypes();
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getGoodsType({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delGoodsType({ data });
    message.success("删除分类成功");
    fetchTypes();
  };

  const handleDelItem = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delGoods({ data });
    message.success("删除物资成功");
    handleReload();
  };

  return (
    <Row wrap={false} gutter={10}>
      <Col flex={`300px`} style={{ minWidth: 0 }}>
        <Card
          bodyStyle={{ padding: 10 }}
          title={`物资分类`}
          style={{ height: "100%" }}
          extra={
            <EditType title={`添加分类`} onOk={() => fetchTypes()} tree={state.types}>
              <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
            </EditType>
          }
        >
          <Tree.DirectoryTree
            className={Style.tree}
            treeData={state.types}
            showIcon={false}
            selectedKeys={[state.typeId]}
            onSelect={(e) => {
              const key = e[0];
              if (state.typeId === key) {
                setState({ typeId: "" });
              } else {
                setState({ typeId: key });
              }
            }}
            titleRender={(item) => {
              return (
                <div className={Style.row} key={item.key}>
                  <div className={Style.title}>{item.title}</div>
                  <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                    <EditType title={`编辑分类`} onOk={() => fetchTypes()} tree={state.types} data={item}>
                      <Tooltip title="编辑分类">
                        <FormOutlined className={Style.ico} />
                      </Tooltip>
                    </EditType>

                    <EditType
                      title={`添加下级`}
                      onOk={() => fetchTypes()}
                      tree={state.types}
                      data={{ parentId: item.id }}
                    >
                      <Tooltip title="添加下级">
                        <ApartmentOutlined className={Style.ico} />
                      </Tooltip>
                    </EditType>

                    <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                      <Tooltip title="删除分类">
                        <DeleteOutlined className={Style.ico} />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                </div>
              );
            }}
          />
        </Card>
      </Col>
      <Col flex={"auto"}>
        <WeTable
          ref={tableRef}
          title={
            <EditItem title={`新增物资`} onOk={handleReload}>
              <WeTable.AddBtn children="新增物资" />
            </EditItem>
          }
          request={(params) => MallApi.getGoods({ params })}
          params={{ goodsCategoryId: state.typeId }}
          search={[
            <Form.Item label="名称" name={`name`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
            <Form.Item label="上下架" name={`saleIn`}>
              <Select
                options={[
                  { label: "上架中", value: 1 },
                  { label: "已下架", value: 0 },
                ]}
                placeholder="请选择上下架"
                allowClear
              />
            </Form.Item>,
          ]}
          columns={[
            { title: "名称", dataIndex: "name" },
            { title: "规格", dataIndex: "specs" },
            { title: "库房/使用单位", render: (c) => `1${c.storeUnit} = ${c.eachStoreUnitInclude}${c.unit}` },
            { title: "预警库存", render: (c) => `${c.inventoryWarningNum} ${c.unit}` },
            {
              title: "上下架",
              dataIndex: "saleIn",
              render: (c) => (
                <>
                  {c == 1 && <Tag color="green">上架</Tag>}
                  {c == 0 && <Tag color="gray">下架</Tag>}
                </>
              ),
            },
            {
              title: "操作",
              render: (item) => (
                <Space>
                  <EditItem title={`编辑物资`} onOk={handleReload} data={item}>
                    <Typography.Link>编辑</Typography.Link>
                  </EditItem>

                  <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDelItem(item)}>
                    <Typography.Link>删除</Typography.Link>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
        />
      </Col>
    </Row>
  );
};

export default GoodsPage;
