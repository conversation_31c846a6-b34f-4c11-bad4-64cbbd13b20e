import { makeApi } from "@/utils/Api";

const MallCollectExchangeProductApi = {
    getMallCollectExchangeProduct: makeApi("get", `/api/mallCollectExchangeProduct`),
    getMallCollectExchangeProductInfo: makeApi("get", `/api/mallCollectExchangeProduct`, true),
    addMallCollectExchangeProduct: makeApi("post", `/api/mallCollectExchangeProduct`),
    putMallCollectExchangeProduct: makeApi("put", `/api/mallCollectExchangeProduct`),
    delMallCollectExchangeProduct: makeApi("delete", `/api/mallCollectExchangeProduct`),
};

export default MallCollectExchangeProductApi;
