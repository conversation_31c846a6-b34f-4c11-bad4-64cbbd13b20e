import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallCollectExchangeProductApi from "./api";
import MallCollectExchangeProductEdit from "./edit";
import { Form, Input, Popconfirm, Space, message, Drawer } from "antd";
import { useSetState } from "react-use";
import { StateMap } from "./types";
const MallCollectExchangeProductPage = (props: { children: any; title: any; activityId: any; collectCardSeriesId: any; }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallCollectExchangeProductApi.delMallCollectExchangeProduct({ data: { ids: item.id } });
    message.success("删除集卡兑换项目成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ activityId: props.activityId }} //默认参数
          request={(p) => MallCollectExchangeProductApi.getMallCollectExchangeProduct({ params: p })}
          title={
            <Space>
              <MallCollectExchangeProductEdit title={"新增集卡兑换项目"} activityId={props.activityId} onOk={handleReload}>
                <WeTable.AddBtn />
              </MallCollectExchangeProductEdit>
            </Space>
          }
          search={[
            <Form.Item label="项目名称" name={`marketProductName`}>
              <Input placeholder="请输入项目名称" />
            </Form.Item>,
          ]}
          columns={[
            { title: "项目名称", dataIndex: "marketProductName", render: (c) => c ? c : "--" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
            { title: "排序", dataIndex: "sort" },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallCollectExchangeProductEdit title={`编辑集卡兑换项目`} data={item} activityId={props.activityId} onOk={handleReload}>
                      <a>编辑</a>
                    </MallCollectExchangeProductEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallCollectExchangeProductPage;
