import WeModal from "@/components/WeModal/WeModal";
import MallCollectExchangeProductApi from "./api";
import { Col, Form, Input, Radio, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import { useSetState } from "react-use";
import { Api } from "../CollectProduct/Api";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };

const MallCollectExchangeProductEdit = (props: { title: any; children: any; onOk?: Function; data?: any; activityId: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    MarketProducts: [] as any[],
  });

  const fetchMarketProductList = async () => {
    const res = await Api.getMarketProduct({ params: { marketType: 20, pageSize: 999 } });
    let list = res?.list || [];
    list = list.map((item: any) => ({ label: item.name, value: item.id }));
    setState({ MarketProducts: list || [] });
  };


  const handleOpen = async () => {
    form.resetFields();
    fetchMarketProductList();
    if (props.data) {
      const data = await MallCollectExchangeProductApi.getMallCollectExchangeProductInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.activityId = props.activityId;
    if (!data.id) {
      await MallCollectExchangeProductApi.addMallCollectExchangeProduct({ data });
      message.success("添加集卡兑换项目成功");
    }
    if (data.id) {
      await MallCollectExchangeProductApi.putMallCollectExchangeProduct({ data });
      message.success("修改集卡兑换项目成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name="marketProductId" label="集卡项目" rules={[{ required: true, message: "请选择集卡项目" }]}>
              <Select options={state.MarketProducts} placeholder="请选择集卡项目" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: true, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallCollectExchangeProductEdit;
