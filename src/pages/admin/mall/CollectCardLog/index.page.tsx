import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallCollectCardLogApi from "./api";
import { Drawer, Form, Input, Popconfirm, Select, Space, Typography, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateType } from "./types";
import UserManager from "@/components/UserManager";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import { useAsync, useSetState } from "react-use";
import MallCollectCardTypeApi from "../CollectCardType/api";

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const MallCollectCardLogPage = (props: { children: any; title: any, activityId: any; collectCardSeriesId: any; }) => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const { value: types = [] } = useAsync(async () => {
    const res = await MallCollectCardTypeApi.getMallCollectCardType({ params: { collectCardSeriesId: props.collectCardSeriesId, pageSize: 999 } });
    let list: any[] = res?.list || [];
    list = list.map((n) => ({ label: n.name, value: n.id }));
    return list;
    // console.log(res);
  }, []);

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ activityId: props.activityId }} //默认参数
          request={(p) => MallCollectCardLogApi.getMallCollectCardLog({ params: p })}
          search={[
            <Form.Item label="会员" name={`ownVipUserId`}>
              <VipPickerInput />
            </Form.Item>,
            <Form.Item label="卡类型" name={`cardTypeId`}>
              <Select options={types} placeholder="请选择卡类型" allowClear />
            </Form.Item>,
            <Form.Item label="卡名称" name={`cardName`}>
              <Input placeholder="请输入卡名称" />
            </Form.Item>,
            <Form.Item label="卡号" name={`cardNo`}>
              <Input placeholder="请输入卡号" />
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Select options={StateType.map((n) => ({ label: n.name, value: n.id }))} placeholder="请选择" allowClear />
            </Form.Item>,
            <Form.Item label="创建日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            {
              fixed: "left",
              title: "会员姓名",
              dataIndex: "vipUser",
              render: (c) => (
                c ? <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
                  : "--"
              ),
            },
            { title: "卡名称", dataIndex: "cardName", render: (c) => (c ? c : "--") },
            { title: "卡号", dataIndex: "cardNo", render: (c) => (c ? c : "--") },
            { title: "状态", dataIndex: "state", render: (c) => renderTag(StateType, c) },
            { title: "来源说明", dataIndex: "sourceDesc" },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <Popconfirm
                      title={`确定要作废这条数据吗？`}
                      onConfirm={async () => {
                        await MallCollectCardLogApi.makeDestroy({ data: { ids: item.id } });
                        message.success("操作成功");
                        handleReload();
                      }}
                    >
                      <Typography.Link disabled={![10, 20].includes(item.state)}>作废</Typography.Link>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallCollectCardLogPage;
