import { makeApi } from "@/utils/Api";

const MallCollectCardLogApi = {
  getMallCollectCardLog: makeApi("get", `/api/mallCollectCardLog`),
  getMallCollectCardLogInfo: makeApi("get", `/api/mallCollectCardLog`, true),
  makeDestroy: makeApi("post", `/api/mallCollectCardLog/abolish`),
  getCollectRule: makeApi("get", `/api/mallCollectCardType/query_rule`),
  putCollectRule: makeApi("post", `/api/mallCollectCardType/save_rule`),
};

export default MallCollectCardLogApi;
