import WeModal from "@/components/WeModal/WeModal";
import MallCollectCardLogApi from "./api";
import { Col, Form, Input, Row } from "antd";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 24 };

const MallCollectCardLogEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  hideSubmit?: any;
}) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await MallCollectCardLogApi.getMallCollectCardLogInfo(props.data.id);
      data.cardImage = ImageUpload.serializer(data.cardImage);
      form.setFieldsValue(data);
    }
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={600}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
    >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`companyId`} label="公司id" rules={[{ required: false, message: "请输入公司id" }]}>
              <Input placeholder="请输入公司id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`cardTypeId`} label="卡类型id" rules={[{ required: false, message: "请输入卡类型id" }]}>
              <Input placeholder="请输入卡类型id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`cardName`} label="卡名称" rules={[{ required: false, message: "请输入卡名称" }]}>
              <Input placeholder="请输入卡名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`cardImage`}
              label="卡图片"
              rules={[{ required: false, message: "请上传卡图片" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`cardNo`} label="卡号" rules={[{ required: false, message: "请输入卡号" }]}>
              <Input placeholder="请输入卡号" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`state`}
              label="状态 10.未生效 20.可使用 30.已使用 99.已作废 "
              rules={[{ required: false, message: "请输入状态 10.未生效 20.可使用 30.已使用 99.已作废 " }]}
              initialValue={0}
            >
              <InputNumber
                min={0}
                placeholder="请输入状态 10.未生效 20.可使用 30.已使用 99.已作废 "
                style={{ width: "100%" }}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`ownUserId`} label="归属用户id" rules={[{ required: false, message: "请输入归属用户id" }]}>
              <Input placeholder="请输入归属用户id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`ownVipUserId`}
              label="归属会员id"
              rules={[{ required: false, message: "请输入归属会员id" }]}
            >
              <Input placeholder="请输入归属会员id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`canGiftTag`}
              label="是否可赠送 1.是 0.否"
              rules={[{ required: false, message: "请输入是否可赠送 1.是 0.否" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入是否可赠送 1.是 0.否" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`keyId`} label="关联id" rules={[{ required: false, message: "请输入关联id" }]}>
              <Input placeholder="请输入关联id" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallCollectCardLogEdit;
