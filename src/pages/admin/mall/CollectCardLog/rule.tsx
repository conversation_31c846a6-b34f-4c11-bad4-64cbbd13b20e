import RichText from "@/components/RichText";
import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, message, Row, Switch } from "antd";
import Api from "./api";

const layout = { row: 10, col: 24 };

export const SignRule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.collectOpenTag = Number(data.collectOpenTag);
    await Api.putCollectRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getCollectRule();
    data.collectOpenTag = !!data.collectOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="集卡规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`collectOpenTag`} label="是否开启集卡" valuePropName="checked">
              <Switch></Switch>
            </Form.Item>
          </Col>
          <Col span={layout.col/3}>
            <Form.Item name={`collectDayMaxTimes`} label="每天抽奖次数">
              <InputNumber placeholder="每天抽奖次数" addonAfter="次" style={{ width: "100%" }} min={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`collectAdvContent`} label="集卡广告语">
              <Input placeholder="请输入广告语" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`collectRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
