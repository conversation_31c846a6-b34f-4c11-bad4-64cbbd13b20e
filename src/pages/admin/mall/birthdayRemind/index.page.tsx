import WeTable from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import dayjs from "dayjs";
import { SexType } from "../member/types";
import UserManager from "@/components/UserManager";
import BirthdayRangePicker from "@/components/Units/BirthdayRangePicker";

const BirthdayRemind = () => {
  return (
    <WeTable
      tableProps={{ scroll: { x: "max-content" } }}
      request={(params) => MallApi.getMemberList({ params })}
      search={[
        <BirthdayRangePicker initialValue={[dayjs().format("MM-DD"), dayjs().format("MM-DD")]} />
      ]}
      columns={[
        {
          fixed: "left",
          width: 100,
          title: "会员姓名",
          dataIndex: "name",
          render: (c, item) => (
            <UserManager userId={item.id}>
              <a>{c}</a>
            </UserManager>
          ),
        },
        { title: "门店", dataIndex: "shopName" },
        { title: "会员号", dataIndex: "vipCard" },
        { title: "手机", dataIndex: "mobile" },
        { title: "顾客来源", dataIndex: "sourceName" },
        { title: "渠道类型", dataIndex: "channelName" },
        { title: "性别", dataIndex: "sex", render: (c) => SexType.find((n) => n.id == c)?.name },
        { title: "生日", dataIndex: "birthday", render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "") },
        { title: "会员等级", dataIndex: "vipLevelName" },
        { title: "有效期", dataIndex: "expireDate", render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "") },
        { title: "钱包余额", dataIndex: "balance" },
        { title: "积分", dataIndex: "integral" },
        { title: "最近消费", dataIndex: "lastConsumeMoney" },
        { title: "累计消费", dataIndex: "totalConsumeMoney" },
        {
          title: "首次到院",
          dataIndex: "firstServiceTime",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : ""),
        },
        {
          title: "最近到院",
          dataIndex: "lastServiceTime",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : ""),
        },
        { title: "所属客服", dataIndex: "adviserName" },
        { title: "所属开发", dataIndex: "developerName" },
        { title: "治疗师", dataIndex: "doctorName" },
        { title: "推荐人", dataIndex: "promotionUser", render: (c) => c?.name ?? "--" },
        {
          title: "建档时间",
          dataIndex: "createDate",
          width: 160,
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
        },
      ]}
    />
  );
};

export default BirthdayRemind;
