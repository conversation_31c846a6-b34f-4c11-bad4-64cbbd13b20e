import VipPlusPicker from "@/components/VipPlusPicker";
import UserPane from "@/components/Units/UserPane";
import { <PERSON><PERSON>, Button, Card, Col, Form, Input, message, Radio, Row } from "antd";
import { useSetState } from "react-use";
import { PickDateTime } from "./PickDateTime";
import Mall<PERSON>pi from "@/services/MallApi";
import UserApi from "@/services/UserApi";
import WeModal from "@/components/WeModal/WeModal";
import { formatDate } from "@/utils/Tools";
import { DeleteOutlined } from "@ant-design/icons";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";

const Group = (props: { span: number; children: any }) => {
  return (
    <Row>
      <Col span={props.span || 8}>{props.children}</Col>
    </Row>
  );
};

export const EditDue = (props: { children: any; title: any; onOk: Function; data?: any; isEdit?: boolean }) => {
  const [form] = Form.useForm();
  const isNew = Form.useWatch("isNew", form);
  const isEdit = !!props.data?.id;

  const [state, setState] = useSetState({
    user: null as any,
    types: [] as any[],
  });

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = (res || [])?.map((n: any) => ({ label: n.name, value: n.id }));
    setState({ types: list });
  };

  const onOpen = async () => {
    form.resetFields();
    setState({ user: null });
    fetchTypes();

    if (props.data) {
      console.log(props.data);
      const { ...data } = props.data;
      data.preTime = formatDate(data.preTime, "YYYY-MM-DD HH:mm");
      data.images = UploadV2.str2arr(data.images);
      // data.lockPreTime = formatDate(data.lockPreTime, "YYYY-MM-DD HH:mm");
      // data.isNew = isEdit && !data.shopVipUserId;
      form.setFieldsValue(data);

      if (data.shopVipUserId) {
        fetchUser();
      }
    }
  };

  const onOk = async () => {
    const data = await form.validateFields();

    if (!data.id) {
      if (!data.isNew) {
        data.shopVipUserId = state.user?.id;
        data.preMobile = state.user?.mobile;

        if (!data.shopVipUserId) {
          message.error("请先选择用户");
          return false;
        }
      }

      data.reservationDataList = data.reservationDataList.map((n: any) => {
        return { ...n, preTime: formatDate(n.preTime, "YYYY-MM-DD HH:mm:00"), images: UploadV2.arr2str(n.images) };
      });

      await MallApi.addReservationGroup({ data });
      message.success("新增预约成功");
      props.onOk?.();
    } else {
      data.images = UploadV2.arr2str(data.images);
      await MallApi.putReservation({ data });
      message.success("修改预约成功");
      props.onOk?.();
    }
  };

  return (
    <Form form={form} labelCol={{ flex: "80px" }}>
      <WeModal
        trigger={props.children}
        title={
          <div className="flex gap-4 fw-normal">
            <div className="fw-bold">{props.title}</div>
            {!isEdit && !props.data?.shopVipUserId && (
              <div>
                <Form.Item noStyle name={"isNew"} initialValue={false}>
                  <Radio.Group
                    options={[
                      { label: "已建档", value: false },
                      { label: "未建档", value: true },
                    ]}
                  />
                </Form.Item>
              </div>
            )}
            {!isNew && !isEdit && !props.data?.shopVipUserId && (
              <div>
                <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} />
              </div>
            )}
          </div>
        }
        width={1000}
        onOpen={onOpen}
        onOk={onOk}
      >
        <div className="h-4"></div>

        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>

        {!isNew && !state.user?.id && !isEdit && (
          <div className="mb-4">
            <Alert message="请先选择用户信息" type="warning" showIcon />
          </div>
        )}

        {!!state.user?.id && !isNew && (
          <div className="mb-4 min-h-110px">
            <UserPane user={state.user} />
          </div>
        )}

        {(isNew || (isEdit && !props.data?.shopVipUserId)) && (
          <Card className="mb-4" size="small" title="客户信息">
            <Row>
              <Col span={8}>
                <Form.Item label="客户姓名" name={`preName`} rules={[{ required: false, message: "请输入客户姓名" }]}>
                  <Input placeholder="请输入客户姓名" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="客户手机" name={`preMobile`} rules={[{ required: false, message: "请输入客户手机" }]}>
                  <Input placeholder="请输入客户手机" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        )}

        {!isEdit && (
          <Form.List name={"reservationDataList"} initialValue={[{}]}>
            {(fields, act) => {
              return (
                <div>
                  {fields.map((field, idx) => {
                    return (
                      <Card
                        key={idx}
                        size="small"
                        title={"预约信息" + (idx + 1)}
                        className="mb-4"
                        extra={!!idx && <Button size="small" type="link" icon={<DeleteOutlined className="text-red" />} onClick={() => act.remove(field.name)} />}
                      >
                        <Form.Item label="预约类型" name={[field.name, `typeId`]} rules={[{ required: true, message: "请选择预约类型" }]}>
                          <Radio.Group options={state.types} />
                        </Form.Item>

                        <Group span={8}>
                          <Form.Item noStyle shouldUpdate={(p, c) => p["reservationDataList"][field.name]["typeId"] !== c["reservationDataList"][field.name]["typeId"]}>
                            {(ins) => {
                              const typeId = ins.getFieldValue(["reservationDataList", field.name, `typeId`]);
                              const preTime = ins.getFieldValue(["reservationDataList", field.name, `preTime`]);
                              return (
                                <Form.Item label="预约日期" name={[field.name, `preTime`]} rules={[{ required: true, message: "请选择预约日期" }]}>
                                  <PickDateTime typeId={typeId} value={preTime} />
                                </Form.Item>
                              );
                            }}
                          </Form.Item>
                        </Group>

                        <Group span={16}>
                          <Form.Item label="预约备注" name={[field.name, `content`]} rules={[{ required: true, message: "请输入预约备注" }]}>
                            <Input.TextArea autoSize={{ minRows: 2 }} placeholder="请输入预约备注" maxLength={400} showCount />
                          </Form.Item>
                        </Group>

                        <Form.Item label="附件" name={[field.name, `images`]}>
                          <UploadV2 maxCount={20} />
                        </Form.Item>
                      </Card>
                    );
                  })}

                  <Button type="dashed" block onClick={() => act.add({})}>
                    新增预约信息
                  </Button>
                </div>
              );
            }}
          </Form.List>
        )}

        {isEdit && (
          <Card size="small" title="预约信息">
            <Form.Item label="预约类型" name={`typeId`} rules={[{ required: true, message: "请选择预约类型" }]}>
              <Radio.Group options={state.types} />
            </Form.Item>

            <Group span={8}>
              <Form.Item noStyle shouldUpdate={(p, c) => p["typeId"] !== c["typeId"]}>
                {(ins) => {
                  const typeId = ins.getFieldValue(`typeId`);
                  const preTime = ins.getFieldValue("preTime");
                  return (
                    <Form.Item label="预约日期" name={`preTime`} rules={[{ required: true, message: "请选择预约日期" }]}>
                      <PickDateTime typeId={typeId} value={preTime} />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Group>

            <Group span={16}>
              <Form.Item label="预约备注" name={`content`} rules={[{ required: true, message: "请输入预约备注" }]}>
                <Input.TextArea autoSize={{ minRows: 2 }} placeholder="请输入预约备注" maxLength={400} showCount />
              </Form.Item>
            </Group>

            <Form.Item label="附件" name={`images`}>
              <UploadV2 maxCount={20} />
            </Form.Item>
          </Card>
        )}
      </WeModal>
    </Form>
  );
};
