export const RState = [
  { id: 10, name: "待确认", color: "cyan" },
  { id: 20, name: "待到院", color: "purple" },
  { id: 30, name: "已到院", color: "green" },
  { id: 40, name: "已超时", color: "red" },
  { id: 99, name: "已取消", color: "gray" },
];

export const SelectState = [
  { id: 10, name: "待确认", color: "cyan" },
  { id: 20, name: "待到院", color: "purple" },
  { id: 30, name: "已到院", color: "green" },
  { id: 40, name: "已超时", color: "red" },
];

//08:00-20:00，时间不同时段颜色数组
export const TimeColor = [
  { value: "00:00", label: "Blue" },
  { value: "00:30", label: "Green" },
  { value: "01:00", label: "Blue" },
  { value: "01:30", label: "Green" },
  { value: "02:00", label: "Blue" },
  { value: "02:30", label: "Green" },
  { value: "03:00", label: "Blue" },
  { value: "03:30", label: "Green" },
  { value: "04:00", label: "Blue" },
  { value: "04:30", label: "Green" },
  { value: "05:00", label: "Blue" },
  { value: "05:30", label: "Green" },
  { value: "06:00", label: "Blue" },
  { value: "06:30", label: "Green" },
  { value: "07:00", label: "Blue" },
  { value: "07:30", label: "Green" },
  { value: "08:00", label: "Blue" },
  { value: "08:30", label: "Green" },
  { value: "09:00", label: "Blue" },
  { value: "09:30", label: "Green" },
  { value: "10:00", label: "Blue" },
  { value: "10:30", label: "Green" },
  { value: "11:00", label: "Blue" },
  { value: "11:30", label: "Green" },
  { value: "12:00", label: "Blue" },
  { value: "12:30", label: "Green" },
  { value: "13:00", label: "Blue" },
  { value: "13:30", label: "Green" },
  { value: "14:00", label: "Blue" },
  { value: "14:30", label: "Green" },
  { value: "15:00", label: "Blue" },
  { value: "15:30", label: "Green" },
  { value: "16:00", label: "Blue" },
  { value: "16:30", label: "Green" },
  { value: "17:00", label: "Blue" },
  { value: "17:30", label: "Green" },
  { value: "18:00", label: "Blue" },
  { value: "18:30", label: "Green" },
  { value: "19:00", label: "Blue" },
  { value: "19:30", label: "Green" },
  { value: "20:00", label: "Blue" },
  { value: "20:30", label: "Green" },
  { value: "21:00", label: "Blue" },
  { value: "21:30", label: "Green" },
  { value: "22:00", label: "Blue" },
  { value: "22:30", label: "Green" },
  { value: "23:00", label: "Blue" },
  { value: "23:30", label: "Green" },
]
export const TimeColor2 = [
  { value: "00:00", label: "#DC143C" },
  { value: "00:30", label: "#FF1493" },
  { value: "01:00", label: "#C71585" },
  { value: "01:30", label: "#FF00FF" },
  { value: "02:00", label: "#800080" },
  { value: "02:30", label: "#4B0082" },
  { value: "03:00", label: "#0000FF" },
  { value: "03:30", label: "#191970" },
  { value: "04:00", label: "#4169E1" },
  { value: "04:30", label: "#00BFFF" },
  { value: "05:00", label: "#5F9EA0" },
  { value: "05:30", label: "#00FFFF" },
  { value: "06:00", label: "#008B8B" },
  { value: "06:30", label: "#00FA9A" },
  { value: "07:00", label: "#FFFF00" },
  { value: "07:30", label: "#808000" },
  { value: "08:00", label: "#DAA520" },
  { value: "08:30", label: "#FF8C00" },
  { value: "09:00", label: "#8B4513" },
  { value: "09:30", label: "#FF7F50" },
  { value: "10:00", label: "#FF4500" },
  { value: "10:30", label: "#FF0000" },
  { value: "11:00", label: "#A52A2A" },
  { value: "11:30", label: "#696969" },
  { value: "12:00", label: "#DC143C" },
  { value: "12:30", label: "#FF1493" },
  { value: "13:00", label: "#C71585" },
  { value: "13:30", label: "#FF00FF" },
  { value: "14:00", label: "#800080" },
  { value: "14:30", label: "#4B0082" },
  { value: "15:00", label: "#0000FF" },
  { value: "15:30", label: "#191970" },
  { value: "16:00", label: "#4169E1" },
  { value: "16:30", label: "#00BFFF" },
  { value: "17:00", label: "#5F9EA0" },
  { value: "17:30", label: "#00FFFF" },
  { value: "18:00", label: "#008B8B" },
  { value: "18:30", label: "#00FA9A" },
  { value: "19:00", label: "#FFFF00" },
  { value: "19:30", label: "#808000" },
  { value: "20:00", label: "#DAA520" },
  { value: "20:30", label: "#FF8C00" },
  { value: "21:00", label: "#8B4513" },
  { value: "21:30", label: "#FF7F50" },
  { value: "22:00", label: "#FF4500" },
  { value: "22:30", label: "#FF0000" },
  { value: "23:00", label: "#A52A2A" },
  { value: "23:30", label: "#696969" },
]



