import UserManager from "@/components/UserManager";
import UserInfoEdit from "@/components/UserManager/UserInfoEdit";
import WeModal from "@/components/WeModal/WeModal";
import MallA<PERSON> from "@/services/MallApi";
import { Col, Form, Input, Popconfirm, Row, Table, message } from "antd";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };

const PhoneRegex = /^1[3-9]\d{9}$/;
const ChineseRegex = /^[\u4e00-\u9fa5]+$/;

const DataRelateNew = (props: { children: any; title: any; open?: boolean; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    open: false,
    search: 0,//0.无 1.有数据 2.无数据
    users: [] as any[],
    searchName: "",
    searchMobile: "",
  });

  const open = props.open ?? state.open;
  const handleOpen = () => {
    setState({ open: true, search: 0, users: [] });
    form.resetFields();
    if (!!props.data?.preMobile) {
      form.setFieldsValue({ searchKey: props.data?.preMobile });
      setTimeout(() => {
        searchUser();
      }, 500);
    }
  };

  const searchUser = async () => {
    setState({ search: 0, users: [], searchMobile: "", searchName: "" });
    const searchKey = form.getFieldValue("searchKey");
    if (!searchKey) return;
    if (PhoneRegex.test(searchKey)) {
      setState({ searchMobile: searchKey });
    }
    if (ChineseRegex.test(searchKey)) {
      setState({ searchName: searchKey });
    }
    const params = { searchKey: searchKey, pageSize: 10 };
    const res = await MallApi.getMemberForSelect({ params });
    let list: any[] = res?.list || [];
    if (list.length > 0) {
      setState({ search: 1, users: list });
    } else {
      setState({ search: 2, users: [] });
    }
  };


  const handleReservation = async (shopVipUserId: any) => {
    const data = { id: props.data.id, shopVipUserId: shopVipUserId };
    await MallApi.relateReservation({ data });
    message.success("关联预约成功");
    props.onOk();
  };
  const closeWin = async () => {
    setState({ open: false });
    props.onOk?.()
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={800}
      open={open}
      onOpen={handleOpen}
      onCancel={closeWin}
      okButtonProps={{ hidden: true }}
      cancelButtonProps={{ hidden: true }}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row>
          <Col span={layout.col}>
            <Form.Item name={`searchKey`} label="查询会员">
              <Input
                placeholder="请输入会员号/姓名/手机号"
                onChange={() => {
                  searchUser();
                }}
              />
            </Form.Item>
          </Col>
          {state.search == 1 && (
            <>
              <Col span={layout.col}>
                <Table
                  dataSource={state.users}
                  size="small"
                  bordered={false}
                  pagination={false}
                  title={() => (<span className="text-red">仅显示查询结果前10条数据</span>)}
                  columns={[
                    {
                      title: "会员",
                      render: (item) => (
                        <UserManager userId={item?.id}>
                          <a>{item?.name}</a>
                        </UserManager>
                      )
                    },
                    { title: "会员号", dataIndex: "vipCard", render: (c) => <span>{c}</span> },
                    { title: "手机号", dataIndex: "mobile", render: (c) => <span>{c}</span> },
                    {
                      title: "操作", render: (item) => (
                        <Popconfirm title={`确定要进行会员关联吗？`} onConfirm={() => handleReservation(item.id)}>
                          <a>关联</a>
                        </Popconfirm>
                      )
                    },
                  ]}
                ></Table>
              </Col>
              <Col span={layout.col} className="pt-5 text-center">
                没有找到匹配的档案信息? 您还可以
                <UserInfoEdit
                  title={"会员建档"}
                  onOk={closeWin}
                  reservationId={props.data.id}
                  preName={state.searchName || props.data.preName}
                  preMobile={state.searchMobile || props.data.preMobile}
                >
                  <a className="pl-2">
                    [新建会员档案]
                  </a>
                </UserInfoEdit>
              </Col>
            </>
          )}
          <Col />
          {state.search == 2 && (
            <Col span={layout.col} className="text-center">
              没有查询到任何数据，请点击
              <UserInfoEdit
                title={"会员建档"}
                onOk={closeWin}
                reservationId={props.data.id}
                preName={state.searchName || props.data.preName}
                preMobile={state.searchMobile || props.data.preMobile}
              >
                <a className="pl-2">
                  [新建会员档案]
                </a>
              </UserInfoEdit>
            </Col>
          )}
        </Row>
      </Form>
    </WeModal>
  );
};

export default DataRelateNew;
