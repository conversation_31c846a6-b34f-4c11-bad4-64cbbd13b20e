import MallApi from "@/services/MallApi";
import { useState } from "react";
import { <PERSON>ge, <PERSON>ton, Card, Checkbox, DatePicker, Divider, Dropdown, Form, Image, Input, Modal, Pagination, Popconfirm, Radio, Select, Space, Table, Typography, message } from "antd";
import { DatePresetRanges, renderTag, str2arr } from "@/utils/Tools";
import { RState, SelectState, TimeColor } from "./types";
import dayjs from "dayjs";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import DictPicker from "@/components/Units/DictPicker";
import UserManager from "@/components/UserManager";
import DaoDian from "./DaoDian";
import { useMount, useSetState } from "react-use";
import { SexType } from "../member/types";
import { ClearOutlined, DownOutlined, PlusOutlined, SearchOutlined, SnippetsOutlined, SwapRightOutlined, UpOutlined, WechatOutlined } from "@ant-design/icons";
import BirthdayRangePicker from "@/components/Units/BirthdayRangePicker";
import VipPicker from "@/components/Units/VipPicker";
import { DueModal } from "./DueModal";
import { IntentionTypePicker } from "@/components/Units/IntentionTypePicker";
import DataRelateNew from "./DataRelateNew";
import { EditDue } from "./EditDue";
import { useTable } from "@/components/WeTablev2/useTable";
import DictCheckbox from "@/components/Units/DictCheckbox";

const StartEndGroup = (props: { name: any }) => {
  return (
    <div style={{ display: "flex" }}>
      <Form.Item noStyle name={`start${props.name}`}>
        <Input placeholder="开始值" allowClear />
      </Form.Item>
      <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
      <Form.Item noStyle name={`end${props.name}`}>
        <Input placeholder="结束值" allowClear />
      </Form.Item>
    </div>
  );
};

const Reservation = (props: { userId?: any; onModeChange?: any; queryType?: any }) => {
  const [fold, setFold] = useState(true);
  const [state, setState] = useSetState({
    keys: [] as any[],

    exdata: [] as any[],
    colors: ["red", "yellow", "orange", "cyan", "green", "blue", "purple"],

    staff: [] as any[],
    vipLv: [] as any[],

    imgShow: false,
    imgs: [] as any[],
  });

  const table = useTable({
    size: props.userId ? 10 : undefined,
    params: { queryType: props.queryType, shopVipUserId: props.userId },
    onFetch: (p) => {
      p = { ...p };
      p.state = p.state?.join(",");
      fetchStat(p);
      return MallApi.getReservation({ params: p });
    },
  });

  useMount(() => {
    fetchStaff();
    fetchVipLv();
  });

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : null);
  const fdate2 = (n: any, f = "YYYY-MM-DD HH:mm") => (n ? dayjs(n).format(f) : null);

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  const handleCancel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delReservation({ data });
    message.success("取消成功");
    handleReload();
  };

  const handleConfirm = async (item: any) => {
    const data = { id: item.id, state: 20 };
    await MallApi.daoDianReservation({ data });
    message.success("确认成功");
    handleReload();
  };

  const fetchStat = async (params: any) => {
    if (props.userId) return;
    params = { countType: 1, ...params };
    const exdata = await MallApi.getReservationStat({ params });
    setState({ exdata });
  };

  return (
    <div className="flex-1 min-w-0 p-2 overflow-y-auto">
      <Form form={table.form} onValuesChange={table.onFormChange}>
        <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
          <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
            <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
              <Form.Item label="预约日期" name={`PreTime`} initialValue={props.userId ? [] : [dayjs().subtract(15, "day").startOf("day"), dayjs().add(15, "day").endOf("day")]}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              {!props.userId && (
                <Form.Item label="会员" name={`shopVipUserId`}>
                  <VipPickerInput/>
                </Form.Item>
              )}
              <Form.Item label="预约状态" name={`state`} initialValue={props.userId ? [] : [10, 20, 40]}>
                <Checkbox.Group options={SelectState.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
              <Form.Item label="预约类型" name={`typeId`}>
                <DictCheckbox type="reservationType" />
              </Form.Item>
              <Form.Item label="到院类型" name={`triageTypeId`}>
                <DictCheckbox type="triageType" />
              </Form.Item>
              <Form.Item label="预约号" name={`serialNo`}>
                <Input placeholder="请输入预约号" />
              </Form.Item>
              <Form.Item label="创建时间" name={`CreateDate`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>

              <Form.Item label="顾客来源" name={`sourceIdExt`}>
                <DictPicker type="shopVipSource" allowClear placeholder="请选择顾客来源" />
              </Form.Item>
              <Form.Item label="渠道类型" name={`channelIdExt`}>
                <DictPicker type="shopVipChannel" allowClear placeholder="请选择渠道类型" />
              </Form.Item>

              <Form.Item label="所属客服" name={`adviserIdExt`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属客服" />
              </Form.Item>
              <Form.Item label="所属开发" name={`developerIdExt`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属开发" />
              </Form.Item>
              <Form.Item label="性别" name={`sexExt`}>
                <Select options={SexType.map((n) => ({ label: n.name, value: n.id }))} placeholder="请选择性别" allowClear />
              </Form.Item>

              <BirthdayRangePicker />

              <Form.Item label="会员等级" name={`vipLevelIdExt`}>
                <Select options={state.vipLv} allowClear showSearch optionFilterProp="label" placeholder="请选择会员等级" />
              </Form.Item>
              <Form.Item label="会员有效期" name={`ExpireDateExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="累计消费">
                <StartEndGroup name={`TotalConsumeMoneyExt`} />
              </Form.Item>
              <Form.Item label="首次到院" name={`FirstServiceTimeExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="最近到院" name={`LastServiceTimeExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>

              <Form.Item label="推荐人" name={`promotionUserIdExt`}>
                <VipPicker placeholder="会员号/姓名/手机号"/>
              </Form.Item>
              <Form.Item label="建档时间" name={`CreateDateExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="已购项目" name={`buyedProjectExt`}>
                <Input placeholder="请输入已购项目" />
              </Form.Item>
              <Form.Item label="咨询意向" name={`intentionTypeExt`}>
                <IntentionTypePicker />
              </Form.Item>
            </div>
            <div className="flex gap-2 pl-3">
              <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
              <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                搜索
              </Button>
              <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
            </div>
            {!props.userId && (
              <div className="pl-3">
                <Radio.Group
                  value={"list"}
                  options={[
                    { label: "列表", value: "list" },
                    { label: "日历", value: "pane" },
                  ]}
                  optionType="button"
                  onChange={(e) => props.onModeChange?.(e.target.value)}
                />
              </div>
            )}
          </div>
        </Card>

        <Card
          classNames={{ body: "!p-0" }}
          title={
            <Space>
              <EditDue title={`新增预约`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
                <Button type="primary" icon={<PlusOutlined />}>
                  新增预约
                </Button>
              </EditDue>
              <Divider type="vertical" />
              <div style={{ display: "flex" }}>
                {state.exdata?.map?.((item, idx) => (
                  <Badge key={idx} color={state.colors[idx]} style={{ margin: "0 15px" }} text={`${item.countName}: ${item.totalCount}人`} />
                ))}
              </div>
            </Space>
          }
          extra={
            <>
              {!props.userId && (
                <DueModal>
                  <Button type="primary" icon={<SnippetsOutlined />}>
                    放号管理
                  </Button>
                </DueModal>
              )}
            </>
          }
        >
          <Table
            rowKey={"id"}
            scroll={{ x: "max-content" }}
            dataSource={table.state.list}
            columns={[
              {
                fixed: "left",
                title: "预约顾客",
                dataIndex: "vipUser",
                hidden: props.userId,
                render: (c, item) =>
                  c ? (
                    <div className="flex items-center gap-1">
                      <WechatOutlined className="text-([16px] #2aae67) data-[disabled=true]:invisible" data-disabled={!c?.wxsRegisterTag} />
                      <UserManager userId={c?.id}>
                        <div>
                          <a>{c?.name}</a>
                          {item?.preName != c?.name ? <span className="text-gray-400 ml-2px">({item?.preName})</span> : ""}
                        </div>
                      </UserManager>
                    </div>
                  ) : (
                    <div className="flex">
                      {item.preName || "--"} / {item.preMobile || "--"}
                      <div className="pl-1">
                        [
                        <DataRelateNew title={"关联会员"} onOk={handleReload} data={item}>
                          <a>关联会员</a>
                        </DataRelateNew>
                        ]
                      </div>
                    </div>
                  ),
              },
              { title: "预约日期", dataIndex: "preTime", render: (c) => fdate2(c, "YYYY-MM-DD") },
              {
                title: "预约时间",
                dataIndex: "preTime",
                //render: (c) => <span className="text-blue font-bold text-14px">{fdate2(c, "HH:mm")}</span>,
                render: (c) => {
                  const time = fdate2(c, "HH:mm");
                  return (
                    <span className="font-bold text-14px" style={{ color: TimeColor.find((i) => i.value === time)?.label }}>
                      {time}
                    </span>
                  );
                },
              },
              { title: "门店", dataIndex: "shopName", hidden: !props.userId },
              { title: "预约类型", dataIndex: "typeName" },
              {
                title: "预约备注",
                dataIndex: "content",
                render: (c) =>
                  c ? (
                    <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                      {c}
                    </Typography.Text>
                  ) : (
                    "--"
                  ),
              },
              {
                title: "预约附件",
                dataIndex: "images",
                render: (n) => {
                  const arr = str2arr(n);
                  if (!arr.length) return "--";

                  return (
                    <a
                      onClick={() => {
                        setState({ imgs: arr, imgShow: true });
                      }}
                    >
                      点击查看({arr.length}张)
                    </a>
                  );
                },
              },
              { title: "预约号", dataIndex: "serialNo" },

              {
                title: "到院类型",
                dataIndex: "triageTypeName",
                render: (c) => c ?? "--",
              },
              { title: "预约医生", dataIndex: "doctorName", render: (c) => c ?? "--" },
              { title: "预约项目", dataIndex: "productName", render: (c) => c ?? "--" },
              {
                title: "创建人",
                dataIndex: "employeeName",
                render: (c) => c ?? "--",
              },
              { title: "创建时间", dataIndex: "createDate", render: (c) => fdate(c) },
              {
                fixed: "right",
                title: "预约状态",
                dataIndex: "state",
                render: (c) => renderTag(RState, c) ?? "--",
              },
              {
                fixed: "right",
                title: "操作",
                render: (item) => (
                  <Space>
                    <EditDue title={`编辑预约 - ${item.preMobile}`} data={item} isEdit onOk={handleReload}>
                      <Typography.Link disabled={![10, 20, 40].includes(item.state)}>编辑</Typography.Link>
                    </EditDue>

                    <Popconfirm title={`确定要取消这条预约记录吗？`} onConfirm={() => handleCancel(item)}>
                      <Typography.Link disabled={![10, 20, 40].includes(item.state)}>取消</Typography.Link>
                    </Popconfirm>

                    <Popconfirm title={`确定要确认这条预约记录吗？`} onConfirm={() => handleConfirm(item)}>
                      <Typography.Link disabled={![10].includes(item.state)}>确认</Typography.Link>
                    </Popconfirm>

                    <DaoDian title={`到院`} onOk={handleReload} data={item}>
                      <Typography.Link disabled={![10, 20, 40].includes(item.state) || !item.vipUser}>到院</Typography.Link>
                    </DaoDian>
                  </Space>
                ),
              },
            ]}
            pagination={false}
            rowSelection={{
              selectedRowKeys: state.keys,
              onChange: (selectedRowKeys) => {
                setState({ keys: selectedRowKeys });
              },
            }}
          />
          <div className="p-4 flex items-center">
            <Space>
              <Button
                onClick={() => {
                  const len = state.keys.length;
                  if (len == table.state.list.length) {
                    setState({ keys: [] });
                  } else {
                    setState({ keys: table.state.list.map((n) => n.id) });
                  }
                }}
              >
                全选
              </Button>
              <Dropdown
                menu={{
                  items: [
                    { label: "批量确认", key: "confrim" },
                    { label: "批量取消", key: "cancel" },
                  ],
                  onClick: (e) => {
                    if (!state.keys.length) return message.error("请先进行选择");

                    if (e.key == "confrim") {
                      Modal.confirm({
                        title: "确定要批量确认这些预约吗？",
                        onOk: async () => {
                          const data = { ids: state.keys.join(",") };
                          await MallApi.confirmReservation({ data });
                          message.success("确认成功");
                          handleReload();
                        },
                      });
                    }

                    if (e.key == "cancel") {
                      Modal.confirm({
                        title: "确定要批量取消这些预约吗？",
                        onOk: async () => {
                          const data = { ids: state.keys.join(",") };
                          await MallApi.delReservation({ data });
                          message.success("取消成功");
                          handleReload();
                        },
                      });
                    }
                  },
                }}
              >
                <Button>
                  批量操作
                  <DownOutlined />
                </Button>
              </Dropdown>
              <div className="text-#666 text-xs">
                {table.state.list.length}条数据(已选{state.keys.length}条)
              </div>
            </Space>
            <Pagination
              className="ml-a"
              {...{
                current: table.state.page,
                pageSize: table.state.size,
                total: table.state.total,
                showSizeChanger: true,
                showTotal: (total) => <div>共 {total} 条数据</div>,
                pageSizeOptions: [5, 10, 20, 50, 100],
                onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
              }}
            />
          </div>
        </Card>
      </Form>

      <Image.PreviewGroup
        preview={{
          visible: state.imgShow,
          // current: 0,
          onVisibleChange: (val) => setState({ imgShow: val }),
        }}
      >
        <div style={{ display: "none" }}>
          {state.imgs.map((n, i) => (
            <Image key={i} src={n} />
          ))}
        </div>
      </Image.PreviewGroup>
    </div>
  );
};

export default Reservation;
