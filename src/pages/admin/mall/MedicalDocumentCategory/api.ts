import { makeApi } from "@/utils/Api";

const MallMedicalDocumentCategoryApi = {
    getMallMedicalDocumentCategory: makeApi("get", `/api/mallMedicalDocumentCategory`),
    getMallMedicalDocumentCategoryInfo: makeApi("get", `/api/mallMedicalDocumentCategory`, true),
    addMallMedicalDocumentCategory: makeApi("post", `/api/mallMedicalDocumentCategory`),
    putMallMedicalDocumentCategory: makeApi("put", `/api/mallMedicalDocumentCategory`),
    delMallMedicalDocumentCategory: makeApi("delete", `/api/mallMedicalDocumentCategory`),
};

export default MallMedicalDocumentCategoryApi;
