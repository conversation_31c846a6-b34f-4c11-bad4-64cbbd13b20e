import WeModal from "@/components/WeModal/WeModal";
import MallMedicalDocumentCategoryApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";

const layout = { row: 10, col: 24 };

const MallMedicalDocumentCategoryEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?:any;}) => {

  const [form] = Form.useForm();

  const handleOpen = async() => {
    form.resetFields();
    if (props.data) {
      const data = await MallMedicalDocumentCategoryApi.getMallMedicalDocumentCategoryInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id)  {
      await MallMedicalDocumentCategoryApi.addMallMedicalDocumentCategory({ data });
      message.success("添加病历文档分类成功");
    }
    if (data.id) {
      await MallMedicalDocumentCategoryApi.putMallMedicalDocumentCategory({ data });
      message.success("修改病历文档分类成功");
    }
    props.onOk?.();
  };

return (
  <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps ={{hidden:props.hideSubmit}} onOk={handleSubmit}>
    <Form form={form} labelCol={{ flex: "100px" }}>
      <Form.Item name={`id`} hidden>
        <Input />
      </Form.Item>
      <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`companyId`} label="公司id" rules={[{ required: false, message: "请输入公司id" }]}>
              <Input placeholder="请输入公司id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: false, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`code`} label="编码" rules={[{ required: false, message: "请输入编码" }]}>
              <Input placeholder="请输入编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态 1.启用 0.禁用" rules={[{ required: false, message: "请输入状态 1.启用 0.禁用" }]} initialValue={0}>
             <InputNumber min={0} placeholder="请输入状态 1.启用 0.禁用" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
      </Row>
    </Form>
  </WeModal>
  );
};

export default MallMedicalDocumentCategoryEdit;
