.tree {
  .row {
    min-width: 0;
    display: flex;
  }

  .title {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 6px;
  }

  .extra {
    visibility: hidden;
  }

  .ico {
    margin-right: 5px;
  }

  :global(.ant-tree-treenode-selected) .extra {
    visibility: visible;
  }

  :global(.ant-tree-treenode) {
    padding: 4px 0 calc(4px + 4px);
  }

  :global(.ant-tree-node-content-wrapper) {
    min-width: 0;
  }
}
