import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Input, InputNumber, Radio, Row, Switch, TreeSelect, message } from "antd";
import { useMemo } from "react";
import { ModelMap } from "./types";

const layout = { row: 10, col: 24 };

const EditType = (props: { children: any; title: any; onOk: Function; tree: any[]; data?: any; property: any }) => {
  const [form] = Form.useForm();
  const isEdit = !!props.data?.id;
  const tree = useMemo(() => {
    return [{ id: -1, name: "顶级节点", children: props.tree }];
  }, [props.tree]);

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      data.parentId = data.parentId || -1;
      data.image = ImageUpload.serializer(data.image);
      data.showImages = ImageUpload.serializer(data.showImages);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();

    if (data.parentId === -1) {
      data.parentId = undefined;
    }

    data.property = props.property;
    data.showState = Number(data.showState);
    data.image = ImageUpload.deserializer(data.image);
    data.showImages = ImageUpload.deserializer(data.showImages);

    if (data.id) {
      await MallApi.putShopProductType({ data });
      message.success("修改项目分类成功");
    } else {
      await MallApi.addShopProductType({ data });
      message.success("修改项目分类成功");
    }

    props.onOk();
  };


  return (
    <WeModal title={props.title} trigger={props.children} onOpen={handleOpen} onOk={handleSubmit} width={500}>
      <Form form={form} labelCol={{ flex: "60px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        {/* <Form.Item hidden name={`property`} initialValue={1}>
          <Input />
        </Form.Item> */}

        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              label="上级"
              name={`parentId`}
              initialValue={-1}
              rules={[{ required: true, message: "请选择上级" }]}
            >
              <TreeSelect
                placeholder="请选择上级"
                treeData={tree}
                fieldNames={{ label: "name", value: "id" }}
                allowClear
                showSearch
                treeNodeFilterProp="name"
                treeDefaultExpandedKeys={[-1]}
                disabled={isEdit}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`mode`} label="模式" rules={[{ required: false, message: "请选择模式" }]} initialValue={1}>
              <Radio.Group options={ModelMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item label="状态" name={`showState`} valuePropName="checked" initialValue={true}>
              <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="封面图"
              name={`image`}
              rules={[{ required: false, message: "请上传封面图" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="轮播图"
              name={`showImages`}
              rules={[{ required: false, message: "请上传展示图" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={10} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditType;
