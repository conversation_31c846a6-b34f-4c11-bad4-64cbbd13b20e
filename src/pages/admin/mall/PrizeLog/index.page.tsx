import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallPrizeLogApi from "./api";
import { Form, Input, Space, Drawer, Select } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useSetState } from "react-use";
import { PropertyMap } from "./types";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";
const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const MallPrizeLogPage = (props: { children: any; title: any, activityId: any; }) => {

  const tableRef = useRef<WeTableRef>(null);


  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };


  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ activityId: props.activityId }}//默认参数
          request={(p) => MallPrizeLogApi.getMallPrizeLog({ params: p })}
          title={
            <Space>
            </Space>
          }
          search={[
            <Form.Item label="会员" name={`vipUserId`}>
              <VipPickerInput />
            </Form.Item>,
            <Form.Item label="奖品名称" name={`prizeName`}>
              <Input placeholder="请输入奖品名称" />
            </Form.Item>,
            <Form.Item label="奖品属性" name={`prizeProperty`}>
              <Select options={PropertyMap} placeholder="请选择属性" allowClear />
            </Form.Item>,
            <Form.Item label="创建日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            {
              fixed: "left",
              title: "会员姓名",
              dataIndex: "vipUser",
              render: (c) => (
                c ? <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
                  : "--"
              ),
            },
            { title: "奖品名称", dataIndex: "prizeName", render: (c) => c ? c : "--" },
            // {
            //   title: "奖品图片",
            //   dataIndex: "prizeImage",
            //   render: (c) => {
            //     return c ? <Image src={c?.length ? c : ""} style={{ maxWidth: 50, maxHeight: 50 }} /> : "--";
            //   },
            // },
            { title: "奖品属性", dataIndex: "prizeProperty", render: (c) => PropertyMap.find((item) => item.value === c)?.label },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallPrizeLogPage;
