import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, Row, Switch, TreeSelect, message } from "antd";
import { useMemo } from "react";
import { MarketCategoryApi } from "./MarketCategoryApi";

const layout = { row: 10, col: 24 };

const MarketEditType = (props: { children: any; title: any; onOk: Function; tree: any[]; data?: any; marketType: any }) => {
  const [form] = Form.useForm();
  const isEdit = !!props.data?.id;
  const tree = useMemo(() => {
    return [{ id: -1, name: "顶级节点", children: props.tree }];
  }, [props.tree]);

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      data.parentId = data.parentId || -1;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();

    if (data.parentId === -1) {
      data.parentId = undefined;
    }

    data.marketType = props.marketType;
    data.showState = Number(data.showState);

    if (data.id) {
      await MarketCategoryApi.putMarketProductType({ data });
      message.success("修改分类成功");
    } else {
      await MarketCategoryApi.addMarketProductType({ data });
      message.success("修改分类成功");
    }

    props.onOk();
  };


  return (
    <WeModal title={props.title} trigger={props.children} onOpen={handleOpen} onOk={handleSubmit} width={400}>
      <Form form={form} labelCol={{ flex: "60px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        {/* <Form.Item hidden name={`property`} initialValue={1}>
          <Input />
        </Form.Item> */}

        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              label="上级"
              name={`parentId`}
              initialValue={-1}
              rules={[{ required: true, message: "请选择上级" }]}
            >
              <TreeSelect
                placeholder="请选择上级"
                treeData={tree}
                fieldNames={{ label: "name", value: "id" }}
                allowClear
                showSearch
                treeNodeFilterProp="name"
                treeDefaultExpandedKeys={[-1]}
                disabled={isEdit}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="状态" name={`showState`} valuePropName="checked" initialValue={true}>
              <Switch checkedChildren="显示" unCheckedChildren="隐藏" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MarketEditType;
