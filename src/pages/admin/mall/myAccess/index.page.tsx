import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import AccessList from "@/components/AccessList";

const MyAccess = () => {
  const [state, setState] = useSetState({
    user: null as any,
  });

  useMount(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user });
  };

  if (!state.user) return null;

  return <AccessList type="today" preEmployeeId={state.user.id} queryType={1} />;
};

export default MyAccess;
