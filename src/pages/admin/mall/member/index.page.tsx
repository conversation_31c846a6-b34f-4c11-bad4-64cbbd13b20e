import { useMount, useSetState } from "react-use";
import * as Api from "./api";
import { Dropdown, message, Modal } from "antd";
import { CaretDownOutlined, FolderOpenOutlined, FolderOutlined, MenuOutlined, MoreOutlined, PlusCircleOutlined, SearchOutlined } from "@ant-design/icons";
import { TreeNodeEdit } from "./comp";
import clsx from "clsx";
import { useRef } from "react";
import { TableComp } from "./TableComp";
import UserApi from "@/services/UserApi";

export default function Member() {
  const tableRef = useRef<any>(null);

  const [state, setState] = useSetState({
    list: [] as any[],
    tree: [] as any[],

    openKeys: new Set<any>([]),
    selectKey: "",

    exopen: false,
    exdata: null as any,
    manager: false as boolean, // 是否管理员
  });

  useMount(() => {
    fetchUserInfo();
    fetchFilter();
  });

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ manager: (user?.property == 9 || user?.property == 99) }); // 9 管理员 99 超级管理员
  };


  const nodeSearch = (data: any) => {
    const json = data?.filterDataJson;
    tableRef.current?.onNodeSearch(json);
  };

  const fetchFilter = async (data?: any) => {
    const params = { module: 10 };
    const res = await Api.getFilter({ params });
    const list = res?.list || [];
    const tree = makeTree(list);

    const openKeys = new Set(tree.map((n: any) => n.id));
    if (data?.parentId) openKeys.add(data.parentId);

    let selectKey;

    if (data?.id && data?.type == 2) {
      selectKey = data.id;
      nodeSearch(data);
    }

    setState({ list, tree, openKeys, selectKey });
  };

  const toggleOpen = (id: string) => {
    const openKeys = new Set(state.openKeys);
    if (openKeys.has(id)) {
      openKeys.delete(id);
    } else {
      openKeys.add(id);
    }
    setState({ openKeys });
  };

  const makeTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];

    list = list || [];

    list.forEach((item) => {
      map[item.id] = item;
    });

    list.forEach((item) => {
      const parent = map[item.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    const tree = [
      { id: 1, name: "公共常用筛选", children: arr.filter((n) => n.ownType == 1) },
      { id: 2, name: "私有常用筛选", children: arr.filter((n) => n.ownType == 2) },
    ];

    return tree;
  };

  const onDelNode = (data: any) => {
    let name =
      (
        {
          1: "目录",
          2: "筛选",
        } as any
      )[data.type] || "数据";

    Modal.confirm({
      title: `确定要删除这条${name}吗？`,
      content: data.name,
      onOk: async () => {
        await Api.delFilter({ data: { ids: data.id } });
        message.success("删除成功");
        fetchFilter();
      },
    });
  };
  const onMoveNode =async (data: any, moveType: number) => {
    await Api.moveFilter({ data: { id: data.id, moveType } });
        message.success("移动成功");
        fetchFilter(data);
  };

  const onEditNode = (data: any) => {
    setState({ exopen: true, exdata: data });
  };

  const renderNode = (data: any) => {
    const ison = state.openKeys.has(data.id);

    let items: any[] = [];

    if (data.type == 1) {
      items = [
        { key: "node-add", label: <span>新建筛选</span> },
        { key: "dir-put", label: <span>编辑分组</span> },
        { key: "dir-del", label: <span className="text-red">删除分组</span> },
        { key: "dir-up", label: <span>上移</span> },
        { key: "dir-down", label: <span>下移</span> },
      ];
    } else if (data.type == 2) {
      items = [
        { key: "node-put", label: <span>编辑筛选</span> },
        { key: "node-del", label: <span className="text-red">删除筛选</span> },
        { key: "node-up", label: <span>上移</span> },
        { key: "node-down", label: <span>下移</span> },
      ];
    }

    return (
      <div key={data.id}>
        <div
          className="py-1 px-2 my-1 rounded cursor-pointer flex [&:not([data-on=true])]:hover:bg-brand/10 data-[on=true]:(bg-brand text-#fff)"
          data-on={state.selectKey == data.id}
          onClick={() => {
            toggleOpen(data.id);
            if (data.type == 2) {
              setState({ selectKey: data.id });
              nodeSearch(data);
            }
          }}
        >
          {data.type == 1 && <>{ison ? <FolderOpenOutlined /> : <FolderOutlined />}</>}
          {data.type == 2 && <SearchOutlined />}
          <div className="flex-1 px-2 flex items-center">
            <span className="line-clamp-1">{data.name}</span>
            <span className="ml-1 flex-shrink-0 text-xs text-#999 data-[hd=true]:hidden" data-hd={data.type == 2}>
              ({data.children?.length || 0})
            </span>
          </div>
          <Dropdown
            trigger={["click"]}
            placement="bottom"
            menu={{
              items,
              onClick: (e) => {
                e.domEvent.stopPropagation();

                switch (e.key) {
                  case "dir-put":
                    onEditNode(data);
                    break;
                  case "dir-del":
                    onDelNode(data);
                    break;
                  case "node-add":
                    tableRef.current?.onEditNode({ parentId: data.id, type: 2, ownType: data.ownType });
                    break;
                  case "node-put":
                    tableRef.current?.onEditNode(data);
                    break;
                  case "node-del":
                    onDelNode(data);
                    break;
                  case "dir-up":
                    onMoveNode(data,1);
                    break;
                  case "dir-down":
                    onMoveNode(data,2);
                    break;
                  case "node-up":
                    onMoveNode(data,1);
                    break;
                  case "node-down":
                    onMoveNode(data,2);
                    break;
                }
              },
            }}
          >
            {((state.manager && data.ownType == 1) || data.ownType == 2) && (
              <MoreOutlined onClick={(e) => e.stopPropagation()} />
            )}
          </Dropdown>
        </div>
        <div className="pl-4 hidden data-[on=true]:block" data-on={ison}>
          {data.children?.map(renderNode)}
        </div>
      </div>
    );
  };

  return (
    <div className="flex gap-2">
      <div className="bg-#fff rounded p-2 w-54">
        <div className="text-sm text-#333 select-none">
          <div
            className="flex cursor-pointer py-1 pl-0 pr-2 my-1"
            onClick={() => {
              setState({ selectKey: "" });
              tableRef.current?.onReset?.();
            }}
          >
            <MenuOutlined className="text-sm text-#555 mr-2" />
            <div className="fw-bold flex-1">全部顾客</div>
          </div>

          {state.tree.map((lv1) => {
            const lv1on = state.openKeys.has(lv1.id);

            return (
              <div key={lv1.id}>
                <div className="flex cursor-pointer py-1 pl-0 pr-2 my-1" onClick={() => toggleOpen(lv1.id)}>
                  <MenuOutlined className="text-sm text-#555 mr-2" />
                  <div className="fw-bold flex-1">{lv1.name}</div>

                  <Dropdown
                    trigger={["click"]}
                    placement="bottom"
                    menu={{
                      items: [
                        { key: "dir-add", label: "新建分组" },
                        { key: "node-add", label: "新建筛选" },
                      ],
                      onClick: (e) => {
                        e.domEvent.stopPropagation();

                        if (e.key == "dir-add") {
                          setState({ exopen: true, exdata: { type: 1, ownType: lv1.id } });
                        }

                        if (e.key == "node-add") {
                          tableRef.current?.onEditNode({ type: 2, ownType: lv1.id });
                        }
                      },
                    }}
                  >
                    {((state.manager && lv1.id == 1) || lv1.id == 2) && (
                      <PlusCircleOutlined className="text-sm text-#555 ml-2" onClick={(e) => e.stopPropagation()} />
                    )}
                  </Dropdown>
                  <CaretDownOutlined className={clsx("text-sm text-#555 ml-2 transition-all rotate-90", { "!rotate-0": lv1on })} />
                </div>

                <div className="pl-2 hidden data-[on=true]:block" data-on={lv1on}>
                  {lv1.children?.map(renderNode)}
                </div>
              </div>
            );
          })}
        </div>

        <div className="h-4"></div>
      </div>

      <div className="flex-1 min-w-0">
        <TableComp
          ref={tableRef}
          onSave={(data?: any) => {
            onEditNode(data);
          }}
        />
      </div>

      <TreeNodeEdit open={state.exopen} onClose={() => setState({ exopen: false })} onOk={fetchFilter} plist={state.list} data={state.exdata} manager={state.manager} />
    </div>
  );
}
