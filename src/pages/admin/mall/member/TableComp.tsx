import { <PERSON><PERSON>, <PERSON><PERSON>, Card, DatePicker, Divider, Drawer, Form, Input, Select, Space, Table, TableColumnType } from "antd";
import { useTable } from "./useTable";
import MallApi from "@/services/MallApi";
import { ClearOutlined, DownloadOutlined, PlusOutlined, SaveOutlined, SearchOutlined, SwapRightOutlined, ToolOutlined, WechatOutlined } from "@ant-design/icons";
import UserManager from "@/components/UserManager";
import { SexType } from "./types";
import { DatePresetRanges, downloadExcel, formatDate } from "@/utils/Tools";
import { useAsyncFn, useMount, useSetState } from "react-use";
import { forwardRef, useImperativeHandle } from "react";
import BirthdayRangePicker from "@/components/Units/BirthdayRangePicker";
import VipPicker from "@/components/Units/VipPicker";
import MallRfmLabelApi from "../RfmLabel/api";
import { IntentionTypePicker } from "@/components/Units/IntentionTypePicker";
import { SingleCheck } from "@/components/Units/Switch01";
import UserInfoEdit from "@/components/UserManager/UserInfoEdit";
import { RangePickerProps } from "antd/es/date-picker";
import dayjs from "dayjs";
import UserApi from "@/services/UserApi";

const StartEndGroup = (props: { name: any; prefix?: any }) => {
  return (
    <div className="flex items-center">
      <div className="flex-1">
        <Form.Item noStyle name={`start${props.name}`}>
          <Input className="box-border" placeholder="开始值" allowClear prefix={props.prefix} />
        </Form.Item>
      </div>
      <SwapRightOutlined className="text-#666 mx-2" />
      <div className="flex-1">
        <Form.Item noStyle name={`end${props.name}`}>
          <Input className="box-border" placeholder="结束值" allowClear prefix={props.prefix} />
        </Form.Item>
      </div>
      {/* <Form.Item noStyle name={`start${props.name}`}>
        <Input placeholder="开始值" allowClear prefix={props.prefix} />
      </Form.Item>
      <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
      <Form.Item noStyle name={`end${props.name}`}>
        <Input placeholder="结束值" allowClear prefix={props.prefix} />
      </Form.Item> */}
    </div>
  );
};

const DateRange = (props: RangePickerProps & { name?: any; value?: any; onChange?: any }) => {
  const form = Form.useFormInstance();
  const start = Form.useWatch("start" + props.name, form);
  const end = Form.useWatch("end" + props.name, form);
  const fdate = (n: any) => (n ? dayjs(n) : null);
  const value: any = [fdate(start), fdate(end)];

  return (
    <div>
      <Form.Item hidden noStyle name={"start" + props.name}>
        <Input />
      </Form.Item>
      <Form.Item hidden noStyle name={"end" + props.name}>
        <Input />
      </Form.Item>

      <DatePicker.RangePicker
        className="w-full"
        presets={DatePresetRanges}
        {...props}
        value={value}
        onChange={(e) => {
          const val: any = e?.map((n) => n?.format("YYYY-MM-DD"));
          form.setFieldValue("start" + props.name, val?.[0]);
          form.setFieldValue("end" + props.name, val?.[1]);
          props.onChange?.();
        }}
      />
    </div>
  );
};

export const TableComp = forwardRef((props: { onSave?: any }, ref) => {
  const [state, setState] = useSetState({
    open: false,
    ext: null as any,

    total: null as any,

    fieldkey: 1,
  });

  const [staff, fetchStaff] = useAsyncFn(async () => {
    const res = await MallApi.getShopStaffForSelect({ params: { pageSize: 9999 } });
    const opts = res?.list?.map((n: any) => ({ label: n.user?.name, value: n.user?.id })) || [];
    return opts;
  }, []);

  const [lvs, fetchLvs] = useAsyncFn(async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  const [rfm, fetchRfm] = useAsyncFn(async () => {
    const res = await MallRfmLabelApi.selectMallRfmLabel();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  const [triage, fetchTriage] = useAsyncFn(async () => {
    const params = { typeEnCode: "triageType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  const [consult, fetchConsult] = useAsyncFn(async () => {
    const params = { typeEnCode: "consultType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  const [shopVipSource, fetchShopVipSource] = useAsyncFn(async () => {
    const params = { typeEnCode: "shopVipSource", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);
  const [shopVipChannel, fetchShopVipChannel] = useAsyncFn(async () => {
    const params = { typeEnCode: "shopVipChannel", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  useMount(() => {
    fetchShopVipSource();
    fetchShopVipChannel();
    fetchStaff();
    fetchLvs();
    fetchRfm();
    fetchTriage();
    fetchConsult();
  });

  const table = useTable({
    onFetch: async (p) => {
      fetchTotal(p);
      return MallApi.getMemberList({ params: p });
    },
  });

  useImperativeHandle(ref, () => {
    return {
      onEditNode,
      onNodeSearch,
      onReset,
    };
  });

  const fetchTotal = async (p: any) => {
    const res = await MallApi.getMemberTotal({ params: p });
    setState({ total: res });
  };

  const onEditNode = async (ext?: any) => {
    setState({ open: true, ext });
    table.form.resetFields();
    table.form.setFieldsValue(ext?.filterDataJson);
  };

  const onNodeSearch = async (ext?: any) => {
    table.form.resetFields();
    table.form.setFieldsValue(ext);
    await table.onSubmit();
    setState({ open: false });
  };

  const onSearch = async () => {
    await table.onSubmit();
    setState({ open: false });
  };

  const onReset = async () => {
    await table.onReset();
    setState({ open: false });
  };

  const onSave = async () => {
    const { ...val } = await table.getParamsForSave();

    let ext = state.ext;

    if (ext == null) {
      ext = { type: 2, ownType: 1, filterDataJson: val };
    } else {
      ext = { ...ext, filterDataJson: val };
    }

    setState({ open: false });

    props.onSave?.(ext);

    // console.log("table save", ext);
  };

  const handleReload = async () => {
    table.onRefresh();
  };

  const sortOrder = (name: string): any => {
    if (table.state.sortKey == name) {
      return table.state.sortType;
    }

    return null;
  };

  const columns: TableColumnType[] = [
    {
      fixed: "left",
      title: "会员姓名",
      dataIndex: "name",
      width: 200,
      render: (c, item) => (
        <div className="flex items-center gap-1">
          <WechatOutlined className="text-([16px] #2aae67) data-[disabled=true]:text-#000/20" data-disabled={!item?.wxsRegisterTag} />
          {c ? (
            <UserManager userId={item?.id}>
              <a>{c}</a>
            </UserManager>
          ) : (
            "--"
          )}
        </div>
      ),
    },
    { title: "会员号", dataIndex: "vipCard", width: 120 },
    { title: "手机号", dataIndex: "mobile", width: 150 },
    { title: "顾客来源", dataIndex: "sourceName", width: 120, sorter: true, sortOrder: sortOrder("sourceName"), render: (c) => (c ? c : "--") },
    { title: "渠道类型", dataIndex: "channelName", width: 120, sorter: true, sortOrder: sortOrder("channelName"), render: (c) => (c ? c : "--") },
    { title: "性别", dataIndex: "sex", width: 80, render: (c) => (c ? SexType.find((n) => n.id == c)?.name : "--") },
    { title: "生日", dataIndex: "birthday", width: 120, sorter: true, sortOrder: sortOrder("birthday"), render: (c) => formatDate(c, "YYYY-MM-DD") || "--" },
    { title: "会员等级", dataIndex: "vipLevelName", width: 120 },
    {
      title: "有效期",
      dataIndex: "expireDate",
      width: 120,
      sorter: true,
      sortOrder: sortOrder("expireDate"),
      render: (c, item) => (item.foreverTag == 1 ? "永久有效" : c ? formatDate(c, "YYYY-MM-DD") : "--"),
    },
    { title: "M币", dataIndex: "coin", width: 120, sorter: true, sortOrder: sortOrder("coin") },
    { title: "可提M币", dataIndex: "cashCoin", width: 120, sorter: true, sortOrder: sortOrder("cashCoin") },
    { title: "累计提现M币", dataIndex: "totalWithdrawCoin", width: 140, sorter: true, sortOrder: sortOrder("totalWithdrawCoin") },
    { title: "最近消费", dataIndex: "lastConsumeMoney", width: 120, sorter: true, sortOrder: sortOrder("lastConsumeMoney") },
    { title: "累计消费", dataIndex: "totalConsumeMoney", width: 120, sorter: true, sortOrder: sortOrder("totalConsumeMoney") },
    { title: "首次到院", dataIndex: "firstServiceTime", width: 120, sorter: true, sortOrder: sortOrder("firstServiceTime"), render: (c) => (c ? formatDate(c, "YYYY-MM-DD") : "--") },
    { title: "最近到院", dataIndex: "lastServiceTime", width: 120, sorter: true, sortOrder: sortOrder("lastServiceTime"), render: (c) => (c ? formatDate(c, "YYYY-MM-DD") : "--") },
    { title: "所属客服", dataIndex: "adviserName", width: 120, render: (c) => (c ? c : "--") },
    { title: "所属开发", dataIndex: "developerName", width: 120, render: (c) => (c ? c : "--") },
    {
      title: "推荐人",
      dataIndex: "promotionUser",
      width: 120,
      render: (c) =>
        c ? (
          <UserManager userId={c?.id}>
            <a>{c?.name}</a>
          </UserManager>
        ) : (
          "--"
        ),
    },
    { title: "建档时间", dataIndex: "createDate", width: 160, sorter: true, render: (c) => formatDate(c) },
  ];

  return (
    <div>
      <Form form={table.form}>
        <Card size="small" className="mb-2" classNames={{ body: "p-0" }}>
          <div className="flex [&_.ant-form-item]:mb-0">
            <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={true}>
              <Form.Item label="会员信息" name={`searchKey`} tooltip="姓名查询至少输入2个字符，手机号/会员号查询至少输入4个字符">
                <Input placeholder="姓名/会员号/手机号" allowClear onChange={table.onFormChange} />
              </Form.Item>
              <Form.Item label="顾客来源" name={`sourceId`}>
                <Select options={shopVipSource.value} allowClear showSearch optionFilterProp="label" placeholder="请选择顾客来源" onChange={handleReload} />
              </Form.Item>
              <Form.Item label="渠道类型" name={`channelId`}>
                <Select options={shopVipChannel.value} allowClear showSearch optionFilterProp="label" placeholder="请选择渠道类型" onChange={handleReload} />
              </Form.Item>
              <Form.Item label="建档时间">
                <DateRange name={"CreateDate"} onChange={handleReload} />
              </Form.Item>
            </div>
            <div className="flex gap-2 pl-3">
              <Button type="default" icon={<ToolOutlined />} onClick={() => setState({ open: true, ext: null })}>
                高级搜索
              </Button>
              <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                搜索
              </Button>
              <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
            </div>
          </div>
        </Card>

        <Card
          classNames={{ body: "!p-0", title: "fw-normal" }}
          title={
            <Space>
              <UserInfoEdit title={"新增会员"} onOk={handleReload}>
                <Button type="primary" icon={<PlusOutlined />}>
                  新增会员
                </Button>
              </UserInfoEdit>
              <Button icon={<DownloadOutlined />} type="primary" onClick={() => downloadExcel(`/api/mallShopVipUser/exportExcel`, table.getParams())}>
                导出
              </Button>
            </Space>
          }
          extra={
            <>
              <div className="text-(14px #000/80)">
                <Badge key={"1"} color={"orange"} text={`累计消费：${state.total?.totalConsumeMoney ?? "--"}`} style={{ margin: "0 15px" }} />
                <Badge key={"2"} color={"green"} text={`M币：${state.total?.coin ?? "--"}`} style={{ margin: "0 15px" }} />
                <Badge key={"3"} color={"blue"} text={`可提M币：${state.total?.cashCoin ?? "--"}`} style={{ margin: "0 15px" }} />
                <Badge key={"4"} color={"purple"} text={`累计提现M币：${state.total?.totalWithdrawCoin ?? "--"}`} style={{ margin: "0 15px" }} />
              </div>
            </>
          }
        >
          <Table
            rowKey="id"
            scroll={{ x: "max-content" }}
            loading={{ spinning: table.state.loading, delay: 300 }}
            pagination={{
              className: "pr-3",
              current: table.state.page,
              pageSize: table.state.size,
              total: table.state.total,
              showSizeChanger: true,
              showTotal: (total) => <div>共 {total} 条数据</div>,
              pageSizeOptions: [5, 10, 20, 50, 100],
            }}
            onChange={table.onTableChange}
            dataSource={table.state.list}
            columns={columns}
          />
        </Card>

        <Drawer
          forceRender
          open={state.open}
          onClose={() => setState({ open: false })}
          width={"90%"}
          title="高级搜索"
          extra={
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={onSearch}>
                搜索
              </Button>
              <Button
                type="default"
                icon={<ClearOutlined />}
                onClick={() => {
                  table.form.resetFields();
                }}
              >
                重置
              </Button>
              <Button type="dashed" icon={<SaveOutlined />} onClick={onSave}>
                保存常用筛选
              </Button>
            </Space>
          }
        >
          <div className="[&_.ant-form-item-label]:min-w-100px [&_.ant-picker-range]:w-full [&_.ant-form-item-control]:flex-1">
            <Divider orientation="left">基本信息</Divider>
            <div className="grid cols-3 gap-x-2">
              <Form.Item label="会员信息" name={"searchKey"} tooltip="姓名查询至少输入2个字符，手机号/会员号查询至少输入4个字符">
                <Input placeholder="姓名/会员号/手机号" />
              </Form.Item>
              <Form.Item label="顾客来源" name={"sourceId"}>
                <Select options={shopVipSource.value} allowClear showSearch optionFilterProp="label" placeholder="请选择顾客来源" />
              </Form.Item>
              <Form.Item label="渠道类型" name={"channelId"}>
                <Select options={shopVipChannel.value} allowClear showSearch optionFilterProp="label" placeholder="请选择渠道类型" />
              </Form.Item>
              <Form.Item label="建档时间">
                <DateRange name={"CreateDate"} />
              </Form.Item>
              <Form.Item label="出生日期">
                <DateRange name={"Birthday"} />
              </Form.Item>
              <BirthdayRangePicker label="生日" name={"BirthdayWithoutYear"} />
              <Form.Item label="年龄">
                <StartEndGroup name={`Age`} />
              </Form.Item>
              <Form.Item label="性别" name={"sex"}>
                <SingleCheck options={SexType.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
              <Form.Item label="身份证号" name={"identityCard"}>
                <Input placeholder="请输入身份证号" />
              </Form.Item>
              <Form.Item label="家庭地址" name={"address"}>
                <Input placeholder="请输入家庭地址" />
              </Form.Item>
              <Form.Item label="邮箱地址" name={"email"}>
                <Input placeholder="请输入邮箱地址" />
              </Form.Item>
              <Form.Item label="会员备注" name={"content"}>
                <Input placeholder="请输入会员备注" />
              </Form.Item>
              <Form.Item label="推荐人" name={"promotionUserId"}>
                <VipPicker placeholder="会员号/姓名/手机号" />
              </Form.Item>
              <Form.Item label="所属客服" name={"adviserId"}>
                <Select options={staff.value} placeholder="请选择所属客服" allowClear />
              </Form.Item>
              <Form.Item label="所属开发" name={"developerId"}>
                <Select options={staff.value} placeholder="请选择所属开发" allowClear />
              </Form.Item>
              <Form.Item label="注册小程序" name={`wxsRegisterTag`}>
                <SingleCheck options={[{ label: "是", value: 1 }]} />
              </Form.Item>
            </div>

            <Divider orientation="left">顾客价值</Divider>
            <div className="grid cols-3 gap-x-2">
              <Form.Item label="会员等级" name={`vipLevelId`}>
                <Select options={lvs.value} placeholder="请选择会员等级" allowClear />
              </Form.Item>
              <Form.Item label="会员有效期">
                <DateRange name={"ExpireDate"} />
              </Form.Item>
              <Form.Item label="" />
              <Form.Item label="钱包余额">
                <StartEndGroup name={`Balance`} prefix="¥" />
              </Form.Item>
              <Form.Item label="累计消费">
                <StartEndGroup name={`TotalConsumeMoney`} prefix="¥" />
              </Form.Item>
              <Form.Item label="最近消费">
                <StartEndGroup name={`LastConsumeMoney`} prefix="¥" />
              </Form.Item>
              <Form.Item label="M币余额">
                <StartEndGroup name={`Coin`} />
              </Form.Item>
              <Form.Item label="可提现M币">
                <StartEndGroup name={`CashCoin`} />
              </Form.Item>
              <Form.Item label="累计提现M币">
                <StartEndGroup name={`TotalWithdrawCoin`} />
              </Form.Item>
              <Form.Item label="下单时间">
                <DateRange name={`OrderCreateDate`} />
              </Form.Item>
              <Form.Item label="订单登记时间">
                <DateRange name={`OrderEnrollDate`} />
              </Form.Item>
            </div>

            <Divider orientation="left">顾客标签</Divider>
            <div className="grid cols-3 gap-x-2">
              <Form.Item label="RFM标签" name={"rfmLabelId"}>
                <Select options={rfm.value} placeholder="请选择RFM标签" allowClear />
              </Form.Item>
              <Form.Item label="兴趣爱好" name={"interest"}>
                <Input placeholder="请输入兴趣爱好" />
              </Form.Item>
              <Form.Item label="顾客标签" name={"labels"}>
                <Input placeholder="请输入顾客标签" />
              </Form.Item>
            </div>

            <Divider orientation="left">到店咨询</Divider>

            <div className="grid cols-3 gap-x-2">
              <Form.Item label="首次到院">
                <DateRange name={`FirstServiceTime`} />
              </Form.Item>
              <Form.Item label="最近到院">
                <DateRange name={`LastServiceTime`} />
              </Form.Item>
              <Form.Item label="已到院" name={`serviceTag`}>
                <SingleCheck options={[{ label: "是", value: 1 }]} />
              </Form.Item>
              <Form.Item label="就诊时间">
                <DateRange name={`TriageTime`} />
              </Form.Item>
              <Form.Item label="就诊类型" name={`triageTypeId`}>
                <Select options={triage.value} allowClear showSearch optionFilterProp="label" placeholder="请选择就诊类型" />
              </Form.Item>
              <Form.Item label="" />
              <Form.Item label="咨询类型" name={`consultType`}>
                <Select options={consult.value} allowClear showSearch optionFilterProp="label" placeholder="请选择咨询类型" />
              </Form.Item>
              <Form.Item label="咨询意向" name={`consultIntentionType`}>
                <IntentionTypePicker />
              </Form.Item>
              <Form.Item label="咨询备注" name={`consultContent`}>
                <Input placeholder="请输入咨询备注" />
              </Form.Item>
            </div>

            <Divider orientation="left">消费治疗</Divider>
            <div className="grid cols-3 gap-x-2">
              <Form.Item label="已购买项目" name={`buyedProject`}>
                <Input placeholder="请输入已购买项目" />
              </Form.Item>
              <Form.Item label="已划扣项目" name={`servicedProject`}>
                <Input placeholder="请输入已划扣项目" />
              </Form.Item>
              <Form.Item label="" />

              <Form.Item label="可用项目" name={`preServicedProjectLessCount`}>
                <Input placeholder="请输入项目剩余次数" addonBefore={"剩余"} addonAfter={"次"} />
              </Form.Item>
              <Form.Item label="可用项目" name={`preServicedProjectLessDays`}>
                <Input placeholder="请输入项目剩余天数" addonBefore={"剩余"} addonAfter={"天"} />
              </Form.Item>
              <Form.Item label="可用项目" name={`preServicedProjectTag`}>
                <SingleCheck options={[{ label: "是", value: 1 }]} />
              </Form.Item>

              <Form.Item label="优惠券剩余" name={`preCouponLessDays`}>
                <Input placeholder="请输入优惠券剩余" addonAfter="天" />
              </Form.Item>
              <Form.Item label="可用优惠券" name={`preCouponTag`}>
                <SingleCheck options={[{ label: "是", value: 1 }]} />
              </Form.Item>
            </div>

            <Divider orientation="left">回访信息</Divider>
            <div className="grid cols-3 gap-x-2">
              <Form.Item label="应回访时间">
                <DateRange name={`PreAccessTime`} />
              </Form.Item>
              <Form.Item label="实际回访时间">
                <DateRange name={`FactAccessTime`} />
              </Form.Item>
              <Form.Item label="" />
              <Form.Item label="回访主题" name={`accessTitle`}>
                <Input placeholder="请输入回访主题" />
              </Form.Item>
              <Form.Item label="回访备注" name={`accessLog`}>
                <Input placeholder="请输入回访备注" />
              </Form.Item>
              <Form.Item label="待回访" name={`preAccessTag`}>
                <SingleCheck options={[{ label: "是", value: 1 }]} />
              </Form.Item>
            </div>
          </div>
        </Drawer>
      </Form>
    </div>
  );
});
