import { makeApi } from "@/utils/Api";

export const getFilter = makeApi("get", `/api/mallCustomFilter`);
export const getFilterInfo = makeApi("get", `/api/mallCustomFilter`, true);
export const addFilter = makeApi("post", `/api/mallCustomFilter`);
export const putFilter = makeApi("put", `/api/mallCustomFilter`);
export const delFilter = makeApi("delete", `/api/mallCustomFilter`);
export const moveFilter = makeApi("post", `/api/mallCustomFilter/move`);
