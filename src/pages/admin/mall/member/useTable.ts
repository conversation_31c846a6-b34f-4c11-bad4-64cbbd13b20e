import { downloadExcel, formatDateRange, useDebounceFn } from "@/utils/Tools";
import { Form, TablePaginationConfig } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";

export const useTable = (props: { size?: number; onFetch: (p?: any) => any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    page: 1,
    size: props.size || 20,
    total: 0,
    sortType: "",
    sortKey: "",

    params: {},

    refresh: 0,
  });

  useEffect(() => {
    fetchList();
  }, [state.refresh]);

  const fetchList = async () => {
    const params = await getParams();
    setState({ loading: true });
    const res = await props?.onFetch?.(params).finally(() => setState({ loading: false }));
    // console.log("fetch-list", res);
    setState({ list: res?.list || [], total: res?.total || 0, size: res?.pageSize });
    return res;
  };

  const getParamsForSave = async () => {
    const data = await form.validateFields();
    Object.keys(data).forEach((key) => {
      if (data[key] === undefined || data[key] === null || data[key] === "") delete data[key];
    });
    return data;
  };

  const getParams = async () => {
    const data = await form.validateFields();

    data.sortType = state.sortType;
    data.sortKey = state.sortKey;
    data.pageNum = state.page;
    data.pageSize = state.size;

    Object.keys(data)
      .filter((key) => /^[A-Z]/.test(key))
      .forEach((key) => {
        console.log(1231231231, key);
        const value = data[key] || [];
        const range = formatDateRange(value);
        data["start" + key] = range.start;
        data["end" + key] = range.end;
        delete data[key];
      });

    Object.keys(data).forEach((key) => {
      if (data[key] === undefined || data[key] === null || data[key] === "") delete data[key];
    });

    return data;
  };

  const onRefresh = () => setState({ refresh: state.refresh + 1 });

  const onFormChange = useDebounceFn(async () => {
    // console.log("onFormChange");
    onRefresh();
  }, 500);

  const onTableChange = (p: TablePaginationConfig, _: any, sort: any) => {
    console.log("table-change", p, sort);
    setState({ page: p.current, size: p.pageSize, sortKey: sort?.field, sortType: sort?.order });
    onRefresh();
  };

  const onReset = async () => {
    form.resetFields();
    setState({ page: 1, sortKey: "", sortType: "" });
    onRefresh();
  };

  const onSubmit = async () => {
    setState({ page: 1 });
    onRefresh();
  };

  const onExport = async (url: string) => {
    const params = await getParams();
    downloadExcel(url, params);
  };

  return { form, state, setState, onReset, onSubmit, onRefresh, onTableChange, onFormChange, onExport, getParams, getParamsForSave };
};
