import WeModal from "@/components/WeModal/WeModal";
import MallShopVipUpgradeConfigApi from "./api";
import { Col, Form, Input, Radio, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import { StateMap } from "./types";
import { useMount, useSetState } from "react-use";
import MallApi from "@/services/MallApi";

const layout = { row: 10, col: 12 };

const MallShopVipUpgradeConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; vipLevelId?: any; type?: any }) => {

  const [form] = Form.useForm();


  const [state, setState] = useSetState({
    vipLv: [] as any[],
  });

  useMount(() => {
    fetchVipLv();
  });

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await <PERSON><PERSON>pi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await MallShopVipUpgradeConfigApi.getMallShopVipUpgradeConfigInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.vipLevelId = props.vipLevelId;
    data.type = props.type;
    if (!data.id) {
      await MallShopVipUpgradeConfigApi.addMallShopVipUpgradeConfig({ data });
      message.success("添加规则成功");
    }
    if (data.id) {
      await MallShopVipUpgradeConfigApi.putMallShopVipUpgradeConfig({ data });
      message.success("修改规则成功");
    }
    props.onOk?.();
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          {/* <Col span={layout.col}>
            <Form.Item name={`type`} label="类型" rules={[{ required: true, message: "请选择类型" }]} initialValue={props.type} >
              <Select options={TypeMap} placeholder="请选择类型" disabled />
            </Form.Item>
          </Col> */}
          {["1", "2"].includes(props.type) && (
            <Col span={layout.col}>
              <Form.Item label="变更为"
                name={`changeToLevelId`}
                rules={[{ required: true, message: "请选择" }]}
              >
                <Select options={state.vipLv} allowClear showSearch optionFilterProp="label" placeholder="请选择会员等级" />
              </Form.Item>
            </Col>
          )}
          {["3"].includes(props.type) && (
            <Col span={layout.col}>
              <Form.Item
                name={`changeStyle`}
                label="变更方式"
                rules={[{ required: true, message: "请选择" }]}
                tooltip="累加：在现有的会员有效期上进行叠加，重置：忽略现有的会员有效期，重新计算有效期"
                initialValue={1}
              >
                <Radio.Group
                  options={[
                    { label: "累加", value: 1 },
                    { label: "重置", value: 2 },
                  ]}
                  optionType="button"
                  buttonStyle="solid"
                  defaultValue={0}
                />
              </Form.Item>
            </Col>
          )}
          <Col span={layout.col}>
            <Form.Item label="变更天数"
              name={`changeDays`}
              rules={[{ required: true, message: "请填写" }]}
            >
              <InputNumber min={0} placeholder="请输入变更天数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          {["1", "3"].includes(props.type) && (
            <>
              <Col span={layout.col}>
                <Form.Item name={`statisticsBeforeDays`} label="统计前N天" rules={[{ required: true, message: "请输入统计天数 0表示不限" }]} >
                  <InputNumber min={0} placeholder="请输入统计天数 0表示不限" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col />
              <Col span={layout.col}>
                <Form.Item name={`promotionNum`} label="要求推荐数" rules={[{ required: true, message: "请输入要求推荐数" }]} >
                  <InputNumber min={0} placeholder="请输入要求推荐数" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item name={`arriveShopNum`} label="要求推荐到店数" rules={[{ required: true, message: "请输入要求推荐到店数" }]} >
                  <InputNumber min={0} placeholder="请输入要求推荐到店数" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item name={`consumeTimes`} label="要求消费次数" rules={[{ required: true, message: "请输入要求消费次数" }]} >
                  <InputNumber min={0} placeholder="请输入要求消费次数" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
              <Col span={layout.col}>
                <Form.Item name={`consumeTotalMoney`} label="要求消费金额" rules={[{ required: true, message: "请输入要求累计消费金额" }]} >
                  <InputNumber min={0} placeholder="请输入要求累计消费金额" style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            </>
          )}

          <Col span={layout.col}>
            <Form.Item name={`sort`} label="优先级" rules={[{ required: false, message: "请输入优先级" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入优先级,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Select options={StateMap} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallShopVipUpgradeConfigEdit;
