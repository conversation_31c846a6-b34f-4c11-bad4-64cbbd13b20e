import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallShopVipUpgradeConfigApi from "./api";
import MallShopVipUpgradeConfigEdit from "./edit";
import { Card, Drawer, Popconfirm, Space, message } from "antd";
import { useMount, useSetState } from "react-use";
import { StateMap, TypeMap } from "./types";

const tabs = [
  { key: "1", tab: '升级', },
  { key: "2", tab: '降级', },
  { key: "3", tab: '延期', },
];

const MallShopVipUpgradeConfigPage = (props: { children: any; title: any, vipLevelId?: any }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
    type: "",
  });

  useMount(() => {
    setState({ type: tabs[0].key })
  });


  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallShopVipUpgradeConfigApi.delMallShopVipUpgradeConfig({ data: { ids: item.id } });
    message.success("删除规则成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >

        <Card
          tabList={tabs}
          activeTabKey={state.type}
          bodyStyle={{ display: "none" }}
          style={{ marginBottom: 10 }}
          onTabChange={(type) => setState({ type })}
        />

        <WeTable
          autoLoad={true}
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ vipLevelId: props.vipLevelId, type: state.type, }}
          request={(params) => MallShopVipUpgradeConfigApi.getMallShopVipUpgradeConfig({ params })}
          title={
            <MallShopVipUpgradeConfigEdit title={"新增规则"} onOk={handleReload} vipLevelId={props.vipLevelId} type={state.type}>
              <WeTable.AddBtn />
            </MallShopVipUpgradeConfigEdit>
          }
          columns={[
            { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((i) => i.value === c)?.label },
            { title: "变更为", dataIndex: "changeToLevelName", hide: !["1", "2"].includes(state.type), render: (c) => (c ? c : "--") },
            { title: "变更天数", dataIndex: "changeDays" },
            { title: "统计天数", dataIndex: "statisticsBeforeDays", hide: !["1", "3"].includes(state.type), render: (c) => c > 0 ? c : "--" },
            { title: "要求推荐数", dataIndex: "promotionNum", hide: !["1", "3"].includes(state.type), render: (c) => c > 0 ? c : "--" },
            { title: "要求推荐到店数", dataIndex: "arriveShopNum", hide: !["1", "3"].includes(state.type), render: (c) => c > 0 ? c : "--" },
            { title: "要求消费次数", dataIndex: "consumeTimes", hide: !["1", "3"].includes(state.type), render: (c) => c > 0 ? c : "--" },
            { title: "要求消费金额", dataIndex: "consumeTotalMoney", hide: !["1", "3"].includes(state.type), render: (c) => c > 0 ? c : "--" },
            { title: "优先级", dataIndex: "sort" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((i) => i.value === c)?.label },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallShopVipUpgradeConfigEdit title={`编辑规则`} data={item} vipLevelId={props.vipLevelId} type={state.type} onOk={handleReload}>
                      <a>编辑</a>
                    </MallShopVipUpgradeConfigEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallShopVipUpgradeConfigPage;
