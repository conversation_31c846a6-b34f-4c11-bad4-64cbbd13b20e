import { makeApi } from "@/utils/Api";

const MallShopVipUpgradeConfigApi = {
    getMallShopVipUpgradeConfig: makeApi("get", `/api/mallShopVipUpgradeConfig`),
    getMallShopVipUpgradeConfigInfo: makeApi("get", `/api/mallShopVipUpgradeConfig`, true),
    addMallShopVipUpgradeConfig: makeApi("post", `/api/mallShopVipUpgradeConfig`),
    putMallShopVipUpgradeConfig: makeApi("put", `/api/mallShopVipUpgradeConfig`),
    delMallShopVipUpgradeConfig: makeApi("delete", `/api/mallShopVipUpgradeConfig`),
};

export default MallShopVipUpgradeConfigApi;
