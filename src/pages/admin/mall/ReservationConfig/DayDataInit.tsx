import WeModal from "@/components/WeModal/WeModal";
import AppUserReservationConfigApi from "./api";
import { Checkbox, Col, DatePicker, Form, Row, message } from "antd";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import dayjs from "dayjs";

const layout = { row: 10, col: 24 };

const DayDataInit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    typeList: [] as any[],
  });

  const weeks = [
    { label: "星期一", value: 1 },
    { label: "星期二", value: 2 },
    { label: "星期三", value: 3 },
    { label: "星期四", value: 4 },
    { label: "星期五", value: 5 },
    { label: "星期六", value: 6 },
    { label: "星期日", value: 7 },
  ];

  const fetchTypeData = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = res ?? [];
    setState({ typeList: list });
  };

  useMount(() => {
    fetchTypeData();
  });

  const handleOpen = async () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    let typeIds = data?.typeIds ?? "";
    if (Array.isArray(typeIds)) {
      data.typeIds = typeIds.join(",");
    }

    let weeks = data?.weeks ?? "";
    if (Array.isArray(weeks)) {
      data.weeks = weeks.join(",");
    }

    const initDate = formatDateRange(data.initDate, "YYYY-MM-DD");
    data.startDate = initDate.start;
    data.endDate = initDate.end;
    delete data.initDate;

    await AppUserReservationConfigApi.initAppUserReservationData({ data });
    message.success("生成数据成功");
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="预约类型" name={`typeIds`} rules={[{ required: true, message: "请选择预约类型" }]} initialValue={state.typeList.map(item => item.id)}>
              <Checkbox.Group options={state.typeList.map(item => ({ label: item.name, value: item.id }))} />
            </Form.Item>
          </Col>
          <Col span={16}>
            <Form.Item label="选择日期" name={`initDate`} rules={[{ required: true, message: "请选择日期" }]}
              initialValue={
                [dayjs().startOf("day"), dayjs().add(1, "month").endOf("day")]
              }>
              <DatePicker.RangePicker
                style={{ width: "100%" }}
                presets={DatePresetRanges}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="选择星期" name={`weeks`} rules={[{ required: true, message: "请选择星期" }]} initialValue={[1,2,3,4,5,6]}>
              <Checkbox.Group options={weeks} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default DayDataInit;
