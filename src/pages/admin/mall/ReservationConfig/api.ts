import { makeApi } from "@/utils/Api";

const AppUserReservationConfigApi = {
  getAppUserReservationConfig: makeApi("get", `/api/appUserReservationConfig`),
  getAppUserReservationConfigInfo: makeApi("get", `/api/appUserReservationConfig`, true),
  addAppUserReservationConfig: makeApi("post", `/api/appUserReservationConfig`),
  putAppUserReservationConfig: makeApi("put", `/api/appUserReservationConfig`),
  delAppUserReservationConfig: makeApi("delete", `/api/appUserReservationConfig`),
  initAppUserReservatioConfig: makeApi("post", `/api/appUserReservationConfig/batch_init`),
  initAppUserReservationData: makeApi("post", `/api/appUserReservationDate/init_data`),

  save: makeApi("post", `/api/appUserReservationConfig/save_config`),
};

export default AppUserReservationConfigApi;
