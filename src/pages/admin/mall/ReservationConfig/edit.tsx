import WeModal from "@/components/WeModal/WeModal";
import AppUserReservationConfigApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";
import DictPicker from "@/components/Units/DictPicker";

const layout = { row: 10, col: 24 };

const AppUserReservationConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserReservationConfigApi.getAppUserReservationConfigInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await AppUserReservationConfigApi.addAppUserReservationConfig({ data });
      message.success("添加预约配置成功");
    }
    if (data.id) {
      await AppUserReservationConfigApi.putAppUserReservationConfig({ data });
      message.success("修改预约配置成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="预约类型" name={`typeId`} rules={[{ required: true, message: "请选择预约类型" }]}>
              <DictPicker type="reservationType" placeholder="请选择预约类型" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`preTime`} label="预约时间" rules={[{ required: false, message: "请输入预约时间" }]}>
              <Input placeholder="请输入预约时间" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`preNum`} label="投放数量" rules={[{ required: false, message: "请输入投放数量" }]} initialValue={0}>
              <InputNumber min={0} placeholder="请输入投放数量" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserReservationConfigEdit;
