import { useEffect } from "react";
import AppUserReservationConfigApi from "./api";
import { <PERSON><PERSON>, <PERSON>, DatePicker, InputNumber, Radio, Space, message } from "antd";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import DayDataInit from "./DayDataInit";
import dayjs from "dayjs";
import ConfigBatchInit from "./ConfigBatchInit";

let i = 0;

const AppUserReservationConfigPage = () => {
  const [state, setState] = useSetState({
    types: [] as any[],
    type: "",

    list: [] as any[],
  });

  useMount(() => {
    fetchTypes();
  });

  useEffect(() => {
    if (!state.type) return;
    fetchList();
  }, [state.type]);

  const fetchList = async () => {
    const res = await AppUserReservationConfigApi.getAppUserReservationConfig({
      params: { pageSize: 9999, typeId: state.type },
    });
    setState({ list: res?.list || [] });
  };

  const fetchTypes = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((n) => ({ label: n.name, value: n.id }));
    // list.unshift({ label: "全部", value: "" });
    setState({ types: list, type: list?.[0]?.value });
  };

  return (
    <div>
      <Card size="small" className="mb-4">
        <div className="flex">
          <Radio.Group
            options={state.types}
            optionType="button"
            buttonStyle="solid"
            value={state.type}
            onChange={(e) => setState({ type: e.target.value })}
          />

          <Space className="ml-a">
            <DayDataInit title={"生成每日预约数据"} onOk={fetchList}>
              <Button type="primary" icon={<PlusOutlined />}>生成每日预约数据</Button>
            </DayDataInit>
          </Space>
        </div>
      </Card>

      <div className="grid gap-1">
        {state.list.length === 0 && (
          <Card size="small" className="!text-center">
            <div className="text-gray-6">暂无预约配置数据</div>
          </Card>
        )}
        {state.list.map((item, idx) => (
          <Card size="small" classNames={{ body: "!py-2" }} key={item.id}>
            <div className="grid gap-4 cols-[200px_100px_100px]">
              <DatePicker
                picker="time"
                format={"HH:mm"}
                showNow={false}
                minuteStep={10}
                allowClear={false}
                value={dayjs(dayjs().format("YYYY-MM-DD " + state.list?.[idx].preTime + ":00"))}
                onChange={(e) => {
                  const v = e.format("HH:mm");
                  const list = state.list;
                  list[idx].preTime = v;
                  setState({ list: [...list] });
                }}
              ></DatePicker>
              <InputNumber
                addonAfter="人"
                value={state.list?.[idx].preNum}
                onChange={(e) => {
                  const v = e;
                  const list = [...state.list];
                  list[idx].preNum = v;
                  setState({ list });
                }}
              ></InputNumber>
              <DeleteOutlined
                className="ml-2 text-red-4 cursor-pointer"
                onClick={() => {
                  const list = state.list.filter((n) => n.id !== item.id);
                  setState({ list });
                }}
              />
            </div>
          </Card>
        ))}
      </div>

      <div className="p-4">
        <Space className="ml-a">
          <ConfigBatchInit title={"批量生成"} onOk={fetchList}>
            <Button type="primary" icon={<PlusOutlined />}>批量生成</Button>
          </ConfigBatchInit>
          <Button
            type="primary"
            onClick={() => {
              const list = [...state.list];
              list.push({ id: i++, preTime: "", preNum: 1 });
              setState({ list });
            }}
          >
            新增一行
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              const list = state.list;
              if (list.some((n) => !n.preTime || !n.preNum)) return message.error("有未填字段");
              await AppUserReservationConfigApi.save({ data: { typeId: state.type, timeList: list } });
              message.success("保存成功");
              fetchList();
            }}
          >
            保存配置
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default AppUserReservationConfigPage;
