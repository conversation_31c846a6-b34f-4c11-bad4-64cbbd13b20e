import WeModal from "@/components/WeModal/WeModal";
import AppUserReservationConfigApi from "./api";
import { Checkbox, Col, Form, InputNumber, Row, TimePicker, message } from "antd";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import { formatDateRange } from "@/utils/Tools";
import dayjs from "dayjs";

const layout = { row: 10, col: 12 };

const ConfigBatchInit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    typeList: [] as any[],
  });

  const fetchTypeData = async () => {
    const params = { typeEnCode: "reservationType", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    const list = res ?? [];
    setState({ typeList: list });
  };

  useMount(() => {
    fetchTypeData();
  });

  const handleOpen = async () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    let typeIds = data?.typeIds ?? "";
    if (Array.isArray(typeIds)) {
      data.typeIds = typeIds.join(",");
    }

    const initDate = formatDateRange(data.initDate, "HH:mm");
    data.preStartTime = initDate.start;
    data.preEndTime = initDate.end;
    delete data.initDate;

    await AppUserReservationConfigApi.initAppUserReservatioConfig({ data });
    message.success("生成数据成功");
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={650} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="预约类型" name={`typeIds`} rules={[{ required: true, message: "请选择预约类型" }]} initialValue={state.typeList.map(item => item.id)}>
              <Checkbox.Group options={state.typeList.map(item => ({ label: item.name, value: item.id }))} />
            </Form.Item>
          </Col>

          <Col span={layout.col * 2}>
            <Form.Item
              label="生成时间"
              name={`initDate`}
              rules={[{ required: true, message: "请输入生成时间" }]}
              initialValue={[dayjs(dayjs().format("YYYY-MM-DD 08:00:00")), dayjs(dayjs().format("YYYY-MM-DD 20:00:00"))]}
            >
              <TimePicker.RangePicker format={`HH:mm`} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="间隔" name={`preInterval`} rules={[{ required: true, message: "请输入间隔" }]} initialValue={30}>
              <InputNumber addonAfter="分钟" placeholder="请输入间隔" />
            </Form.Item>
          </Col>
          <Col />
          <Col span={layout.col}>
            <Form.Item label="投放数量" name={`preNum`} rules={[{ required: true, message: "请输入投放数量" }]} initialValue={1}>
              <InputNumber addonAfter="人" placeholder="请输入投放数量" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ConfigBatchInit;
