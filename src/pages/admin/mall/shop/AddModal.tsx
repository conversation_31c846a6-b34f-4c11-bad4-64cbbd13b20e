import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import { Cascader, Col, Divider, Form, Input, Row, Select, TimePicker, message } from "antd";
import { useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import AreaPicker from "@/components/AreaPicker";
import dayjs from "dayjs";
import MallApi from "@/services/MallApi";
import { BusinessMode, ShopBusinessState, ShopShowState, ShopStateMap } from "./type";
import { QQMapPicker } from "@/components/MapPicker/qqmap";

const layout = { row: 10, col: 8 };

const AddModal = (props: { title: any; children: any; onOk?: Function; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    hangye: [] as any[],
  });

  const handleOpenOnce = () => {
    fetchHangye();
  };

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : "");
      const s2a = (s: string) => (s || "").split(",").filter((s) => s);
      const data = { ...props.data };

      data.map = {
        lat: data.latitude,
        lng: data.longitude,
        address: data.fullAddress,
      };

      data.area = [data.provinceId, data.cityId, data.districtId];

      data.mainBusiness = s2a(data.mainBusiness);
      data.labels = s2a(data.labels);
      data.industryIds = s2a(data.industryIds);

      data.logo = ImageUpload.serializer(data.logo);
      data.coverImages = ImageUpload.serializer(data.coverImages);
      data.showImages = ImageUpload.serializer(data.showImages);
      data.qualificationsImages = ImageUpload.serializer(data.qualificationsImages);
      if (data.businessHour) {
        const businessHour_ = data.businessHour.split("-");
        data.businessHour = [fdate("2000-01-01 " + businessHour_[0] + ":00"), fdate("2000-01-01 " + businessHour_[1] + ":00")];
      } else {
        data.businessHour = "";
      }

      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();
    const ftime = (n: any) => (n ? dayjs(n).format("HH:mm") : "");

    const data = { ...val };
    data.latitude = data.map?.lat || "";
    data.longitude = data.map?.lng || "";
    data.mainBusiness = data.mainBusiness.join(",");
    data.labels = data.labels.join(",");
    data.industryIds = data.industryIds.join(",");
    data.logo = ImageUpload.deserializer(data.logo);
    data.coverImages = ImageUpload.deserializer(data.coverImages);
    data.showImages = ImageUpload.deserializer(data.showImages);
    data.qualificationsImages = ImageUpload.deserializer(data.qualificationsImages);
    data.businessHour = ftime(data.businessHour[0]) + "-" + ftime(data.businessHour[1]);

    const area = AreaPicker.deserializer(data.area);
    data.provinceId = area.provinceId;
    data.cityId = area.cityId;
    data.districtId = area.districtId;

    delete data.map;
    delete data.area;

    if (data.id) {
      await MallApi.putShop({ data });
      message.success("修改门店成功");
    } else {
      await MallApi.addShop({ data });
      message.success("新增门店成功");
    }
    props.onOk?.();
  };

  const genTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];
    list = list || [];
    list.forEach((item) => {
      map[item.id] = item;
    });
    list.forEach((item) => {
      const parent = map[item.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });
    return arr;
  };

  const fetchHangye = async () => {
    let res: any[] = await UserApi.getDictByCode({
      params: { typeEnCode: "IndustryType" },
    });
    const hangye = genTree(res);
    setState({ hangye });
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit} onOpenOnce={handleOpenOnce}>
      <Form form={form} layout="vertical">
        <Row gutter={layout.row}>
          <Col span={layout.col} hidden>
            <Form.Item name={`id`}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="门店名称" name={`name`} rules={[{ required: true, message: "请输入门店名称" }]}>
              <Input placeholder="请输入门店名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="门店简称" name={`shortName`} rules={[{ required: false, message: "请输入门店简称" }]}>
              <Input placeholder="请输入门店简称" />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="品牌名称" name={`brandName`} rules={[{ required: true, message: "请输入品牌名称" }]}>
              <Input placeholder="请输入品牌名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="行业分类" name={`industryIds`} rules={[{ required: true, message: "请选择行业分类" }]} initialValue={[]}>
              <Cascader options={state.hangye} fieldNames={{ label: "name", value: "id" }} placeholder="请选择行业分类" />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="门店主营" name={`mainBusiness`} rules={[{ required: true, message: "请输入门店主营" }]} tooltip="回车或小写逗号分隔" initialValue={[]}>
              <Select mode="tags" allowClear tokenSeparators={[","]} placeholder="请输入门店主营" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="门店标签" name={`labels`} rules={[{ required: true, message: "请输入门店标签" }]} tooltip="回车或小写逗号分隔" initialValue={[]}>
              <Select mode="tags" allowClear tokenSeparators={[","]} placeholder="请输入门店标签" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="省市区" name={`area`} rules={[{ required: true, message: "请选择省市区" }]}>
              <AreaPicker></AreaPicker>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="经纬度" name={`map`}>
              {/* <MapPicker
                onChange={(e) => {
                  console.log(e.address);
                  form.setFieldsValue({ fullAddress: e.address });
                }}
              /> */}
              <QQMapPicker
                address={true}
                onChange={(e) => {
                  // console.log(e);
                  form.setFieldsValue({ fullAddress: e.address });
                }}
              />
            </Form.Item>
          </Col>

          <Col span={layout.col * 3}>
            <Form.Item label="地址" name={`fullAddress`} rules={[{ required: true, message: "请输入地址" }]}>
              <Input placeholder="请输入地址" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="经营模式" name={`businessMode`} rules={[{ required: true, message: "请选择经营模式" }]}>
              <Select allowClear placeholder="请选择经营模式" options={BusinessMode} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="联系人" name={`contactName`} rules={[{ required: true, message: "请输入联系人" }]}>
              <Input placeholder="请输入联系人" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="联系电话" name={`contactPhone`} rules={[{ required: true, message: "请输入联系电话" }]}>
              <Input placeholder="请输入联系电话" />
            </Form.Item>
          </Col>

          <Divider orientation="left">营业信息</Divider>
          <Col span={layout.col}>
            <Form.Item label="经营状态" name={`shopState`} initialValue={10} rules={[{ required: true, message: "请选择经营状态" }]}>
              <Select options={ShopStateMap} placeholder="请选择经营状态" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="营业状态" name={`businessState`} initialValue={1} rules={[{ required: true, message: "请选择营业状态" }]}>
              <Select options={ShopBusinessState} placeholder="请选择营业状态" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="营业时间" name={`businessHour`} rules={[{ required: true, message: "请输入营业时间" }]} initialValue={[]}>
              <TimePicker.RangePicker format={`HH:mm`} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="显示状态" name={`showState`} initialValue={1} rules={[{ required: true, message: "请选择显示状态" }]}>
              <Select options={ShopShowState} placeholder="请选择显示状态" />
            </Form.Item>
          </Col>

          <Divider orientation="left">扩展信息</Divider>

          <Col span={24}>
            <Form.Item label="门店logo" name={`logo`} rules={[{ required: true, message: "请上传门店logo" }]} valuePropName="fileList">
              <ImageUpload />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="门店抬头图" name={`coverImages`} rules={[{ required: false, message: "请上传门店抬头图" }]} valuePropName="fileList">
              <ImageUpload maxCount={10} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="门店展示图" name={`showImages`} rules={[{ required: false, message: "请上传门店展示图" }]} valuePropName="fileList">
              <ImageUpload maxCount={10} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="门店资质" name={`qualificationsImages`} valuePropName="fileList">
              <ImageUpload maxCount={10} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="门店介绍" name={`content`} rules={[{ required: false, message: "请输入门店介绍" }]}>
              <Input.TextArea autoSize={{ minRows: 4 }} allowClear maxLength={200} />
            </Form.Item>
          </Col>

          {/* 
          <Col span={layout.col}>
            <Form.Item label="接单模式" name={`takeOrderMode`} rules={[{ required: true, message: "请选择接单模式" }]}>
              <Select
                allowClear
                placeholder="请选择接单模式"
                options={TakeOrderMode}
                fieldNames={{ label: "name", value: "id" }}
              />
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </WeModal>
  );
};

export default AddModal;
