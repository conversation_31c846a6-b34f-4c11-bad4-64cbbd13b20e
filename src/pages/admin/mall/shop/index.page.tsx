import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, Popconfirm, Space, Tag, Typography, message } from "antd";
import { useRef } from "react";
import AddModal from "./AddModal";
import { ShopStateMap } from "./type";

const ShopPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delShop({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        request={(params) => MallApi.getShopList({ params })}
        search={[
          <Form.Item label="门店名称" name={`name`}>
            <Input placeholder="请输入门店名称" />
          </Form.Item>,
          <Form.Item label="门店地址" name={`fullAddress`}>
            <Input placeholder="请输入门店地址" />
          </Form.Item>,
        ]}
        title={
          <AddModal title={`新增门店`} onOk={handleReload}>
            <WeTable.AddBtn />
          </AddModal>
        }
        columns={[
          { title: "门店名称", dataIndex: "name" },
          { title: "联系人", dataIndex: "contactName" },
          { title: "联系电话", dataIndex: "contactPhone" },
          { title: "门店地址", dataIndex: "fullAddress" },
          { title: "营业时间", dataIndex: "businessHour" },
          {
            title: "经营状态",
            dataIndex: "shopState",
            render: (c) => ShopStateMap.find((item) => item.value === c)?.label || "未知",
          },
          {
            title: "到店订单默认",
            dataIndex: "serviceOrderDefaultTag",
            render: (c, item) => (
              <div>
                {c === 1 ? (
                  <b>当前默认</b>
                ) : (
                  <>
                    [
                    <Typography.Link
                      onClick={async () => {
                        await MallApi.defaultServiceOrderShop({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      设为默认
                    </Typography.Link>
                    ]
                  </>
                )}
              </div>
            ),
          },
          {
            title: "配送订单默认",
            dataIndex: "deliveryOrderDefaultTag",
            render: (c, item) => (
              <div>
                {c === 1 ? (
                  <b>当前默认</b>
                ) : (
                  <>
                    [
                    <Typography.Link
                      onClick={async () => {
                        await MallApi.defaultDeliveryOrderShop({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      设为默认
                    </Typography.Link>
                    ]
                  </>
                )}
              </div>
            ),
          },
          {
            fixed: "right",
            title: "显示状态",
            dataIndex: "showState",
            render: (c) => (c === 1 ? <Tag color="success">显示</Tag> : <Tag color="error">不显示</Tag>),
          },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AddModal title={`编辑门店 - ${item.name}`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AddModal>
                  <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                  {/* <TypeList data={item}>
                    <a>项目分类管理</a>
                  </TypeList> */}
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default ShopPage;
