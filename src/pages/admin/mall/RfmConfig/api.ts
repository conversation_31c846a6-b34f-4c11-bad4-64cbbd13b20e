import { makeApi } from "@/utils/Api";

const RfmApi = {
  initConfig: makeApi("post", `/api/mallRfmConfig/init_data`),
  getConfig: makeApi("get", `/api/mallRfmConfig/query_my_config`),
  putConfig: makeApi("put", `/api/mallRfmConfig`),

  getTag: makeApi("get", `/api/mallRfmLabel/select_rfm_list`),
  putTag: makeApi("put", `/api/mallRfmLabel/batch_modify`),

  getTemp: makeApi("get", `/api/mallRfmLabelTemplate/select_list`),
};

export default RfmApi;
