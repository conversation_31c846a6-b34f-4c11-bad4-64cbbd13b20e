import { useMount, useSetState } from "react-use";
import RfmApi from "./api";
import { Button, Form, Input, InputNumber, message, Popconfirm, Radio, Space, Table, Tooltip } from "antd";
import { QuestionCircleOutlined, ReloadOutlined, SaveOutlined } from "@ant-design/icons";
import { useEffect } from "react";

export default function RfmPage() {
  const [state, setState] = useSetState({
    ready: null as any,
  });

  useMount(() => {
    fetchCfg();
  });

  const fetchCfg = async () => {
    const res = await RfmApi.getConfig();
    setState({ ready: !!res?.id });
  };

  if (state.ready === null) return null;

  return (
    <div>
      {!state.ready && <InitSet onOk={fetchCfg} />}
      {state.ready && (
        <div className="flex gap-2">
          <Configs />
          <TagTable></TagTable>
        </div>
      )}
    </div>
  );
}

const InitSet = (props: { onOk?: any }) => {
  const [state, setState] = useSetState({
    list: [] as any[],
    lv: 2,
  });

  useEffect(() => {
    fetchTemp(state.lv);
  }, [state.lv]);

  const fetchTemp = async (lv: any) => {
    const params = { rfmLevel: lv };
    const res = await RfmApi.getTemp({ params });
    setState({ list: res?.list || [] });
    // console.log("res", res);
  };

  const onOk = async () => {
    const data = { rfmLevel: state.lv };
    await RfmApi.initConfig({ data });
    message.success("初始化RFM成功~");
    props.onOk?.();
  };

  return (
    <Form layout="vertical">
      <div className="py-10">
        <div className="pb-10 text-(#111 center 2xl) fw-bold">RFM配置初始化</div>
        <div className="w-200 mx-a rounded bg-#fff p-8">
          <Form.Item label="选择层级" initialValue={2}>
            <Radio.Group
              options={[
                { label: "2层", value: 2 },
                { label: "3层", value: 3 },
                { label: "4层", value: 4 },
                { label: "5层", value: 5 },
              ]}
              value={state.lv}
              onChange={(e) => setState({ lv: e.target.value })}
            />
          </Form.Item>
          <Form.Item label="分组标签">
            <Table
              pagination={false}
              size="small"
              bordered
              dataSource={state.list}
              columns={[
                { title: "标签名称", dataIndex: "name", width: "35%" },
                { title: "营销策略", dataIndex: "strategyContent" },
              ]}
            />
          </Form.Item>

          <div className="flex justify-center">
            <Popconfirm
              title={
                <div>
                  确定使用 <b className="text-brand">{state.lv}层</b> 初始化RFM配置吗？
                </div>
              }
              onConfirm={onOk}
            >
              <Button type="primary">开始初始化</Button>
            </Popconfirm>
          </div>
        </div>
      </div>
    </Form>
  );
};

const StaffBar = (props: { value?: any[]; onChange?: any; reverse?: boolean }) => {
  const value = props.value || [];

  return (
    <div className="pos-relative py-2">
      {/* {!props.reverse ? (
        <>
          <span className="pos-absolute left-0 top-6 -mt-6px">0</span>
          <span className="pos-absolute right-0 top-6 -mt-6px">∞</span>
        </>
      ) : (
        <>
          <span className="pos-absolute left-0 top-6 -mt-6px">∞</span>
          <span className="pos-absolute right-0 top-6 -mt-6px">0</span>
        </>
      )} */}
      <div className="flex b-b-(2px solid #555)">
        {Array(value.length + 1)
          .fill(null)
          .map((_, i) => (
            <div className="pos-relative flex-1 h-6 flex items-center justify-center text-(#555)">
              {props.reverse ? <span>{value.length + 1 - i}分</span> : <span>{i + 1}分</span>}
              <i className="pos-absolute bottom-0 right-0 w-2px h-2 bg-#555" />
              {i == 0 && <i className="pos-absolute bottom-0 left-0 w-2px h-2 bg-#555" />}
            </div>
          ))}
      </div>
      <div className="flex pt-2">
        <div className="text-left text-#999" style={{ width: `calc(100% / 2 / ${value.length + 1})` }}>
          0
        </div>
        {value.map((_, i) => (
          <div className="flex-1 flex justify-center" key={i}>
            <InputNumber
              controls={false}
              className="w-16 ![&_input]:text-center"
              size="small"
              value={value[i]}
              onChange={(e) => {
                const val = [...value];
                val[i] = e;
                props.onChange?.(val);
              }}
            />
          </div>
        ))}
        <div className="text-right text-#999" style={{ width: `calc(100% / 2 / ${value.length + 1})` }}>
          ∞
        </div>
      </div>
    </div>
  );
};

const Configs = () => {
  const [form] = Form.useForm();

  useMount(() => {
    fetchConfig();
  });

  const fetchConfig = async () => {
    const res = await RfmApi.getConfig();
    form.setFieldsValue(res);
  };

  const onReload = async () => {
    await fetchConfig();
    message.success("刷新成功");
  };

  const onSave = async () => {
    const val = await form.validateFields();
    await RfmApi.putConfig({ data: val });
    fetchConfig();
    message.success("保存成功");
  };

  return (
    <Form form={form}>
      <div className="w-100 bg-#fff rounded p-2">
        <div className="fw-bold text-(lg #333) pb-2 flex items-center">
          <div className="flex-1">RFM配置</div>
          <Popconfirm title="确定刷新？" onConfirm={onReload}>
            <ReloadOutlined className="ml-2 text-#666 hover:text-#333" />
          </Popconfirm>

          <Popconfirm title="确定保存？" onConfirm={onSave}>
            <SaveOutlined className="ml-2 text-#666 hover:text-#333" />
          </Popconfirm>
        </div>
        <div className="space-y-3">
          <Form.Item hidden name={"id"}>
            <Input />
          </Form.Item>

          <div className="flex items-center justify-between">
            <div className="fw-bold">R – Recency（最近消费，单位：天）</div>
            <Tooltip title="客户最近一次购买距离今天有多少天。数值越小越好 ：表示客户最近有消费，更有可能再次购买。">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
          <Form.Item noStyle name={"recencyRuleDataList"}>
            <StaffBar reverse={true} />
          </Form.Item>

          <div className="h-4" />

          <div className="flex items-center justify-between">
            <div className="fw-bold">F – Frequency（消费频率，单位：次）</div>
            <Tooltip title="客户在一段时间内购买的次数。数值越大越好 ：表示客户经常购买，忠诚度高。">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
          <div className="flex gap-2">
            <Form.Item noStyle name={`frequencyRuleDays`}>
              <InputNumber className="w-full" addonBefore="时间范围" addonAfter="天" />
            </Form.Item>
          </div>
          <div className="">
            <Form.Item noStyle name={"frequencyRuleDataList"}>
              <StaffBar />
            </Form.Item>
          </div>

          <div className="h-4" />

          <div className="flex items-center justify-between">
            <div className="fw-bold">M – Monetary（消费金额，单位：元）</div>
            <Tooltip title="客户在一段时间内的总消费金额或平均消费金额。数值越大越好 ：表示客户贡献了更高的收入。 ">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
          <div className="flex gap-2">
            <Form.Item noStyle name={`monetaryRuleDays`}>
              <InputNumber className="w-full" addonBefore="时间范围" addonAfter="天" />
            </Form.Item>
          </div>
          <div className="">
            <Form.Item noStyle name={"monetaryRuleDataList"}>
              <StaffBar />
            </Form.Item>
          </div>
        </div>
      </div>
    </Form>
  );
};

const TagTable = () => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    edit: "",
  });

  const isEdit = (id: any) => state.edit == id;

  useMount(() => {
    fetchList();
  });

  const fetchList = async () => {
    setState({ loading: true });
    const p = { pageSize: 9999 };
    const res = await RfmApi.getTag({ params: p }).finally(() => setState({ loading: false }));
    setState({ list: res?.list || [] });
  };

  const onSave = async (item: any) => {
    const val = await form.validateFields();

    const list = state.list.map((n) => {
      if (n.id == item.id) {
        return { ...val, id: item.id };
      }
      return { id: n.id, name: n.name, strategyContent: n.strategyContent };
    });

    await RfmApi.putTag({ data: { lableList: list } });
    await fetchList();
    setState({ edit: "" });
    message.success("保存成功");
  };

  return (
    <div className="flex-1 min-w-0 bg-#fff rounded p-2">
      <div className="flex items-center items-center gap-2 pb-2">
        <div className="flex-1 fw-bold text-(lg #333) ">分组标签</div>
      </div>
      <div>
        <Form form={form}>
          <Table
            loading={state.loading}
            rowKey={"id"}
            dataSource={state.list}
            scroll={{ x: "max-content" }}
            columns={[
              {
                title: "分组名称",
                width: "20%",
                render: (c) => {
                  // if (isEdit(c.id)) {
                  //   return (
                  //     <Form.Item noStyle name={`name`}>
                  //       <Input placeholder="请输入分组名称" />
                  //     </Form.Item>
                  //   );
                  // }

                  return c?.name;
                },
              },
              {
                title: "分组说明",
                dataIndex: "nameDesc",
              },
              {
                title: "营销策略",
                render: (c) => {
                  if (isEdit(c.id)) {
                    return (
                      <Form.Item noStyle name={`strategyContent`}>
                        <Input placeholder="请输入营销策略" />
                      </Form.Item>
                    );
                  }

                  return c?.strategyContent;
                },
              },
              {
                title: "编辑",
                width: 100,
                render: (c) => {
                  return (
                    <Space>
                      {!isEdit(c.id) ? (
                        <a
                          onClick={() => {
                            setState({ edit: c.id });
                            form.resetFields();
                            form.setFieldsValue(c);
                          }}
                        >
                          编辑
                        </a>
                      ) : (
                        <>
                          {/* <a onClick={() => onSave(c)}>保存</a> */}
                          <Popconfirm title="确定要修改这条数据吗？" onConfirm={() => onSave(c)}>
                            <a>保存</a>
                          </Popconfirm>
                          <a onClick={() => setState({ edit: "" })}>取消</a>
                        </>
                      )}
                    </Space>
                  );
                },
              },
            ]}
          />
        </Form>
      </div>
    </div>
  );
};
