import WeTable, { WeTableRef } from "@/components/WeTable";
import { DatePicker, Drawer, Form, Input, message, Popconfirm, Select, Space, Typography } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import MallDoctorApi from "./api";
import dayjs from "dayjs";

const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const Comment = (props: any) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });

  const toggle = async (item: any) => {
    const data = { id: item.id, showState: Number(!item.showState) };
    await MallDoctorApi.putDocComment({ data });
    tableRef.current?.reload();
    message.success("操作成功");
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  useEffect(() => {
    if (state.open) {
      tableRef.current?.reset();
    }
  }, [state.open]);

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} width={1200} title="评论列表" onClose={() => setState({ open: false })}>
        <WeTable
          size={10}
          ref={tableRef}
          request={(p) => MallDoctorApi.getDocComment({ params: p })}
          params={props.params}
          search={[
            <Form.Item label="已读状态" name={`reviewState`}>
              <Select
                placeholder="请选择"
                options={[
                  { label: "未读", value: 1 },
                  { label: "已读", value: 2 },
                ]}
                allowClear
              />
            </Form.Item>,
            <Form.Item label="发布人" name={`appUserData`}>
              <Input placeholder="请输入" />
            </Form.Item>,
            <Form.Item label="内容" name={`content`}>
              <Input placeholder="请输入" />
            </Form.Item>,
            <Form.Item label="发布时间" name={`CreateDate`}>
              <DatePicker.RangePicker />
            </Form.Item>,
          ]}
          columns={[
            {
              title: "发布人",
              dataIndex: "appUser",
              render: (c) => (
                c ? c.name : "--"
              ),
            },
            { title: "内容", dataIndex: "content" },
            { title: "总分", dataIndex: "score" },
            {
              title: "沟通 | 技术 | 审美",
              render: (item) => (
                <div className="flex items-center">
                  <div className="mr-20px">
                    <span className="ml-5px">{item.communicateScore}</span>
                  </div>
                  <div className="mr-20px">
                    <span className="ml-5px">{item.technologyScore}</span>
                  </div>
                  <div className="mr-20px">
                    <span className="ml-5px">{item.estheticScore}</span>
                  </div>
                </div>
              ),
            },
            { title: "隐藏", dataIndex: "showState", render: (c) => (c ? "" : "已隐藏") },
            { title: "发布时间", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              title: (
                <div className="flex items-center">
                  <div>操作</div>
                  <Popconfirm
                    title="确认？"
                    onConfirm={async () => {
                      await MallDoctorApi.makeRead({ data: { reviewAll: 1 } });
                      handleReload();
                    }}
                  >
                    <a className="text-xs ml-4">全部已读</a>
                  </Popconfirm>
                </div>
              ),
              render: (item) => {
                return (
                  <Space>
                    <Typography.Link
                      disabled={item.reviewState != 1}
                      onClick={async () => {
                        await MallDoctorApi.makeRead({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      已读
                    </Typography.Link>
                    <Popconfirm title="确定要隐藏这条评论？" onConfirm={() => toggle(item)}>
                      <Typography.Link disabled={!item.showState}>隐藏</Typography.Link>
                    </Popconfirm>
                    <Popconfirm title="确定要显示这条评论？" onConfirm={() => toggle(item)}>
                      <Typography.Link disabled={!!item.showState}>显示</Typography.Link>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </>
  );
};
