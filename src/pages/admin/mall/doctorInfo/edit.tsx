import WeModal from "@/components/WeModal/WeModal";
import MallD<PERSON>tor<PERSON><PERSON> from "./api";
import { Col, Form, Input, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";
import { useSetState } from "react-use";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import RichText from "@/components/RichText";

const layout = { row: 10, col: 12 };

const MallDoctorEdit = (props: { title: any; children: any; onOk?: Function; data?: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    users: [] as any[],
  });

  const getUser = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopUserSelect({ params });
    let users: any[] = res?.list || [];
    users = users.map((n) => ({ value: n.id, label: n.organizeName + " - " + n.name }));
    setState({ users });
  };

  const handleOpen = async () => {
    getUser();
    form.resetFields();

    if (props.data) {
      const s2a = (s: string) => (s || "").split(",").filter((s) => s);

      const data = await MallDoctorApi.getMallDoctorInfo(props.data.id);
      data.photo = ImageUpload.serializer(data.photo);
      data.headPicture = ImageUpload.serializer(data.headPicture);
      data.certificateImage = ImageUpload.serializer(data.certificateImage);
      data.coverImage = ImageUpload.serializer(data.coverImage);
      data.adeptInfo = s2a(data.adeptInfo);
      data.tags = s2a(data.tags);

      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    data.photo = ImageUpload.deserializer(data.photo);
    data.headPicture = ImageUpload.deserializer(data.headPicture);
    data.certificateImage = ImageUpload.deserializer(data.certificateImage);
    data.coverImage = ImageUpload.deserializer(data.coverImage);
    data.adeptInfo = data.adeptInfo.join(",");
    data.tags = data.tags.join(",");

    if (data.id) {
      await MallDoctorApi.putMallDoctor({ data });
      message.success("修改医生成功");
    } else {
      await MallDoctorApi.addMallDoctor({ data });
      message.success("添加医生成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="医生姓名/昵称" rules={[{ required: true, message: "请输入医生姓名/昵称" }]}>
              <Input placeholder="请输入医生姓名/昵称" />
            </Form.Item>
          </Col>
          {!props.data && (
            <Col span={layout.col}>
              <Form.Item label="绑定用户" name={`sysUserId`} rules={[{ required: true, message: "请选择用户" }]}>
                <Select placeholder="请选择用户" options={state.users} showSearch allowClear />
              </Form.Item>
            </Col>
          )}
          {props.data && <Col span={layout.col} />}
          <Col span={8}>
            <Form.Item
              name={`headPicture`}
              label="个人头像"
              rules={[{ required: true, message: "请上传个人头像" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`coverImage`}
              label="封面图"
              rules={[{ required: true, message: "请上传封面图" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name={`photo`}
              label="个人照片"
              rules={[{ required: true, message: "请上传个人照片" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={`alias`} label="别名" rules={[{ required: false, message: "请输入别名" }]}>
              <Input placeholder="请输入别名Dr.XXX" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={`practiceNo`} label="职业编号" rules={[{ required: false, message: "请输入职业编号" }]}>
              <Input placeholder="请输入职业编号" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item name={`jobTitle`} label="职位" rules={[{ required: false, message: "请输入医生职位" }]}>
              <Input placeholder="请输入医生职位" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item
              label="擅长信息"
              name={`adeptInfo`}
              rules={[{ required: false, message: "请输入擅长信息" }]}
              tooltip="回车或小写逗号分隔"
              initialValue={[]}
            >
              <Select mode="tags" allowClear tokenSeparators={[","]} placeholder="请输入擅长信息" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item
              name={`personalDeclaration`}
              label="个人宣言"
              rules={[{ required: false, message: "请输入个人宣言" }]}
            >
              <Input placeholder="请输入个人宣言" />
            </Form.Item>
          </Col>
          <Col span={18}>
            <Form.Item
              label="个性标签"
              name={`tags`}
              rules={[{ required: false, message: "请输入个性标签" }]}
              tooltip="回车或小写逗号分隔"
              initialValue={[]}
            >
              <Select mode="tags" allowClear tokenSeparators={[","]} placeholder="请输入个性标签" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name={`reservationTimes`}
              label="预约次数"
              rules={[{ required: false, message: "请输入预约次数" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入预约次数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name={`score`}
              label="总评分"
              rules={[{ required: false, message: "请输入评分0~5" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入评分0~5" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name={`communicateScore`}
              label="沟通评分"
              rules={[{ required: false, message: "请输入评分0~5" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入评分0~5" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name={`technologyScore`}
              label="技术评分"
              rules={[{ required: false, message: "请输入评分0~5" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入评分0~5" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name={`estheticScore`}
              label="审美评分"
              rules={[{ required: false, message: "请输入评分0~5" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入评分0~5" style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          {/* <Col span={layout.col * 2}>
            <Form.Item name={`certificateImage`} label="资质证书" rules={[{ required: false, message: "请上传资质证书" }]} valuePropName="fileList">
              <ImageUpload maxCount={10} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`honorInfo`} label="荣誉信息" rules={[{ required: false, message: "请输入荣誉信息" }]}>
              <Input.TextArea placeholder="请输入荣誉信息" rows={4} />
            </Form.Item>
          </Col> */}
          <Col span={layout.col * 2}>
            <Form.Item
              name={`personalInformation`}
              label="个人介绍"
              rules={[{ required: false, message: "请输入个人介绍" }]}
            >
              <RichText placeholder="请输入个人介绍" />
            </Form.Item>
          </Col>
            <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber style={{ width: "100%" }} placeholder="请输入排序，范围1~999" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallDoctorEdit;
