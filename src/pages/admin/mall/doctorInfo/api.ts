import { makeApi } from "@/utils/Api";

const MallDoctorApi = {
  getMallDoctor: makeApi("get", `/api/mallDoctor`),
  getMallDoctorInfo: makeApi("get", `/api/mallDoctor`, true),
  addMallDoctor: makeApi("post", `/api/mallDoctor`),
  putMallDoctor: makeApi("put", `/api/mallDoctor`),
  delMallDoctor: makeApi("delete", `/api/mallDoctor`),

  getDocComment: makeApi("get", `/api/mallDoctorComment`),
  putDocComment: makeApi("put", `/api/mallDoctorComment`),

  getUnread: makeApi("get", `/api/mallDoctorComment/query_review_state_total`),
  makeRead: makeApi("post", `/api/mallDoctorComment/review`),
};

export default MallDoctorApi;
