import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallDoctor<PERSON><PERSON> from "./api";
import MallDoctorEdit from "./edit";
import { Badge, Button, Form, Input, Popconfirm, Space, message } from "antd";
import { Comment } from "./Comment";
import { useSetState } from "react-use";
import { MessageOutlined } from "@ant-design/icons";
const MallDoctorPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ unread: 0 });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallDoctorApi.delMallDoctor({ data: { ids: item.id } });
    message.success("删除医生成功");
    handleReload();
  };

  const getUnread = async () => {
    const res = await MallDoctorApi.getUnread();
    setState({ unread: res.state1Num });
  };

  return (
    <div className="![&_.ant-card-head-title]:overflow-visible">
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => {
          getUnread();
          return MallDoctorApi.getMallDoctor({ params: p });
        }}
        title={
          <Space>
            <MallDoctorEdit title={"新增医生"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallDoctorEdit>
            <Comment>
              <Badge dot={!!state.unread}>
                <Button type="primary" icon={<MessageOutlined />} >查看全部评论</Button>
              </Badge>
            </Comment>
          </Space>
        }
        search={[
          <Form.Item label="医生姓名/昵称" name={`name`}>
            <Input placeholder="请输入医生姓名/昵称" />
          </Form.Item>,
        ]}
        columns={[
          { title: "医生姓名/昵称", dataIndex: "name" },
          { title: "职业编号", dataIndex: "practiceNo", render: (c) => c || "--" },
          { title: "职位", dataIndex: "jobTitle", render: (c) => c || "--" },
          // {
          //   title: "擅长信息",
          //   dataIndex: "adeptInfo",
          //   width: 200,
          //   render: (c) => (
          //     <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
          //       {c}
          //     </Typography.Text>
          //   ),
          // },
          { title: "擅长信息", dataIndex: "adeptInfo" },
          { title: "总评分", dataIndex: "score" },
          {
            title: "沟通 | 技术 | 审美",
            render: (item) => (
              <div className="flex items-center">
                <div className="mr-20px">
                  <span className="ml-5px">{item.communicateScore}</span>
                </div>
                <div className="mr-20px">
                  <span className="ml-5px">{item.technologyScore}</span>
                </div>
                <div className="mr-20px">
                  <span className="ml-5px">{item.estheticScore}</span>
                </div>
              </div>
            ),
          },
          { title: "预约次数", dataIndex: "reservationTimes" },
          {
            title: "关联门店",
            render: (item) => {
              const shops = item.shopList || [];
              return <div>{shops.map((s: any) => s.shopName).join(" | ")}</div>;
            },
          },
          { title: "排序", dataIndex: "sort" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallDoctorEdit title={`编辑医生`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallDoctorEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                  <Comment data={item} params={{ doctorId: item.id }}>
                    <a>评论</a>
                  </Comment>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallDoctorPage;
