import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, InputNumber, message, Row } from "antd";
import { Api } from "./Api";
import RichText from "@/components/RichText";
import { Switch01 } from "@/components/Units/Switch01";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.collectMallOpenTag = Number(data.collectMallOpenTag);
    await Api.putRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getRule();
    data.collectMallOpenTag = !!data.collectMallOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="砍价规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`chopMallOpenTag`} label="开启砍价">
              <Switch01 />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`chopMallUnpaidCheckTime`} label="未付款倒计时" tooltip="未付款订单超过此时间，订单自动取消">
              <InputNumber placeholder="请输入未付款倒计时" addonAfter="分钟" step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`chopMallJoinDayLimitNum`} label="每日帮砍次数" tooltip="用户每日可砍的次数">
              <InputNumber placeholder="请输入每日帮砍次数" addonAfter="次" step={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`chopMallRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
