import WeTable, { WeTableRef } from "@/components/WeTable";
import { Drawer, Form, Input, InputNumber, Popconfirm, Select, Typography, } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import { Api } from "./Api";
import dayjs from "dayjs";
import { ChopState } from "./types";
import UserManager from "@/components/UserManager";
import VipPicker from "@/components/Units/VipPicker";


const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const ChopList = (props: { children: any; marketProductId?: string }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({ open: false });

  useEffect(() => {
    if (!state.open) return;
    handleReload();
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} onClose={() => setState({ open: false })} width={"80%"} title="砍价记录">
        <WeTable
          ref={tableRef}
          params={{ marketProductId: props.marketProductId }}
          request={(p) => Api.getLogs({ params: p })}
          search={[
            <Form.Item label="砍价状态" name="state">
              <Select
                options={ChopState}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择中状态"
                allowClear
              />
            </Form.Item>,
            <Form.Item label="发起人" name={`publisherId`}>
              <VipPicker placeholder="会员号/姓名/手机号" />
            </Form.Item>,
            <Form.Item label="商品名称" name="productName">
              <Input placeholder="请输入" />
            </Form.Item>,
            <Form.Item label="砍价号" name="serialNo">
              <Input placeholder="请输入砍价号" />
            </Form.Item>,
            <Form.Item label="帮砍次数" name="chopNum">
              <InputNumber className="w-full" placeholder="请输入" />
            </Form.Item>,
          ]}
          columns={[
            { title: "砍价号", dataIndex: "serialNo" },
            { title: "商品名称", dataIndex: "productName" },
            { title: "商品单价", dataIndex: "productPrice" },
            {
              title: "发起人",
              dataIndex: "publisher",
              render: (c) => (
                c ? <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
                  : "--"
              ),
            },
            { title: "可砍次数", dataIndex: "chopNum" },
            { title: "已砍次数", dataIndex: "joinNum" },
            {
              title: "砍价状态",
              dataIndex: "state",
              render: (c) => {
                const s = ChopState.find((n) => c == n.id);
                return <span style={{ color: s?.color }}>{s?.name}</span>;
              }
            },
            { title: "开始时间", dataIndex: "startDate", render: (c) => fdate(c) },
            { title: "截止时间", dataIndex: "endDate", render: (c) => fdate(c) },
            {
              title: "操作",
              render: (c) => (
                <>
                  <Popconfirm
                    title="确定要结束吗？"
                    onConfirm={async () => {
                      await Api.finish({ data: { id: c.id } });
                      handleReload();
                    }}
                  >
                    <Typography.Link disabled={![10, 20].includes(c.state)}>结束</Typography.Link>
                  </Popconfirm>
                </>
              )
            }
          ]}
        />
      </Drawer>
    </>
  );
};
