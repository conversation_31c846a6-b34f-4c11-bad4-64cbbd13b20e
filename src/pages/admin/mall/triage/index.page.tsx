import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON>pi from "@/services/MallApi";
import { Badge, DatePicker, Divider, Form, Input, Popconfirm, Select, Space, Typography, message } from "antd";
import DictPicker from "@/components/Units/DictPicker";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useRef } from "react";
import EditTriage from "./EditTriage";
import { useMount, useSetState } from "react-use";
import { SwapRightOutlined, WechatOutlined } from "@ant-design/icons";
import { SexType } from "../member/types";
import BirthdayRangePicker from "@/components/Units/BirthdayRangePicker";
import VipPicker from "@/components/Units/VipPicker";
import { IntentionTypePicker } from "@/components/Units/IntentionTypePicker";

const StartEndGroup = (props: { name: any }) => {
  return (
    <div style={{ display: "flex" }}>
      <Form.Item noStyle name={`start${props.name}`}>
        <Input placeholder="开始值" allowClear />
      </Form.Item>
      <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
      <Form.Item noStyle name={`end${props.name}`}>
        <Input placeholder="结束值" allowClear />
      </Form.Item>
    </div>
  );
};

const TriageList = (props: { userId?: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    exdata: [] as any[],
    colors: ["red", "yellow", "orange", "cyan", "green", "blue", "purple"],

    staff: [] as any[],
    vipLv: [] as any[],
  });

  useMount(() => {
    fetchStaff();
    fetchVipLv();
  });

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  const handleReload = () => {
    tableRef.current?.reload();
  };

  // const handleCancel = async (item: any) => {
  //   const data = { id: item.id };
  //   await MallApi.cancelAppUserTriage({ data });
  //   message.success("撤销分诊成功");
  //   handleReload();
  // };

  // const handleLeave = async (item: any) => {
  //   const data = { id: item.id, state: 30 };
  //   await MallApi.putAppUserTriageState({ data });
  //   message.success("设置离店成功");
  //   handleReload();
  // };

  const fetchStat = async (params: any) => {
    if (props.userId) return;
    const exdata = await MallApi.getAppUserTriageStat({ params });
    setState({ exdata });
  };

  // const handleJieZhen = async (item: any) => {
  //   const data = { id: item.id, state: 20 };
  //   await MallApi.putAppUserTriageState({ data });
  //   message.success("接待成功");
  //   handleReload();
  // };

  return (
    <WeTable
      size={props.userId ? 10 : undefined}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      title={
        <>
          <EditTriage title={`新增到院`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
            <WeTable.AddBtn>新增到院</WeTable.AddBtn>
          </EditTriage>
          <Divider type="vertical" />
          <div style={{ display: "flex" }}>
            {state.exdata?.map?.((item, idx) => (
              <Badge key={idx} color={state.colors[idx]} style={{ margin: "0 15px" }} text={`${item.typeName}: ${item.totalCount}人`} />
            ))}
          </div>
        </>
      }
      params={{ queryType: props.queryType, shopVipUserId: props.userId }}
      request={(params) => {
        fetchStat(params);
        return MallApi.getAppUserTriage({ params });
      }}
      search={[
        <Form.Item label="到院时间" name={`CreateDate`} initialValue={props.userId ? [] : [dayjs().startOf("day"), dayjs().endOf("day")]}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        !props.userId && (
          <Form.Item label="会员" name={`shopId`}>
            <VipPickerInput />
          </Form.Item>
        ),
        /*
        <Form.Item label="服务状态" name={`state`}>
          <Select
            options={StateMap.map((n) => ({ label: n.name, value: n.id }))}
            allowClear
            showSearch
            optionFilterProp="label"
            placeholder="请选择服务状态"
          />
        </Form.Item>,
        */
        <Form.Item label="成交状态" name={`transactionState`}>
          <Select
            options={[
              { label: "未成交", value: 0 },
              { label: "成交", value: 1 },
            ]}
            optionFilterProp="label"
            placeholder="请选择成交状态"
            allowClear
            showSearch
          />
        </Form.Item>,

        <Form.Item label="就诊类型" name={`triageTypeId`}>
          <DictPicker type="triageType" placeholder="请选择就诊类型" />
        </Form.Item>,

        // =======================

        <Form.Item label="顾客来源" name={`sourceIdExt`}>
          <DictPicker type="shopVipSource" allowClear placeholder="请选择顾客来源" />
        </Form.Item>,

        <Form.Item label="渠道类型" name={`channelIdExt`}>
          <DictPicker type="shopVipChannel" allowClear placeholder="请选择渠道类型" />
        </Form.Item>,


        <Form.Item label="所属客服" name={`adviserIdExt`}>
          <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属客服" />
        </Form.Item>,
        // <Form.Item label="所属医生" name={`doctorIdExt`}>
        //   <Select
        //     options={state.staff}
        //     allowClear
        //     showSearch
        //     optionFilterProp="label"
        //     placeholder="请选择所属医生"
        //   />
        // </Form.Item>,
        <Form.Item label="所属开发" name={`developerIdExt`}>
          <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属开发" />
        </Form.Item>,

        <Form.Item label="性别" name={`sexExt`}>
          <Select options={SexType.map((n) => ({ label: n.name, value: n.id }))} placeholder="请选择性别" allowClear />
        </Form.Item>,

        <BirthdayRangePicker />,

        <Form.Item label="会员等级" name={`vipLevelIdExt`}>
          <Select options={state.vipLv} allowClear showSearch optionFilterProp="label" placeholder="请选择会员等级" />
        </Form.Item>,
        <Form.Item label="会员有效期" name={`ExpireDateExt`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,

        // <Form.Item label="钱包余额">
        //   <StartEndGroup name={`BalanceExt`} />
        // </Form.Item>,
        // <Form.Item label="M币">
        //   <StartEndGroup name={`CoinExt`} />
        // </Form.Item>,
        <Form.Item label="累计消费">
          <StartEndGroup name={`TotalConsumeMoneyExt`} />
        </Form.Item>,

        <Form.Item label="首次到院" name={`FirstServiceTimeExt`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="最近到院" name={`LastServiceTimeExt`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,

        <Form.Item label="推荐人" name={`promotionUserIdExt`}>
          <VipPicker placeholder="会员号/姓名/手机号" />
        </Form.Item>,
        <Form.Item label="建档时间" name={`CreateDateExt`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="已购项目" name={`buyedProjectExt`}>
          <Input placeholder="请输入已购项目" />
        </Form.Item>,
        <Form.Item label="咨询意向" name={`intentionTypeExt`}>
          <IntentionTypePicker />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "vipUser",
          hide: props.userId,
          render: (c) => (
            <div className="flex items-center gap-1">
              <WechatOutlined className="text-([16px] #2aae67) data-[disabled=true]:invisible" data-disabled={!c?.wxsRegisterTag} />
              {c ? (
                <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
              ) : (
                "--"
              )}
            </div>
          ),
        },
        {
          title: "会员等级",
          dataIndex: "vipUser",
          hide: props.userId,
          render: (c) => c?.vipLevelName,
        },
        { title: "门店", dataIndex: "shopName", hide: !props.userId },
        { title: "所属客服", dataIndex: "adviserName", sorter: true },
        { title: "所属开发", dataIndex: "developerName", sorter: true },
        { title: "就诊类型", dataIndex: "triageTypeName" },
        { title: "预约号", dataIndex: "reservationSerialNo" },

        {
          title: "到院时间",
          dataIndex: "createDate",
          sorter: true,
          render: (c) => fdate(c),
        },
        {
          title: "到院备注",
          dataIndex: "content",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
        {
          title: "成交状态",
          dataIndex: "transactionState",
          render: (c) => (c ? "成交" : "未成交"),
        },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <Popconfirm
                title={`删除当前用户？ - ${item.vipUser?.name}`}
                onConfirm={async () => {
                  let data = { ids: item.id };
                  await MallApi.delAppUserTriage({ data });
                  message.success("删除成功");
                  handleReload();
                }}
              >
                <Typography.Link>删除</Typography.Link>
              </Popconfirm>
            </Space>
          ),
        },
        /*{ fixed: "right", title: "服务状态", dataIndex: "state", render: (c) => renderTag(StateMap, c) },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
               <Popconfirm title={`接待当前用户？ - ${item.vipUser?.name}`} onConfirm={() => handleJieZhen(item)}>
                <Typography.Link disabled={!(item.state == 10)}>接待</Typography.Link>
              </Popconfirm>

              <EditTriage title={`修改`} onOk={handleReload} data={item}>
                <Typography.Link disabled={!(item.state !== 30)}>修改</Typography.Link>
              </EditTriage>
              <OrderCreator userId={item.shopVipUserId} params={{ triageId: item.id }}>
                <Typography.Link disabled={!(item.state !== 30)}>开单</Typography.Link>
              </OrderCreator>

              <ConsultLogEdit
                title={`意向推荐`}
                data={{ id: item.consultId, shopVipUserId: item.shopVipUserId, triageId: item.id }}
                onOk={handleReload}
              >
                <Typography.Link>意向</Typography.Link>
              </ConsultLogEdit>

              <ModalEdit
                title={`新增治疗单`}
                data={{ shopVipUserId: item.shopVipUserId }}
                onOk={handleReload}
                exData={{ triageId: item.id }}
              >
                <Typography.Link disabled={!(item.state !== 30)}>治疗单</Typography.Link>
              </ModalEdit>
           
              <Popconfirm title={`确认设置离店?`} onConfirm={() => handleLeave(item)}>
                <Typography.Link disabled={![10, 20].includes(item.state)}>离店</Typography.Link>
              </Popconfirm>
              <AccessDropActions userId={item.shopVipUserId} onOk={handleReload} />
              <Popconfirm title={`撤销分诊提交后，预约信息将恢复到未到院状态。`} onConfirm={() => handleCancel(item)}>
                <Typography.Link disabled={!(item.state !== 30)}>撤销分诊</Typography.Link>
              </Popconfirm>
            </Space>
          ),
        },*/
      ]}
    />
  );
};

export default TriageList;
