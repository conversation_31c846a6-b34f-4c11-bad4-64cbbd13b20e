import VipPlusPicker from "@/components/VipPlusPicker";
import DictPicker from "@/components/Units/DictPicker";
import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { Alert, Col, Form, Input, Row, Select, message } from "antd";
import { useEffect } from "react";

import { useSetState } from "react-use";

const layout = { row: 10, col: 8 };

const Group = (props: any) => {
  return (
    <Col span={24}>
      <Row gutter={layout.row}>{props.children}</Row>
    </Col>
  );
};

const EditTriage = (props: {
  children: any;
  title: any;
  onOk: Function;
  data?: any;
}) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    user: null as any,
    isEdit: !!props.data?.id,
    staff: [] as any[],
  });

  useEffect(() => {
    form.setFieldsValue({
      adviserId: state?.user?.adviserId,
      developerId: state?.user?.developerId,
    });
  }, [state.user]);

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const handleOpen = () => {
    setState({ user: null });
    form.resetFields();
    fetchStaff();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);

      if (data.shopVipUserId) {
        fetchUser();
      }
    }
  };

  const handleSubmit = async () => {
    if (!state.user) {
      message.error("请选择用户");
      return false;
    }

    const { ...data } = await form.validateFields();

    if (data.id) {
      await MallApi.putAppUserTriage({ data });
      message.success("分诊完成");
    } else {
      data.shopVipUserId = state.user.id;
      data.shopId = state.user.shopId;

      await MallApi.addAppUserTriage({ data });
      message.success("新增到院分诊成功");
    }

    props.onOk();
  };

  return (
    <WeModal
      width={1000}
      trigger={props.children}
      title={
        <div
          style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}
        >
          <span style={{ marginRight: 10 }}>{props.title}</span>
          {!props.data?.shopVipUserId && (
            <VipPlusPicker
              onChange={(user) => setState({ user })}
              style={{ width: 400 }}
            />
          )}
        </div>
      }
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      <div style={{ marginBottom: 20 }}>
        {state.user ? (
          <UserPane user={state.user} />
        ) : (
          <Alert message="请先选择用户信息" type="warning" showIcon />
        )}
      </div>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Group>
            <Col span={layout.col}>
              <Form.Item
                label="到院类型"
                name={`triageTypeId`}
                rules={[{ required: true, message: "请选择到院类型" }]}
              >
                <DictPicker type="triageType" placeholder="请选择到院类型" />
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col}>
              <Form.Item label="所属客服" name={`adviserId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  disabled
                  optionFilterProp="label"
                  placeholder="请选择所属客服"
                />
              </Form.Item>
            </Col>
            {/* <Col span={layout.col}>
              <Form.Item label="所属医生" name={`doctorId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  disabled
                  optionFilterProp="label"
                  placeholder="请选择所属医生"
                />
              </Form.Item>
            </Col> */}
            {<Col span={layout.col}>
              <Form.Item label="所属开发" name={`developerId`}>
                <Select
                  options={state.staff}
                  allowClear
                  showSearch
                  disabled
                  optionFilterProp="label"
                  placeholder="请选择所属开发" />
              </Form.Item>
            </Col>}
          </Group>

          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="到院备注" name={`content`}>
                <Input.TextArea
                  autoSize={{ minRows: 3 }}
                  maxLength={300}
                  showCount
                  allowClear
                  placeholder="请输入到院备注"
                />
              </Form.Item>
            </Col>
          </Group>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditTriage;
