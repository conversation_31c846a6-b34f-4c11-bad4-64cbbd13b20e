import WeModal from "@/components/WeModal/WeModal";
import WeTable from "@/components/WeTable";
import MallApi from "@/services/MallApi";

export const FxLog = (props: { children: any; oid: any }) => {
  return (
    <WeModal trigger={props.children} title="推广佣金" footer={false} width={800}>
      <WeTable
        size={10}
        tableProps={{ size: "small" }}
        request={(p) => MallApi.getFxLog({ params: { ...p, keyId: props.oid } })}
        columns={[
          // { title: "日期", dataIndex: "createDate", render: (c) => formatDate(c) },
          // { title: "类型", dataIndex: "incomeExpenseType", render: (c) => (c == 1 ? "收入" : "支出") },
          // { title: "佣金", dataIndex: "money" },
          { title: "账户", dataIndex: "accountDesc" },
          { title: "分佣类型", dataIndex: "typeName" },
          { title: "所得佣金", render: (c) => c?.profitCommission + c?.profitCommissionUnit },
          {
            title: "佣金状态",
            dataIndex: "state",
            render: (c) =>
              [
                { id: 10, name: "未入账" },
                { id: 20, name: "已入账" },
              ].find((n) => n.id == c)?.name,
          },
        ]}
      />
    </WeModal>
  );
};
