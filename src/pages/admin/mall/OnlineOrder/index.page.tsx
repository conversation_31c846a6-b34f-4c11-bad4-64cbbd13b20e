import MallApi from "@/services/MallApi";
import { ClearOutlined, DownloadOutlined, DownOutlined, SearchOutlined, SwapRightOutlined, UpOutlined, WechatOutlined } from "@ant-design/icons";
import { Badge, Button, Card, DatePicker, Dropdown, Form, Input, message, Popconfirm, Radio, Select, Space, Table, TableColumnType, TablePaginationConfig, Tooltip, Typography } from "antd";
import { useMount, useSetState } from "react-use";
import DictPicker from "@/components/Units/DictPicker";
import { DatePresetRanges, downloadExcel, formatDate, formatDateRange, useDebounceFn } from "@/utils/Tools";
import { useEffect } from "react";
import { ModelState, OrderSourceSelect, OrderSourceSelectFull, OrderSourceShow, OrderState, SexType } from "./types";
import UserManager from "@/components/UserManager";
import RefundConfirm from "./RefundConfirm";
import RefundOrder from "./RefundOrder";
import { FxLog } from "./FxLog";
import ModalDetail from "./ModalDetail";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import BirthdayRangePicker from "@/components/Units/BirthdayRangePicker";
import VipPicker from "@/components/Units/VipPicker";

const useTable = (props: { size?: number; onFetch: (p?: any) => any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    page: 1,
    size: props.size || 20,
    total: 0,

    params: {},

    refresh: 0,
  });

  useEffect(() => {
    fetchList();
  }, [state.refresh]);

  const fetchList = async () => {
    const params = await getParams();
    setState({ loading: true });
    const res = await props?.onFetch?.(params).finally(() => setState({ loading: false }));
    // const res = await MallApi.getOrderListNew({ params }).finally(() => setState({ loading: false }));
    // console.log("fetch-list", res);
    setState({ list: res?.list || [], total: res?.total || 0, size: res?.pageSize });
    return res;
  };

  const getParams = async () => {
    const data = await form.validateFields();

    data.pageNum = state.page;
    data.pageSize = state.size;

    Object.keys(data)
      .filter((key) => /^[A-Z]/.test(key))
      .forEach((key) => {
        const value = data[key] || [];
        const range = formatDateRange(value);
        data["start" + key] = range.start;
        data["end" + key] = range.end;
        delete data[key];
      });

    return data;
  };

  const onRefresh = () => setState({ refresh: state.refresh + 1 });

  const onFormChange = useDebounceFn(async () => {
    // console.log("onFormChange");
    onRefresh();
  }, 500);

  const onTableChange = (p: TablePaginationConfig) => {
    // console.log("table-change", p);
    setState({ page: p.current, size: p.pageSize });
    onRefresh();
  };

  const onReset = async () => {
    form.resetFields();
    setState({ page: 1 });
    onRefresh();
  };

  const onSubmit = async () => {
    setState({ page: 1 });
    onRefresh();
  };

  const onExport = async (url: string) => {
    const params = await getParams();
    downloadExcel(url, params);
  };

  return { form, state, setState, onReset, onSubmit, onRefresh, onTableChange, onFormChange, onExport };
};

const StartEndGroup = (props: { name: any }) => {
  return (
    <div style={{ display: "flex" }}>
      <Form.Item noStyle name={`start${props.name}`}>
        <Input placeholder="开始值" allowClear />
      </Form.Item>
      <SwapRightOutlined style={{ color: "#666", margin: "0 10px" }} />
      <Form.Item noStyle name={`end${props.name}`}>
        <Input placeholder="结束值" allowClear />
      </Form.Item>
    </div>
  );
};

export default function Page(props: { userId?: any }) {
  const isInUser = !!props.userId;
  const [state, setState] = useSetState({
    shops: [] as any[],
    staff: [] as any[],
    vipLv: [] as any[],
    total: {} as any,

    fold: true,
  });

  const onFetch = async (params: any) => {
    params = { ...params, shopVipUserId: isInUser ? props.userId : params.shopVipUserId };
    const res = await MallApi.getOrderListNew({ params });
    fetchTotal(params);
    return res;
  };

  const fetchTotal = async (params: any) => {
    const res = await MallApi.getOrderTotal({ params });
    setState({ total: res });
  };

  const table = useTable({
    // size: isInUser ? 10 : 20,
    // size: 20,
    onFetch: onFetch,
  });

  const handleReload = table.onRefresh;

  useMount(() => {
    fetchShops();
    fetchStaff();
    fetchVipLv();
  });

  const fetchShops = async () => {
    const params = { pageSize: 9999, businessMode: 1 };
    const res = await MallApi.getShopListForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item?.name,
      value: item?.id,
    }));
    setState({ shops: list });
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  const handeSendOrder = async (item: any) => {
    const data = { orderId: item.id };
    await MallApi.sendOrderNew({ data });
    message.success("操作成功");
    handleReload();
  };

  const columns: TableColumnType[] = [
    {
      fixed: "left",
      title: "会员姓名",
      dataIndex: "vipUser",
      render: (c) => (
        <div className="flex items-center gap-1">
          <WechatOutlined className="text-([16px] #2aae67) data-[disabled=true]:invisible" data-disabled={!c?.wxsRegisterTag} />
          {c ? (
            <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
          ) : (
            "--"
          )}
        </div>
      ),
      hidden: isInUser,
    },
    { title: "会员建档门店", dataIndex: "initShop", render: (c) => (c ? c.name : "--") },

    {
      onHeaderCell: () => ({ className: "!p-0" }),
      onCell: () => ({ className: "!p-0" }),
      title: () => {
        return (
          <div className="grid h-38px cols-[400px_80px_80px_80px_80px]">
            <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">项目名称</div>
            <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">次数</div>
            <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">单价</div>
            <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">数量</div>
            <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">折后合计</div>
          </div>
        );
      },
      render: (c) => {
        return (
          <div>
            {c.itemList?.map((item: any) => {
              const needSub = item.productModel == 2;

              return (
                <div key={item.id} className="grid cols-[400px_80px_80px_80px_80px] b-b-(1 solid #f0f0f0) last:b-0">
                  <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">
                    <div className="flex-1">
                      {needSub ? (
                        <>
                          <Tooltip placement="topLeft" title={item?.productName + (item?.productSpecificationName && item?.productName !== item?.productSpecificationName ? " - " + item.productSpecificationName : "")}>
                            <div className="line-clamp-1">
                              {item?.productName}
                              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                                <span className="text-gray-400 ml-2px">
                                  {" - " + item.productSpecificationName}
                                </span>
                              )}
                            </div>
                          </Tooltip>
                          <div>
                            {item.projectList?.map((sub: any) => {
                              return (
                                <Tooltip placement="topLeft" title={sub?.productName + (sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName ? " - " + sub.productSpecificationName : "")}>
                                  <div key={sub.id} className="line-clamp-1 ml-1px mt-10px" title={sub.productName}>
                                    <span className="p-2 text-gray-400">-</span>
                                    {sub?.productName}
                                    {sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName && (
                                      <span className="text-gray-400 ml-2px">
                                        {" - " + sub.productSpecificationName}
                                      </span>
                                    )}
                                  </div>
                                </Tooltip>
                              );
                            })}
                          </div>
                        </>
                      ) : (
                        <>
                          <Tooltip placement="topLeft" title={item?.productName + (item?.productSpecificationName && item?.productName !== item?.productSpecificationName ? " - " + item.productSpecificationName : "")}>
                            <div className="line-clamp-1">
                              {item?.productName}
                              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                                <span className="text-gray-400 ml-2px">
                                  {" - " + item.productSpecificationName}
                                </span>
                              )}
                            </div>
                          </Tooltip>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">
                    <div className="flex-1">
                      {needSub ? (
                        <>
                          <div>&nbsp;</div>
                          {item.projectList?.length > 0 ? (
                            item.projectList.map((sub: any) => (
                              <div key={sub.id} className="flex-1 mt-10px">
                                {sub.lessNum}/{sub.includeNum}
                              </div>
                            ))
                          ) : (
                            <div>--</div>
                          )}
                        </>
                      ) : (
                        <>
                          {!!item.projectList?.[0]?.id ? (
                            <div>
                              {item.projectList[0].lessNum}/{item.projectList[0].includeNum}
                            </div>
                          ) : (
                            <div>--</div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">&yen;{item.productPrice}</div>
                  <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">{item.totalCount}</div>
                  <div className="flex items-center p-2 b-r-(1 solid #f0f0f0) last:b-0">&yen;{item.lastTotalMoney}</div>
                </div>
              );
            })}
          </div>
        );
      },
    },

    {
      title: "支付金额",
      render: (item) => {
        return (
          <div>
            <Tooltip
              title={
                <>
                  <div style={{ fontSize: 11 }}>商品合计：{item.totalMoney}</div>
                  <div style={{ fontSize: 11 }}>门诊费用：{item.totalOutpatientFee}</div>
                  <div style={{ fontSize: 11 }}>治疗费用：{item.totalServiceFee}</div>
                  <div style={{ fontSize: 11 }}>优惠券抵：-{item.couponConvertMoney}</div>
                  <div style={{ fontSize: 11 }}>会员减免：-{item.discountPreferentialMoney}</div>
                  <div style={{ fontSize: 11 }}>M币抵：-{item.integralConvertMoney}</div>
                </>
              }
            >
              <span style={{ color: "#1677FF" }}>&yen;{item.payMoney}</span>
            </Tooltip>
          </div>
        );
      },
    },

    { title: "订单号", dataIndex: "serialNo" },
    {
      title: "订单来源",
      dataIndex: "source",
      render: (c) => {
        const sourceInfo = OrderSourceShow.find((n) => n.id === c);
        return sourceInfo ? sourceInfo.name : "--";
        //return renderTag(OrderSourceShow, c);
      },
    },
    {
      title: "服务方式",
      dataIndex: "model",
      render: (c) => ModelState.find((n) => n.id == c)?.name || "--",
    },
    {
      title: "下单日期",
      dataIndex: "createDate",
      width: 180,
      render: (c) => formatDate(c, "YYYY-MM-DD HH:mm:ss") || "--",
    },
    {
      title: "订单备注",
      dataIndex: "orderRemark",
      width: 180,
      ellipsis: true,
      render: (c) =>
        c ? (
          <Typography.Text style={{ width: 180 }} ellipsis={{ tooltip: true }}>
            {c}
          </Typography.Text>
        ) : (
          "--"
        ),
    },
    {
      fixed: "right",
      title: "订单状态",
      dataIndex: "orderState",
      render: (c) => {
        const m = OrderState.find((n) => n.id == c);
        return <Badge color={m?.color} status={m?.flash ? "processing" : "default"} text={m?.name} />;
      },
    },
    {
      fixed: "right",
      title: "操作",
      render: (item) => (
        <Space>
          <ModalDetail data={item}>
            <Typography.Link>详情</Typography.Link>
          </ModalDetail>

          <Dropdown
            menu={{
              items: [
                {
                  key: "1",
                  disabled: ![70].includes(item.orderState),
                  label: (
                    <RefundConfirm title={`同意退款`} data={item} onOk={handleReload}>
                      <Typography.Link disabled={!(item.orderState == 70)}>同意退款</Typography.Link>
                    </RefundConfirm>
                  ),
                },
                {
                  key: "2",
                  disabled: !([21, 92, 93, 94, 95, 96, 97].includes(item.source) && [10, 20, 30, 40].includes(item.orderState)),
                  label: (
                    <RefundOrder title={`主动退款`} data={item} onOk={handleReload}>
                      <Typography.Link disabled={!([21, 92, 93, 94, 95, 96, 97].includes(item.source) && [10, 20, 30, 40].includes(item.orderState))}>主动退款</Typography.Link>
                    </RefundOrder>
                  ),
                },
                {
                  key: "3",
                  disabled: !([21, 92, 93, 94, 95, 96, 97].includes(item.source) && [10, 20].includes(item.orderState) && item.model == 2),
                  label: (
                    <Popconfirm title={`确定要发货吗？`} onConfirm={() => handeSendOrder(item)}>
                      <Typography.Link disabled={!([21, 92, 93, 94, 95, 96, 97].includes(item.source) && [10, 20].includes(item.orderState) && item.model == 2)}>发货</Typography.Link>
                    </Popconfirm>
                  ),
                },
                {
                  key: "4",
                  label: (
                    <FxLog oid={item.id}>
                      <a>分佣明细</a>
                    </FxLog>
                  ),
                },
              ],
            }}
          >
            <Typography.Link>更多</Typography.Link>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Form form={table.form} className="flex flex-col gap-3" labelCol={{ flex: "80px" }} onValuesChange={table.onFormChange}>
        <Card size="small" classNames={{ body: "p-0" }}>
          <div className="flex [&_.ant-form-item]:mb-0">
            <div className="flex-1 grid cols-4 gap-3 overflow-hidden data-[on=true]:h-8" data-on={state.fold}>
              {!isInUser && (
                <Form.Item label="会员" name={`shopVipUserId`}>
                  <VipPickerInput />
                </Form.Item>
              )}
              <Form.Item label="建档门店" name={`initShopId`}>
                <Select options={state.shops} allowClear showSearch optionFilterProp="label" placeholder="请选择建档门店" />
              </Form.Item>
              <Form.Item label="顾客来源" name={`sourceIdExt`}>
                <DictPicker type="shopVipSource" allowClear placeholder="请选择顾客来源" />
              </Form.Item>
              <Form.Item label="渠道类型" name={`channelIdExt`}>
                <DictPicker type="shopVipChannel" allowClear placeholder="请选择渠道类型" />
              </Form.Item>
              <Form.Item label="服务方式" name={`model`}>
                <Select options={ModelState} fieldNames={{ label: "name", value: "id" }} allowClear placeholder="请选择服务方式" />
              </Form.Item>
              <Form.Item label="订单号" name={`serialNo`}>
                <Input placeholder="请输入订单号" />
              </Form.Item>
              <Form.Item label="项目名称" name={`productName`}>
                <Input placeholder="请输入项目名称" />
              </Form.Item>
              <Form.Item label="退款时间" name={`RefundFactDate`}>
                <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
              </Form.Item>
              <Form.Item label="所属客服" name={`adviserIdExt`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属客服" />
              </Form.Item>
              <Form.Item label="所属开发" name={`developerIdExt`}>
                <Select options={state.staff} allowClear showSearch optionFilterProp="label" placeholder="请选择所属开发" />
              </Form.Item>
              <Form.Item label="性别" name={`sexExt`}>
                <Select options={SexType.map((n) => ({ label: n.name, value: n.id }))} placeholder="请选择性别" allowClear />
              </Form.Item>
              <BirthdayRangePicker />
              <Form.Item label="会员等级" name={`vipLevelIdExt`}>
                <Select options={state.vipLv} allowClear showSearch optionFilterProp="label" placeholder="请选择会员等级" />
              </Form.Item>
              <Form.Item label="会员有效期" name={`ExpireDateExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="累计消费">
                <StartEndGroup name={`TotalConsumeMoneyExt`} />
              </Form.Item>
              <Form.Item label="首次到院" name={`FirstServiceTimeExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="最近到院" name={`LastServiceTimeExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
              <Form.Item label="推荐人" name={`promotionUserIdExt`}>
                <VipPicker placeholder="会员号/姓名/手机号" />
              </Form.Item>
              <Form.Item label="建档时间" name={`CreateDateExt`}>
                <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
              </Form.Item>
            </div>
            <div className="flex gap-2 pl-3">
              <Button type="dashed" icon={state.fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setState({ fold: !state.fold })} />
              <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                搜索
              </Button>
              <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
            </div>
          </div>
        </Card>

        <Card classNames={{ body: "!p-0", title: "fw-normal" }}>
          <div className="py-3 px-3">
            <div className="flex">
              <div className="flex-1 flex flex-col gap-3">
                <div className="flex items-center gap-3">
                  <Form.Item noStyle name={`CreateDate`}>
                    <DatePicker.RangePicker presets={DatePresetRanges} />
                  </Form.Item>
                  {"今天 昨天 本月 上月 今年".split(" ").map((s) => {
                    const arr1 = DatePresetRanges.find((n) => n.label == s)?.value;
                    const arr2 = table.form.getFieldValue("CreateDate");

                    let ison = arr1?.[0]?.isSame(arr2?.[0]) && arr1?.[1]?.isSame(arr2?.[1]);

                    return (
                      <Button
                        key={s}
                        type={ison ? "primary" : "default"}
                        onClick={() => {
                          if (ison) {
                            table.form.setFieldValue("CreateDate", []);
                          } else {
                            table.form.setFieldValue("CreateDate", arr1);
                          }
                          handleReload();
                        }}
                      >
                        {s}
                      </Button>
                    );
                  })}
                </div>

                <Form.Item noStyle name={"source"} initialValue={OrderSourceSelectFull}>
                  <Radio.Group options={[{ label: "全部", value: OrderSourceSelectFull }, ...OrderSourceSelect.map((n) => ({ label: n.name, value: n.id }))]} optionType="button" />
                </Form.Item>

                <Form.Item noStyle name={"orderState"} initialValue={""}>
                  <Radio.Group options={[{ label: "全部", value: "" }, ...OrderState.map((n) => ({ label: n.name, value: n.id }))]} optionType="button" />
                </Form.Item>
              </div>
              <div className="flex flex-col">
                <div className="text-(sm #333) leading-relaxed">
                  <div>合计顾客：{state.total?.orderUserNum ?? "--"}人</div>
                  <div>合计消费：{state.total?.payMoney ?? "--"}元</div>
                </div>
              </div>
            </div>
            <div className="flex items-center pt-3">
              <Button icon={<DownloadOutlined />} type="primary" onClick={() => table.onExport("/api/mallOrderInfo/exportExcel")}>
                导出
              </Button>
              <div className="ml-a"></div>
              {/* <ColFilter /> */}
              {/* <Button icon={<AppstoreOutlined />} className="ml-a" /> */}
            </div>
          </div>

          <Table
            bordered
            rowKey="id"
            scroll={{ x: "max-content" }}
            loading={{ spinning: table.state.loading, delay: 300 }}
            pagination={{
              className: "pr-3",
              current: table.state.page,
              pageSize: table.state.size,
              total: table.state.total,
              showSizeChanger: true,
              showTotal: (total) => <div>共 {total} 条数据</div>,
              pageSizeOptions: [5, 10, 20, 50, 100],
            }}
            onChange={table.onTableChange}
            dataSource={table.state.list}
            columns={columns}
          />
        </Card>
      </Form>
    </div>
  );
}
