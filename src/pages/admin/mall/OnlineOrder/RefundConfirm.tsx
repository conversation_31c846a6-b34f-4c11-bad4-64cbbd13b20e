import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Divider, Form, Image, Input, InputNumber, Row, message, Table } from "antd";
import dayjs from "dayjs";
import { CancelTypes } from "../ShopOrder/types";

const layout = { row: 10, col: 24 };

export default function RefundConfirm(props: { children: any; title: any; data: any; onOk?: Function }) {
  const [form] = Form.useForm();
  const item = props.data;
  const imgs: any[] = (item.cancelImage || "").split(",").filter((n: any) => n);

  const handleOpen = () => {
    const initialValues: any = {
      refundApplyMoney: item.refundApplyMoney,
      cancelType: item.cancelType,
      cancelDesc: item.cancelDesc
    };

    // 初始化退款次数字段
    item.itemList?.forEach((item: any, index: number) => {
      if (item.projectList?.length > 0) {
        item.projectList.forEach((project: any, projectIndex: number) => {
          initialValues[`projectRefundNum_${index}_${projectIndex}`] = project.lessNum;
        });
      }
    });

    form.setFieldsValue(initialValues);
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.orderId = item.id;

    // 处理项目退款次数数据
    const projectRefundList: any[] = [];
    item.itemList?.forEach((item: any, index: number) => {
      if (item.projectList?.length > 0) {
        item.projectList.forEach((project: any, projectIndex: number) => {
          const refundNum = data[`projectRefundNum_${index}_${projectIndex}`];
          if (refundNum > 0) {
            projectRefundList.push({
              mallOrderProjectId: project.id,
              refundNum: refundNum
            });
          }
        });
      }
    });
    data.refundProjects = projectRefundList;

    await MallApi.orderRefundNew({ data });
    message.success("退款成功");
    props.onOk?.();
  };

  // 构建退款项目表格数据
  const getRefundItems = () => {
    const items: any[] = [];
    item.itemList?.forEach((item: any, index: number) => {
      if (item.productModel == 1 && item.projectList?.length > 0) {
        // 普通商品
        items.push({
          key: item.id,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: item.projectList?.length > 0 ? `${item.projectList[0].lessNum} / ${item.projectList[0].includeNum}` : '--',
          actual: item.projectList?.length > 0 ? (
            <Form.Item
              name={`projectRefundNum_${index}_0`}
              noStyle
              rules={[{ required: true, message: "请输入扣减次数" }]}
            >
              <InputNumber
                min={0}
                max={item.projectList?.[0]?.lessNum || 0}
                defaultValue={item.projectList?.[0]?.lessNum || 0}
                style={{ width: 150 }}
                addonAfter="次"
              />
            </Form.Item>
          ) : null,
          isParent: false
        });
      } else if (item.productModel == 2 && item.projectList?.length > 0) {
        // 组合商品
        items.push({
          key: `parent_${item.id}`,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: null,
          actual: null,
          isParent: true
        });

        item.projectList.forEach((sub: any, projectIndex: number) => {
          items.push({
            key: sub.id,
            name: (
              <div style={{ paddingLeft: 20 }}>
                <span className="p-2 text-gray-400">-</span>
                {sub?.productName}
                {sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName && (
                  <span className="text-gray-400 ml-2px">
                    {" - " + sub.productSpecificationName}
                  </span>
                )}
              </div>
            ),
            refundable: `${sub.lessNum} / ${sub.includeNum}`,
            actual: (
              <Form.Item
                name={`projectRefundNum_${index}_${projectIndex}`}
                noStyle
              >
                <InputNumber
                  min={0}
                  max={sub.lessNum}
                  defaultValue={sub.lessNum}
                  style={{ width: 150 }}
                  addonBefore="实扣"
                  addonAfter="次"
                  readOnly
                />
              </Form.Item>
            ),
            isParent: false
          });
        });
      } else {
        // 普通商品
        items.push({
          key: item.id,
          name: (
            <>
              {item?.productName}
              {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                <span className="text-gray-400 ml-2px">
                  {" - " + item.productSpecificationName}
                </span>
              )}
            </>
          ),
          refundable: "--",
          actual: "--",
          isParent: false
        });
      }
    });
    return items;
  };

  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '次数',
      dataIndex: 'refundable',
      key: 'refundable',
      width: 120,
      align: 'center' as const
    },
    {
      title: '实扣',
      dataIndex: 'actual',
      key: 'actual',
      width: 200,
      align: 'center' as const
    }
  ];

  return (
    <WeModal
      title={props.title}
      trigger={props.children}
      width={1000}
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={[layout.row, layout.row]}>
          <Col span={layout.col}>
            <Form.Item label="实付金额">
              <Input value={item.payMoney + "元"} bordered={false} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              label="退款金额"
              name="refundApplyMoney"
              rules={[{ required: true, message: "请输入退款金额" }]}
            >
              <InputNumber min={0} max={item.payMoney} addonAfter="元" />
            </Form.Item>
          </Col>

          {/* 添加退款项目 */}
          <Col span={24}>
            <Form.Item label="退款项目" required>
              <Table
                size="small"
                dataSource={getRefundItems()}
                columns={columns}
                pagination={false}
                showHeader={true}
                bordered
              />
            </Form.Item>
          </Col>

          <Divider orientation="left">退款原因</Divider>
          <Col span={layout.col}>
            <Form.Item
              label="申请时间"
            >
              <Input value={item.refundApplyDate ? dayjs(item.refundApplyDate).format("YYYY-MM-DD HH:mm:ss") : ""} bordered={false} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="退款类型">
              <Input value={CancelTypes.find(type => type.value === item.cancelType)?.label} bordered={false} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="退款原因" name="cancelDesc" initialValue={item.cancelDesc}>
              <Input.TextArea
                allowClear
                autoSize={{ minRows: 2 }}
                readOnly
                bordered={false}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="退款图片">
              <div>
                <Image.PreviewGroup>
                  {imgs.map((url) => (
                    <Image src={url} key={url} width={80} height={80} />
                  ))}
                </Image.PreviewGroup>
              </div>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
}