import { AppstoreOutlined } from "@ant-design/icons";
import { Button, Checkbox, Popover } from "antd";

export const ColFilter = () => {
  return (
    <>
      {/* <Select
        open
        className="w-40"
        options={[
          { label: "hello", value: "1" },
          { label: "world", value: "2" },
        ]}
      /> */}
      <Popover
        open
        arrow={false}
        placement="bottomRight"
        // classNames={{ body: "!p-0" }}
        content={
          <>
            <div>
              {/* <Input placeholder="关键字..." /> */}
              <div className="py-3 flex flex-col">
                {[1, 2, 3, 4, 5, 6].map((n) => (
                  <div className="flex items-center h-8 rounded hover:bg-#f5f5f5 px-2 data-[on=true]:bg-" data-on={n == 2}>
                    <Checkbox>我是某一个选项</Checkbox>
                  </div>
                ))}
              </div>
              <div className="flex gap-3">
                <Button className="flex-1">还原</Button>
                <Button className="flex-1" type="primary">
                  确定
                </Button>
              </div>
            </div>
          </>
        }
      >
        <Button icon={<AppstoreOutlined />} />
      </Popover>
    </>
  );
};
