import VipPlusPicker from "@/components/VipPlusPicker";
import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { <PERSON>ert, Badge, Button, Col, Descriptions, Form, Input, Row, Spin, message } from "antd";
import { useSetState } from "react-use";

const layout = { row: 10, col: 24 };

const Group = (props: any) => {
  return (
    <Col span={24}>
      <Row gutter={layout.row}>{props.children}</Row>
    </Col>
  );
};

const StateMap = [
  { id: 10, name: "异常", color: "error" },
  { id: 20, name: "可使用", color: "success" },
  { id: 30, name: "已使用", color: "warning" },
  { id: 99, name: "异常", color: "error" },
];

const ForUse = (props: { children: any; title: any; onOk: Function }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    info: null as any,
    loading: false,
    user: null as any,
  });

  const fetchDetail = async () => {
    const cdKey = form.getFieldValue("cardKey");
    if (!cdKey) return message.error("请输入兑换码");
    setState({ loading: true });
    const params = { cdKey };
    const res = await MallApi.getCardNoDetail({ params }).finally(() => setState({ loading: false }));
    setState({ info: res });
  };

  const handleOpen = () => {
    form.resetFields();
    setState({ loading: false, info: null, user: null });
  };

  const handleSubmit = async () => {
    if (!state.user) {
      message.error("请先选择会员");
      return false;
    }

    const data = await form.validateFields();
    data.useShopVipUserId = state.user.id;
    await MallApi.useCardNo({ data });
    message.success("使用成功");
    props.onOk();
  };

  return (
    <WeModal
      trigger={props.children}
      title={
        <div style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}>
          <span style={{ marginRight: 10 }}>{props.title}</span>
          <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} />
        </div>
      }
      width={800}
      okText="确定兑换"
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "80px" }}>
        <div style={{ marginBottom: 15 }}>
          {state.user ? <UserPane user={state.user} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}
        </div>

        <Row gutter={layout.row}>
          <Group>
            <Col span={layout.col}>
              <Form.Item label="兑换码" required>
                <div style={{ display: "flex" }}>
                  <Form.Item noStyle name={`cardKey`} rules={[{ required: true, message: "请输入兑换码" }]}>
                    <Input placeholder="请输入兑换码" />
                  </Form.Item>
                  <Button style={{ marginLeft: 10 }} onClick={() => fetchDetail()}>
                    查询
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col}>
              <div style={{ marginLeft: 80 }}>
                <Form.Item>
                  <Spin spinning={state.loading}>
                    <Descriptions column={1} size="small" bordered labelStyle={{ width: 80 }}>
                      <Descriptions.Item label="卡券名">{state.info?.cardTypeName}</Descriptions.Item>
                      <Descriptions.Item label="卡券号">{state.info?.cardNo}</Descriptions.Item>
                      <Descriptions.Item label="价值">{state.info?.cardValue}</Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Badge
                          status={StateMap.find((n) => n.id == state.info?.state)?.color as any}
                          text={StateMap.find((n) => n.id == state.info?.state)?.name}
                        />
                      </Descriptions.Item>
                    </Descriptions>
                  </Spin>
                </Form.Item>
              </div>
            </Col>
          </Group>

          <Col span={layout.col}>
            <Form.Item label="兑换备注" name={`useContent`}>
              <Input.TextArea autoSize={{ minRows: 1, maxRows: 2 }} placeholder="请输入兑换备注" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ForUse;
