import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallApi from "@/services/MallApi";
import { Button, DatePicker, Form, Input, Typography } from "antd";
import ForUse from "./ForUse";
import { useRef } from "react";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { TransactionOutlined } from "@ant-design/icons";

const PrizeCardExchange = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <WeTable
      ref={tableRef}
      request={(params) => MallApi.getCardNoSelf({ params })}
      title={
        <ForUse title={`兑换`} onOk={handleReload}>
          <Button type="primary" icon={<TransactionOutlined />}>兑换</Button>
        </ForUse>
      }
      search={[
        <Form.Item label="卡券号" name={`cardNo`}>
          <Input placeholder="请输入卡券号" />
        </Form.Item>,
        <Form.Item label="兑换人" name={`useShopVipUserId`}>
          <VipPickerInput />
        </Form.Item>,
        <Form.Item label="兑换时间" name={`UseTime`}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
      ]}
      columns={[
        { title: "卡券名", dataIndex: "cardTypeName" },
        { title: "卡券号", dataIndex: "cardNo" },
        { title: "价值", dataIndex: "cardValue" },
        { title: "兑换人", dataIndex: "vipUser", render: (c) => !!c ? c?.name + " / " + c?.mobile : "--" },
        { title: "兑换时间", dataIndex: "useTime", render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "") },
        {
          title: "兑换备注",
          dataIndex: "useContent",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
      ]}
    />
  );
};

export default PrizeCardExchange;
