import WeModal from "@/components/WeModal/WeModal";
import AppRecruitmentLogApi from "./api";
import { Col, Form, Input, Row, } from "antd";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 24 };

const AppRecruitmentLogEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppRecruitmentLogApi.getAppRecruitmentLogInfo(props.data.id);
      data.submitterImage = ImageUpload.serializer(data.submitterImage);
      form.setFieldsValue(data);
    }
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} >
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`submitterName`} label="姓名" rules={[{ required: false, message: "请输入姓名" }]}>
              <Input placeholder="请输入姓名" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`submitterMobile`} label="电话" rules={[{ required: false, message: "请输入电话" }]}>
              <Input placeholder="请输入电话" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`submitterAge`} label="年龄" rules={[{ required: false, message: "请输入年龄" }]}>
              <Input placeholder="请输入年龄" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`submitterImage`} label="图片" rules={[{ required: false, message: "请上传图片" }]} valuePropName="fileList">
              <ImageUpload maxCount={3} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`submitterContent`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input.TextArea placeholder="请输入说明" rows={4} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppRecruitmentLogEdit;
