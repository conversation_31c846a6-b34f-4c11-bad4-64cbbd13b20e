import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import AppRecruitmentLogApi from "./api";
import AppRecruitmentLogEdit from "./edit";
import { Form, Input, Space, Drawer, Select, Image } from "antd";
import { Typography } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useSetState } from "react-use";
import AppRecruitmentInfoApi from "../RecruitmentInfo/api";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppRecruitmentLogPage = (props: { children: any; title: any }) => {

  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    open: false,
    recruitments: [] as any[],
  });

  useEffect(() => {
    if (state.open) {
      fatchRecruitments();
      handleReload();
    }
  }, [state.open]);


  const fatchRecruitments = async () => {
    const res = await AppRecruitmentInfoApi.getAppRecruitmentInfo();
    const recruitments = (res?.list || []).map((item: any) => ({
      label: item.title,
      value: item.id,
    }));

    setState({ recruitments });
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };


  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{}}//默认参数
          request={(p) => AppRecruitmentLogApi.getAppRecruitmentLog({ params: p })}
          search={[
            <Form.Item label="招募" name={`recruitmentId`}>
              <Select options={state.recruitments} placeholder="请选择招募信息" allowClear />
            </Form.Item>,
            <Form.Item label="姓名" name={`submitterName`}>
              <Input placeholder="请输入姓名" />
            </Form.Item>,
            <Form.Item label="电话" name={`submitterMobile`}>
              <Input placeholder="请输入电话" />
            </Form.Item>,
            <Form.Item label="报名日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "招募", dataIndex: "recruitmentInfo", render: (c) => c ? c.title : "--" },
            { title: "姓名", dataIndex: "submitterName", render: (c) => c ? c : "--" },
            { title: "电话", dataIndex: "submitterMobile", render: (c) => c ? c : "--" },
            { title: "年龄", dataIndex: "submitterAge", render: (c) => c ? c : "--" },
            {
              title: "图片",
              dataIndex: "submitterImage",
              width: 200,
              render: (c) => {
                if (!c) return "--";
                const imageUrls = c.split(",");
                return (
                  <div style={{ display: "flex", flexWrap: "wrap", gap: 8 }}>
                    {imageUrls.map((url: any) => (
                      <Image src={url.trim()} style={{ width: "50px", height: "50px", objectFit: "cover" }} />
                    ))}
                  </div>
                );
              },
            },
            {
              title: "说明",
              dataIndex: "submitterContent",
              width: 200,
              render: (c) => (
                <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
                  {c ? c : "--"}
                </Typography.Text>
              ),
            },
            { title: "报名日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <AppRecruitmentLogEdit title={`查看招募报名记录`} data={item} hideSubmit={true}>
                      <a>查看</a>
                    </AppRecruitmentLogEdit>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default AppRecruitmentLogPage;
