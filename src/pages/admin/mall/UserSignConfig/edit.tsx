import WeModal from "@/components/WeModal/WeModal";
import AppUserSignConfigApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";

const layout = { row: 10, col: 24 };

const AppUserSignConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any }) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserSignConfigApi.getAppUserSignConfigInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      await AppUserSignConfigApi.addAppUserSignConfig({ data });
      message.success("添加签到配置成功");
    }
    if (data.id) {
      await AppUserSignConfigApi.putAppUserSignConfig({ data });
      message.success("修改签到配置成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={400}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              name={`signNo`}
              label="签到序号"
              rules={[{ required: false, message: "请输入签到序号 1,2,3..." }]}
            >
              <InputNumber min={1} placeholder="请输入签到序号" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`coin`}
              label="奖励M币"
              rules={[{ required: false, message: "请输入奖励M币" }]}
              initialValue={0}
            >
              <InputNumber min={0} placeholder="请输入奖励M币" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserSignConfigEdit;
