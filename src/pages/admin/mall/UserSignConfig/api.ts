import { makeApi } from "@/utils/Api";

const AppUserSignConfigApi = {
    getAppUserSignConfig: makeApi("get", `/api/appUserSignConfig`),
    getAppUserSignConfigInfo: makeApi("get", `/api/appUserSignConfig`, true),
    addAppUserSignConfig: makeApi("post", `/api/appUserSignConfig`),
    putAppUserSignConfig: makeApi("put", `/api/appUserSignConfig`),
    delAppUserSignConfig: makeApi("delete", `/api/appUserSignConfig`),
};

export default AppUserSignConfigApi;
