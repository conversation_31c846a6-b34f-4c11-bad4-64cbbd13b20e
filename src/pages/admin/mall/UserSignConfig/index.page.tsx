import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useRef } from "react";
import AppUserSignConfigApi from "./api";
import AppUserSignConfigEdit from "./edit";
import { Drawer, Popconfirm, Space, message } from "antd";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppUserSignConfigPage = (props: { children: any; onClose?: Function }) => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
    exdata: {} as any,
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserSignConfigApi.delAppUserSignConfig({ data: { ids: item.id } });
    message.success("删除签到配置成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, {
        onClick: () => setState({ open: true }),
      })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={"签到配置"}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{}} //默认参数
          request={(p) => AppUserSignConfigApi.getAppUserSignConfig({ params: p })}
          title={
            <>
              <AppUserSignConfigEdit title={"新增签到配置"} onOk={handleReload}>
                <WeTable.AddBtn />
              </AppUserSignConfigEdit>
            </>
          }
          columns={[
            { title: "签到序号", dataIndex: "signNo" },
            { title: "奖励M币", dataIndex: "coin" },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <AppUserSignConfigEdit title={`编辑签到配置`} data={item} onOk={handleReload}>
                      <a>编辑</a>
                    </AppUserSignConfigEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default AppUserSignConfigPage;
