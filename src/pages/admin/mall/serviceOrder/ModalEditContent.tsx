import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Button, Col, Form, Input, Modal, Radio, Row, message } from "antd";
import TextTemplatePage from "../Texttemplate/index.page";

const layout = { row: 10, col: 12 };

const ModalEditContent = (props: { children: any; title: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    await MallApi.putServiceOrderModifyContent({ data });
    message.success("操作成功");
    props.onOk();
  };

  const templateSelect = async (content: any) => {
    const currentContent = form.getFieldValue('serviceContent');
    if (currentContent) {
      // 如果当前内容不为空，提示选择覆盖或追加
      Modal.confirm({
        title: '请选择操作方式',
        content: (
          <div>
            <p>当前治疗备注已有内容，请选择操作方式：</p>
            <Radio.Group defaultValue="append" id="operationType">
              <Radio value="append">追加</Radio>
              <Radio value="override">覆盖</Radio>
            </Radio.Group>
          </div>
        ),
        onOk: () => {
          const selectedRadio = document.getElementById('operationType')?.querySelector('input:checked');
          const operationType = (selectedRadio instanceof HTMLInputElement) ? selectedRadio.value : 'append';
          if (operationType === 'override') {
            // 覆盖模式
            form.setFieldsValue({ serviceContent: content });
          } else {
            // 追加模式，添加换行符后再追加内容
            form.setFieldsValue({ serviceContent: `${currentContent}\n${content}` });
          }
        }
      });
    } else {
      // 如果当前内容为空，直接使用模板内容
      form.setFieldsValue({ serviceContent: content });
    }
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={900} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="治疗备注" name={`serviceContent`}>
              <Input.TextArea
                placeholder="请输入治疗备注信息"
                autoSize={{ minRows: 10 }}
                maxLength={1000}
                showCount
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <div style={{ textAlign: 'left', marginTop: '-20px', marginLeft: '65px', marginBottom: '20px' }}>
              <TextTemplatePage title={`选择备注模板`} module={10} onSelect={templateSelect}>
                <Button type="link">选择备注模板</Button>
              </TextTemplatePage>
            </div>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalEditContent;
