import MatterPicker from "@/components/MatterPicker";
import VipPlusPicker from "@/components/VipPlusPicker";
import AsyncPicker from "@/components/Units/AsyncPicker";
import { Switch01 } from "@/components/Units/Switch01";
import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import MallApi from "@/services/MallApi";
import { PlusOutlined } from "@ant-design/icons";
import { Alert, Button, Col, Divider, Form, Input, InputNumber, Row, Table, message, Modal } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";
import { ProjectPicker } from "./ProjectPicker";
import UserInfoEdit from "@/components/UserManager/UserInfoEdit";

const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={10}>{props.children}</Row>
  </Col>
);

const ModalEdit = (props: { children: any; title: any; onOk: Function; data?: any; exData?: any }) => {
  const isEdit = props.data?.id;
  const goodsListName = "goodsList";
  const [form] = Form.useForm();
  const itemList: any[] = Form.useWatch("itemList", form);

  const [state, setState] = useSetState({
    user: null as any,
    showModal: false, // 添加控制modal显示的状态
    userPaneKey: 0, // 添加用于强制刷新UserPane的key
  });

  useEffect(() => {
    if (!isEdit) {
      form.resetFields();
      form.setFieldsValue({ nurseId: state?.user?.developerId });
    }
    if (!isEdit && state.user?.id) {
      if (!state.user?.adviserId) {
        setState({ showModal: true }); // 显示普通modal
      }
    }
  }, [state.user?.id, isEdit]);

  useEffect(() => {
    if (isEdit) return;

    getMatterByProject(itemList?.map((n) => n.id).join(","));
  }, [itemList?.map((n) => n.id).join(","), isEdit]);

  // 第一条项目id变化
  useEffect(() => {
    if (isEdit) return;

    const first = itemList?.[0];
    if (first) {
      form.setFieldsValue({
        nurseId: first?.serviceUserId ?? state?.user?.developerId,
      });
      updateAccess(first?.productId);
    } else {
      form.setFieldsValue({ nurseId: state?.user?.developerId });
      form.setFieldsValue({ accessTemplateId: "" });
    }
  }, [itemList?.[0]?.id, isEdit]);

  const handleOpen = () => {
    form.resetFields();
    form.setFieldValue(goodsListName, []);
    setState({ user: null, userPaneKey: state.userPaneKey + 1 });

    if (props.data?.shopVipUserId) {
      fetchUser();
    }

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }

    if (props.data?.id) {
      getMatterByService(props.data?.id);
    }
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const reloadUser = async () => {
    const res = await MallApi.getMember(state.user?.id);
    setState({ user: res, userPaneKey: state.userPaneKey + 1 }); // 更新用户信息后增加key值强制刷新
  };

  const getMatterByProject = async (mallOrderProjectId: any) => {
    if (!mallOrderProjectId) return form.setFieldValue(goodsListName, []);
    const params = { mallOrderProjectId, pageSize: 9999 };
    const res = await MallApi.getOrderListMatterByProject({ params });
    form.setFieldValue(goodsListName, res?.list || []);
  };

  const getMatterByService = async (serviceOrderId: any) => {
    const params = { pageSize: 9999, serviceOrderId };
    const res = await MallApi.getServiceOrderMatterByService({ params });
    form.setFieldValue(goodsListName, res?.list || []);
  };

  const handleSubmit = async () => {
    if (!state.user?.id) {
      message.error("请先选择用户");
      return false;
    }

    let data = await form.validateFields();
    data = { ...data, ...props.exData };
    data.shopVipUserId = state.user?.id;

    if (!data.itemList?.length) {
      message.error("请选择项目");
      return false;
    }

    if (data.id) {
      await MallApi.putServiceOrder({ data });
      message.success("修改治疗单成功");
    } else {
      await MallApi.addServiceOrderV2({ data });
      message.success("添加治疗单成功");
    }

    props.onOk();
  };

  const updateAccess = async (productId: any) => {
    form.setFieldsValue({ accessTemplateId: "" });
    const res = await MallApi.getAccessTempDef({ params: { productId } });
    form.setFieldsValue({ accessTemplateId: res?.templateId });
  };

  return (
    <WeModal
      trigger={props.children}
      width={1000}
      title={
        <div style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}>
          <span style={{ marginRight: 10 }}>{props.title}</span>
          {!props.data?.shopVipUserId && <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} />}
        </div>
      }
      onOk={handleSubmit}
      onOpen={handleOpen}
    >

      {!!state.user ? <UserPane user={state.user} key={state.userPaneKey} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}

      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={10}>
          <Form.Item hidden name={`id`}>
            <Input />
          </Form.Item>
          <Divider orientation="left">项目信息</Divider>

          <Col span={24}>
            <Form.List name={"itemList"} initialValue={[]}>
              {(fields, actions) => {
                return (
                  <div>
                    <Table
                      size="small"
                      locale={{ emptyText: "暂无数据" }}
                      pagination={false}
                      dataSource={fields}
                      columns={[
                        {
                          title: "项目名称",
                          render: (item) => {
                            const productName = form.getFieldValue(["itemList", item.name, "productName"]);
                            const productSpecificationName = form.getFieldValue(["itemList", item.name, "productSpecificationName"]);
                            return <div className="line-clamp-1">
                              {productName}
                              {productSpecificationName && productName !== productSpecificationName && (
                                <span className="text-gray-400 ml-2px">
                                  {" - " + productSpecificationName}
                                </span>
                              )}
                            </div>;
                          },
                        },
                        {
                          hidden: isEdit,
                          title: "总次数",
                          width: 150,
                          render: (c) => {
                            return <div>{form.getFieldValue(["itemList", c.name, "includeNum"])}</div>;
                          },
                        },
                        {
                          hidden: isEdit,
                          title: "剩余次数",
                          width: 150,
                          render: (c) => {
                            return <div>{form.getFieldValue(["itemList", c.name, "lessNum"])}</div>;
                          },
                        },
                        {
                          hidden: isEdit,
                          title: "划扣次数",
                          width: 160,
                          render: (c) => {
                            const less = form.getFieldValue(["itemList", c.name, "lessNum"]);
                            return (
                              <Form.Item noStyle name={[c.name, "useNum"]}>
                                <InputNumber className="w-full" placeholder="请输入" min={1} max={less} />
                              </Form.Item>
                            );
                          },
                        },
                        {
                          hidden: !isEdit,
                          title: "划扣次数",
                          width: 160,
                          render: (c) => {
                            return form.getFieldValue(["itemList", c.name, "serviceNum"]);
                          },
                        },
                        {
                          hidden: isEdit,
                          title: "操作",
                          width: 80,
                          render: (c) => {
                            return <a onClick={() => actions.remove(c.name)}>删除</a>;
                          },
                        },
                      ]}
                    />

                    <ProjectPicker
                      ext={{
                        shopVipUserId: state.user?.id,
                      }}
                      onOk={(e: any[]) => {
                        const arr1: any[] = form.getFieldValue("itemList");
                        const arr2: any[] = [...arr1, ...e];
                        const arr3 = arr2
                          .filter((n, i) => arr2.findIndex((m) => m.id == n.id) == i)
                          .map((n) => ({ ...n, useNum: n.useNum || 1, orderProjectId: n.id }));
                        form.setFieldValue("itemList", arr3);
                      }}
                    >
                      <Button
                        className="mt-10px"
                        type="dashed"
                        icon={<PlusOutlined />}
                        block
                        disabled={!state.user?.id}
                        hidden={isEdit}
                      >
                        添加项目
                      </Button>
                    </ProjectPicker>
                    <div className="mb-10px"></div>
                  </div>
                );
              }}
            </Form.List>
          </Col>

          <Divider orientation="left">执行人</Divider>
          <Col span={8}>
            <Form.Item label="医生" name={`doctorId`}>
              <AsyncPicker
                fetch={async () => {
                  const res = await MallApi.getShopStaffForSelect({
                    params: { pageSize: 9999, positionCode: "Doctor" },
                  });
                  let list: any[] = res?.list || [];
                  list = list.map((item) => ({ label: item.user?.name, value: item.user?.id }));
                  return list;
                }}
                placeholder="请选择医生"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="治疗师" name={`nurseId`}>
              <AsyncPicker
                fetch={async () => {
                  const res = await MallApi.getShopStaffForSelect({
                    params: { pageSize: 9999, positionCode: "Therapist" },
                  });
                  let list: any[] = res?.list || [];
                  list = list.map((item) => ({ label: item.user?.name, value: item.user?.id }));
                  return list;
                }}
                placeholder="请选择治疗师"
              />
            </Form.Item>
          </Col>
          <Col span={16}>
            <Form.Item label="备注" name={`orderContent`}>
              <Input.TextArea autoSize={{ minRows: 3 }} maxLength={300} showCount allowClear placeholder="请输入备注" />
            </Form.Item>
          </Col>

          <Divider orientation="left">回访设置</Divider>
          <Col span={24}>
            <Form.Item label="回访模板">
              <div className="flex items-center">
                <div className="mr-20px">
                  <Form.Item noStyle name={`initAccessTag`} initialValue={1}>
                    <Switch01 />
                  </Form.Item>
                </div>
                <Form.Item noStyle dependencies={["initAccessTag"]}>
                  {(form) => {
                    const value = form.getFieldValue("initAccessTag");
                    if (!value) return null;

                    return (
                      <div className="w-400px">
                        <Form.Item
                          noStyle
                          name={`accessTemplateId`}
                          rules={[{ required: true, message: "请选择模板" }]}
                        >
                          <AsyncPicker
                            fetch={async () => {
                              const res = await MallApi.getAccessTempList();
                              const list: any[] = res?.list || [];
                              return list.map((n) => ({ label: n.name, value: n.id }));
                            }}
                            placeholder="请选择模板"
                            allowClear
                          />
                        </Form.Item>
                      </div>
                    );
                  }}
                </Form.Item>
              </div>
            </Form.Item>
          </Col>

          <Divider orientation="left">关联物资</Divider>

          <Group>
            <Col span={24}>
              <Form.List name={goodsListName} initialValue={[]}>
                {(fields, action) => {
                  return (
                    <>
                      <Table
                        size="small"
                        locale={{ emptyText: "暂无数据" }}
                        pagination={false}
                        dataSource={fields}
                        columns={[
                          {
                            title: "编号",
                            render: (c) => {
                              const name = form.getFieldValue([goodsListName, c.name, "serialNo"]);
                              return name;
                            },
                          },
                          {
                            title: "名称",
                            render: (c) => {
                              const name = form.getFieldValue([goodsListName, c.name, "goodsName"]);
                              return name;
                            },
                          },
                          {
                            title: "规格",
                            render: (c) => {
                              const name = form.getFieldValue([goodsListName, c.name, "specs"]);
                              return name || "--";
                            },
                          },
                          {
                            title: "数量",
                            render: (c) => {
                              const unit = form.getFieldValue([goodsListName, c.name, "unit"]);
                              return (
                                <Form.Item style={{ marginBottom: 0 }} name={[c.name, "num"]} initialValue={1}>
                                  <InputNumber min={1} addonAfter={unit} />
                                </Form.Item>
                              );
                            },
                          },
                          {
                            title: "操作",
                            render: (c) => {
                              return <a onClick={() => action.remove(c.name)}>删除</a>;
                            },
                          },
                        ]}
                      />

                      <div style={{ height: 20 }}></div>
                      <MatterPicker
                        onSelect={(rows) => {
                          const list: any[] = form.getFieldValue(goodsListName);
                          rows.forEach((item) => {
                            if (list.every((n) => n.goodsId !== item.id))
                              list.push({
                                specs: item.specs,
                                serialNo: item.serialNo,
                                goodsId: item.id,
                                goodsName: item.name,
                                num: 1,
                                unit: item.unit,
                              });
                          });

                          form.setFieldValue(goodsListName, list);
                        }}
                      >
                        <Button type="dashed" block icon={<PlusOutlined />}>
                          添加物资
                        </Button>
                      </MatterPicker>
                    </>
                  );
                }}
              </Form.List>
            </Col>
          </Group>
          <Alert
            style={{ marginTop: 20 }}
            message="最终划扣库存以此处提交的物资数量为准！物资可进行增减、更换。若项目需要划扣多次，请自行调整划扣物资的总数量。"
            type="error"
          />
        </Row>
      </Form>


      {/* 普通提示modal */}
      <Modal
        title="温馨提示"
        width={350}
        open={state.showModal}
        onOk={() => setState({ showModal: false })}
        onCancel={() => setState({ showModal: false })}
        cancelText="我知道了"
        okText={
          <UserInfoEdit
            title={`编辑用户信息 `}
            data={state.user}
            onOk={reloadUser}
          >
            <a>
              点击完善
            </a>
          </UserInfoEdit>
        }
        closable={false} // 隐藏右上角关闭按钮
      >
        <p>该用户信息不完整，请完善后再进行操作！</p>
      </Modal>

    </WeModal>
  );
};

export default ModalEdit;