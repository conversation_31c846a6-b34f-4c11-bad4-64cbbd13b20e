import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON>pi from "@/services/MallApi";
import { Button, Col, Divider, Form, Input, Modal, Radio, Row, Select, Table, message } from "antd";
import { useSetState } from "react-use";
import TextTemplatePage from "../Texttemplate/index.page";

const layout = { row: 10, col: 12 };

const ModalStart = (props: { children: any; title: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    staff: [] as any[],
    user: null as any,
  });

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const handleOpen = () => {
    form.resetFields();
    fetchStaff();
    fetchUser();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const fetchUser = async () => {
    const res = await MallApi.getMember(props.data?.shopVipUserId);
    setState({ user: res });
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    await MallApi.putServiceOrderStart({ data });
    message.success("操作成功");
    props.onOk();
  };

  const templateSelect = async (content: any) => {
    const currentContent = form.getFieldValue('serviceContent');
    if (currentContent) {
      // 如果当前内容不为空，提示选择覆盖或追加
      Modal.confirm({
        title: '请选择操作方式',
        content: (
          <div>
            <p>当前治疗备注已有内容，请选择操作方式：</p>
            <Radio.Group defaultValue="append" id="operationType">
              <Radio value="append">追加</Radio>
              <Radio value="override">覆盖</Radio>
            </Radio.Group>
          </div>
        ),
        onOk: () => {
          const selectedRadio = document.getElementById('operationType')?.querySelector('input:checked');
          const operationType = (selectedRadio instanceof HTMLInputElement) ? selectedRadio.value : 'append';
          if (operationType === 'override') {
            // 覆盖模式
            form.setFieldsValue({ serviceContent: content });
          } else {
            // 追加模式，添加换行符后再追加内容
            form.setFieldsValue({ serviceContent: `${currentContent}\n${content}` });
          }
        }
      });
    } else {
      // 如果当前内容为空，直接使用模板内容
      form.setFieldsValue({ serviceContent: content });
    }
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={900} onOpen={handleOpen} onOk={handleSubmit}>
      {!!state.user && <UserPane user={state.user} />}

      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Divider orientation="left">项目信息</Divider>
          <Col span={24}>
            <Form.List name={"itemList"} initialValue={[]}>
              {(fields) => {
                return (
                  <div>
                    <Table
                      size="small"
                      locale={{ emptyText: "暂无数据" }}
                      pagination={false}
                      dataSource={fields}
                      columns={[
                        {
                          title: "项目名称",
                          render: (item) => {
                            const productName = form.getFieldValue(["itemList", item.name, "productName"]);
                            const productSpecificationName = form.getFieldValue(["itemList", item.name, "productSpecificationName"]);
                            return <div className="line-clamp-1">
                              {productName}
                              {productSpecificationName && productName !== productSpecificationName && (
                                <span className="text-gray-400 ml-2px">
                                  {" - " + productSpecificationName}
                                </span>
                              )}
                            </div>;
                          },
                        },
                        {
                          title: "划扣次数",
                          width: 160,
                          render: (c) => {
                            return form.getFieldValue(["itemList", c.name, "serviceNum"]);
                          },
                        },
                      ]}
                    />

                    <div className="mb-10px"></div>
                  </div>
                );
              }}
            </Form.List>
          </Col>

          <Divider orientation="left">治疗信息</Divider>

          <Col span={layout.col}>
            <Form.Item label="所属医生" name={`doctorId`} rules={[{ required: true, message: "请选择所属医生" }]}>
              <Select
                placeholder="请选择所属医生"
                options={state.staff}
                optionFilterProp="label"
                allowClear
                showSearch
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="所属治疗师" name={`nurseId`}>
              <Select placeholder="请选择治疗师" options={state.staff} optionFilterProp="label" allowClear showSearch />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="治疗备注" name={`serviceContent`}>
              <Input.TextArea
                placeholder="请输入治疗备注信息"
                autoSize={{ minRows: 10 }}
                maxLength={1000}
                showCount
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <div style={{ textAlign: 'left', marginTop: '-20px', marginLeft: '65px', marginBottom: '20px' }}>
              <TextTemplatePage title={`选择备注模板`} module={10} onSelect={templateSelect}>
                <Button type="link">选择备注模板</Button>
              </TextTemplatePage>
            </div>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalStart;
