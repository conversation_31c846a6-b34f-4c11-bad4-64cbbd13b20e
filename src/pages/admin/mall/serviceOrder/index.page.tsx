import UserManager from "@/components/UserManager";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallApi from "@/services/MallApi";
import { renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { DeductionState, EvaluationState, ServiceState } from "./types";
import { DatePicker, Dropdown, Form, Input, Popconfirm, Select, Space, Typography, message } from "antd";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import { useRef } from "react";
import ModalEdit from "./ModalEdit";
import ModalStart from "./ModalStart";
import ModalHuakou from "./ModalHuakou";
import AccessDropActions from "@/components/AccessList/AccessDropActions";
import { DatePresetRanges } from "@/utils/Tools";
import { FormOutlined } from "@ant-design/icons";
import ModalEditContent from "./ModalEditContent";
import ModalDetail from "./ModalDetail";

const ServiceOrder = (props: { userId?: any; doctorId?: any; goodsKeeper?: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const isDoctor = !!props.doctorId;
  const isGoodsKeeper = !!props.goodsKeeper;

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { id: item.id };
    await MallApi.delServiceOrderv2({ data });
    message.success("作废成功");
    handleReload();
  };

  return (
    <WeTable
      size={props.userId ? 10 : undefined}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      request={(params) => MallApi.getServiceOrder({ params })}
      params={{ queryType: props.queryType, shopVipUserId: props.userId, doctorId: props.doctorId }}
      title={
        <ModalEdit title={`新增治疗单`} onOk={handleReload} data={{ shopVipUserId: props.userId }}>
          <WeTable.AddBtn>治疗开单</WeTable.AddBtn>
        </ModalEdit>
      }
      search={[
        <Form.Item label="日期" name={`CreateDate`} initialValue={props.userId ? [] : [dayjs().startOf("day"), dayjs().endOf("day")]}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
        !props.userId && (
          <Form.Item label="会员" name={`shopVipUserId`}>
            <VipPickerInput />
          </Form.Item>
        ),
        !isGoodsKeeper && (
          <Form.Item label="治疗状态" name={`serviceState`}>
            <Select options={ServiceState} fieldNames={{ label: "name", value: "id" }} optionFilterProp="name" placeholder="请选择治疗状态" allowClear showSearch />
          </Form.Item>
        ),
        <Form.Item label="治疗单号" name={`serialNo`}>
          <Input placeholder="请输入治疗单号" />
        </Form.Item>,
        <Form.Item label="项目名称" name={`productName`}>
          <Input placeholder="请输入项目名称" />
        </Form.Item>,
        !isDoctor && (
          <Form.Item label="划扣状态" name={`deductionState`}>
            <Select options={DeductionState} fieldNames={{ label: "name", value: "id" }} allowClear showSearch optionFilterProp="name" placeholder="请选择划扣状态" />
          </Form.Item>
        ),
        <Form.Item label="评价状态" name={`evaluationState`}>
          <Select options={EvaluationState} fieldNames={{ label: "name", value: "id" }} allowClear showSearch optionFilterProp="name" placeholder="请选择评价状态" />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "vipUser",
          hide: props.userId,
          render: (c) =>
            c ? (
              <UserManager userId={c?.id}>
                <a>{c?.name}</a>
              </UserManager>
            ) : (
              "--"
            ),
        },
        {
          title: "门店",
          dataIndex: "shop",
          hide: !props.userId,
          render: (c) => c?.name ?? "--",
        },
        { title: "医生", dataIndex: "doctorName" },
        { title: "治疗师", dataIndex: "nurseName" },
        { title: "治疗单号", dataIndex: "serialNo" },
        {
          title: "治疗项目",
          dataIndex: "itemList",
          render: (c) => {
            return (
              <div className="w-400px">
                {c?.map((sub: any) => {
                  return (
                    <div className="flex items-center">
                      <div
                        className="flex-1 min-w-0 line-clamp-1"
                        title={
                          sub.productName +
                          " " +
                          (sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName ? " - " + sub.productSpecificationName : "") +
                          " * " +
                          sub.serviceNum
                        }
                      >
                        <div className="line-clamp-1">
                          {sub?.productName}
                          {sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName && <span className="text-gray-400 ml-2px">{" - " + sub.productSpecificationName}</span>}
                          &nbsp;* {sub.serviceNum}
                        </div>
                      </div>
                      {/*  <div className="w-100px text-right">划扣:{sub.deductionMoney}元</div> */}
                    </div>
                  );
                })}
              </div>
            );
          },
        },
        { title: "划扣金额", dataIndex: "deductionMoney" },
        {
          title: "开单备注",
          dataIndex: "orderContent",
          width: 150,
          render: (c) =>
            c ? (
              <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                {c}
              </Typography.Text>
            ) : (
              "--"
            ),
        },
        {
          fixed: "right",
          title: "治疗状态",
          dataIndex: "serviceState",
          hide: isGoodsKeeper,
          render: (c) => renderTag(ServiceState, c),
        },
        {
          title: "治疗备注",
          dataIndex: "serviceContent",
          width: 200,
          hide: isGoodsKeeper,
          render: (c, item) => (
            <>
              {c ? (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
              ) : (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  --
                </Typography.Text>
              )}
              {item.serviceState === 20 && (
                <ModalEditContent title={`编辑治疗单`} onOk={handleReload} data={item}>
                  <FormOutlined />
                </ModalEditContent>
              )}
            </>
          ),
        },
        {
          fixed: "right",
          title: "物资划扣状态",
          dataIndex: "deductionState",
          hide: isDoctor,
          render: (c) => renderTag(DeductionState, c),
        },
        {
          title: "评价状态",
          dataIndex: "evaluationState",
          render: (c) => (c > 0 ? renderTag(EvaluationState, c) : "--"),
        },

        // {
        //   title: "物资划扣备注",
        //   dataIndex: "deductionContent",
        //   hide: isDoctor,
        //   render: (c) =>
        //     c ? (
        //       <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
        //         {c}
        //       </Typography.Text>
        //     ) : (
        //       "--"
        //     ),
        // },
        {
          title: "日期",
          dataIndex: "createDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
        },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <ModalDetail data={item}>
                <Typography.Link>详情</Typography.Link>
              </ModalDetail>
              {!isGoodsKeeper && (
                <ModalStart title={`治疗`} onOk={handleReload} data={item}>
                  <Typography.Link disabled={!(item.serviceState == 10)}>治疗</Typography.Link>
                </ModalStart>
              )}

              {!isGoodsKeeper && <AccessDropActions userId={item.shopVipUserId} onOk={handleReload} />}

              {!isDoctor && (
                <ModalHuakou onOk={handleReload} data={item}>
                  <Typography.Link disabled={!(item.deductionState == 10)}>物资划扣</Typography.Link>
                </ModalHuakou>
              )}

              <Dropdown
                menu={{
                  items: [
                    {
                      key: "1",
                      disabled: !(item.serviceState == 10),
                      label: (
                        <ModalEdit title={`修改治疗单`} onOk={handleReload} data={item}>
                          <Typography.Link disabled={!(item.serviceState == 10)}>修改</Typography.Link>
                        </ModalEdit>
                      ),
                    },
                    {
                      key: "2",
                      disabled: !(item.serviceState == 10),
                      label: (
                        <Popconfirm title={`确定要作废这条数据吗？`} onConfirm={() => handleDel(item)}>
                          <Typography.Link disabled={!(item.serviceState == 10)}>作废</Typography.Link>
                        </Popconfirm>
                      ),
                    },
                  ],
                }}
              >
                <Typography.Link>更多</Typography.Link>
              </Dropdown>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default ServiceOrder;
