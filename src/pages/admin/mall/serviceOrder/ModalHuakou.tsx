import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Input, Row, message } from "antd";

const layout = { row: 10, col: 12 };

const ModalHuakou = (props: { children: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    await MallApi.getServiceOrderHuakou({ data });
    message.success("物资划扣成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={`物资划扣`} width={600} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="物资划扣备注" name={`deductionContent`}>
              <Input.TextArea
                placeholder="请输入物资划扣备注信息"
                autoSize={{ minRows: 3 }}
                maxLength={300}
                showCount
                allowClear
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalHuakou;
