import WeModal from "@/components/WeModal/WeModal";
import WeTable from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input } from "antd";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const INIT_STATE = { items: [] as any[] };

export const ProjectPicker = (props: { children: any; ext?: any; onOk?: Function }) => {
  const [state, setState] = useSetState({ ...INIT_STATE });

  return (
    <WeModal
      trigger={props.children}
      title="选择项目"
      width={950}
      onOpen={() => setState({ ...INIT_STATE })}
      onOk={async () => props.onOk?.(state.items)}
    >
      <div className="">
        <WeTable
          size={10}
          searchNum={2}
          tableProps={{
            size: "small",
            rowSelection: {
              type: "checkbox",
              preserveSelectedRowKeys: true,
              selectedRowKeys: state.items.map((n) => n.id),
              onChange: (_, r) => {
                setState({ items: r });
              },
            },
            onRow: (r) => {
              return {
                onClick: () => {
                  const arr = state.items.filter((n) => n.id !== r.id);
                  if (arr.length == state.items.length) arr.push(r);
                  setState({ items: arr });
                },
              };
            },
          }}
          params={{}}
          request={(p) => {
            const params = { ...props.ext, ...p };
            return MallApi.selectproject({ params });
          }}
          search={[
            <Form.Item label="项目名称" name={`productName`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
          ]}
          columns={[
            {
              title: "项目名称",
              width: 300,
              render: (item) => (
                <div className="line-clamp-1">
                  {item?.productName}
                  {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                    <span className="text-gray-400 ml-2px">
                      {" - " + item.productSpecificationName}
                    </span>
                  )}
                </div>
              )
            },
            {
              title: "剩余/总次数",
              width: 100,
              render: (item) => (
                <span>{item.lessNum} / {item.includeNum}</span>
              )
            },
            {
              title: "有效期",
              width: 150,
              render: (item) =>
                item?.useEffectiveStyle == 0 ? "永久有效" : dayjs(item.useEffectiveEndDate).format("YYYY-MM-DD HH:mm:ss"),
            },
            { title: "销售门店", dataIndex: "orderShopName", width: 150, render: (c) => c || "--" },
          ]}
        />
      </div>
    </WeModal>
  );
};
