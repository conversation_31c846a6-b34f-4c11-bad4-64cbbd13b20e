import WeModal from "@/components/WeModal/WeModal";
import { Descriptions, Image, Table, } from "antd";
import { formatDate } from "@/utils/Tools";
import { EvaluationState } from "../serviceEvaluation/types";
import { DeductionState, ServiceState } from "./types";

const ModalDetail = (props: { children: any; data: any }) => {
  const data = props.data;
  const user = data.vipUser;
  const shop = data.shop;

  return (
    <WeModal
      trigger={props.children}
      width={1000}
      title="治疗单详情"
      okButtonProps={{ hidden: true }}
      cancelText="关闭"
    >
      <Descriptions
        className="mt-5"
        size="small"
        title="治疗信息"
        items={[
          {
            label: "会员信息",
            children: (
              <>
                {user?.name} ({user?.vipCard})
              </>
            ),
          },
          { label: "门店", children: shop.name },
          { label: "治疗单号", children: data.serialNo },
          { label: "医生", children: data.doctorName },
          { label: "治疗师", children: data.nurseName },
          { label: "治疗日期", children: formatDate(data.createDate) },
          { label: "划扣金额", children: data.deductionMoney },
          { label: "开单备注", children: data.orderContent || "--", span: 3 },
          { label: "治疗状态", children: ServiceState.find((n) => n.id == data.serviceState)?.name },
          { label: "治疗备注", children: data.serviceContent || "--", span: 2 },
          { label: "物资划扣状态", children: DeductionState.find((n) => n.id == data.deductionState)?.name },
          { label: "物资划扣备注", children: data.deductionContent || "--", span: 2 },
        ]}
      />

      <Descriptions
        className="mt-5"
        size="small"
        title="评价信息"
        items={[
          { label: "评价状态", children: EvaluationState.find((n) => n.id == data.evaluationState)?.name },
          { label: "评价日期", children: data.evaluationDate ? formatDate(data.evaluationDate) : "--" },
          { children: "" },
          { label: "门店评分", children: data.evaluationScore || "--" },
          { label: "医生评分", children: data.doctorCommentData?.score || "--" },
          { children: "" },
          { label: "门店评价", children: data.evaluationDesc || "--", span: 3 },
          {
            label: "评价图片",
            span: 3,
            children: data.evaluationImage
              ? data.evaluationImage.split(',').map((item: string) => (
                <Image key={item} src={item} style={{ width: "50px", height: "50px", objectFit: "cover" }} />
              ))
              : "--"
          },
        ]}
      />
      <Descriptions className="mt-5" size="small" title={"项目清单"} />
      <Table
        dataSource={data.itemList}
        size="small"
        bordered={false}
        pagination={false}
        columns={[
          {
            title: "项目名称",
            render: (item) => (
              <div className="line-clamp-1">
                {item?.productName}
                {item?.productSpecificationName && item?.productName !== item?.productSpecificationName && (
                  <span className="text-gray-400 ml-2px">
                    {" - " + item.productSpecificationName}
                  </span>
                )}
              </div>
            )
          },
          { title: "划扣次数", dataIndex: "serviceNum", render: (c) => <span>{c}</span> },
          { title: "划扣金额", dataIndex: "deductionMoney", render: (c) => <span>{c}</span> },
        ]}
      ></Table>

    </WeModal>
  );
};

export default ModalDetail;
