import { DatePicker, Form, Input, Modal, Button, message, InputNumber, Space, Typography } from "antd";
import { FC, useRef, useState } from "react";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallApi from "@/services/MallApi";
import dayjs from "dayjs";
import { DatePresetRanges } from "@/utils/Tools";

const UseProject: FC<{ userId: any; queryType?: any }> = ({ userId, queryType }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [form] = Form.useForm();

  const handleDeduct = (record: any) => {
    setCurrentRecord(record);
    setModalVisible(true);
    form.resetFields();
    // 设置表单初始值，包括剩余次数
    form.setFieldsValue({
      lessNum: record.lessNum,
      reduceNum: undefined,
      reduceReason: undefined,
    });
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      const data = { ...values, projectId: currentRecord.id };
      await MallApi.reduceProject({ data });
      message.success("扣减成功");
      setModalVisible(false);
      tableRef.current?.reload();
    } catch (error) {
      console.error(error);
    }
  };

  const handleModalCancel = () => {
    setModalVisible(false);
  };

  // 添加全部按钮点击处理函数
  const handleUseAll = () => {
    form.setFieldsValue({
      reduceNum: currentRecord?.lessNum,
    });
  };

  return (
    <>
      <WeTable
        ref={tableRef}
        defaultSort={{
          key: "lessNum",
          type: "descend",
        }}
        request={(params) => {
          //console.log(params);
          if (params.CreateDate?.length) {
            params.startCreateDate = dayjs(params.CreateDate[0]).format("YYYY-MM-DD HH:mm:ss");
            params.endCreateDate = dayjs(params.CreateDate[1]).format("YYYY-MM-DD HH:mm:ss");
          }
          return MallApi.getproject({ params });
        }}
        params={{
          queryType: queryType,
          source: 20,
          shopVipUserId: userId,
          showAll: 1,
        }}
        search={[
          <Form.Item label="日期" name="CreateDate" initialValue={[]}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
          <Form.Item label="项目名称" name={`productName`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
        ]}
        columns={[
          { title: "门店", dataIndex: "orderShopName", render: (c) => c || "--" },
          {
            title: "项目名称",
            dataIndex: "productName",
            sorter: true,
          },
          {
            title: "总次数",
            dataIndex: "includeNum",
            sorter: true,
          },
          {
            title: "剩余次数",
            dataIndex: "lessNum",
            sorter: true,
            defaultSortOrder: "descend",
            render: (c) => <div>{c > 0 ? c : <span style={{ color: "red" }}>{c}</span>}</div>,
          },
          {
            title: "有效期",
            sorter: true,
            render: (item) => (item?.useEffectiveStyle == 0 ? "永久有效" : dayjs(item.useEffectiveEndDate).format("YYYY-MM-DD HH:mm:ss")),
          },
          {
            title: "日期",
            dataIndex: "createDate",
            sorter: true,
            render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
          },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <Typography.Link disabled={!(item.lessNum > 0)}>
                    <span onClick={() => handleDeduct(item)}>扣除</span>
                  </Typography.Link>
                </Space>
              );
            },
          },
        ]}
      />
      <Modal title="项目扣减" visible={modalVisible} onOk={handleModalOk} onCancel={handleModalCancel} destroyOnClose>
        <Form form={form} layout="vertical">
          <Form.Item
            label={
              <span>
                当前剩余(<span style={{ color: "red" }}> {currentRecord?.lessNum} </span>)次
                <Button type="link" size="small" onClick={handleUseAll} disabled={!currentRecord?.lessNum} style={{ paddingLeft: 8 }}>
                  [全部扣除]
                </Button>
              </span>
            }
            name="reduceNum"
            rules={[{ required: true, message: "请输入扣减次数" }]}
          >
            <InputNumber min={1} max={currentRecord?.lessNum} step={1} style={{ width: "100%" }} placeholder="请输入扣减次数" addonBefore={"扣减"} addonAfter={"次"} />
          </Form.Item>
          <Form.Item label="扣减原因" name="reduceReason" rules={[{ required: true, message: "请输入扣减原因" }]}>
            <Input.TextArea placeholder="请输入扣减原因" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default UseProject;
