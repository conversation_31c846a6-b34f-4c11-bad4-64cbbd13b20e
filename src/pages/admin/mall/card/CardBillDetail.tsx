import WeModal, { WeModalRef } from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Table } from "antd";
import { useRef } from "react";
import { useSetState, useUpdateEffect } from "react-use";

const CardBillDetail = (props: { children: any; title: any; billId: any }) => {
  const modalRef = useRef<WeModalRef>(null);
  const [state, setState] = useSetState({
    list: [] as any[],
    loading: false,
    size: 10,
    page: 1,
    total: 0,
  });

  const handleOpen = () => {
    if (state.page !== 1) {
      setState({ page: 1 });
    } else {
      fetchList();
    }
  };

  useUpdateEffect(() => {
    fetchList();
  }, [state.page, state.size]);

  const fetchList = async () => {
    const params = { pageSize: state.size, pageNum: state.page, billId: props.billId };
    setState({ loading: true });
    const res = await MallApi.getCardBillDetail({ params }).finally(() => setState({ loading: false }));

    setState({ list: res?.list || [], total: res?.total || 0, size: res.pageSize, page: res.pageNum });
  };

  return (
    <WeModal ref={modalRef} trigger={props.children} title={props.title} width={600} footer={null} onOpen={handleOpen}>
      <Table
        rowKey={`id`}
        size="small"
        dataSource={state.list}
        loading={state.loading}
        columns={[
          { title: "类型", dataIndex: "cardTypeName" },
          { title: "卡号", dataIndex: "cardNo" },
        ]}
        pagination={{
          total: state.total,
          current: state.page,
          pageSize: state.size,
          showTotal: (n) => `共${n}条数据`,
          showSizeChanger: true,
          onChange: (page, size) => setState({ page, size }),
        }}
      />
    </WeModal>
  );
};

export default CardBillDetail;
