import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, Radio, Row, message } from "antd";
import CardTypePicker from "./CardTypePicker";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const layout = { row: 10, col: 24 };

const CardNoAdd = (props: { children: any; title: any; onOk: Function; cardProperty: any }) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    await MallApi.addCardNo({ data });
    message.success("生成成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="卡类型" name={`cardTypeId`} rules={[{ required: true, message: "请选择卡类型" }]}>
              <CardTypePicker params={{ cardProperty: props.cardProperty }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="生成数量"
              name={`num`}
              initialValue={1}
              rules={[{ required: true, message: "请输入生成数量" }]}
            >
              <InputNumber addonAfter="张" min={1} max={5000} style={{ width: "100%" }} placeholder="请输入" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="出厂" name={`needOutBound`} initialValue={0}>
              <Radio.Group
                options={[
                  { label: "是", value: 1 },
                  { label: "否", value: 0 },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="备注" name={`operRemark`}>
              <Input.TextArea placeholder="请输入备注" autoSize maxLength={100} showCount />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default CardNoAdd;
