import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { <PERSON>ton, Card, Popconfirm, Space, Typography, message } from "antd";
import AddType from "./AddType";
import { useRef } from "react";
import dayjs from "dayjs";
import { CardProperty } from "./types";
import { useSetState } from "react-use";
import CardNoList from "./CardNoList";
import CardBillList from "./CardBillList";
import { FileTextOutlined, SnippetsOutlined } from "@ant-design/icons";

const CardPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ tabKey: 1 });
  const cardType = state.tabKey == 2 ? "奖品卡" : "套餐卡";

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delCardType({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <div>
      <Card
        size="small"
        tabList={CardProperty.map((n) => ({ key: n.id + "", tab: n.name }))}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        activeTabKey={state.tabKey + ""}
        onTabChange={(e) => setState({ tabKey: Number(e) })}
      />
      <WeTable
        ref={tableRef}
        request={(params) => MallApi.getCardType({ params })}
        params={{ cardProperty: state.tabKey }}
        title={
          <>
            <AddType title={`新增${cardType}`} onOk={handleReload} cardProperty={state.tabKey as any}>
              <WeTable.AddBtn children={`新增${cardType}`} />
            </AddType>
            <CardNoList title={`卡号管理 - ${cardType}`} cardProperty={state.tabKey}>
              <Button type="primary" icon={<SnippetsOutlined />}>卡号管理</Button>
            </CardNoList>
            <CardBillList title={`操作单 - ${cardType}`} cardProperty={state.tabKey}>
              <Button type="primary" icon={<FileTextOutlined />}>操作单</Button>
            </CardBillList>
          </>
        }
        columns={[
          { title: "名称", dataIndex: "name" },
          { title: "英文编码", dataIndex: "enCode", render: (c) => (c ? c : "--") },
          { title: "卡价值", dataIndex: "cardValue" },
          {
            title: "核卡失效方式",
            render: (c) => (
              <>
                {c.effectiveStyle == 0 && "永久有效"}
                {c.effectiveStyle == 10 && `领取后${c.effectiveDeferredDays}天生效，有效期${c.effectiveDays}天`}
                {c.effectiveStyle == 20 &&
                  `从 ${dayjs(c.effectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.effectiveEndDate).format(
                    "YYYY-MM-DD"
                  )}`}
              </>
            ),
          },
          {
            hide: state.tabKey == 2,
            title: "使用失效方式",
            render: (c) => (
              <>
                {c.useEffectiveStyle == 0 && "永久有效"}
                {c.useEffectiveStyle == 10 &&
                  `核卡后${c.useEffectiveDeferredDays}天生效，有效期${c.useEffectiveDays}天`}
                {c.useEffectiveStyle == 20 &&
                  `从 ${dayjs(c.useEffectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.useEffectiveEndDate).format(
                    "YYYY-MM-DD"
                  )}`}
              </>
            ),
          },
          { title: "注册数量", dataIndex: "registeredCount" },
          {
            title: "操作",
            render: (item) => (
              <Space>
                <AddType title={`修改${cardType}`} onOk={handleReload} cid={item.id} cardProperty={state.tabKey as any}>
                  <Typography.Link>修改</Typography.Link>
                </AddType>
                <Popconfirm title={`确定要删除这条数据吗？`} onConfirm={() => handleDel(item)}>
                  <Typography.Link>删除</Typography.Link>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
      />
    </div>
  );
};

export default CardPage;
