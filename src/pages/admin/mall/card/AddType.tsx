import WeModal from "@/components/WeModal/WeModal";
import { Button, Col, DatePicker, Form, Input, InputNumber, Row, Select, Table, message, } from "antd";
import { EffectiveStyle, UseEffectiveStyle } from "./types";
import { PlusOutlined } from "@ant-design/icons";
import GoodsPicker from "@/components/GoodsPicker";
import MallApi from "@/services/MallApi";
import { formatDateRange } from "@/utils/Tools";
import dayjs from "dayjs";
import { useState } from "react";
import RewardModal from "./RewardModal";
import { DatePresetRanges } from "@/utils/Tools";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 12 };
const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

const AddType = (props: { title: any; onOk?: Function; children: any; cid?: any; cardProperty: 1 | 2 }) => {
  const [form] = Form.useForm();
  const productListName = "productList";
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rewardList, setRewardList] = useState<any[]>([]);

  const handleOpen = async () => {
    form.resetFields();
    form.resetFields(["productList"]);

    if (props.cid) {
      const data = await MallApi.getCardTypeDetail(props.cid);

      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      data.productList = data?.includeVO?.productList || [];
      data.productList = data.productList.sort((a: any, b: any) => (a.groupTag < b.groupTag ? -1 : 1));
      data.edate = [fdate(data.effectiveStartDate), fdate(data.effectiveEndDate)];
      if (data?.includeVO?.prizeList?.length) {
        data?.includeVO?.prizeList.map((item: any, index: number) => {
          item.indexKey = index + 1;
          //item.winRate = item?.winRate * 100;
          item.projectImage = ImageUpload.serializer(item.projectImage);
        });
      }
      setRewardList(data?.includeVO?.prizeList);
      form.setFieldsValue(data);
    } else {
      setRewardList([]);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.cardProperty = props.cardProperty;
    let a = JSON.stringify(rewardList);
    let reward = JSON.parse(a);
    reward.forEach((element: any) => {
      //element.winRate = element?.winRate / 100;
      element.projectImage = ImageUpload.deserializer(element?.projectImage);
    });

    data.includeVO = { productList: data.productList, prizeList: reward };

    const edate = formatDateRange(data.edate);
    data.effectiveStartDate = edate.start;
    data.effectiveEndDate = edate.end;
    delete data.edate;

    const useEdate = formatDateRange(data.useEdate);
    data.useEffectiveStartDate = useEdate.start;
    data.useEffectiveEndDate = useEdate.end;
    delete data.useEdate;

    if (data.id) {
      await MallApi.putCardType({ data });
      message.success("修改成功");
    } else {
      await MallApi.addCardType({ data });
      message.success("添加成功");
    }

    props.onOk?.();
  };
  return (
    <WeModal trigger={props.children} title={props.title} width={900} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "110px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Group>
            <Col span={layout.col}>
              <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
                <Input placeholder="请输入名称" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="英文编码" name={`enCode`} rules={[{ required: false, message: "请输入英文编码" }]}>
                <Input placeholder="请输入英文编码" />
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={layout.col}>
              <Form.Item label="卡价值" name={`cardValue`} rules={[{ required: true, message: "请输入卡价值" }]}>
                <InputNumber placeholder="请输入卡价值" addonAfter="元" style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="卡成本" name={`cardCost`} rules={[{ required: true, message: "请输入卡成本" }]}>
                <InputNumber placeholder="请输入卡成本" addonAfter="元" style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={layout.col}>
              <Form.Item
                label="核卡失效方式"
                name={`effectiveStyle`}
                rules={[{ required: true, message: "请选择核卡失效方式" }]}
              >
                <Select
                  options={EffectiveStyle}
                  fieldNames={{ label: "name", value: "id" }}
                  allowClear
                  placeholder="请选择核卡失效方式"
                />
              </Form.Item>
            </Col>
            <Form.Item noStyle dependencies={["effectiveStyle"]}>
              {(form) => {
                const value = form.getFieldValue("effectiveStyle");

                if (value == 10) {
                  return (
                    <Col span={layout.col}>
                      <div style={{ display: "flex" }}>
                        <Form.Item
                          name={`effectiveDeferredDays`}
                          rules={[{ required: true, message: "请输入生效时间" }]}
                        >
                          <InputNumber min={0} addonBefore="领取后" addonAfter="天生效" />
                        </Form.Item>
                        <div style={{ width: 10 }} />
                        <Form.Item name={`effectiveDays`} rules={[{ required: true, message: "请输入有效期" }]}>
                          <InputNumber min={0} addonBefore="有效期" addonAfter="天" />
                        </Form.Item>
                      </div>
                    </Col>
                  );
                }

                if (value == 20) {
                  return (
                    <Col span={layout.col}>
                      <Form.Item name={`edate`} rules={[{ required: true, message: "请选择有效时间" }]}>
                        <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
                      </Form.Item>
                    </Col>
                  );
                }

                return null;
              }}
            </Form.Item>
          </Group>

          {props.cardProperty == 1 && (
            <Group>
              <Col span={layout.col}>
                <Form.Item
                  label="使用失效方式"
                  name={`useEffectiveStyle`}
                  rules={[{ required: true, message: "请选择使用失效方式" }]}
                >
                  <Select
                    options={UseEffectiveStyle}
                    fieldNames={{ label: "name", value: "id" }}
                    allowClear
                    placeholder="请选择使用失效方式"
                  />
                </Form.Item>
              </Col>
              <Form.Item noStyle dependencies={["useEffectiveStyle"]}>
                {(form) => {
                  const value = form.getFieldValue("useEffectiveStyle");
                  if (value == 10) {
                    return (
                      <Col span={layout.col}>
                        <div style={{ display: "flex" }}>
                          <Form.Item
                            name={`useEffectiveDeferredDays`}
                            rules={[{ required: true, message: "请输入生效时间" }]}
                          >
                            <InputNumber min={0} addonBefore="核卡后" addonAfter="天生效" />
                          </Form.Item>
                          <div style={{ width: 10 }} />
                          <Form.Item name={`useEffectiveDays`} rules={[{ required: true, message: "请输入有效期" }]}>
                            <InputNumber min={0} addonBefore="有效期" addonAfter="天" />
                          </Form.Item>
                        </div>
                      </Col>
                    );
                  }

                  if (value == 20) {
                    return (
                      <Col span={layout.col}>
                        <Form.Item name={`useEdate`} rules={[{ required: true, message: "请选择有效时间" }]}>
                          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
                        </Form.Item>
                      </Col>
                    );
                  }

                  return null;
                }}
              </Form.Item>
            </Group>
          )}

          <Group>
            <Col span={layout.col}>
              <Form.Item
                label="号码前缀"
                name={`noPre`}
                rules={[
                  { required: true, message: "请输入号码前缀" },
                  { pattern: /^[A-Z]{2}$/, message: "2位英文大写字母" },
                ]}
              >
                <Input placeholder="请输入号码前缀" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="排序" name={`sort`}>
                <InputNumber style={{ width: "100%" }} placeholder="请输入排序，范围1~999" />
              </Form.Item>
            </Col>
          </Group>
          {/*  <Group>
            <Col span={layout.col}>
              <Form.Item label="中奖数" name={`winNum`} rules={[{ required: true, message: "请输入中奖数" }]}>
                <InputNumber placeholder="请输入中奖数" style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Group> */}
          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="规则说明" name={`ruleContent`} rules={[{ required: false, message: "请输入规则说明" }]}>
                <Input.TextArea placeholder="请输入规则说明" style={{ width: "100%" }} rows={3} />
              </Form.Item>
            </Col>
          </Group>

          {props.cardProperty === 1 && (
            <Group>
              <Col span={24}>
                <Form.Item label="关联商品">
                  <Form.List name={productListName} initialValue={[]}>
                    {(fields, action) => {
                      return (
                        <>
                          <Table
                            size="small"
                            locale={{ emptyText: "暂无数据" }}
                            pagination={false}
                            dataSource={fields}
                            columns={[
                              {
                                title: "套餐分组",
                                render: (c) => {
                                  return (
                                    <Form.Item
                                      style={{ marginBottom: 0 }}
                                      name={[c.name, "groupTag"]}
                                      initialValue={"A"}
                                    >
                                      <Select
                                        placeholder="请选择"
                                        options={"ABCD".split("").map((n) => ({ label: n, value: n }))}
                                      />
                                    </Form.Item>
                                  );
                                },
                              },
                              {
                                title: "名称",
                                render: (c) => {
                                  const name = form.getFieldValue([productListName, c.name, "productName"]);
                                  return name;
                                },
                              },
                              {
                                title: "价值",
                                render: (c) => {
                                  return (
                                    <Form.Item style={{ marginBottom: 0 }} name={[c.name, "price"]}>
                                      <InputNumber min={1} />
                                    </Form.Item>
                                  );
                                },
                              },
                              {
                                title: "次数/数量",
                                render: (c) => {
                                  return (
                                    <Form.Item style={{ marginBottom: 0 }} name={[c.name, "num"]} initialValue={1}>
                                      <InputNumber min={1} />
                                    </Form.Item>
                                  );
                                },
                              },
                              {
                                title: "操作",
                                render: (c) => {
                                  return <a onClick={() => action.remove(c.name)}>删除</a>;
                                },
                              },
                            ]}
                          />

                          <div style={{ height: 20 }}></div>
                          <GoodsPicker
                            property={1}
                            params={{ productModel: 1, isAll: 1, saleInAll: 1 }}
                            onSelect={(rows) => {
                              const list: any[] = form.getFieldValue(productListName);
                              rows.forEach((item) => {
                                // if (list.every((n) => n.productId !== item.id))
                                list.push({
                                  groupTag: "A",
                                  productId: item.id,
                                  productName: item.name,
                                  price: item.oldPrice,
                                  num: item.includeNum || 1,
                                  property: item.property,
                                });
                              });

                              form.setFieldValue(productListName, list);
                            }}
                          >
                            <Button type="dashed" block icon={<PlusOutlined />}>
                              添加
                            </Button>
                          </GoodsPicker>
                        </>
                      );
                    }}
                  </Form.List>
                </Form.Item>
              </Col>
            </Group>
          )}
          {/*   {props.cardProperty === 2 && (
            <Group>
              <Col span={24}>
                <Form.Item label="关联奖项">
                  <Table
                    pagination={false}
                    locale={{ emptyText: "暂无数据" }}
                    dataSource={rewardList}
                    columns={[
                      {
                        title: "名称",
                        dataIndex: "projectName",
                        key: "projectName",
                      },
                      {
                        title: "图片",
                        dataIndex: "projectImage",
                        render: (c) => {
                          // console.log(c);

                          return <Image src={c?.length ? c[0].url : ""} style={{ maxWidth: 100, maxHeight: 100 }} />;
                        },
                      },
                      {
                        title: "价值",
                        dataIndex: "projectValue",
                        key: "projectValue",
                      },
                      {
                        title: "中奖率",
                        dataIndex: "winRate",
                        key: "winRate",
                      },
                      {
                        title: "排序",
                        dataIndex: "sort",
                        key: "sort",
                      },
                      {
                        title: "操作",
                        render: (c) => {
                          return (
                            <Space>
                              <a
                                onClick={() => {
                                  setIsModalOpen(c);
                                }}
                              >
                                修改
                              </a>
                              <a
                                onClick={() => {
                                  let a = JSON.stringify(rewardList);
                                  let arr = JSON.parse(a);
                                  arr.splice(
                                    arr.findIndex((item: any) => item.indexKey === c.indexKey),
                                    1
                                  );
                                  setRewardList(arr);
                                }}
                              >
                                删除
                              </a>
                            </Space>
                          );
                        },
                      },
                    ]}
                  />
                  <div style={{ height: 20 }}></div>
                  <Button
                    type="dashed"
                    block
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setIsModalOpen(true);
                    }}
                  >
                    添加
                  </Button>
                </Form.Item>
              </Col>
            </Group>
          )} */}
        </Row>
      </Form>

      {/* 新增中奖 */}
      {isModalOpen && (
        <RewardModal
          isModalOpen={isModalOpen}
          setIsModalOpen={setIsModalOpen}
          setRewardList={setRewardList}
          rewardList={rewardList}
        />
      )}
    </WeModal>
  );
};

export default AddType;
