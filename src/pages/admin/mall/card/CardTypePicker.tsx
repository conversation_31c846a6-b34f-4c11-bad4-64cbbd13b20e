import Mall<PERSON>pi from "@/services/MallApi";
import { Select, SelectProps } from "antd";
import { useEffect } from "react";
import { useSetState } from "react-use";

const CardTypePicker = (props: SelectProps & { params?: any }) => {
  const { params, ...rect } = props;
  const [state, setState] = useSetState({
    list: [] as any[],
  });

  useEffect(() => {
    fetchList();
  }, [JSON.stringify(props.params)]);

  const fetchList = async () => {
    const params = { pageSize: 9999, ...props.params };
    const res = await MallApi.getCardType({ params });
    // console.log(res);
    setState({ list: res?.list || [] });
  };

  return (
    <Select
      options={state.list}
      placeholder="请选择卡类型"
      fieldNames={{ label: "name", value: "id" }}
      allowClear
      showSearch
      optionFilterProp="name"
      {...rect}
    />
  );
};

export default CardTypePicker;
