import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Form, Input, Row, message } from "antd";

const layout = { row: 10, col: 24 };

const CardNoFilter = (props: {
  children: any;
  title: any;
  onOk: Function;
  type: "out" | "del";
  cardProperty: any;
  clist?: any;
}) => {
  const [form] = Form.useForm();

  const handleOpen = () => {
    form.resetFields();

    if (props.clist) {
      form.setFieldValue("cardNoList", props.clist);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.cardProperty = props.cardProperty;
    data.cardNoList = data.cardNoList
      .split("\n")
      .map((n: string) => n.trim())
      .filter((n: any) => n);

    if (props.type === "out") {
      await MallApi.outCardNo({ data });
      message.success("出厂操作成功");
    } else {
      await MallApi.delCardNo({ data });
      message.success("删除操作成功");
    }

    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="备注" name={`operRemark`}>
              <Input.TextArea placeholder="请输入备注" autoSize maxLength={100} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              label="卡号列表"
              name={`cardNoList`}
              rules={[
                {
                  required: true,
                  message: "请输入卡号列表",
                },
                {
                  validator: (_, value) => {
                    const arr = (value || "").split("\n");
                    if (arr.length <= 5000) return Promise.resolve();
                    return Promise.reject(new Error("单次操作最多处理5000个卡号"));
                  },
                },
              ]}
            >
              <Input.TextArea
                placeholder={`请输入完整卡号， 一行一个\n格式示例:\nSN1000000001\nSN1000000002\nSN1000000003\n...`}
                autoSize={{ minRows: 10, maxRows: 10 }}
                disabled={!!props.clist}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default CardNoFilter;
