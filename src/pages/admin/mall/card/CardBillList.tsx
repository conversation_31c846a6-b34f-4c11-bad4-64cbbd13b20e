import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { getAuth, renderTag } from "@/utils/Tools";
import { DatePicker, Drawer, Form, Popconfirm, Select, Space, Typography } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import { OperType } from "./types";
import axios from "axios";
import Configs from "@/utils/Configs";
import dayjs from "dayjs";
import CardBillDetail from "./CardBillDetail";
import { DatePresetRanges } from "@/utils/Tools";

const CardBillList = (props: { children: any; cardProperty: any; title: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });

  useEffect(() => {
    if (state.open) {
      tableRef.current?.reset();
    }
  }, [state.open]);

  const handleExport = async (item: any) => {
    const link = document.createElement("a");
    link.href = axios.getUri({
      baseURL: Configs.apiHost,
      method: "get",
      url: "/api/mallCardBill/exportExcel",
      params: {
        token: getAuth().token || "",
        billId: item.id,
      },
    });
    link.setAttribute("download", "");
    link.click();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        title={props.title}
        onClose={() => setState({ open: false })}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          request={(params) => MallApi.getCardBill({ params })}
          params={{ cardProperty: props.cardProperty }}
          search={[
            <Form.Item label="操作类型" name={`operType`}>
              <Select
                options={OperType}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择操作类型"
                allowClear
              />
            </Form.Item>,
            <Form.Item label="日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "单号", dataIndex: "serialNo" },
            { title: "操作类型", dataIndex: "operType", render: (c) => renderTag(OperType, c) },
            { title: "数量", dataIndex: "operNum" },
            {
              title: "备注",
              dataIndex: "operRemark",
              render: (c) => (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
              ),
            },
            {
              title: "日期",
              dataIndex: "createDate",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
            },
            {
              title: "操作",
              render: (item: any) => (
                <Space>
                  <Popconfirm title={`确定要导出数据吗？`} onConfirm={() => handleExport(item)}>
                    <Typography.Link>导出</Typography.Link>
                  </Popconfirm>
                  <CardBillDetail title={`查看卡号`} billId={item.id}>
                    <Typography.Link>查看卡号</Typography.Link>
                  </CardBillDetail>
                </Space>
              ),
            },
          ]}
        />
      </Drawer>
    </>
  );
};

export default CardBillList;
