import ImageUpload from "@/components/ImageUpload";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, Divider, Form, Input, InputNumber, Modal, Radio, Row, Select } from "antd";
import { useEffect } from "react";
import { useAsync } from "react-use";

const layout = { row: 10, col: 12 };
const RewardModal = (props: {
  setIsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isModalOpen: any;
  setRewardList: React.Dispatch<React.SetStateAction<any[]>>;
  rewardList: any[];
}) => {
  const { isModalOpen, setIsModalOpen, rewardList, setRewardList } = props;
  const [form] = Form.useForm();
  const property = Form.useWatch("property", form);

  const { value: JMList = [] } = useAsync(async () => {
    const res = await MallApi.getCollectCard({ params: { pageSize: 999 } });
    let list = res?.list || [];
    list = list.map((item: any) => ({ label: item.name, value: item.id }));
    return list;
  }, []);

  useEffect(() => {
    form.setFieldsValue({
      ...isModalOpen,
    });
    if (!isModalOpen.indexKey) {
      form.resetFields();
    }
  }, []);

  return (
    <>
      <Modal
        width={600}
        title="中奖率"
        open={isModalOpen}
        onOk={async () => {
          const val = await form.validateFields();
          let arr: any[] = [];
          if (isModalOpen.indexKey) {
            let a = JSON.stringify(rewardList);
            let arr = JSON.parse(a);
            let index = rewardList.findIndex((item: any) => item.indexKey == isModalOpen.indexKey);
            arr[index] = { ...val, indexKey: arr[index].indexKey };
            setRewardList(arr);
          } else {
            arr = [...rewardList, val];
            arr.forEach((item: any, index: number) => {
              item.indexKey = index + 1;
            });
            setRewardList(arr);
          }
          setIsModalOpen(false);
        }}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <Form form={form} labelCol={{ flex: "120px" }}>
          <Row gutter={layout.row}>
            <Col span={layout.col * 2}>
              <Form.Item label="属性" name="property" rules={[{ required: true, message: "请选择属性" }]}>
                <Radio.Group
                  onChange={() => {
                    form.setFieldsValue({ keyData: "" });
                  }}
                  options={[
                    { label: "未中奖", value: 0 },
                    { label: "积分", value: 10 },
                    { label: "券码", value: 20 },
                    { label: "集卡", value: 30 },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="名称" name="projectName" rules={[{ required: true, message: "请输入名称" }]}>
                <Input placeholder="请输入名称" style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              {property == 20 && (
                <Form.Item label="价值" name="projectValue" rules={[{ required: true, message: "请输入价值" }]}>
                  <InputNumber placeholder="请输入价值" addonAfter="元" style={{ width: "100%" }} min={0} />
                </Form.Item>
              )}
              {property == 10 && (
                <Form.Item label="积分" name="keyData" rules={[{ required: true, message: "请输入积分" }]}>
                  <InputNumber placeholder="请输入积分" addonAfter="积分" style={{ width: "100%" }} min={0} />
                </Form.Item>
              )}

              {property == 30 && (
                <Form.Item label="集卡" name="keyData" rules={[{ required: true, message: "请选择集卡" }]}>
                  <Select options={JMList} placeholder="请选择集卡" />
                </Form.Item>
              )}
            </Col>
            <Col span={layout.col}>

              <Form.Item label="中奖率" name="winRate" rules={[{ required: true, message: "请输入中奖率" }]}
                tooltip="此奖项中奖值占所有奖项中奖值的比例。">
                <InputNumber placeholder="请输入中奖率" style={{ width: "100%" }} step="1" max={100} min={0} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>

              <Form.Item label="排序" name={`sort`} rules={[{ required: true, message: "请输入排序" }]}>
                <InputNumber style={{ width: "100%" }} placeholder="请输入排序，范围1~999" min={0} />
              </Form.Item>
            </Col>
            <Col span={layout.col * 2}>

              <Form.Item
                label="图片"
                name="projectImage"
                valuePropName="fileList"
              //rules={[{ required: true, message: "请上传图片" }]}
              >
                <ImageUpload></ImageUpload>
              </Form.Item>
            </Col>


            <Divider orientation="left">加强策略</Divider>
            <Col span={layout.col}>
              <Form.Item label="从N次加强" name="provideTimes" rules={[{ required: false, message: "请输入从N次加强" }]} initialValue={0}
                tooltip="当累计抽奖次数达到此项设定时。若从未抽中，则往后每一次中奖概率进行加强，直至中奖为止。（0表示不加强）">
                <InputNumber placeholder="请输入从第几次开始概率加强(0表示不加强)" style={{ width: "100%" }} max={100} min={0} />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="每次加强几率" name="provideEachRate" rules={[{ required: false, message: "请输入每次加强几率" }]} initialValue={0}
                tooltip="加强后，在现有的中奖几率上每次增加指定中奖记录（0表示不加强）">
                <InputNumber placeholder="请输入每次加强几率(0表示不加强)" style={{ width: "100%" }} max={100} min={0} />
              </Form.Item>
            </Col>
          </Row>

        </Form>
      </Modal>
    </>
  );
};

export default RewardModal;
