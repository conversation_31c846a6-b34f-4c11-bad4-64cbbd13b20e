import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Button, Drawer, Form, Input, Select, Space, Typography } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import { CardState } from "./types";
import { renderTag } from "@/utils/Tools";
import CardNoAdd from "./CardNoAdd";
import CardTypePicker from "./CardTypePicker";
import dayjs from "dayjs";
import CardNoFilter from "./CardNoFilter";
import { CheckCircleOutlined, MinusCircleOutlined, PlusCircleOutlined } from "@ant-design/icons";

const CardNoList = (props: { children: any; cardProperty: any; title: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      tableRef.current?.reset();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        title={props.title}
        onClose={() => setState({ open: false })}
        keyboard={false}
        extra={
          <Space>
            <CardNoAdd title={`卡号生成`} onOk={handleReload} cardProperty={props.cardProperty}>
              <Button type="primary" icon={<PlusCircleOutlined />}>卡号生成</Button>
            </CardNoAdd>
            <CardNoFilter title={`卡号出厂`} onOk={handleReload} type="out" cardProperty={props.cardProperty}>
              <Button type="primary" icon={<CheckCircleOutlined />}>卡号出厂</Button>
            </CardNoFilter>
            <CardNoFilter title={`卡号作废`} onOk={handleReload} type="del" cardProperty={props.cardProperty}>
              <Button type="primary" icon={<MinusCircleOutlined />}>卡号作废</Button>
            </CardNoFilter>
          </Space>
        }
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          request={(params) => MallApi.getCardNo({ params })}
          params={{ cardProperty: props.cardProperty }}
          search={[
            <Form.Item label="类型" name={`cardTypeId`}>
              <CardTypePicker params={{ cardProperty: props.cardProperty }} />
            </Form.Item>,
            <Form.Item label="卡号" name={`cardNo`}>
              <Input placeholder="请输入卡号" />
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Select
                options={CardState}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择状态"
                allowClear
              />
            </Form.Item>,
          ]}
          columns={[
            { fixed: "left", title: "卡号", dataIndex: "cardNo" },
            { title: "类型", dataIndex: "cardTypeName" },
            { title: "卡价值", dataIndex: "cardValue" },
            {
              title: "核卡失效方式",
              render: (c) => (
                <>
                  {c.effectiveStyle == 0 && "永久有效"}
                  {c.effectiveStyle == 10 && `领取后${c.effectiveDeferredDays}天生效，有效期${c.effectiveDays}天`}
                  {c.effectiveStyle == 20 &&
                    `从 ${dayjs(c.effectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.effectiveEndDate).format(
                      "YYYY-MM-DD"
                    )}`}
                </>
              ),
            },
            {
              hide: props.cardProperty == 2,
              title: "使用失效方式",
              render: (c) => (
                <>
                  {c.useEffectiveStyle == 0 && "永久有效"}
                  {c.useEffectiveStyle == 10 &&
                    `核卡后${c.useEffectiveDeferredDays}天生效，有效期${c.useEffectiveDays}天`}
                  {c.useEffectiveStyle == 20 &&
                    `从 ${dayjs(c.useEffectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.useEffectiveEndDate).format(
                      "YYYY-MM-DD"
                    )}`}
                </>
              ),
            },
            { hide: props.cardProperty == 2, title: "归属代理", dataIndex: "ownCompanyName" },
            { hide: props.cardProperty == 2, title: "持卡代理/分销", dataIndex: "holdCompanyName" },
            { title: "使用人", dataIndex: "vipUser", render: (c) => c?.name },
            {
              title: "使用时间",
              dataIndex: "useTime",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
            },
            { fixed: "right", title: "状态", dataIndex: "state", render: (c) => renderTag(CardState, c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => (
                <Space>
                  <CardNoFilter
                    title={`出厂`}
                    onOk={handleReload}
                    type="out"
                    cardProperty={props.cardProperty}
                    clist={item.cardNo}
                  >
                    <Typography.Link disabled={!(item.state == 10)}>出厂</Typography.Link>
                  </CardNoFilter>
                  <CardNoFilter
                    title={`作废`}
                    onOk={handleReload}
                    type="del"
                    cardProperty={props.cardProperty}
                    clist={item.cardNo}
                  >
                    <Typography.Link disabled={item.state == 99}>作废</Typography.Link>
                  </CardNoFilter>
                </Space>
              ),
            },
          ]}
        />
      </Drawer>
    </>
  );
};

export default CardNoList;
