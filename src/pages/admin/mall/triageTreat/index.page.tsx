import { useMount, useSetState } from "react-use";
import ServiceOrder from "../serviceOrder/index.page";
import UserApi from "@/services/UserApi";

const TriageTreat = () => {
  const [state, setState] = useSetState({
    user: null as any,
  });

  useMount(() => {
    fetchUser();
  });

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user });
  };

  if (!state.user) return null;

  return <ServiceOrder doctorId={state.user.id} />;
};

export default TriageTreat;
