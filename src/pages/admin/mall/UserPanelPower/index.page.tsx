import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserPanelPowerApi from "./api";
import AppUserPanelPowerEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { PowerTypeMap } from "./types";

const AppUserPanelPowerPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserPanelPowerApi.delAppUserPanelPower({ data: { ids: item.id } });
    message.success("删除面板权限成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppUserPanelPowerApi.getAppUserPanelPower({ params: p })}
        title={
          <AppUserPanelPowerEdit title={"新增面板权限"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppUserPanelPowerEdit>
        }
        search={[
          <Form.Item label="手机号" name={`mobile`}>
            <Input placeholder="请输入手机号" />
          </Form.Item>,
          <Form.Item label="权限类型" name={`powerType`}>
            <Select options={PowerTypeMap} placeholder="请选择权限类型" />
          </Form.Item>,
        ]}
        columns={[
          { title: "姓名", dataIndex: "user", render: (c) => c?.name },
          { title: "手机", dataIndex: "user", render: (c) => c?.mobile },
          { title: "权限类型", dataIndex: "powerType", render: (c) => PowerTypeMap.find((i) => i.value === c)?.label },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppUserPanelPowerPage;
