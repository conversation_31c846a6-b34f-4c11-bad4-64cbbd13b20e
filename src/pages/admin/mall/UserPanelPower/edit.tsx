import WeModal from "@/components/WeModal/WeModal";
import AppUserPanelPowerApi from "./api";
import { Col, Form, Input, Row, Select, message } from "antd";
import { PowerTypeMap } from "./types";
import { useSetState } from "react-use";
import Mall<PERSON>pi from "@/services/MallApi";

const layout = { row: 10, col: 24 };

const AppUserPanelPowerEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    users: [] as any[],
  });


  const getUser = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopUserSelect({ params });
    // console.log(res);
    let users: any[] = res?.list || [];
    users = users.map((n) => ({ value: n.id, label: n.organizeName + " - " + n.name }));
    setState({ users });
  };


  const handleOpen = async () => {
    getUser();
    form.resetFields();
    if (props.data) {
      const data = await AppUserPanelPowerApi.getAppUserPanelPowerInfo(props.data.id);
      data.powerType = [data.powerType];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.powerType = data.powerType.join(",");
    if (!data.id) {
      await AppUserPanelPowerApi.addAppUserPanelPower({ data });
      message.success("添加面板权限成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="员工" name={`sysUserId`} rules={[{ required: true, message: "请选择员工" }]}>
              <Select
                placeholder="请选择员工"
                options={state.users}
                showSearch
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`powerType`} label="权限类型" rules={[{ required: true, message: "请选择权限类型" }]} >
              <Select
                options={PowerTypeMap}
                placeholder="请选择权限类型"
                allowClear
                mode="multiple"
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserPanelPowerEdit;
