import { makeApi } from "@/utils/Api";

const AppUserTriggerRewardApi = {
    getAppUserTriggerReward: makeApi("get", `/api/appUserTriggerReward`),
    getAppUserTriggerRewardInfo: makeApi("get", `/api/appUserTriggerReward`, true),
    addAppUserTriggerReward: makeApi("post", `/api/appUserTriggerReward`),
    putAppUserTriggerReward: makeApi("put", `/api/appUserTriggerReward`),
    delAppUserTriggerReward: makeApi("delete", `/api/appUserTriggerReward`),
};

export default AppUserTriggerRewardApi;
