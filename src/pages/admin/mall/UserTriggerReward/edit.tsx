import WeModal from "@/components/WeModal/WeModal";
import AppUserTriggerRewardApi from "./api";
import { Button, Col, Form, Input, Radio, Row, Select, Table, message } from "antd";
import { InputNumber } from "antd";
import { useSetState } from "react-use";
import { GroupTypeMap, UserSourceMap, StateMap, TypeMap, RegisterSourceMap } from "./types";
import { GoodsSkuPicker } from "@/components/GoodsSkuPicker";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import CouponPicker from "@/components/CouponPicker";
import { CouponType } from "../member/types";
import dayjs from "dayjs";

const layout = { row: 10, col: 12 };

const AppUserTriggerRewardEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    product: null as any,
    coupon: null as any,
    types: [] as any[],
  });
  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserTriggerRewardApi.getAppUserTriggerRewardInfo(props.data.id);
      data.rewardImage = UploadV2.str2arr(data.rewardImage);
      form.setFieldsValue(data);
      let product = data.product;

      product = { ...data.product };
      product.productId = data.product?.id;
      product.productName = data.product?.name;
      product.productSpecificationId = data.productSpecification?.id;
      product.productSpecificationName = data.productSpecification?.name;

      setState({ product: product, coupon: data.coupon });
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    data.productId = state.product?.productId;
    data.productSpecificationId = state.product?.productSpecificationId;
    data.couponId = state.coupon?.id;
    data.rewardImage = UploadV2.arr2str(data.rewardImage);

    if (data.type == 10 && !data.productId) {
      message.error("请选择商品");
      return false;
    }

    if (data.type == 20 && !data.couponId) {
      message.error("请选择优惠券");
      return false;
    }

    if (!data.id) {
      await AppUserTriggerRewardApi.addAppUserTriggerReward({ data });
      message.success("添加用户触发奖励成功");
    }
    if (data.id) {
      await AppUserTriggerRewardApi.putAppUserTriggerReward({ data });
      message.success("修改用户触发奖励成功");
    }
    props.onOk?.();
  };

  const type = Form.useWatch("type", form);
  const isEdit = !!props.data?.id;

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`groupType`} label="奖励分组" rules={[{ required: true, message: "请选择奖励分组" }]}>
              <Select placeholder="请选择奖励分组" options={GroupTypeMap.map((n) => ({ label: n.label, value: n.value }))} disabled={isEdit} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item label="类型" name={"type"} initialValue={10} rules={[{ required: true, message: "请选择类型" }]}>
              <Radio.Group options={TypeMap.map((n) => ({ label: n.label, value: n.value }))} disabled={isEdit} />
            </Form.Item>
          </Col>

          {type == 10 && (
            <Col span={layout.col * 2}>
              <Form.Item label="项目" required>
                <div>
                  <GoodsSkuPicker
                    params={{ property: 2, isAll: 1 }}
                    max={1}
                    onOk={(e) => {
                      const item = e?.[0];
                      setState({ product: item });
                      form.setFieldsValue({ name: item?.productName + "(" + item?.productSpecificationName + ")", coverImage: UploadV2.str2arr(item?.coverImage) });
                    }}
                  >
                    <Button disabled={isEdit}>选择项目</Button>
                  </GoodsSkuPicker>
                </div>
                {!!state.product && (
                  <div className="mt-10px">
                    <Table
                      rowKey="productSpecificationId"
                      size="small"
                      pagination={false}
                      dataSource={[state.product]}
                      columns={[
                        { title: "名称", dataIndex: "productName" },
                        { title: "规格", dataIndex: "productSpecificationName" },
                        { title: "分类", dataIndex: "shopCategoryName" },
                        {
                          title: "模式",
                          dataIndex: "productModel",
                          render: (c) => {
                            if (c == 1) {
                              return "项目单品";
                            } else if (c == 2) {
                              return "项目套餐";
                            } else if (c == 11) {
                              return "配送商品";
                            } else if (c == 12) {
                              return "服务商品";
                            }
                          },
                        },
                        {
                          title: "次数",
                          dataIndex: "includeNum",
                          render: (c) => (c ? c + "次" : "--"),
                        },
                        {
                          title: "划线价",
                          dataIndex: "oldPrice",
                          render: (c) => (c ?? "--") + "元",
                        },
                        {
                          title: "售价",
                          dataIndex: "salePrice",
                          render: (c) => (c ?? "--") + "元",
                        },
                        {
                          title: "库存",
                          render: (c) => (c.inventoryLimit == 1 ? c.inventoryCount : "无限"),
                        },
                        { title: "", render: () => (!isEdit ? <a onClick={() => setState({ product: null })}>删除</a> : "") },
                      ]}
                    ></Table>
                  </div>
                )}
              </Form.Item>
            </Col>
          )}

          {type == 20 && (
            <Col span={layout.col * 2}>
              <Form.Item label="优惠券" required>
                <div>
                  <CouponPicker
                    max={1}
                    onOk={(r) => {
                      const item = r?.[0];
                      setState({ coupon: item });
                      form.setFieldsValue({ name: item?.name, coverImage: UploadV2.str2arr(item?.coverImage) });
                    }}
                  >
                    <Button disabled={isEdit}>选择优惠券</Button>
                  </CouponPicker>
                </div>
                {!!state.coupon && (
                  <div className="mt-10px">
                    <Table
                      size="small"
                      rowKey="id"
                      pagination={false}
                      dataSource={[state.coupon]}
                      columns={[
                        { title: "名称", dataIndex: "name" },
                        {
                          title: "优惠方式",
                          dataIndex: "type",
                          render: (c) => CouponType.find((n) => n.id == c)?.name ?? "--",
                        },
                        {
                          title: "优惠内容",
                          render: (c) => (
                            <>
                              {c.type == 10 && `${c.preferentialMoney} 元`} {c.type == 20 && `${c.discount} 折`}
                            </>
                          ),
                        },
                        {
                          title: "消费门槛",
                          dataIndex: "minConsumeMoney",
                          render: (c) => <>{c ? `${c} 元` : "无门槛"}</>,
                        },
                        {
                          title: "失效方式",
                          render: (c) => (
                            <>
                              {c.effectiveStyle == 10 &&
                                `领取后${c.effectiveDeferredDays}天生效，有效期${c.effectiveDays}天`}
                              {c.effectiveStyle == 20 &&
                                `从 ${dayjs(c.effectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(
                                  c.effectiveEndDate
                                ).format("YYYY-MM-DD")}`}
                            </>
                          ),
                        },
                        { title: "", render: () => (!isEdit ? <a onClick={() => setState({ coupon: null })}>删除</a> : "") },
                      ]}
                    ></Table>
                  </div>
                )}
              </Form.Item>
            </Col>
          )}
          {type == 30 && (
            <>
              <Col span={layout.col}>
                <Form.Item
                  label="抽奖次数"
                  name={"includeNum"}
                  initialValue={1}
                  rules={[{ required: true, message: "请输入抽奖次数" }]}
                >
                  <InputNumber className="w-200px" addonAfter="次" min={1} max={1000} />
                </Form.Item>
              </Col>
              <Col />
            </>
          )}
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          {[10, 20].includes(type) ? (
            <>
              <Col span={layout.col}>
                <Form.Item name={`effectiveDays`} label="有效天数" rules={[{ required: true, message: "请输入有效天数" }]} initialValue={1}>
                  <InputNumber min={1} style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            </>
          ) : <Col />}
          <Col span={layout.col}>
            <Form.Item name={`userSourceLimit`} label="用户来源限定" rules={[{ required: true, message: "请选择" }]} initialValue={0}>
              <Radio.Group options={UserSourceMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`registerWithinDays`} label="建档限定天数" rules={[{ required: true, message: "请输入建档限定天数 0表示不限" }]}>
              <InputNumber min={0} placeholder="请输入建档限定天数 0表示不限" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`registerSourceLimit`} label="建档方式限定" rules={[{ required: true, message: "请选择" }]}>
              <Radio.Group options={RegisterSourceMap} />
            </Form.Item>
          </Col>
          <Col />
          <Col span={layout.col}>
            <Form.Item name={`selfObtainTag`} label="自己获得" rules={[{ required: false, message: "请选择状态" }]} initialValue={0}>
              <Radio.Group options={[
                { label: "是", value: 1 },
                { label: "否", value: 0 },
              ]} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`parentObtainTag`} label="上级获得" rules={[{ required: false, message: "请选择状态" }]} initialValue={0}>
              <Radio.Group options={[
                { label: "是", value: 1 },
                { label: "否", value: 0 },
              ]} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Form.Item label="奖励图片" name="rewardImage" rules={[{ required: false, message: "请上传奖励图片" }]}>
            <UploadV2 />
          </Form.Item>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserTriggerRewardEdit;
