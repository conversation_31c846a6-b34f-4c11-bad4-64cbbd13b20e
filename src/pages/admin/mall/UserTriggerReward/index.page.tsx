import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserTriggerRewardApi from "./api";
import AppUserTriggerRewardEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import { GroupTypeMap, UserSourceMap, RegisterSourceMap, StateMap, TypeMap } from "./types";

const AppUserTriggerRewardPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserTriggerRewardApi.delAppUserTriggerReward({ data: { ids: item.id } });
    message.success("删除用户触发奖励成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppUserTriggerRewardApi.getAppUserTriggerReward({ params: p })}
        title={
          <AppUserTriggerRewardEdit title={"新增用户触发奖励"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppUserTriggerRewardEdit>
        }
        search={[
          <Form.Item label="奖励分组" name={`groupType`}>
            <Select placeholder="请选择奖励分组" options={GroupTypeMap} allowClear />
          </Form.Item>,
          <Form.Item label="奖励类型" name={`type`}>
            <Select placeholder="请选择奖励类型" options={TypeMap} allowClear />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select placeholder="请选择状态" options={StateMap} allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "奖励分组", dataIndex: "groupType", render: (c) => GroupTypeMap.find((i) => i.value === c)?.label },
          { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((i) => i.value === c)?.label },
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "有效天数", dataIndex: "effectiveDays", render: (c, item) => [10, 20].includes(item.type) ? c : "--" },
          { title: "包含数量", dataIndex: "includeNum" },
          { title: "自己获得", dataIndex: "selfObtainTag", render: (c) => c ? "是" : "--" },
          { title: "上级获得", dataIndex: "parentObtainTag", render: (c) => c ? "是" : "--" },
          { title: "用户来源限定", dataIndex: "userSourceLimit", render: (c) => UserSourceMap.find((i) => i.value === c)?.label },
          { title: "注册限定天数", dataIndex: "registerWithinDays", render: (c) => c > 0 ? c : "--" },
          { title: "建档方式限定", dataIndex: "registerSourceLimit", render: (c) => RegisterSourceMap.find((i) => i.value === c)?.label },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => StateMap.find((i) => i.value === c)?.label },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppUserTriggerRewardEdit title={`编辑用户触发奖励`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppUserTriggerRewardEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppUserTriggerRewardPage;
