import { useSetState } from "react-use";
import MallTextTemplateApi from "./api";
import { Button, Drawer, Dropdown, message, Modal } from "antd";
import { CaretDownOutlined, FolderOpenOutlined, FolderOutlined, MenuOutlined, MoreOutlined, PlusCircleOutlined, SearchOutlined } from "@ant-design/icons";
import clsx from "clsx";
import { cloneElement, useEffect, useRef } from "react";
import UserApi from "@/services/UserApi";
import { EditNode } from "./edit";

const TextTemplatePage = (props: { children: any; title: any, module: any; onSelect?: Function; }) => {
  const tableRef = useRef<any>(null);

  const [state, setState] = useSetState({
    list: [] as any[],
    tree: [] as any[],

    openKeys: new Set<any>([]),
    selectKey: "",

    nodeopen: false,
    nodedata: null as any,

    manager: false as boolean, // 是否管理员
    open: false,
  });


  useEffect(() => {
    if (state.open) {
      fetchUserInfo();
      fetchTextTemplate();
    }
  }, [state.open]);

  const fetchUserInfo = async () => {
    const user = await UserApi.getUserInfo();
    setState({ manager: (user?.property == 9 || user?.property == 99) }); // 9 管理员 99 超级管理员
  };


  const templateSearch = (data: any) => {
    const json = data?.filterDataJson;
    tableRef.current?.onNodeSearch(json);
  };

  const fetchTextTemplate = async (data?: any) => {
    const params = { module: 10 };
    const res = await MallTextTemplateApi.getMallTextTemplate({ params });
    const list = res?.list || [];
    const tree = makeTree(list);

    // 默认展开所有节点
    const openKeys = new Set<any>([]);
    const collectAllKeys = (nodes: any[]) => {
      nodes.forEach(node => {
        openKeys.add(node.id);
        if (node.children) {
          collectAllKeys(node.children);
        }
      });
    };

    // 收集根节点和所有子节点的ID
    collectAllKeys(tree);
    tree.forEach(lv1 => {
      openKeys.add(lv1.id);
      if (lv1.children) {
        collectAllKeys(lv1.children);
      }
    });

    let selectKey;

    if (data?.id && data?.type == 2) {
      selectKey = data.id;
      templateSearch(data);
    }

    setState({ list, tree, openKeys, selectKey });
  };

  const toggleOpen = (id: string) => {
    const openKeys = new Set(state.openKeys);
    if (openKeys.has(id)) {
      openKeys.delete(id);
    } else {
      openKeys.add(id);
    }
    setState({ openKeys });
  };

  const makeTree = (list: any[]) => {
    const map: any = {};
    const arr: any[] = [];

    list = list || [];

    list.forEach((item) => {
      map[item.id] = item;
    });

    list.forEach((item) => {
      const parent = map[item.parentId];
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    const tree = [
      { id: 1, name: "公共模板", children: arr.filter((n) => n.ownType == 1) },
      { id: 2, name: "私有模板", children: arr.filter((n) => n.ownType == 2) },
    ];

    return tree;
  };

  const onDelNode = (data: any) => {
    let name =
      (
        {
          1: "目录",
          2: "模板",
        } as any
      )[data.type] || "数据";

    Modal.confirm({
      title: `确定要删除这条${name}吗？`,
      content: data.name,
      onOk: async () => {
        await MallTextTemplateApi.delMallTextTemplate({ data: { ids: data.id } });
        message.success("删除成功");
        fetchTextTemplate();
      },
    });
  };
  const onMoveNode = async (data: any, moveType: number) => {
    await MallTextTemplateApi.moveMallTextTemplate({ data: { id: data.id, moveType } });
    message.success("移动成功");
    fetchTextTemplate(data);
  };

  const onEditNode = (data: any) => {
    setState({ nodeopen: true, nodedata: data });
  };

  const renderNode = (data: any) => {
    const ison = state.openKeys.has(data.id);

    let items: any[] = [];

    if (data.type == 1) {
      items = [
        { key: "template-add", label: <span>新建模板</span> },
        { key: "dir-put", label: <span>编辑分组</span> },
        { key: "dir-del", label: <span className="text-red">删除分组</span> },
        { key: "dir-up", label: <span>上移</span> },
        { key: "dir-down", label: <span>下移</span> },
      ];
    } else if (data.type == 2) {
      items = [
        { key: "template-put", label: <span>编辑模板</span> },
        { key: "template-del", label: <span className="text-red">删除模板</span> },
        { key: "template-up", label: <span>上移</span> },
        { key: "template-down", label: <span>下移</span> },
      ];
    }

    // 处理选择按钮点击事件
    const handleSelect = (data: any) => {
      if (props.onSelect) {
        props.onSelect(data?.templateData);
      }
      setState({ open: false });
    };

    return (
      <div key={data.id}>
        <div
          className="py-1 px-2 my-1 rounded cursor-pointer flex [&:not([data-on=true])]:hover:bg-brand/10"
          data-on={state.selectKey == data.id}
          onClick={() => {
            toggleOpen(data.id);
            if (data.type == 2) {
              setState({ selectKey: data.id });
              templateSearch(data);
            }
          }}
        >
          {data.type == 1 && <>{ison ? <FolderOpenOutlined /> : <FolderOutlined />}</>}
          {data.type == 2 && <SearchOutlined />}
          <div className="flex-1 px-2 flex items-center">
            <span className="line-clamp-1">{data.name}</span>
            <span className="ml-1 flex-shrink-0 text-xs text-#999 data-[hd=true]:hidden" data-hd={data.type == 2}>
              ({data.children?.length || 0})
            </span>
          </div>
          <span className="ml-auto flex-shrink-0 text-xs text-#999 data-[hd=true]:hidden" data-hd={data.type == 1}>
            <Button type="link"  onClick={(e) => {
              e.stopPropagation();
              handleSelect(data);
            }}>
              选择
            </Button>
          </span>
          <Dropdown
            trigger={["click"]}
            placement="bottom"
            menu={{
              items,
              onClick: (e) => {
                e.domEvent.stopPropagation();

                switch (e.key) {
                  case "dir-put":
                    onEditNode(data);
                    break;
                  case "dir-del":
                    onDelNode(data);
                    break;
                  case "template-add":
                    setState({ nodeopen: true, nodedata: { parentId: data.id, type: 2, ownType: data.ownType } });
                    break;
                  case "template-put":
                    onEditNode(data);
                    break;
                  case "template-del":
                    onDelNode(data);
                    break;
                  case "dir-up":
                    onMoveNode(data, 1);
                    break;
                  case "dir-down":
                    onMoveNode(data, 2);
                    break;
                  case "template-up":
                    onMoveNode(data, 1);
                    break;
                  case "template-down":
                    onMoveNode(data, 2);
                    break;
                }
              },
            }}
          >
            {((state.manager && data.ownType == 1) || data.ownType == 2) && (
              <MoreOutlined onClick={(e) => e.stopPropagation()} />
            )}
          </Dropdown>
        </div>
        <div className="pl-4 hidden data-[on=true]:block" data-on={ison}>
          {data.children?.map(renderNode)}
        </div>
      </div>
    );
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={500}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <div className="flex gap-2">
          <div className="bg-#fff rounded p-2 w-full">
            <div className="text-sm text-#333 select-none">

              {state.tree.map((lv1) => {
                const lv1on = state.openKeys.has(lv1.id);

                return (
                  <div key={lv1.id}>
                    <div className="flex cursor-pointer py-1 pl-0 pr-2 my-1" onClick={() => toggleOpen(lv1.id)}>
                      <MenuOutlined className="text-sm text-#555 mr-2" />
                      <div className="fw-bold flex-1">{lv1.name}</div>

                      <Dropdown
                        trigger={["click"]}
                        placement="bottom"
                        menu={{
                          items: [
                            { key: "dir-add", label: "新建分组" },
                            { key: "template-add", label: "新建模板" },
                          ],
                          onClick: (e) => {
                            e.domEvent.stopPropagation();

                            if (e.key == "dir-add") {
                              setState({ nodeopen: true, nodedata: { type: 1, ownType: lv1.id } });
                            }

                            if (e.key == "template-add") {
                              setState({ nodeopen: true, nodedata: { type: 2, ownType: lv1.id } });
                            }
                          },
                        }}
                      >
                        {((state.manager && lv1.id == 1) || lv1.id == 2) && (
                          <PlusCircleOutlined className="text-sm text-#555 ml-2" onClick={(e) => e.stopPropagation()} />
                        )}
                      </Dropdown>
                      <CaretDownOutlined className={clsx("text-sm text-#555 ml-2 transition-all rotate-90", { "!rotate-0": lv1on })} />
                    </div>

                    <div className="pl-2 hidden data-[on=true]:block" data-on={lv1on}>
                      {lv1.children?.map(renderNode)}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="h-4"></div>
          </div>

          <EditNode open={state.nodeopen} onClose={() => setState({ nodeopen: false })} onOk={fetchTextTemplate} plist={state.list} data={state.nodedata} manager={state.manager} />
        </div>
      </Drawer>
    </div>
  );
}


export default TextTemplatePage;