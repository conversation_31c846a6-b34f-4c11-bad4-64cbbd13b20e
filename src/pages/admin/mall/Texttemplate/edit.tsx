import { Form, Input, message, Modal, Radio, Select } from "antd";
import MallTextTemplateApi from "./api";
import { useEffect, useMemo } from "react";

export const EditNode = (props: { open?: boolean; onClose?: any; onOk?: any; plist?: any[]; data?: any; manager?: boolean }) => {
  const [form] = Form.useForm();
  const isDir = props.data?.type == 1;
  const isNode = props.data?.type == 2;
  const ownType = Form.useWatch("ownType", form);

  useEffect(() => {
    if (props.open) {
      form.resetFields();
      if (props.data) {
        const { ...data } = props.data;
        form.setFieldsValue(data);
      }
    }
  }, [props.open]);

  const title = useMemo(() => {
    const data = props.data;
    if (data?.type == 1) return data?.id ? "编辑目录" : "新增目录";
    if (data?.type == 2) return data?.id ? "编辑模板" : "新增模板";
    return "--";
  }, [props.data]);

  const onOk = async () => {
    const val = await form.validateFields();
    const data = { ...val };

    let res = null;

    if (data.id) {
      res = await MallTextTemplateApi.putMallTextTemplate({ data });
      message.success("修改成功");
    } else {
      res = await MallTextTemplateApi.addMallTextTemplate({ data });
      message.success("添加成功");
    }

    const exdata = await MallTextTemplateApi.getMallTextTemplateInfo(res.id);

    props.onClose?.();
    props.onOk?.(exdata);
  };

  return (
    <Modal open={props.open} title={title} width={800} onCancel={props.onClose} onOk={onOk}>
      <div className="h-2"></div>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item hidden name={"id"}>
          <Input />
        </Form.Item>
        <Form.Item hidden name={"module"} initialValue={10}>
          <Input />
        </Form.Item>
        <Form.Item hidden name={"type"}>
          <Input />
        </Form.Item>
        <Form.Item hidden name={"templateData"}>
          <Input />
        </Form.Item>

        <Form.Item label="分组属性" name={"ownType"} rules={[{ required: true, message: "请选择分组属性" }]} initialValue={props.manager ? 1 : 2}  >
          <Radio.Group
            options={
              props.manager
                ? [
                  { label: "公共", value: 1 },
                  { label: "私有", value: 2 },
                ]
                : [
                  { label: "私有", value: 2 },
                ]
            }
            onChange={() => form.setFieldValue("parentId", "")}
          />
        </Form.Item>

        {isNode && (
          <Form.Item label="分组名称" name={"parentId"} rules={[{ required: false, message: "请选择分组名称" }]} initialValue={""}>
            <Select
              placeholder="请选择分组名称"
              options={[
                { label: "顶级分组", value: "" }, // 新增的顶级选项
                ...props.plist?.filter((n) => n.ownType == ownType && n.type == 1).map((n) => ({
                  label: n.name,
                  value: n.id,
                })) || []
              ]}
              allowClear
            />
          </Form.Item>
        )}

        {isDir && (
          <Form.Item label="目录名称" name={"name"} rules={[{ required: true, message: "请输入目录名称" }]}>
            <Input placeholder="请输入目录名称" />
          </Form.Item>
        )}

        {isNode && (<>
          <Form.Item label="模板名称" name={"name"} rules={[{ required: true, message: "请输入模板名称" }]}>
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          <Form.Item label="模板内容" name={"templateData"} rules={[{ required: true, message: "请输入模板内容" }]}>
            <Input.TextArea placeholder="请输入模板内容" rows={12} showCount/>
          </Form.Item>
        </>
        )}

      </Form>
    </Modal>
  );
};
