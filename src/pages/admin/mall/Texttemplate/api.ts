import { makeApi } from "@/utils/Api";

const MallTextTemplateApi = {
    getMallTextTemplate: makeApi("get", `/api/mallTextTemplate`),
    getMallTextTemplateInfo: makeApi("get", `/api/mallTextTemplate`, true),
    addMallTextTemplate: makeApi("post", `/api/mallTextTemplate`),
    putMallTextTemplate: makeApi("put", `/api/mallTextTemplate`),
    delMallTextTemplate: makeApi("delete", `/api/mallTextTemplate`),
    moveMallTextTemplate: makeApi("post", `/api/mallTextTemplate/move`),
};

export default MallTextTemplateApi;
