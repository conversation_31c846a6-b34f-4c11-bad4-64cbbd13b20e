import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Col, DatePicker, Form, Input, InputNumber, Radio, Row, Select, Switch, message } from "antd";
import { AdJumpType, StateMap } from "./types";
import DictPicker from "@/components/Units/DictPicker";
import RichText from "@/components/RichText";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import dayjs from "dayjs";
import { useState } from "react";
import LinkSelectorModal from "../../comm/IndexPageComponent/LinkSelectorModal";

const layout = { row: 10, col: 12 };

const AdEdit = (props: { children: any; title: string; data?: any; onOk?: Function }) => {
  const [form] = Form.useForm();
  const jumpType = Form.useWatch("jumpType", form);
  // 添加状态管理链接选择面板
  const [isModalVisible, setIsModalVisible] = useState(false);

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const { ...data } = props.data;
      //data.state = !!data.state;
      data.image = ImageUpload.serializer(data.image);
      if (data.jumpType === 2) {
        data.jumpKey = ImageUpload.serializer(data.jumpKey);
      }
      data.limitDate = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.image = ImageUpload.deserializer(data.image);
    if (data.jumpType === 2) {
      data.jumpKey = ImageUpload.deserializer(data.jumpKey);
    }

    //data.state = Number(data.state);
    data.timeLimit = Number(data.timeLimit);
    const limitDate = formatDateRange(data.limitDate, "YYYY-MM-DD HH:mm:ss");
    data.startDate = limitDate.start;
    data.endDate = limitDate.end;
    delete data.limitDate;

    if (data.id) {
      await MallApi.putAds({ data });
    } else {
      await MallApi.addAds({ data });
    }

    message.success("操作成功");
    props.onOk?.();
  };

  // 添加显示和处理链接选择模态框的函数
  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = (selectedLink: string) => {
    // 解析链接值（假设格式为 "名称|链接"）
    const [_, linkValueTemp] = selectedLink.split('|');

    form.setFieldsValue({
      jumpKey: linkValueTemp || selectedLink
    });

    setIsModalVisible(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={"id"} hidden>
          <Input></Input>
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="显示位置" name="showPositionId" rules={[{ required: true, message: "请选择显示位置" }]}>
              <DictPicker type="IndexAdv" placeholder="请选择显示位置" disabled={!!props.data?.id} />
            </Form.Item>
          </Col>
          <Col span={layout.col} />
          <Col span={layout.col * 2}>
            <Form.Item
              label="图片"
              name="image"
              valuePropName="fileList"
              rules={[{ required: true, message: "请上传图片" }]}
            >
              <ImageUpload></ImageUpload>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="跳转类型"
              name="jumpType"
              rules={[{ required: true, message: "请选择跳转类型" }]}
              initialValue={0}
            >
              <Select options={AdJumpType} fieldNames={{ label: "name", value: "id" }} placeholder="请选择跳转类型" />
            </Form.Item>
          </Col>
          <Col span={layout.col} />
          {jumpType == 1 && (
            <Col span={layout.col * 2}>
              <Form.Item label="URL地址" name="jumpKey" rules={[{ required: true, message: "请输入URL地址" }]}>
                <Input
                  placeholder="请输入URL地址"
                  addonAfter={
                    <a onClick={showModal}>
                      选择
                    </a>
                  }
                />
              </Form.Item>
            </Col>
          )}
          {jumpType == 2 && (
            <Col span={layout.col * 2}>
              <Form.Item
                label="关联图片"
                name="jumpKey"
                valuePropName="fileList"
                rules={[{ required: true, message: "请上传图片" }]}
              >
                <ImageUpload></ImageUpload>
              </Form.Item>
            </Col>
          )}
          {jumpType == 3 && (
            <Col span={layout.col * 2}>
              <Form.Item label="图文内容" name="jumpKey">
                <RichText></RichText>
              </Form.Item>
            </Col>
          )}
          {jumpType == 4 && (
            <Col span={layout.col * 2}>
              <Form.Item label="商品ID" name="jumpKey" rules={[{ required: true, message: "请输入商品ID" }]}>
                <Input placeholder="请输入商品ID" />
              </Form.Item>
            </Col>
          )}
          <Col span={layout.col * 2}>
            <Form.Item label="限时开启">
              <div style={{ display: "flex", alignItems: "center" }}>
                <div style={{ marginRight: 20 }}>
                  <Form.Item noStyle name={`timeLimit`} valuePropName="checked" initialValue={false}>
                    <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                  </Form.Item>
                </div>
                <div style={{ flex: 1 }}>
                  <Form.Item noStyle dependencies={["timeLimit"]}>
                    {(form) => {
                      const value = form.getFieldValue("timeLimit");
                      return (
                        !!value && (
                          <Form.Item
                            noStyle
                            name={`limitDate`}
                            initialValue={[]}
                            rules={[
                              {
                                required: true,
                                message: "请选择限时时间",
                              },
                            ]}
                          >
                            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} showTime />
                          </Form.Item>
                        )
                      );
                    }}
                  </Form.Item>
                </div>
              </div>
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              label="状态"
              name="state"
              rules={[{ required: true, message: "请选择状态" }]}
              initialValue={1}
            >
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item label="排序" name="sort" initialValue={999}>
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      {/* 添加链接选择模态框 */}
      <LinkSelectorModal
        visible={isModalVisible}
        onCancel={handleCancel}
        onOk={handleOk}
        linkType="page" // 根据实际需求设置链接类型
      />
    </WeModal >
  );
};

export default AdEdit;
