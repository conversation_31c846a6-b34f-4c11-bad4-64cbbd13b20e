import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { useMount } from "react-use";
import AdEdit from "./Edit";
import { useRef } from "react";
import { Form, Image, Popconfirm, Select, Space, message } from "antd";
import { renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { AdJumpType, StateMap } from "./types";
import DictPicker from "@/components/Units/DictPicker";

const Ads = () => {
  const tableRef = useRef<WeTableRef>(null);

  useMount(() => { });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delAds({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <WeTable
      ref={tableRef}
      request={(p) => MallApi.getAds({ params: { ...p } })}
      title={
        <AdEdit title="添加广告" onOk={handleReload}>
          <WeTable.AddBtn></WeTable.AddBtn>
        </AdEdit>
      }
      search={[
        <Form.Item label="显示位置" name={`showPositionId`}>
          <DictPicker type="IndexAdv" placeholder="请选择显示位置" />
        </Form.Item>,
        <Form.Item label="状态" name={`state`}>
          <Select options={StateMap} placeholder="请选择状态" allowClear />
        </Form.Item>,
      ]}
      columns={[
        { title: "位置", dataIndex: "showPositionName" },
        {
          title: "图片",
          dataIndex: "image",
          render: (c) => <Image src={c} style={{ maxWidth: 100, maxHeight: 100 }} />,
        },
        { title: "状态", dataIndex: "state", render: (c) => (StateMap.find((item) => item.value === c)?.label || "--") },
        { title: "跳转类型", dataIndex: "jumpType", render: (c) => renderTag(AdJumpType, c) },
        {
          title: "跳转关键字",
          width: 550,
          render: (c) => {
            if (c.jumpType == 1) {
              return (
                <a href={c.jumpKey} target="_blank">
                  {c.jumpKey}
                </a>
              );
            } else if (c.jumpType == 2) {
              return <Image src={c.jumpKey} style={{ maxWidth: 100, maxHeight: 100 }} />;
            } else {
              return c.jumpKey;
            }
          },
        },
        {
          title: "有效期",
          render: (c) => (
            <>
              {c.timeLimit == 0 && "永久"}
              {c.timeLimit == 1 &&
                `${dayjs(c.startDate).format("YYYY-MM-DD")} 至 ${dayjs(c.endDate).format(
                  "YYYY-MM-DD"
                )}`}
            </>
          ),
        },
        { title: "排序", dataIndex: "sort" },
        {
          title: "创建时间",
          dataIndex: "createDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
        },
        {
          title: "操作",
          render: (item) => {
            return (
              <Space>
                <AdEdit title={`编辑广告`} data={item} onOk={handleReload}>
                  <a>编辑</a>
                </AdEdit>
                <Popconfirm title={`确定要删除这条数据吗?`} onConfirm={() => handleDel(item)}>
                  <a>删除</a>
                </Popconfirm>
              </Space>
            );
          },
        },
      ]}
    />
  );
};

export default Ads;
