import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, Select, message } from "antd";
import { useSetState } from "react-use";

const DoctorEdit = (props: { children: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    staff: [] as any[],
  });

  const handleOpen = () => {
    fetchStaff();

    form.resetFields();
    form.setFieldsValue(props.data);
  };

  const fetchStaff = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getShopStaffForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item.user?.name,
      value: item.user?.id,
    }));
    setState({ staff: list });
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    await MallApi.putAppUserTriage({ data });
    message.success("转分诊成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={`转面诊`} onOpen={handleOpen} width={400} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Form.Item label="治疗师" name={`doctorId`}>
          <Select options={state.staff} showSearch allowClear optionFilterProp="label" placeholder="请选择治疗师" />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default DoctorEdit;
