import WeTable, { WeTableRef } from "@/components/WeTable";
import MallA<PERSON> from "@/services/MallApi";
import { DatePicker, Form, Select, Space, Typography } from "antd";
import DictPicker from "@/components/Units/DictPicker";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useRef } from "react";
import DoctorEdit from "./DoctorEdit";
import UserApi from "@/services/UserApi";
import { useMount, useSetState } from "react-use";
import ConsultLogEdit from "@/components/UserManager/ConsultLogEdit";
import AccessDropActions from "@/components/AccessList/AccessDropActions";
import OrderCreatorNew from "@/components/OrderCreatorNew";

const TriageConsult = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    user: null as any,
  });

  useMount(() => {
    fetchUser();
  });

  // useEffect(() => {
  //   // console.log(state.user?.id);
  //   if (!state.user?.id) return;
  //   handleReload();
  // }, [state.user?.id]);

  // const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    // console.log(user);
    setState({ user });
  };

  // const handleJieDai = async (item: any) => {
  //   const data = { id: item.id, consultState: 20 };
  //   await MallApi.putAppUserTriageStateZixun({ data });
  //   message.success("接待成功");
  //   handleReload();
  // };

  // const handleEnd = async (item: any) => {
  //   const data = { id: item.id, consultState: 30 };
  //   await MallApi.putAppUserTriageStateZixun({ data });
  //   message.success("结束成功");
  //   handleReload();
  // };

  return (
    <WeTable
      autoLoad={false}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      request={(params) => MallApi.getAppUserTriage({ params })}
      params={{ adviserId: state.user?.id }}
      search={[
        <Form.Item label="分诊时间" name={`CreateDate`} initialValue={[dayjs().startOf("day"), dayjs().endOf("day")]}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="会员" name={`shopVipUserId`}>
          <VipPickerInput />
        </Form.Item>,
        // <Form.Item label="咨询状态" name={`consultState`}>
        //   <Select
        //     options={ConsultState.map((n) => ({ label: n.name, value: n.id }))}
        //     allowClear
        //     showSearch
        //     optionFilterProp="label"
        //     placeholder="请选择咨询状态"
        //   />
        // </Form.Item>,
        <Form.Item label="成交状态" name={`transactionState`}>
          <Select
            options={[
              { label: "未成交", value: 0 },
              { label: "成交", value: 1 },
            ]}
            optionFilterProp="label"
            placeholder="请选择成交状态"
            allowClear
            showSearch
          />
        </Form.Item>,
        // <Form.Item label="咨询时间" name={`ConsultReceptionTime`}>
        //   <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        // </Form.Item>,
        <Form.Item label="就诊类型" name={`triageTypeId`}>
          <DictPicker type="triageType" placeholder="请选择就诊类型" />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "vipUser",
          render: (c) => (
            c ? <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
              : "--"
          ),
        },
        // { title: "门店", dataIndex: "shopName" },
        // { title: "会员号", dataIndex: "vipUser", render: (c) => c?.vipCard },
        // { title: "手机", dataIndex: "vipUser", width: 120, render: (c) => c?.mobile },

        { title: "所属客服", dataIndex: "adviserName" },
        { title: "所属医生", dataIndex: "doctorName" },
        { title: "所属治疗师", dataIndex: "nurseName" },

        { title: "就诊类型", dataIndex: "triageTypeName" },
        { title: "预约号", dataIndex: "reservationSerialNo" },
        // { title: "咨询时间", dataIndex: "consultReceptionTime", render: (c) => fdate(c) },
        {
          title: "咨询备注",
          dataIndex: "consultContent",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
        {
          title: "分诊时间",
          dataIndex: "createDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
        },
        {
          title: "分诊备注",
          dataIndex: "content",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },

        { title: "成交状态", dataIndex: "transactionState", render: (c) => (c ? "成交" : "未成交") },
        // { fixed: "right", title: "咨询状态", dataIndex: "consultState", render: (c) => renderTag(ConsultState, c) },
        {
          width: 300,
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              {/* <Popconfirm title={`接待当前用户？ - ${item.vipUser?.name}`} onConfirm={() => handleJieDai(item)}>
                <Typography.Link disabled={!(item.consultState == 10)}>接待</Typography.Link>
              </Popconfirm> */}

              <OrderCreatorNew userId={item.shopVipUserId} params={{ triageId: item.id }}>
                <Typography.Link>开单</Typography.Link>
              </OrderCreatorNew>

              <ConsultLogEdit
                title={`意向推荐`}
                data={{ id: item.consultId, shopVipUserId: item.shopVipUserId, triageId: item.id }}
                onOk={handleReload}
              >
                <Typography.Link>意向推荐</Typography.Link>
              </ConsultLogEdit>

              <DoctorEdit data={item} onOk={handleReload}>
                <Typography.Link disabled={![20, 30].includes(item.consultState)}>转面诊</Typography.Link>
              </DoctorEdit>

              {/* <Popconfirm title={`咨询结束？`} onConfirm={() => handleEnd(item)}>
                <Typography.Link disabled={!(item.consultState == 20)}>咨询结束</Typography.Link>
              </Popconfirm> */}

              <AccessDropActions userId={item.shopVipUserId} onOk={handleReload} />
            </Space>
          ),
        },
      ]}
    />
  );
};

export default TriageConsult;
