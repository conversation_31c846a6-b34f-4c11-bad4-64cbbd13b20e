.page {
  line-height: 1.3;

  :global {
    .ant-table-cell {
      background: #fff !important;
      &::before {
        display: none;
      }
    }
  }
}

.header {
  margin-bottom: 10px;
}

.grid0 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;

  .left {
    grid-column: span 2;

    > div {
      margin-bottom: 10px;
    }
  }

  .right {
    > div {
      margin-bottom: 10px;
    }
  }
}

.grid1 {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-template-rows: repeat(3, 1fr);
  grid-gap: 10px;
}

.grid2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
}

.grid3 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
}

.list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);

  .item {
    border-right: 1px solid #eee;
    margin-right: 20px;

    &:last-child {
      margin-right: 0;
      border-right: 0;
    }

    .tit {
      font-size: 12px;
      color: #999;
    }
    .num {
      padding: 4px 0;
      font-size: 24px;
      color: #000;
    }
    em {
      margin-left: 4px;
      font-style: normal;
      font-size: 0.6em;
    }
    b {
      font-weight: normal;
      margin-right: 4px;
    }
  }
}
