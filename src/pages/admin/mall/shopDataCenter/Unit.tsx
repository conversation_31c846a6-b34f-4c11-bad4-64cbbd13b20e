import { CSSProperties, useEffect } from "react";
import Style from "./Unit.module.scss";
import { useSetState } from "react-use";
import api from "@/utils/Api";
import { AxiosRequestConfig } from "axios";
import { ConfigProvider, Spin, Table, TableProps } from "antd";

export const useRequest = (config: AxiosRequestConfig) => {
  const [state, setState] = useSetState({
    data: undefined as any,
    loading: false,
  });

  useEffect(() => {
    if (!config || !config.url) return; // 空配置不请求
    fetch();
  }, [JSON.stringify(config)]);

  const fetch = async () => {
    setState({ loading: true });
    const res = await api(config).finally(() => setState({ loading: false }));
    // const res: any = setTimeout(() => setState({ loading: false }), 3000);
    setState({ data: res });
  };

  return { data: state.data, loading: state.loading } as const;
};

export const Card = (props: { title?: any; extra?: any; children?: any; loading?: boolean; style?: CSSProperties; className?: string }) => {
  return (
    <div className={Style.card} style={props.style}>
      <Spin spinning={!!props.loading}>
        <div className={Style.header}>
          {!!props.title && <div className={Style.title}>{props.title}</div>}
          {!!props.extra && <div className={Style.side}>{props.extra}</div>}
        </div>
        <div className={Style.cont}>{props.children}</div>
      </Spin>
    </div>
  );
};

export const UTable = (props: TableProps<any>) => {
  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {},
        },
      }}
    >
      <Table size="small" rowKey="id" pagination={false} {...props} />
    </ConfigProvider>
  );
};

export const genPie1Opts = (data: any[]) => {
  return {
    dataset: {
      dimensions: ["name", "totalAmount", "totalNum"],
      source: data || [],
    },
    // 金额 数量
    tooltip: {
      show: false,
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "10",
      top: "10",
    },
    series: [
      {
        type: "pie",
        radius: ["50%", "80%"],
        center: ["60%", "50%"],
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            formatter: "{a|{b}}\n{b| 金额：{@totalAmount} }\n{b| 数量：{@totalNum} }",
            rich: {
              a: {
                color: "#333",
                lineHeight: 40,
                align: "center",
                fontSize: 24,
              },
              b: {
                color: "#4C5058",
                lineHeight: 22,
                align: "center",
              },
            },
          },
        },
      },
    ],
  };
};

export const genBarOpts = (data: any[], sortType = 1) => {
  data = data || [];

  const key = sortType == 1 ? "totalAmount" : "totalNum";
  // const max = data[0]?.[key] || 0;
  data.sort((a, b) => b[key] - a[key]);
  const max = data[0]?.[key] || 0;
  const min = 0;

  data = data.map((n) => ({ ...n, tmp: max }));

  const res = {
    dataset: { source: data },
    grid: { containLabel: false, left: 20, right: 20, top: 20, bottom: 20 },
    xAxis: { show: false, type: "value", max: "dataMax" },
    yAxis: {
      type: "category",
      show: false,
      inverse: true,
    },
    visualMap: {
      orient: "horizontal",
      left: "center",
      min,
      max,
      show: false,
      dimension: key,
      inRange: {
        color: ["#65B581", "#FFCE34", "#FD665F"],
      },
      seriesIndex: 0,
    },
    series: [
      {
        type: "bar",
        barWidth: 14,
        encode: {
          x: key,
          y: "name",
        },
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
          borderRadius: 14,
        },
        itemStyle: {
          borderRadius: 14,
        },
        label: {
          show: true,
          position: [0, -20],
          formatter: "{b}",
          align: "left",
          fontSize: 14,
          fontWeight: 600,
        },
      },
      {
        type: "bar",
        colorBy: "data",
        animation: false,
        barWidth: 14,
        encode: {
          x: "tmp",
          y: "name",
        },
        itemStyle: {
          color: "rgba(0, 0, 0, 0)",
        },
        barGap: "-100%",
        label: {
          show: true,
          offset: [0, -20],
          formatter: "销售额：{amount|{@totalAmount}} 销售量：{num|{@totalNum}}",
          position: "insideRight",
          rich: {
            amount: {
              width: 70, // 设置销售额的固定宽度
              align: "left", // 可选：设置内容左对齐
            },
            num: {
              width: 35, // 设置销售量的固定宽度
              align: "left", // 可选：设置内容左对齐
            },
          },
        },
      },
    ],
  };

  console.log(111, res);
  return res;
};
