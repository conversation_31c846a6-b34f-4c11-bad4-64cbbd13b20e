import { useEffect, useMemo, useRef } from "react";
import * as echarts from "echarts";

export const PieChart = (props: { data: any }) => {
  const divRef = useRef<any>(null);
  const chartRef = useRef<any>(null);

  useEffect(() => {
    const chart = (chartRef.current = echarts.init(divRef.current));

    chart.on("mouseover", (e) => {
      chart.dispatchAction({
        type: "downplay",
      });
      chart.dispatchAction({
        type: "highlight",
        dataIndex: e.dataIndex,
      });
    });

    return () => {
      chart.dispose();
    };
  }, []);

  const opts = useMemo(() => {
    let data: any[] = props.data || [];
    const total = data.reduce((p, c) => p + c.totalAmount, 0);
    data = data.map((item) => {
      const rate = Math.floor((item.totalAmount / total || 0) * 100);
      const name2 = item.name + " (" + rate + "%)";
      return { ...item, name2 };
    });

    return {
      dataset: {
        dimensions: ["name2", "totalAmount", "totalNum"],
        source: data,
      },
      legend: {
        orient: "vertical",
        left: "10",
        top: "10",
      },
      series: [
        {
          type: "pie",
          radius: ["50%", "80%"],
          center: ["60%", "50%"],
          legendHoverLink: false,
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: "#fff",
            borderWidth: 2,
          },
          label: {
            show: false,
            position: "center",
            formatter: "{a|{@name2}}\n{b| 金额：{@totalAmount} }\n{b| 数量：{@totalNum} }",
            rich: {
              a: {
                color: "#333",
                lineHeight: 40,
                align: "center",
                fontSize: 24,
              },
              b: {
                color: "#4C5058",
                lineHeight: 22,
                align: "center",
              },
            },
          },
          emphasis: {
            label: {
              show: true,
            },
          },
        },
      ],
    };
  }, [props.data]);

  useEffect(() => {
    const chart = chartRef.current;
    chart.setOption(opts);
    chart.dispatchAction({
      type: "downplay",
    });
    chart.dispatchAction({
      type: "highlight",
      dataIndex: 0,
    });
  }, [opts]);

  return <div style={{ height: 360 }} ref={divRef}></div>;
};
