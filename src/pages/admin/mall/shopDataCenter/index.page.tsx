import { Button, DatePicker, Empty, Form, Radio, Select, Table, Tooltip } from "antd";
import Style from "./index.module.scss";
import { useMount, useSetState } from "react-use";
import dayjs from "dayjs";
import { DatePresetRanges } from "@/utils/Tools";
import { Card, UTable, genBarOpts, useRequest } from "./Unit";
import { useMemo } from "react";
import ECharts from "./ECharts";
import { QuestionCircleOutlined } from "@ant-design/icons";
import { PieChart } from "./PieChart";
import MallApi from "@/services/MallApi";
import UserApi from "@/services/UserApi";

const DataCenter2 = () => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    date: [dayjs().startOf("day"), dayjs().endOf("day")] as any,
    updateKey: 0,
    updateTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
    sortType: 1,
    shops: [] as any[],
    shopId: "",
    user: null as any,
  });

  useMount(() => {
    fetchUser();
    fetchShops();
  });

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user, shopId: user?.currentShopId || "" });
  };

  const fetchShops = async () => {
    const res = await MallApi.getShopListForManage();
    let list: any[] = res.list || [];
    list = list.map((n) => ({ label: n.name, value: n.id }));
    setState({ shops: list });
  };

  const params = useMemo(() => {
    const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : undefined);
    return { shopId: state.shopId, startCreateDate: fdate(state.date[0]), endCreateDate: fdate(state.date[1]), _update: state.updateKey };
  }, [state.shopId, state.date, state.updateKey]);

  const num2Rate = (n: any) => (n ? n * 100 : 0).toFixed(2) + "%";
  const panCustomer = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_customer_total", params };
    }, [state.shopId, params])
  );

  const panTransaction = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_transaction_total", params };
    }, [state.shopId, params])
  );

  const panReservation = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_reservation_total", params };
    }, [state.shopId, params])
  );

  const chartCustomerSourceOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_customer_source_order_total", params };
    }, [state.shopId, params])
  );
  const chartCustomerChannelOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_customer_channel_order_total", params };
    }, [state.shopId, params])
  );

  const chartProductCategory = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_category_total", params };
    }, [state.shopId, params])
  );

  const chartProductRanking = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_product_total", params };
    }, [state.shopId, params])
  );

  const tableTriage = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_triage_total", params };
    }, [state.shopId, params])
  );

  const tableSourceOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_order_source_order_total", params };
    }, [state.shopId, params])
  );

  const tableDeveloperOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_developer_order_total", params };
    }, [state.shopId, params])
  );

  const tableAdviserOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_adviser_order_total", params };
    }, [state.shopId, params])
  );

  const tableDoctorServiceOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_doctor_service_order_total", params };
    }, [state.shopId, params])
  );

  const tableNurseServiceOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_nurse_service_order_total", params };
    }, [state.shopId, params])
  );

  const tableServiceOrder = useRequest(
    useMemo(() => {
      if (!state.shopId) return {};
      return { url: "/api/shopHome/query_shop_service_order_total", params };
    }, [state.shopId, params])
  );

  return (
    <div className={Style.page}>
      <Card style={{ marginBottom: 10 }}>
        <div style={{ display: "flex", alignItems: "center" }}>
          <Form form={form}>
            <Select
              style={{ width: 300 }}
              options={state.shops}
              value={state.shopId || undefined}
              placeholder="请选择门店"
              onChange={(value) => setState({ shopId: value })}
            />
            <Form.Item noStyle name={`SearchDate`} initialValue={DatePresetRanges.find((n) => n.label == "今天")?.value}>
              <DatePicker.RangePicker
                style={{ marginLeft: 10, width: 300 }}
                allowClear={false}
                presets={DatePresetRanges}
                //value={state.date}
                onChange={(e) => {
                  const start = e?.[0]?.startOf("day") || undefined;
                  const end = e?.[1]?.endOf("day") || undefined;
                  setState({ date: [start, end] });
                }}
              />
            </Form.Item>

            {"今天 昨天 本月 上月 今年".split(" ").map((s) => {
              const arr1 = DatePresetRanges.find((n) => n.label == s)?.value;
              const arr2 = form.getFieldValue("SearchDate");

              let ison = arr1?.[0]?.isSame(arr2?.[0]) && arr1?.[1]?.isSame(arr2?.[1]);

              return (
                <Button
                  className="ml-1"
                  key={s}
                  type={ison ? "primary" : "default"}
                  onClick={() => {
                    if (ison) {
                      form.setFieldValue("SearchDate", []);
                    } else {
                      form.setFieldValue("SearchDate", arr1);
                    }
                    setState({ date: arr1 });
                  }}
                >
                  {s}
                </Button>
              );
            })}
          </Form>

          <div style={{ marginLeft: "auto" }}>
            <a
              style={{ marginRight: 10 }}
              onClick={() =>
                setState({ updateKey: state.updateKey + 1, updateTime: dayjs().format("YYYY-MM-DD HH:mm:ss") })
              }
            >
              [刷新数据]
            </a>
            <span style={{ color: "#999" }}>更新时间：{state.updateTime}</span>
          </div>
        </div>
      </Card>

      <div className={Style.grid0}>
        <div className={Style.left}>
          <div className={Style.grid1}>
            <Card title={`顾客情况`} loading={panCustomer.loading}>
              <div className={Style.list}>
                <div className={Style.item}>
                  <Tooltip title="到院顾客：所有实际大欧安的顾客总数。当日多次到院只统计一次。">
                    <div className={Style.tit}>
                      到院顾客(人)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                    <div className={Style.num}>{panCustomer.data?.triageNum || 0}</div>
                  </Tooltip>
                </div>
                <div className={Style.item}>
                  <Tooltip title="成交顾客：所有到院实际消费的顾客数量。当日多次消费只统计一次。">
                    <div className={Style.tit}>
                      成交顾客(人)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{panCustomer.data?.transactionNum || 0}</div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="新客数量：首次到店消费的顾客数量，同一天多次消费仅统计一次。">
                    <div className={Style.tit}>
                      新客数量(人)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                    <div className={Style.num}>{panCustomer.data?.firstConsumeCustomerNum || 0}</div>
                  </Tooltip>
                </div>
                <div className={Style.item}>
                  <div className={Style.tit}>
                    <Tooltip title="划扣顾客：进行项目划扣的顾客数量，同一天多次划扣仅统计一次。">
                      <div className={Style.tit}>
                        划扣顾客(人)&nbsp;
                        <QuestionCircleOutlined />
                      </div>
                      <div className={Style.num}>{panCustomer.data?.deductionCustomerNum || 0}</div>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </Card>

            <Card title={`业绩/划扣情况`} loading={panTransaction.loading}>
              <div className={Style.list}>
                <div className={Style.item}>
                  <Tooltip title="营业总额：实际支付金额-退款总额。">
                    <div className={Style.tit}>
                      营业总额(CNY)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{panTransaction.data?.transactionAmount || 0}</div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="新客业绩：首次到店消费金额总额-退款总额。">
                    <div className={Style.tit}>
                      新客业绩(CNY)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{panTransaction.data?.firstConsumeBillAmount || 0}</div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="退款总额：订单退款金额总额。">
                    <div className={Style.tit}>
                      退款总额(CNY)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{panTransaction.data?.refundAmount || 0}</div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="划扣总额：进行项目划扣的金额总额。">
                    <div className={Style.tit}>
                      划扣总额(CNY)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{panTransaction.data?.deductionAmount || 0}</div>
                </div>

              </div>
            </Card>

            <Card title={`预约/回访情况`} loading={panReservation.loading}>
              <div className={Style.list}>
                <div className={Style.item}>
                  <div className={Style.tit}>实际到院 / 预约总数</div>
                  <div className={Style.num}>
                    {panReservation.data?.triageNum || 0} / {panReservation.data?.reservationNum || 0}
                  </div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="预约到院率：所有预约的顾客数量和实际到院的顾客数量比例。">
                    <div className={Style.tit}>
                      预约到院率(%)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{num2Rate(panReservation.data?.triageRate || 0)}</div>
                </div>
                <div className={Style.item}>
                  <div className={Style.tit}>已回访 / 应回访</div>
                  <div className={Style.num}>
                    {panReservation.data?.successAccessNum || 0} / {panReservation.data?.totalAccessNum || 0}
                  </div>
                </div>
                <div className={Style.item}>
                  <Tooltip title="回访进度：已回访和应回访数量比例。">
                    <div className={Style.tit}>
                      回访进度(%)&nbsp;
                      <QuestionCircleOutlined />
                    </div>
                  </Tooltip>
                  <div className={Style.num}>{num2Rate(panReservation.data?.accessRate || 0)}</div>
                </div>
              </div>
            </Card>
          </div>

          <div className={Style.grid2}>
            <Card
              title={
                <Tooltip title="顾客来源-销售总览：按照顾客来源统计，统计订单支付金额-退款金额。">
                  <div className={Style.tit}>
                    顾客来源-销售总览&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={chartCustomerSourceOrder.loading}
            >
              {chartCustomerSourceOrder.data ? (
                <PieChart data={chartCustomerSourceOrder.data} />
              ) : (
                <div className="flex h-250px justify-center  items-center">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </Card>

            <Card
              title={
                <Tooltip title="渠道类型-销售总览：按照渠道类型统计，统计订单支付金额-退款金额。">
                  <div className={Style.tit}>
                    渠道类型-销售总览&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={chartCustomerChannelOrder.loading}
            >
              {chartCustomerChannelOrder.data ? (
                <PieChart data={chartCustomerChannelOrder.data} />
              ) : (
                <div className="flex h-250px justify-center  items-center">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </Card>
            {/* 
            <Card
              title={
                <Tooltip title=" 项目分类-销售总览：按照项目分类汇总，统计项目销售金额(包含退款部分)。">
                  <div className={Style.tit}>
                    项目分类-销售总览&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={chartProductCategory.loading}
            >
              {chartProductCategory.data ? (
                <PieChart data={chartProductCategory.data} />
              ) : (
                <div className="flex h-250px justify-center  items-center">
                  <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
                </div>
              )}
            </Card> */}
          </div>

          <div className={Style.grid3}>
            <Card
              title={
                <Tooltip title="销售业绩-来源：统计订单支付金额-退款总额。">
                  <div className={Style.tit}>
                    销售业绩-来源&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={tableSourceOrder.loading}
            >
              <UTable
                dataSource={tableSourceOrder.data}
                columns={[
                  { title: "来源", dataIndex: "name", width: "16.5%" },
                  { title: "收款(单)", dataIndex: "payNum", width: "16.5%" },
                  { title: "收款(CNY)", dataIndex: "payAmount", width: "16.5%" },
                  { title: "退款(单)", dataIndex: "refundNum", width: "16.5%" },
                  { title: "退款(CNY)", dataIndex: "refundAmount", width: "16.5%" },
                  { title: "业绩(CNY)", dataIndex: "actualAmount", width: "16.5%" },
                  // { title: "业绩占比(%)", dataIndex: "rate", render: (c) => num2Rate(c) },
                ]}
                summary={(pd) => {
                  if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);

                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("payNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("payAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={3}>
                          <span className="c-red-5 fw-bold">{calc1("refundNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4}>
                          <span className="c-red-5 fw-bold">{calc2("refundAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={5}>
                          <span className="c-red-5 fw-bold">{calc2("actualAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>

            <Card title={`划扣业绩-门店`} loading={tableServiceOrder.loading}>
              <UTable
                dataSource={tableServiceOrder.data}
                columns={[
                  { title: "销售门店", dataIndex: "name", width: "33%" },
                  { title: "次数", dataIndex: "totalNum", width: "33%" },
                  { title: "业绩(CNY)", dataIndex: "totalAmount", width: "33%" },
                ]}
                summary={(pd) => {
                   if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);
                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("totalNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("totalAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>

            <Card
              title={
                <Tooltip title="销售业绩-开发：统计订单支付金额-退款总额。">
                  <div className={Style.tit}>
                    销售业绩-开发&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={tableDeveloperOrder.loading}
            >
              <UTable
                dataSource={tableDeveloperOrder.data}
                columns={[
                  { title: "姓名", dataIndex: "name", width: "16.5%" },
                  { title: "收款(单)", dataIndex: "payNum", width: "16.5%" },
                  { title: "收款(CNY)", dataIndex: "payAmount", width: "16.5%" },
                  { title: "退款(单)", dataIndex: "refundNum", width: "16.5%" },
                  { title: "退款(CNY)", dataIndex: "refundAmount", width: "16.5%" },
                  { title: "业绩(CNY)", dataIndex: "actualAmount", width: "16.5%" },
                  // { title: "业绩占比(%)", dataIndex: "rate", render: (c) => num2Rate(c) },
                ]}
                summary={(pd) => {
                   if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);

                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("payNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("payAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={3}>
                          <span className="c-red-5 fw-bold">{calc1("refundNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4}>
                          <span className="c-red-5 fw-bold">{calc2("refundAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={5}>
                          <span className="c-red-5 fw-bold">{calc2("actualAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>

            <Card title={`划扣业绩-医生`} loading={tableDoctorServiceOrder.loading}>
              <UTable
                dataSource={tableDoctorServiceOrder.data}
                columns={[
                  { title: "姓名", dataIndex: "name", width: "33%" },
                  { title: "单数", dataIndex: "totalNum", width: "33%" },
                  { title: "业绩(CNY)", dataIndex: "totalAmount", width: "33%" },
                ]}
                summary={(pd) => {
                   if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);

                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("totalNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("totalAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>


            <Card
              title={
                <Tooltip title="销售业绩-顾问：统计订单支付金额-退款总额。">
                  <div className={Style.tit}>
                    销售业绩-顾问&nbsp;
                    <QuestionCircleOutlined />
                  </div>
                </Tooltip>
              }
              loading={tableAdviserOrder.loading}
            >
              <UTable
                dataSource={tableAdviserOrder.data}
                columns={[
                  { title: "姓名", dataIndex: "name", width: "16.5%" },
                  { title: "收款(单)", dataIndex: "payNum", width: "16.5%" },
                  { title: "收款(CNY)", dataIndex: "payAmount", width: "16.5%" },
                  { title: "退款(单)", dataIndex: "refundNum", width: "16.5%" },
                  { title: "退款(CNY)", dataIndex: "refundAmount", width: "16.5%" },
                  { title: "业绩(CNY)", dataIndex: "actualAmount", width: "16.5%" },
                  // { title: "业绩占比(%)", dataIndex: "rate", render: (c) => num2Rate(c) },
                ]}
                summary={(pd) => {
                   if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);

                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("payNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("payAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={3}>
                          <span className="c-red-5 fw-bold">{calc1("refundNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4}>
                          <span className="c-red-5 fw-bold">{calc2("refundAmount")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={5}>
                          <span className="c-red-5 fw-bold">{calc2("actualAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>

            <Card title={`划扣业绩-治疗师`} loading={tableNurseServiceOrder.loading}>
              <UTable
                dataSource={tableNurseServiceOrder.data}
                columns={[
                  { title: "姓名", dataIndex: "name", width: "33%" },
                  { title: "单数", dataIndex: "totalNum", width: "33%" },
                  { title: "业绩(CNY)", dataIndex: "totalAmount", width: "33%" },
                ]}
                summary={(pd) => {
                   if (!pd?.length) return null;
                  const calc1 = (key: string) => pd.reduce((p, c) => p + c[key] * 100, 0) / 100;
                  const calc2 = (key: string) => (pd.reduce((p, c) => p + c[key] * 100, 0) / 100)?.toFixed(2);
                  return (
                    <>
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0}>
                          <span className="fw-bold">合计</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1}>
                          <span className="c-red-5 fw-bold">{calc1("totalNum")}</span>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2}>
                          <span className="c-red-5 fw-bold">{calc2("totalAmount")}</span>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </>
                  );
                }}
              />
            </Card>
          </div>
        </div>

        <div className={Style.right}>
          <Card
            title={`到院数据`}
            loading={tableTriage.loading}
            extra={
              <>
                <span>
                  到院总数: <b>{tableTriage.data?.triageNum || 0}</b>人
                </span>
              </>
            }
          >
            <UTable
              rowKey="typeId"
              dataSource={tableTriage.data?.list || []}
              columns={[
                { title: "就诊类型", dataIndex: "typeName" },
                { title: "人数", dataIndex: "totalCount" },
              ]}
            />
          </Card>

          <Card
            title={
              <Tooltip title=" 项目分类-销售总览：按照项目分类汇总，统计项目销售金额(包含退款部分)。">
                <div className={Style.tit}>
                  项目分类-销售总览&nbsp;
                  <QuestionCircleOutlined />
                </div>
              </Tooltip>
            }
            loading={chartProductCategory.loading}
          >
            <UTable
              rowKey="id"
              dataSource={chartProductCategory.data || []}
              columns={[
                { title: "分类名称", dataIndex: "name" },
                { title: "销售额", dataIndex: "totalAmount" },
                { title: "销售量", dataIndex: "totalNum" },
              ]}
            />
          </Card>

          <Card
            title={
              <Tooltip title="项目销售排行：按照项目汇总，统计订单金额(包含退款部分)。">
                <div className={Style.tit}>
                  项目销售排行&nbsp;
                  <QuestionCircleOutlined />
                </div>
              </Tooltip>
            }
            extra={
              <Radio.Group
                options={[
                  { label: "销售额", value: 1 },
                  { label: "销售量", value: 2 },
                ]}
                value={state.sortType}
                onChange={(e) => setState({ sortType: e.target.value })}
                optionType="button"
                buttonStyle="solid"
              />
            }
          >
            <div className="h-700px overflow-y-auto">
              {chartProductRanking.data?.list?.length ? (
                <ECharts
                  key={chartProductRanking.data?.list?.length}
                  option={genBarOpts(chartProductRanking.data?.list || [], state.sortType)}
                  style={{ height: 100 + chartProductRanking.data?.list?.length * 45 }}
                />
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DataCenter2;
