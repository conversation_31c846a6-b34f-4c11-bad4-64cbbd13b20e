import { CSSProperties, forwardRef, useEffect, useImperativeHandle, useRef } from "react";
import * as echarts from "echarts";

const ECharts = forwardRef((props: { option?: any; style?: CSSProperties }, ref) => {
  const divRef = useRef<any>(null);
  const chartRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({ chart: chartRef }));

  useEffect(() => {
    const chart = (chartRef.current = echarts.init(divRef.current));

    return () => {
      chart.dispose();
    };
  }, []);

  useEffect(() => {
    const option = props.option || {};
    chartRef.current?.setOption(option);
  }, [props.option]);

  return <div ref={divRef} style={props.style}></div>;
});

export default ECharts;
