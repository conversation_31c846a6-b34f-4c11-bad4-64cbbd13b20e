.card {
  display: flex;
  flex-flow: column;
  background: #fff;
  border-radius: 8px;
  padding: 10px;

  .header {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    &:empty {
      padding-bottom: 0;
    }
  }

  .side {
    margin-left: auto;
    font-size: 12px;
    color: #666;
  }

  .title {
    font-size: 14px;
    color: #000;
    font-weight: bold;
    // padding-bottom: 10px;
  }
  .cont {
    min-height: 0;
    flex: 1;
  }
}
