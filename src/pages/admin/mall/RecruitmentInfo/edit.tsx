import WeModal from "@/components/WeModal/WeModal";
import AppRecruitmentInfoApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";
import DictPicker from "@/components/Units/DictPicker";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import { StateMap } from "./types";
import RichText from "@/components/RichText";

const layout = { row: 10, col: 12 };

const AppRecruitmentInfoEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const data = await AppRecruitmentInfoApi.getAppRecruitmentInfoInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      data.Date = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.image = ImageUpload.deserializer(data.image);
    const Date = formatDateRange(data.Date);
    data.startDate = Date.start + " 00:00:00";
    data.endDate = Date.end + " 23:59:59";
    delete data.Date;
    if (!data.id) {
      await AppRecruitmentInfoApi.addAppRecruitmentInfo({ data });
      message.success("添加招募信息成功");
    }
    if (data.id) {
      await AppRecruitmentInfoApi.putAppRecruitmentInfo({ data });
      message.success("修改招募信息成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`typeId`} label="类型" rules={[{ required: true, message: "请输入类型" }]}>
              <DictPicker type="RecruitmentType" placeholder="请选择类型" disabled={!!props.data} />
            </Form.Item>
          </Col>
          <Col />
          <Col span={layout.col * 2}>
            <Form.Item name={`title`} label="标题" rules={[{ required: true, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>

          <Col span={layout.col * 2}>
            <Form.Item name={`image`} label="图片" rules={[{ required: true, message: "请上传图片" }]} valuePropName="fileList">
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`Date`} label="有效时间" rules={[{ required: true, message: "请选择有效时间" }]}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group
                options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: true, message: "请输入内容" }]}>
              <RichText />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppRecruitmentInfoEdit;
