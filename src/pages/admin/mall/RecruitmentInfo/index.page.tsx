import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppRecruitmentInfoApi from "./api";
import AppRecruitmentInfoEdit from "./edit";
import { Button, Form, Input, Popconfirm, Select, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, } from "@/utils/Tools";
import dayjs from "dayjs";
import DictPicker from "@/components/Units/DictPicker";
import { StateMap } from "./types";
import AppRecruitmentLogPage from "../RecruitmentLog/index.page";
import { FileTextOutlined } from "@ant-design/icons";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppRecruitmentInfoPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppRecruitmentInfoApi.delAppRecruitmentInfo({ data: { ids: item.id } });
    message.success("删除招募信息成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppRecruitmentInfoApi.getAppRecruitmentInfo({ params: p })}

        title={
          <Space>
            <AppRecruitmentInfoEdit title={"新增招募信息"} onOk={handleReload}>
              <WeTable.AddBtn />
            </AppRecruitmentInfoEdit>
            <AppRecruitmentLogPage title={"报名记录"}>
              <Button type="primary" icon={<FileTextOutlined />}>报名记录</Button>
            </AppRecruitmentLogPage>
          </Space>
        }
        search={[
          <Form.Item label="类型" name={`typeId`}>
            <DictPicker type="RecruitmentType" placeholder="请选择类型" />
          </Form.Item>,
          <Form.Item label="标题" name={`title`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select placeholder="请选择状态" options={StateMap} allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "类型", dataIndex: "typeName", render: (c) => c ? c : "--" },
          { title: "标题", dataIndex: "title", render: (c) => c ? c : "--" },
          {
            title: "有效期",
            render: (c) => (
              <>
                {dayjs(c.startDate).format("YYYY-MM-DD")} 至 {dayjs(c.endDate).format("YYYY-MM-DD")}
              </>
            ),
          },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppRecruitmentInfoEdit title={`编辑招募信息`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppRecruitmentInfoEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppRecruitmentInfoPage;
