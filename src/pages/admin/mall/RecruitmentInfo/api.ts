import { makeApi } from "@/utils/Api";

const AppRecruitmentInfoApi = {
    getAppRecruitmentInfo: makeApi("get", `/api/appRecruitmentInfo`),
    getAppRecruitmentInfoInfo: makeApi("get", `/api/appRecruitmentInfo`, true),
    addAppRecruitmentInfo: makeApi("post", `/api/appRecruitmentInfo`),
    putAppRecruitmentInfo: makeApi("put", `/api/appRecruitmentInfo`),
    delAppRecruitmentInfo: makeApi("delete", `/api/appRecruitmentInfo`),
};

export default AppRecruitmentInfoApi;
