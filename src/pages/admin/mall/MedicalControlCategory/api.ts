import { makeApi } from "@/utils/Api";

const MallMedicalControlCategoryApi = {
    getMallMedicalControlCategory: makeApi("get", `/api/mallMedicalControlCategory`),
    getMallMedicalControlCategoryInfo: makeApi("get", `/api/mallMedicalControlCategory`, true),
    addMallMedicalControlCategory: makeApi("post", `/api/mallMedicalControlCategory`),
    putMallMedicalControlCategory: makeApi("put", `/api/mallMedicalControlCategory`),
    delMallMedicalControlCategory: makeApi("delete", `/api/mallMedicalControlCategory`),
};

export default MallMedicalControlCategoryApi;
