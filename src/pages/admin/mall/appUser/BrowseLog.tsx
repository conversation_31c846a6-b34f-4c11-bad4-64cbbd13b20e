import { Drawer } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import AppUserBrowseLogPage from "../UserBrowseLog/index.page";
import { WeTableRef } from "@/components/WeTable";

export const BrowseLog = (props: { title: any, userId?: any; children?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });


  useEffect(() => {
    if (state.open) {
      tableRef.current?.reset();
    }
  }, [state.open]);

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} width={"70%"} title={props.title} onClose={() => setState({ open: false })}>
        <AppUserBrowseLogPage userId={props.userId} />
      </Drawer>
    </>
  );
};
