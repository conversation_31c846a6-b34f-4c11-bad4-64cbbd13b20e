import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, Switch, message } from "antd";

const UserEdit = (props: { children: any; title: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();

  const BSwitch = (props: any) => <Switch checked={!!props.value} onChange={(e) => props.onChange(Number(e))} />;

  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    // console.log(data);
    await MallApi.putAppUser({ data });
    message.success("编辑成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} onOk={handleSubmit} width={300} onOpen={handleOpen}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item hidden name={`id`}>
          <Input disabled />
        </Form.Item>
        <Form.Item label="是否可登录" name={`loginFlag`} initialValue={1}>
          <BSwitch />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default UserEdit;
