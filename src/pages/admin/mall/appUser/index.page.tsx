import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Button, DatePicker, Form, Input, Space } from "antd";
import dayjs from "dayjs";
import UserEdit from "./UserEdit";
import { useRef } from "react";
import { BrowseLog } from "./BrowseLog";
import { Api } from "@/utils/Api";
import { DatePresetRanges } from "@/utils/Tools";
import UserManager from "@/components/UserManager";
import { FileTextOutlined } from "@ant-design/icons";

const AppUser = () => {
  const tableRef = useRef<WeTableRef>(null);
  const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");
  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <WeTable
      ref={tableRef}
      // export="/api/appUser/exportExcel"
      request={(params) => MallApi.getAppUser({ params })}
      search={[
        <Form.Item label="用户名称" name={`name`}>
          <Input placeholder="请输入用户名称" />
        </Form.Item>,
        <Form.Item label="用户手机号" name={`mobile`}>
          <Input placeholder="请输入用户手机号" />
        </Form.Item>,
        <Form.Item label="注册时间" name={`CreateDate`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
      ]}
      title={
        <BrowseLog title={"浏览记录-全部"} userId={""} >
          <Button type="primary" icon={<FileTextOutlined />}>全部浏览记录</Button>
        </BrowseLog>
      }
      columns={[
        {
          title: "用户",
          render: (c) => (
            c ? <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
              : "--"
          ),
        },
        { title: "手机号", dataIndex: "mobile" },
        {
          title: "推荐人",
          dataIndex: "parentUser",
          render: (c) =>
            c ? (
              <UserManager userId={c?.id}>
                <a> {c?.name}</a>
              </UserManager>
            ) : (
              "--"
            ),
        },
        {
          title: "手机归属地",
          dataIndex: "mobileArea",
          render: (c, r) =>
            c ? (
              c
            ) : (
              <a
                onClick={async () => {
                  await Api({
                    method: "put",
                    url: `/api/mallShopVipUser/refresh_mobile_area`,
                    data: { id: r?.id },
                  });
                  handleReload();
                }}
              >
                获取归属地
              </a>
            ),
        },
        { title: "登录ip", dataIndex: "loginIp", render: (c) => c ? c : '--' },
        { title: "最近登录时间", dataIndex: "loginDate", sorter: true, render: (c) => fdate(c) },
        // { title: "城市", dataIndex: "cityName", render: (c) => c ? c : '--' },
        // { title: "区域", dataIndex: "areaName", render: (c) => c ? c : '--' },
        { title: "注册时间", dataIndex: "createDate", render: (c) => fdate(c) },
        /*  {
           title: "是否可登录",
           dataIndex: "loginFlag",
           render: (c) => (
             <>
               {c == 1 && <Tag color="green">启用</Tag>}
               {c == 0 && <Tag color="gray">禁用</Tag>}
             </>
           ),
         }, */
        {
          title: "操作",
          render: (item) => {
            return (
              <Space>
                <BrowseLog title={`浏览记录-${item.name}`} userId={item.id} >
                  <a>浏览记录</a>
                </BrowseLog>
                <UserEdit title={`编辑用户 - ${item.name}`} onOk={handleReload} data={item}>
                  <a>编辑</a>
                </UserEdit>
              </Space >
            );
          },
        },
      ]}
    />
  );
};

export default AppUser;
