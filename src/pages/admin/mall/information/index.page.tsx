import { Card, Col, Form, Input, Popconfirm, Row, Space, Tooltip, Tree, Typography, message } from "antd";
import Style from "./index.module.scss";
import WeTable, { WeTableRef } from "@/components/WeTable";
import { useMount, useSetState } from "react-use";
import EditCategory from "./EditCategory";
import { ApartmentOutlined, DeleteOutlined, FormOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { useEffect, useRef } from "react";
import EditItem from "./EditItem";
import MallInformationArticleApi from "./api";

const tabs = [
  { key: "10", tab: '药品', },
  { key: "11", tab: '仪器', },
  { key: "12", tab: '材料', },
  { key: "20", tab: '术后事项', },
];

const InformationPage = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    cates: [] as any[],
    cateId: null as any,
    type: "",
  });

  useMount(() => {
    setState({ type: tabs[0].key })
  });

  useEffect(() => {
    if (!state.type) return
    setState({ cateId: null })
    fetchCategory(state.type)
  }, [state.type])


  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchCategory = async (type: string) => {
    const params = { type, pageSize: 9999 };
    const res = await MallInformationArticleApi.getMallInformationCategory({ params });
    const cates = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ cates: cates });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await MallInformationArticleApi.delMallInformationCategory({ data });
    message.success("删除分类成功");
    fetchCategory(state.type);
  };

  const handleDelItem = async (item: any) => {
    const data = { ids: item.id };
    await MallInformationArticleApi.delMallInformationArticle({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={tabs}
        activeTabKey={state.type}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(type) => setState({ type })}
      />

      <Row wrap={false} gutter={10}>
        <Col flex={`300px`} style={{ minWidth: 0 }}>
          <Card
            bodyStyle={{ padding: 10 }}
            title={`分类`}
            style={{ height: "100%" }}
            extra={
              <EditCategory title={`添加分类`} type={state.type} onOk={() => fetchCategory(state.type)} tree={state.cates}>
                <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
              </EditCategory>
            }
          >
            <Tree.DirectoryTree
              className={Style.tree}
              treeData={state.cates}
              showIcon={false}
              selectedKeys={[state.cateId]}
              onSelect={(e) => {
                const key = e[0];
                if (state.cateId === key) {
                  setState({ cateId: "" });
                } else {
                  setState({ cateId: key });
                }
              }}
              titleRender={(item) => {
                return (
                  <div className={Style.row} key={item.key}>
                    <div className={Style.title}>{item.title}</div>
                    <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                      <EditCategory title={`编辑分类`} type={state.type} onOk={() => fetchCategory(state.type)} tree={state.cates} data={item}>
                        <Tooltip title="编辑分类">
                          <FormOutlined className={Style.ico} />
                        </Tooltip>
                      </EditCategory>

                      <EditCategory
                        title={`添加下级`}
                        type={state.type}
                        onOk={() => fetchCategory(state.type)}
                        tree={state.cates}
                        data={{ parentId: item.id }}
                      >
                        <Tooltip title="添加下级">
                          <ApartmentOutlined className={Style.ico} />
                        </Tooltip>
                      </EditCategory>

                      <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                        <Tooltip title="删除分类">
                          <DeleteOutlined className={Style.ico} />
                        </Tooltip>
                      </Popconfirm>
                    </div>
                  </div>
                );
              }}
            />
          </Card>
        </Col>
        <Col flex={"auto"}>
          <WeTable
            autoLoad={false}
            ref={tableRef}
            title={
              <EditItem title={`新增`} type={state.type} onOk={handleReload}>
                <WeTable.AddBtn children="新增" />
              </EditItem>
            }
            request={(params) => MallInformationArticleApi.getMallInformationArticle({ params })}
            params={{ type: state.type, cateId: state.cateId }}
            search={[
              <Form.Item label="标题" name={`title`} style={{ width: 400 }}>
                <Input placeholder="请输入查询标题" />
              </Form.Item>,
            ]}
            columns={[
              { title: "标题", dataIndex: "title" },
              {
                width: 200,
                fixed: "right",
                title: "操作",
                render: (item) => (
                  <Space>
                    <EditItem title={`编辑`} type={state.type} onOk={handleReload} data={item}>
                      <Typography.Link>编辑</Typography.Link>
                    </EditItem>

                    <Popconfirm title={`确定要删除 ${item.name} 吗？`} onConfirm={() => handleDelItem(item)}>
                      <Typography.Link>删除</Typography.Link>
                    </Popconfirm>
                  </Space>
                ),
              },
            ]}
          />
        </Col>
      </Row>
    </div>
  );
};

export default InformationPage;
