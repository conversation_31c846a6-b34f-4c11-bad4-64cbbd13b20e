import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, Row, TreeSelect, message } from "antd";
import { useMemo } from "react";
import MallInformationArticleApi from "./api";

const layout = { row: 10, col: 24 };

const EditCategory = (props: { children: any; onOk: Function; title: any; type: any; data?: any; tree: any }) => {
  const [form] = Form.useForm();

  const tree = useMemo(() => {
    return [{ key: -1, title: "顶级节点", children: props.tree }];
  }, [props.tree]);


  const handleOpen = () => {
    form.resetFields();
    if (props.data) {
      const { ...data } = props.data;
      data.parentId = data.parentId || -1;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (data.parentId === -1) {
      data.parentId = undefined;
    }
    data.type = props.type;

    if (data.id) {
      await MallInformationArticleApi.putMallInformationCategory({ data });
      message.success("编辑分类成功");
    } else {
      await MallInformationArticleApi.addMallInformationCategory({ data });
      message.success("添加分类成功");
    }

    props.onOk();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={400} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "60px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item
              label="父级"
              name={`parentId`}
              rules={[{ required: true, message: "请选择父级" }]}
              initialValue={-1}
            >
              <TreeSelect
                placeholder="请选择父级"
                treeData={tree}
                fieldNames={{ label: "title", value: "key" }}
                treeDefaultExpandedKeys={[-1]}
                disabled={!!props.data}
              // treeExpandedKeys={}
              // disabled={props.data?.id}
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`}>
              <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditCategory;
