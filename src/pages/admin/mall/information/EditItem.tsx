import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, InputNumber, Row, TreeSelect, message } from "antd";
import { useSetState } from "react-use";
import MallInformationArticleApi from "./api";
import RichText from "@/components/RichText";

const layout = { row: 10, col: 12 };


const EditItem = (props: { children: any; title: any; type: any; onOk: Function; data?: any }) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    categorys: [] as any[],
  });

  const handleOpen = () => {
    form.resetFields();
    fetchCategorys();

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    // console.log(data);

    if (data.id) {
      await MallInformationArticleApi.putMallInformationArticle({ data });
      message.success("编辑成功");
    } else {
      await MallInformationArticleApi.addMallInformationArticle({ data });
      message.success("新增成功");
    }

    props.onOk();

    // return false;
  };

  const fetchCategorys = async () => {
    const params = { type: props.type, pageSize: 9999 };
    const res = await MallInformationArticleApi.getMallInformationCategory({ params });
    const categorys = genTree(res?.list || [], (item) => ({ title: item.name, value: item.id, ...item }));
    setState({ categorys: categorys });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "110px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item label="标题" name={`title`} rules={[{ required: true, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="分类" name={`categoryId`} rules={[{ required: true, message: "请选择分类" }]}>
              <TreeSelect
                treeData={state.categorys}
                placeholder="请选择分类"
                showSearch
                treeNodeFilterProp="title"
                allowClear
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`} initialValue={999}>
              <InputNumber placeholder="请输入排序，范围1~999" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: true, message: "请输入内容" }]}>
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditItem;
