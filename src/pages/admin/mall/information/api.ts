import { makeApi } from "@/utils/Api";

const MallInformationArticleApi = {

    getMallInformationCategory: makeApi("get", `/api/mallInformationCategory`),
    getMallInformationCategoryInfo: makeApi("get", `/api/mallInformationCategory`, true),
    addMallInformationCategory: makeApi("post", `/api/mallInformationCategory`),
    putMallInformationCategory: makeApi("put", `/api/mallInformationCategory`),
    delMallInformationCategory: makeApi("delete", `/api/mallInformationCategory`),


    getMallInformationArticle: makeApi("get", `/api/mallInformationArticle`),
    getMallInformationArticleInfo: makeApi("get", `/api/mallInformationArticle`, true),
    addMallInformationArticle: makeApi("post", `/api/mallInformationArticle`),
    putMallInformationArticle: makeApi("put", `/api/mallInformationArticle`),
    delMallInformationArticle: makeApi("delete", `/api/mallInformationArticle`),

};

export default MallInformationArticleApi;
