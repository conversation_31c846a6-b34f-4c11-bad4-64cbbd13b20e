import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallMedicalTemplateCategoryApi from "./api";
import MallMedicalTemplateCategoryEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallMedicalTemplateCategoryPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallMedicalTemplateCategoryApi.delMallMedicalTemplateCategory({ data: { ids: item.id } });
    message.success("删除病历模板分类成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallMedicalTemplateCategoryApi.getMallMedicalTemplateCategory({ params: p })}
        title={
          <Space>
            <MallMedicalTemplateCategoryEdit title={"新增病历模板分类"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallMedicalTemplateCategoryEdit>
          </Space>
        }
        search={[
          <Form.Item label="公司id" name={`companyId`}>
            <Input placeholder="请输入公司id" />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="编码" name={`code`}>
            <Input placeholder="请输入编码" />
          </Form.Item>,
          <Form.Item label="状态 1.启用 0.禁用" name={`state`}>
            <Input placeholder="请输入状态 1.启用 0.禁用" />
          </Form.Item>,
          <Form.Item label="排序" name={`sort`}>
            <Input placeholder="请输入排序" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "公司id", dataIndex: "companyId", render: (c) => c ? c : "--" },
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "编码", dataIndex: "code", render: (c) => c ? c : "--" },
          { title: "状态 1.启用 0.禁用", dataIndex: "state" },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallMedicalTemplateCategoryEdit title={`编辑病历模板分类`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallMedicalTemplateCategoryEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallMedicalTemplateCategoryPage;
