import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppSensitiveWordsApi from "./api";
import AppSensitiveWordsEdit from "./edit";
import { Form, Input, Popconfirm, Space, message } from "antd";

const AppSensitiveWordsPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppSensitiveWordsApi.delAppSensitiveWords({ data: { ids: item.id } });
    message.success("删除敏感词成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppSensitiveWordsApi.getAppSensitiveWords({ params: p })}
        title={
          <AppSensitiveWordsEdit title={"新增敏感词"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppSensitiveWordsEdit>
        }
        search={[
          <Form.Item label="敏感词" name={`words`}>
            <Input placeholder="请输入敏感词" />
          </Form.Item>,
        ]}
        columns={[
          { title: "敏感词", dataIndex: "words", render: (c) => c ? c : "--" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppSensitiveWordsEdit title={`编辑敏感词`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppSensitiveWordsEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppSensitiveWordsPage;
