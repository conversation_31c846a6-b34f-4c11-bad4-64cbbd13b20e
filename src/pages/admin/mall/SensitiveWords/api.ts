import { makeApi } from "@/utils/Api";

const AppSensitiveWordsApi = {
    getAppSensitiveWords: makeApi("get", `/api/appSensitiveWords`),
    getAppSensitiveWordsInfo: makeApi("get", `/api/appSensitiveWords`, true),
    addAppSensitiveWords: makeApi("post", `/api/appSensitiveWords`),
    putAppSensitiveWords: makeApi("put", `/api/appSensitiveWords`),
    delAppSensitiveWords: makeApi("delete", `/api/appSensitiveWords`),
};

export default AppSensitiveWordsApi;
