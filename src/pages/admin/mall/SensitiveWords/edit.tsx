import WeModal from "@/components/WeModal/WeModal";
import AppSensitiveWordsApi from "./api";
import { Col, Form, Input, Row, message } from "antd";

const layout = { row: 10, col: 24 };

const AppSensitiveWordsEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppSensitiveWordsApi.getAppSensitiveWordsInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id) {
      //将换行变成数组
      data.wordsList = data.words.split("\n").filter((item: any) => item);
      delete data.words;
      await AppSensitiveWordsApi.addAppSensitiveWords({ data });
      message.success("添加敏感词成功");
    }
    if (data.id) {
      await AppSensitiveWordsApi.putAppSensitiveWords({ data });
      message.success("修改敏感词成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "60px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`words`} label="敏感词" rules={[{ required: true, message: "请输入敏感词" }]}>
              <Input.TextArea placeholder="请输入敏感词,一行一个" rows={20} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppSensitiveWordsEdit;
