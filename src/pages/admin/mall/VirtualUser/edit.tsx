import WeModal from "@/components/WeModal/WeModal";
import AppVirtualUserApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 24 };

const AppVirtualUserEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppVirtualUserApi.getAppVirtualUserInfo(props.data.id);
      data.photo = ImageUpload.serializer(data.photo);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.photo = ImageUpload.deserializer(data.photo);
    if (!data.id) {
      await AppVirtualUserApi.addAppVirtualUser({ data });
      message.success("添加虚拟用户成功");
    }
    if (data.id) {
      await AppVirtualUserApi.putAppVirtualUser({ data });
      message.success("修改虚拟用户成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`mobile`} label="手机" rules={[{ required: true, message: "请输入手机" }]}>
              <Input placeholder="请输入手机" disabled={!!props.data} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="头像"
              name="photo"
              valuePropName="fileList"
              rules={[{ required: true, message: "请上传图片" }]}
            >
              <ImageUpload></ImageUpload>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppVirtualUserEdit;
