import { makeApi } from "@/utils/Api";

const AppVirtualUserApi = {
    getAppVirtualUser: makeApi("get", `/api/appVirtualUser`),
    getAppVirtualUserInfo: makeApi("get", `/api/appVirtualUser`, true),
    addAppVirtualUser: makeApi("post", `/api/appVirtualUser`),
    putAppVirtualUser: makeApi("put", `/api/appVirtualUser`),
    delAppVirtualUser: makeApi("delete", `/api/appVirtualUser`),
};

export default AppVirtualUserApi;
