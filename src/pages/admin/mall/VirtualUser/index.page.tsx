import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppVirtualUserApi from "./api";
import AppVirtualUserEdit from "./edit";
import { Form, Image, Input, Popconfirm, Space, message } from "antd";
const AppVirtualUserPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppVirtualUserApi.delAppVirtualUser({ data: { ids: item.id } });
    message.success("删除虚拟用户成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppVirtualUserApi.getAppVirtualUser({ params: p })}
        title={
          <AppVirtualUserEdit title={"新增虚拟用户"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppVirtualUserEdit>
        }
        search={[
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="手机" name={`mobile`}>
            <Input placeholder="请输入手机" />
          </Form.Item>,
        ]}
        columns={[
          {
            title: "图片",
            dataIndex: "photo",
            render: (c) => <Image src={c} style={{ maxWidth: 50, maxHeight: 50 }} />,
          },
          { title: "姓名", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "手机", dataIndex: "mobile", render: (c) => c ? c : "--" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppVirtualUserEdit title={`编辑虚拟用户`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppVirtualUserEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppVirtualUserPage;
