.group {
  border-radius: 8px;
  padding: 10px;
  padding-bottom: 0;
  background: #f5f5f5;
  display: flex;
  flex-flow: wrap;

  .item {
    box-sizing: content-box;
    margin: 0 10px 10px 0;
    background: rgba(0, 0, 0, 0.02);
    border: 1px dashed #d9d9d9;
    border-radius: 8px;
    width: 120px;
    height: 170px;
    overflow: hidden;
    cursor: pointer;

    &:nth-child(5n) {
      margin-right: 0;
    }
  }
}

.ibox {
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;

  .head {
    position: relative;
    width: 100%;
    height: 140px;

    &:hover .mask {
      visibility: visible;
      opacity: 1;
    }

    .img {
      width: 100%;
      height: 100%;
      background: no-repeat center/cover;
    }

    .mask {
      transition: all 0.3s;
      opacity: 0;
      visibility: hidden;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;

      .ico {
        color: #fff;
        font-size: 20px;
        padding: 6px;
      }
    }
  }

  .foot {
    flex: 1;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    .ipt {
      height: 100%;
      text-align: center;
      color: #333;

      :global .ant-select-selection-placeholder {
        color: #333;
      }
    }
  }
}

.upbox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  color: #999;
}

.srow {
  display: flex;

  .scol {
    padding: 0 3px;
    flex: 1;

    &:nth-child(2) {
      min-width: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
