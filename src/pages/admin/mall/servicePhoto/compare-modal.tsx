import { Button } from "antd";
import { TransformWrapper, TransformComponent, ReactZoomPanPinchContentRef } from "react-zoom-pan-pinch";
import { RotateRightOutlined, ReloadOutlined, ZoomInOutlined, ZoomOutOutlined, CloseOutlined, SettingOutlined } from "@ant-design/icons";
import { useState } from "react";

// const imgs = [
//   "https://oss.gaomei168.com/file-release/20250526/1376565736264626176_400_400",
//   "https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20240906/1281634602078076928.jpg",
//   "https://cq-gaomei.oss-cn-qingdao.aliyuncs.com/file-release/20240906/1281634599955759104.jpg",
// ];

// Comparison photos

// 可缩放拖拽旋转的图片组件
const ZoomableImage = ({ src, alt }: { src: string; alt?: string }) => {
  const [rotation, setRotation] = useState(0);

  const handleRotate = () => {
    setRotation((prev) => prev + 90);
  };

  const handleReset = (cref: ReactZoomPanPinchContentRef) => {
    setRotation(0);
    cref.resetTransform();
    // cref.resetTransform(0);
    // cref.centerView(undefined, 0);
  };

  return (
    <div className="relative size-full bg-#000/80">
      <TransformWrapper
        initialScale={1}
        minScale={0.1}
        maxScale={5}
        limitToBounds={true}
        centerOnInit={true}
        // centerZoomedOut={true}
        doubleClick={{ mode: "zoomIn" }}
      >
        {(cref) => (
          <>
            {/* 说明文字 - 左下角 */}
            <div className="absolute bottom-2 left-2 z-10">
              <div className="bg-#000/50 px-2 py-1 rounded text-xs text-#fff/70">{alt}</div>
            </div>

            {/* 控制按钮 - 右下角 */}
            <div className="absolute bottom-2 right-2 z-10 flex flex-row gap-1">
              <Button size="small" type="primary" icon={<ZoomInOutlined />} onClick={() => cref.zoomIn()} className="!bg-#000/70 !border-#fff/30 hover:!bg-#000/90" />
              <Button size="small" type="primary" icon={<ZoomOutOutlined />} onClick={() => cref.zoomOut()} className="!bg-#000/70 !border-#fff/30 hover:!bg-#000/90" />
              <Button size="small" type="primary" icon={<RotateRightOutlined />} onClick={handleRotate} className="!bg-#000/70 !border-#fff/30 hover:!bg-#000/90" />
              <Button size="small" type="primary" icon={<ReloadOutlined />} onClick={() => handleReset(cref)} className="!bg-#000/70 !border-#fff/30 hover:!bg-#000/90" />
            </div>

            {/* 可变换的图片容器 */}
            <TransformComponent wrapperClass="!size-full !cursor-grab active:!cursor-grabbing">
              <img
                src={src}
                alt={alt}
                style={{
                  transform: `rotate(${rotation}deg)`,
                  transition: "transform 0.3s ease",
                  maxWidth: "100%",
                  maxHeight: "100%",
                  objectFit: "contain",
                }}
                draggable={false}
              />
            </TransformComponent>
          </>
        )}
      </TransformWrapper>
    </div>
  );
};

export const CompareModal = (props: { list: any[]; onClose?: any }) => {
  const maxCol = 4;
  // 默认一行显示完所有图片
  const [columns, setColumns] = useState(Math.min(props.list.length, maxCol));

  return (
    <>
      <div className="size-full bg-#000 flex flex-col overflow-hidden">
        {/* 设置区域 */}
        <div className="absolute top-4 left-4 z-20">
          <div className="bg-#000/50 px-3 py-1 rounded flex items-center gap-2">
            <SettingOutlined className="text-xs text-#fff/70" />
            <span className="text-xs text-#fff/70">每行显示:</span>
            <div className="flex gap-1">
              {Array.from({ length: Math.min(props.list.length, maxCol) }, (_, i) => i + 1).map((num) => (
                <button
                  key={num}
                  onClick={() => setColumns(num)}
                  className={`w-6 h-6 text-xs rounded border transition-colors ${
                    columns === num ? "bg-#fff/20 border-#fff/40 text-#fff" : "bg-transparent border-#fff/20 text-#fff/60 hover:bg-#fff/10 hover:text-#fff/80"
                  }`}
                >
                  {num}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <div className="absolute top-4 right-4 z-20" onClick={props.onClose}>
          <Button size="large" type="primary" icon={<CloseOutlined />} className="!bg-#000/70 !border-#fff/30 hover:!bg-#000/90 !text-#fff" shape="circle" />
        </div>

        <div className={`flex-1 min-h-0 grid cols-2 auto-rows-fr gap-2px text-(sm #fff) bg-#fff/30`} style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {props.list.map((item, index) => (
            <div key={index} className="min-h-0 overflow-hidden">
              <ZoomableImage src={item.url} alt={item.name} />
            </div>
          ))}
        </div>
      </div>
    </>
  );
};
