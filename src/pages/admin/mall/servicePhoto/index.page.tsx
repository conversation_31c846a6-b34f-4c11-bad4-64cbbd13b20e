import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallA<PERSON> from "@/services/MallApi";
import { Badge, Button, DatePicker, Divider, Form, Image, Modal, Popconfirm, Space, Typography, message } from "antd";
import { useRef } from "react";
import ModalEdit from "./ModalEdit";
import UserManager from "@/components/UserManager";
import { formatDate, renderTag } from "@/utils/Tools";
import { HPTypes } from "./types";
import dayjs from "dayjs";
import { useSetState } from "react-use";
import {
  BulbOutlined,
  EyeOutlined,
  InfoCircleOutlined,
  LeftOutlined,
  LogoutOutlined,
  RightOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  SwapOutlined,
  UndoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from "@ant-design/icons";
import { DatePresetRanges } from "@/utils/Tools";
import { CompareModal } from "./compare-modal";

const ServicePhoto = (props: { userId: any; queryType?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    exKeys: [] as any[],

    previewOpen: false,
    previewIdx: 0,
    previewList: [] as any[],

    compareMode: false,
    compareOpen: false,
    compareList: [] as any[],
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delHealPhoto({ data });
    message.success("删除成功");
    handleReload();
  };

  const fetchList = async (params: any) => {
    const res = await MallApi.getHealPhoto({ params });
    let list: any[] = res?.list || [];

    if (props.userId) {
      setState({ exKeys: list.map((n) => n.id) });
    } else {
      setState({ exKeys: [] });
    }

    return res;
  };

  const toggleKey = (key: any) => {
    let exKeys = state.exKeys.filter((n) => n !== key);

    if (exKeys.length == state.exKeys.length) {
      exKeys.push(key);
    }

    setState({ exKeys });
  };

  return (
    <>
      <WeTable
        size={props.userId ? 10 : undefined}
        ref={tableRef}
        params={{ queryType: props.queryType, shopVipUserId: props.userId }}
        tableProps={{
          size: "small",
          scroll: { x: "max-content" },
          onExpand: (_, item) => toggleKey(item.id),
          expandable: {
            expandedRowKeys: state.exKeys,
            expandedRowRender: (group) => {
              return (
                <div className="flex flex-wrap gap-2">
                  {group.photoList?.map((item: any, idx: any) => {
                    const uid = group.id + "-" + idx;
                    return (
                      <div
                        key={idx}
                        className="group pos-relative size-24 box-border cursor-pointer bg-#fff p-0 b-(0 solid #fff) data-[on=true]:(b-brand b-2 p-0.5)"
                        data-on={!!state.compareList.find((n) => n.id == uid)}
                        onClick={() => {
                          if (state.compareMode) {
                            const data = {
                              id: uid,
                              url: item.url,
                              name: (item.tag || "") + " · " + formatDate(item.dateStr),
                            };
                            const compareList = state.compareList.filter((n) => n.id !== uid);

                            if (compareList.length == state.compareList.length) {
                              compareList.push(data);
                            }

                            setState({ compareList });
                          } else {
                            const previewList = group.photoList?.map((n: any) => n.url);
                            setState({ previewOpen: !state.previewOpen, previewList, previewIdx: idx });
                          }
                        }}
                      >
                        <img className="block size-full object-cover" src={item.url} alt="" />
                        {!state.compareMode && (
                          <div className="group-hover:(visible opacity-100) invisible opacity-0 transition-all pos-absolute top-0 left-0 size-full flex items-center justify-center flex-col bg-#000/60">
                            <EyeOutlined className="text-(lg #fff)" />
                            <span className="text-(sm #fff) line-clamp-1">{item.tag || "无标签"}</span>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              );
            },
          },
        }}
        request={fetchList}
        title={
          <>
            {!state.compareMode && (
              <>
                <ModalEdit onOk={handleReload} title={`添加治疗照`} data={{ shopVipUserId: props.userId }}>
                  <WeTable.AddBtn />
                </ModalEdit>
                <Divider type="vertical" />
                <Button icon={<SwapOutlined />} onClick={() => setState({ compareMode: !state.compareMode })}>
                  治疗照对比
                </Button>
              </>
            )}
            {state.compareMode && (
              <>
                <Button type="primary" icon={<BulbOutlined />} onClick={() => setState({ compareOpen: true })} disabled={!state.compareList.length}>
                  开始对比 <Badge size="small" showZero count={state.compareList.length}></Badge>
                </Button>
                <Button icon={<LogoutOutlined />} onClick={() => setState({ compareMode: false, compareList: [] })} />
              </>
            )}
          </>
        }
        search={[
          <Form.Item label="上传日期" name={`CreateDate`} initialValue={props.userId ? [] : [dayjs().startOf("day"), dayjs().endOf("day")]}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: `100%` }} />
          </Form.Item>,
          !props.userId && (
            <Form.Item label="会员" name={`shopVipUserId`}>
              <VipPickerInput />
            </Form.Item>
          ),
        ]}
        columns={[
          {
            fixed: "left",
            title: "会员姓名",
            dataIndex: "vipUser",
            hide: props.userId,
            render: (c) =>
              c ? (
                <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
              ) : (
                "--"
              ),
          },
          { title: "门店", dataIndex: "shopName", hide: !props.userId },
          // { title: "会员号", dataIndex: "vipUser", render: (c) => c?.vipCard },
          // { title: "手机", dataIndex: "vipUser", render: (c) => c?.mobile },
          {
            title: "治疗项目",
            dataIndex: "serviceOrder",
            render: (c) => c?.itemList?.map((aa: any) => aa.productName)?.join(","),
          },
          { title: "治疗单号", dataIndex: "serviceOrder", render: (c) => c?.serialNo },
          { title: "治疗日期", dataIndex: "serviceOrder", render: (c) => (c?.createDate ? dayjs(c?.createDate).format("YYYY-MM-DD HH:mm:ss") : "") },
          { title: "类型", dataIndex: "type", render: (c) => renderTag(HPTypes, c) },
          {
            title: "图片",
            dataIndex: "photoList",
            render: (c, item) => (
              <a onClick={() => toggleKey(item.id)}>
                {state.exKeys.includes(item.id) ? "收起" : "展开"} ({c?.length}张)
              </a>
            ),
          },
          {
            title: "备注",
            dataIndex: "content",
            render: (c) =>
              c ? (
                <Typography.Text style={{ width: 160 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
              ) : (
                "--"
              ),
          },
          { title: "上传日期", dataIndex: "updateDate", render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "") },
          {
            fixed: "right",
            title: "操作",
            render: (item) => (
              <Space>
                <ModalEdit title={`修改治疗照`} data={item} onOk={handleReload}>
                  <Typography.Link>修改</Typography.Link>
                </ModalEdit>

                <Popconfirm title={`确定要删除这条治疗照数据吗？`} onConfirm={() => handleDel(item)}>
                  <Typography.Link>删除</Typography.Link>
                </Popconfirm>
              </Space>
            ),
          },
        ]}
      />

      <Image.PreviewGroup
        items={state.previewList}
        preview={{
          visible: state.previewOpen,
          current: state.previewIdx,
          rootClassName: "[&_.ant-image-preview-switch-right]:(bg-#000/50) [&_.ant-image-preview-switch-left]:(bg-#000/50)",
          onVisibleChange: (e) => setState({ previewOpen: e }),
          onChange: (crt) => setState({ previewIdx: crt }),
          toolbarRender: (_, act) => {
            return (
              <div>
                <Space className="px-24px text-(20px #fff) bg-#000/10 rounded-100px [&_.anticon]:(p-12px cursor-pointer)">
                  <LeftOutlined onClick={() => act.actions.onActive?.(-1)} />
                  <RightOutlined onClick={() => act.actions.onActive?.(1)} />
                  <SwapOutlined rotate={90} onClick={() => act.actions.onFlipY()} />
                  <SwapOutlined onClick={() => act.actions.onFlipX()} />
                  <RotateLeftOutlined onClick={() => act.actions.onRotateLeft()} />
                  <RotateRightOutlined onClick={() => act.actions.onRotateRight()} />
                  <ZoomOutOutlined onClick={() => act.actions.onZoomOut()} />
                  <ZoomInOutlined onClick={() => act.actions.onZoomIn()} />
                  <UndoOutlined onClick={() => act.actions.onReset()} />
                </Space>
                <div className="flex items-center justify-center mt-10px text-(12px #fff/80)">
                  <InfoCircleOutlined className="mr-4px" />
                  使用方向键可以切换图片
                </div>
              </div>
            );
          },
        }}
      />

      <Modal
        open={state.compareOpen}
        centered
        destroyOnHidden
        closable={false}
        footer={false}
        onCancel={() => setState({ compareOpen: false })}
        classNames={{
          wrapper: "[&_.ant-modal]:(!w-full h-full max-w-full)",
          content: "!bg-transparent !rounded-0 !p-0",
          body: "h-100vh",
        }}
      >
        <CompareModal list={state.compareList} onClose={() => setState({ compareOpen: false })} />
      </Modal>
    </>
  );
};

export default ServicePhoto;
