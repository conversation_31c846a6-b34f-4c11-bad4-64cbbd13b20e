import VipPlusPicker from "@/components/VipPlusPicker";
import UserPane from "@/components/Units/UserPane";
import WeModal from "@/components/WeModal/WeModal";
import { Alert, Col, Form, Image, Input, Radio, Row, Select, Tooltip, message } from "antd";
import { useSetState } from "react-use";
import Style from "./ModalEdit.module.scss";
import { HPTypes } from "./types";
import axios from "axios";
import Configs from "@/utils/Configs";
import { DeleteOutlined, EyeOutlined, PlusOutlined } from "@ant-design/icons";
import UserApi from "@/services/UserApi";
import { useEffect } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import dayjs from "dayjs";
import Compressor from "compressorjs";

const layout = { row: 10, col: 8 };

const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

const ModalEdit = (props: { children: any; onOk: Function; title?: any; data?: any }) => {
  const [form] = Form.useForm();
  const isEdit = props.data?.id;
  const [state, setState] = useSetState({
    user: null as any,
    imgs: [] as any[],
    orders: [] as any[],

    prevew: false as any,
    tags: [] as any[],
  });

  useEffect(() => {
    if (!isEdit) {
      fetchOrders();
    }
  }, [state.user?.id, isEdit]);

  const handleOpen = async () => {
    setState({ user: null, imgs: [] });
    fetchTags();
    form.resetFields();

    if (props.data?.shopVipUserId) {
      fetchUser();
    }

    if (props.data?.photoList) {
      setState({ imgs: props.data?.photoList || [] });
    }

    if (props.data) {
      const { ...data } = props.data;
      form.setFieldsValue(data);
    }
  };

  const fetchUser = async () => {
    const uid = props.data?.shopVipUserId;
    if (!uid) return;
    const user = await MallApi.getMember(uid);
    setState({ user });
  };

  const fetchOrders = async () => {
    const uid = state.user?.id;

    if (!uid) {
      setState({ orders: [] });
      return;
    }

    const params = { shopVipUserId: uid, pageSize: 9999 };
    const res = await MallApi.getServiceOrderForSelect({ params });
    let orders: any[] = res?.list || [];
    orders = orders.map((item) => ({
      desc: (
        <div className={Style.srow}>
          <div className={Style.scol}>{dayjs(item.createDate)?.format("YYYY-MM-DD HH:mm:ss")}</div>
          <div className={Style.scol}>
            <Tooltip title={item.itemList?.map((aa: any) => aa.productName)?.join(",")}>
              {item.itemList?.map((aa: any) => aa.productName)?.join(",")}
            </Tooltip>
          </div>
        </div>
      ),
      value: item.id,
      name: item.productName,
      label: item.itemList?.map((aa: any) => aa.productName)?.join(","),
    }));
    orders.unshift({
      desc: (
        <div className={Style.srow}>
          <div className={Style.scol}>治疗时间</div>
          <div className={Style.scol}>治疗项目</div>
        </div>
      ),
      value: null,
      disabled: true,
      label: "",
    });

    setState({ orders });
  };

  const fetchTags = async () => {
    const params = { typeEnCode: "bodyPosition" };
    const res = await UserApi.getDictDataByCode({ params });
    setState({ tags: res || [] });
  };

  const beforeUpload: any = (file: any) => {
    if (file?.type == "image/gif") return file;
    return new Promise((resolve) => {
      new Compressor(file, {
        maxWidth: 1920,
        convertSize: 1024 * 200,
        success: (res) => resolve(res),
        error: () => resolve(file),
      });
    });
  };

  const uploadImg = () => {
    return new Promise((resolve, reject) => {
      let ipt: any = document.createElement("input");
      ipt.type = "file";
      ipt.accept = "image/*";
      ipt.multiple = true;

      ipt.onchange = async () => {
        // const file = ipt.files?.[0];
        const files = [...ipt.files];
        ipt = null;
        if (!files?.length) return;
        const arr: any[] = [];

        for (let file of files) {
          const file2 = await beforeUpload(file);

          const fdata = new FormData();
          fdata.append("file", file2);
          const res = await axios.post(Configs.uploadHost, fdata, {
            headers: { "Content-Type": "multipart/form-data" },
          });
          const url = res.data.data?.allFullPath;
          if (!url) {
            message.error("上传失败，请重试");
            return reject();
          }
          arr.push(url);
        }

        resolve(arr);
      };

      ipt.click();
    });
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.photoList = state.imgs;

    if (!data.photoList.length) {
      message.error("请上传照片");
      return false;
    }

    if (data.id) {
      await MallApi.putHealPhoto({ data });
      message.success("修改治疗照成功");
    } else {
      await MallApi.addHealPhoto({ data });
      message.success("添加治疗照成功");
    }

    props.onOk();
  };

  return (
    <WeModal
      trigger={props.children}
      width={800}
      title={
        <div style={{ display: "flex", alignItems: "center", paddingBottom: 10 }}>
          <span style={{ marginRight: 10 }}>{props.title}</span>
          {!props.data?.shopVipUserId && <VipPlusPicker onChange={(user) => setState({ user })} style={{ width: 400 }} />}
        </div>
      }
      onOpen={handleOpen}
      onOk={handleSubmit}
    >
      {state.user ? <UserPane user={state.user} /> : <Alert message="请先选择用户信息" type="warning" showIcon />}
      <div style={{ height: 20 }}></div>

      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Form.Item hidden name={`id`}>
            <Input />
          </Form.Item>
          {/* <Group>
            <Col span={layout.col}>
              <Form.Item label="门店" required>
                <SelectShop onChange={(e) => console.log(e)} />
              </Form.Item>
            </Col>
          </Group> */}
          {!isEdit && (
            <Group>
              <Col span={layout.col * 3}>
                <Form.Item label="治疗单" name={`serviceOrderId`} rules={[{ required: true, message: "请选择治疗单" }]}>
                  <Select
                    placeholder="请选择治疗单"
                    options={state.orders}
                    optionRender={(option) => option.data.desc}
                  />
                </Form.Item>
              </Col>
            </Group>
          )}
          {isEdit && (
            <Group>
              <Col span={layout.col * 3}>
                <Form.Item label="治疗单">
                  <Input
                    disabled
                    value={props.data?.serviceOrder?.itemList?.map((aa: any) => aa.productName)?.join(",")}
                  />
                </Form.Item>
              </Col>
            </Group>
          )}
          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="类型" name={`type`} rules={[{ required: true, message: "请选择类型" }]}>
                <Radio.Group
                  options={HPTypes.map((n) => ({ label: n.name, value: n.id }))}
                  optionType="button"
                  buttonStyle="solid"
                />
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={24}>
              <Form.Item label="照片" required>
                <div style={{ display: "none" }}>
                  <Image
                    preview={{
                      visible: !!state.prevew,
                      src: state.prevew,
                      onVisibleChange: (e) => setState({ prevew: e }),
                    }}
                  />
                </div>
                <div className={Style.group}>
                  {state.imgs.map((item, idx) => (
                    <div className={Style.item} key={idx}>
                      <div className={Style.ibox}>
                        <div className={Style.head}>
                          <div className={Style.mask}>
                            <EyeOutlined className={Style.ico} onClick={() => setState({ prevew: item.url })} />
                            {/* <DownloadOutlined className={Style.ico} onClick={() => {}} /> */}
                            <DeleteOutlined
                              className={Style.ico}
                              onClick={() => {
                                let imgs = state.imgs.filter((_, i) => i !== idx);
                                setState({ imgs });
                              }}
                            />
                          </div>
                          <div className={Style.img} style={{ backgroundImage: `url(${item.url})` }}></div>
                        </div>
                        <div className={Style.foot}>
                          <Select
                            bordered={false}
                            className={Style.ipt}
                            placeholder="选择标签"
                            showArrow={false}
                            options={state.tags}
                            fieldNames={{ label: "name", value: "name" }}
                            allowClear
                            value={item.tag}
                            onChange={(tag) => {
                              let imgs = state.imgs.map((item, crt) => {
                                if (crt === idx) {
                                  return { ...item, tag };
                                } else {
                                  return item;
                                }
                              });
                              setState({ imgs });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}

                  <div
                    className={Style.item}
                    onClick={async () => {
                      let arr: any = await uploadImg();
                      arr = arr.map((n: any) => ({
                        url: n,
                        tag: undefined,
                        dateStr: dayjs().format("YYYY-MM-DD HH:mm:ss"),
                      }));
                      setState((prev) => ({ imgs: [...prev.imgs, ...arr] }));

                      // setState((prev) => ({
                      //   imgs: [...prev.imgs, { url, tag: undefined, dateStr: dayjs().format("YYYY-MM-DD HH:mm:ss") }],
                      // }));
                    }}
                  >
                    <div className={Style.upbox}>
                      <PlusOutlined className={Style.icon} />
                    </div>
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={layout.col * 3}>
              <Form.Item label="备注" name={`content`}>
                <Input.TextArea maxLength={1000} allowClear placeholder="请输入备注" rows={4} />
              </Form.Item>
            </Col>
          </Group>
        </Row>
      </Form>
    </WeModal>
  );
};

export default ModalEdit;
