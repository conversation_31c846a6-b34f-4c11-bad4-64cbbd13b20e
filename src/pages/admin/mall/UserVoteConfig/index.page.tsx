import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserVoteConfigApi from "./api";
import AppUserVoteConfigEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { Typography } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { StateMap, VoteModeMap } from "./types";
import AppUserVoteOptionPage from "../UserVoteOption/index.page";
import AppUserVoteLogPage from "../UserVoteLog/index.page";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppUserVoteConfigPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppUserVoteConfigApi.delAppUserVoteConfig({ data: { ids: item.id } });
    message.success("删除投票配置成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppUserVoteConfigApi.getAppUserVoteConfig({ params: p })}
        title={
          <AppUserVoteConfigEdit title={"新增投票配置"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppUserVoteConfigEdit>
        }
        search={[
          <Form.Item label="标题" name={`name`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="开始时间" name={`StartDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
          <Form.Item label="结束时间" name={`EndDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
          <Form.Item label="投票模式" name={`voteMode`}>
            <Select options={VoteModeMap} placeholder="请选择投票模式" allowClear />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "标题", dataIndex: "name", render: (c) => c ? c : "--" },
          {
            title: "内容",
            dataIndex: "content",
            width: 200,
            render: (c) => (
              <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          { title: "开始时间", dataIndex: "startDate", render: (c) => c ? fdate(c) : "--" },
          { title: "结束时间", dataIndex: "endDate", render: (c) => c ? fdate(c) : "--" },
          { title: "投票模式", dataIndex: "voteMode", render: (c) => c ? VoteModeMap.find((item) => item.value === c)?.label : "--" },
          { title: "每人票数", dataIndex: "baseVoteNum" },
          { title: "累计票数", dataIndex: "totalVoteNum" },
          { title: "排序", dataIndex: "sort" },
          { title: "状态", dataIndex: "state", render: (c) => c ? StateMap.find((item) => item.value === c)?.label : "--" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppUserVoteConfigEdit title={`编辑投票配置`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppUserVoteConfigEdit>
                  <AppUserVoteOptionPage title={`投票选项`} voteId={item.id} >
                    <a>投票选项</a>
                  </AppUserVoteOptionPage>
                  <AppUserVoteLogPage title={`投票记录`} voteId={item.id} >
                    <a>投票记录</a>
                  </AppUserVoteLogPage>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppUserVoteConfigPage;
