import { makeApi } from "@/utils/Api";

const AppUserVoteConfigApi = {
    getAppUserVoteConfig: makeApi("get", `/api/appUserVoteConfig`),
    getAppUserVoteConfigInfo: makeApi("get", `/api/appUserVoteConfig`, true),
    addAppUserVoteConfig: makeApi("post", `/api/appUserVoteConfig`),
    putAppUserVoteConfig: makeApi("put", `/api/appUserVoteConfig`),
    delAppUserVoteConfig: makeApi("delete", `/api/appUserVoteConfig`),
};

export default AppUserVoteConfigApi;
