import WeModal from "@/components/WeModal/WeModal";
import AppUserVoteConfigApi from "./api";
import { Col, Form, Input, Radio, Row, Select, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";
import { StateMap, VoteModeMap } from "./types";

const layout = { row: 10, col: 12 };

const AppUserVoteConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const data = await AppUserVoteConfigApi.getAppUserVoteConfigInfo(props.data.id);

      data.Date = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();

    const Date = formatDateRange(data.Date);
    data.startDate = Date.start + " 00:00:00";
    data.endDate = Date.end + " 23:59:59";
    delete data.Date;

    if (!data.id) {
      await AppUserVoteConfigApi.addAppUserVoteConfig({ data });
      message.success("添加投票配置成功");
    }
    if (data.id) {
      await AppUserVoteConfigApi.putAppUserVoteConfig({ data });
      message.success("修改投票配置成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 2}>
            <Form.Item name={`name`} label="标题" rules={[{ required: false, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={4} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item
              name={`Date`}
              label="投票时间"
              initialValue={[]}
              rules={[{ required: true, message: "请选择投票时间" }]}
            >
              <DatePicker.RangePicker
                presets={DatePresetRanges}
                showTime
                className="w-full"
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`voteMode`} label="投票模式" rules={[{ required: false, message: "请输入投票模式" }]} initialValue={1}>
              <Select options={VoteModeMap} placeholder="请选择投票模式" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`baseVoteNum`} label="每人票数" rules={[{ required: false, message: "请输入每人票数" }]} initialValue={0}>
              <InputNumber min={0} placeholder="请输入每人票数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserVoteConfigEdit;
