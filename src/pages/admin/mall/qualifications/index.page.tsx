import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import EditQualifications from "./EditQualifications";
import { useRef } from "react";
import { Image, Popconfirm, Space, Tag, message } from "antd";

const QualificationsPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delQualifications({ data });
    message.success("删除成功");
    handleReload();
  };

  return (
    <WeTable
      ref={tableRef}
      title={
        <EditQualifications title="新增资质" onOk={handleReload}>
          <WeTable.AddBtn />
        </EditQualifications>
      }
      request={(params) => MallApi.getQualificationsList({ params })}
      columns={[
        { title: "图片", dataIndex: "image", render: (c) => <Image src={c} width={60} /> },
        {
          title: "状态",
          dataIndex: "state",
          render: (c) => (c === 1 ? <Tag color="success">显示</Tag> : <Tag color="error">不显示</Tag>),
        },
        { title: "排序", dataIndex: "sort" },

        {
          title: "操作",
          render: (item) => (
            <Space>
              <EditQualifications title="编辑资质" onOk={handleReload} data={item}>
                <a>编辑</a>
              </EditQualifications>
              <Popconfirm title={`确定删除 ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                <a>删除</a>
              </Popconfirm>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default QualificationsPage;
