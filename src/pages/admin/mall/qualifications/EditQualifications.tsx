import ImageUpload from "@/components/ImageUpload";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import {
  Col,
  Form,
  Input,
  InputNumber,
  Row,
  Switch,
  message,
} from "antd";

const layout = {
  col: 24,
  row: 10,
};

const EditQualifications = (props: {
  children: any;
  title: string;
  onOk: Function;
  data?: any;
}) => {
  const [form] = Form.useForm();

  const handleOpenOnce = () => { };

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = { ...props.data };
      data.image = ImageUpload.serializer(data.image);
      data.state = !!data.state;
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const val = await form.validateFields();

    const data = { ...val };
    data.image = ImageUpload.deserializer(data.image);
    data.state = Number(data.state);
    data.sort = Number(data.sort);

    if (data.id) {
      await Mall<PERSON>pi.putQualifications({ data });
      message.success("修改成功");
    } else {
      await Mall<PERSON><PERSON>.addQualifications({ data });
      message.success("添加成功");
    }

    props.onOk();
  };

  const isRequired = false;

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={400}
      onOpen={handleOpen}
      onOk={handleSubmit}
      onOpenOnce={handleOpenOnce}
    >
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={[layout.row, layout.row]}>
          <Col span={layout.col} hidden>
            <Form.Item name={`id`}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="图片"
              name={`image`}
              rules={[{ required: isRequired, message: "请上传图片" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              label="状态"
              name={`state`}
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="排序" name={`sort`} tooltip="数字越小越靠前">
              <InputNumber />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default EditQualifications;
