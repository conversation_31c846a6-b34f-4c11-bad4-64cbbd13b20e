import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Form, Input, message } from "antd";

const EndModal = (props: { children: any; onOk: Function; data: any }) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.id = props.data.id;
    data.diagnosisState = 30;

    await MallApi.putAppUserTriageStateMianzhen({ data });
    message.success("结束面诊成功");
    props.onOk();
  };

  return (
    <WeModal trigger={props.children} onOk={handleSubmit} title="结束面诊">
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item label="备注" name={`diagnosisContent`}>
          <Input.TextArea autoSize={{ minRows: 2 }} maxLength={200} showCount allowClear placeholder="请输入备注信息" />
        </Form.Item>
      </Form>
    </WeModal>
  );
};

export default EndModal;
