import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { DatePicker, Form, Popconfirm, Select, Space, Typography, message } from "antd";
import DictPicker from "@/components/Units/DictPicker";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";
import { DatePresetRanges, renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { useRef } from "react";
import UserApi from "@/services/UserApi";
import { useMount, useSetState } from "react-use";
import { DiagnosisState } from "../triageConsult/types";
import EndModal from "./EndModal";
import ModalEdit from "../serviceOrder/ModalEdit";
import ConsultLogEdit from "@/components/UserManager/ConsultLogEdit";
import AccessDropActions from "@/components/AccessList/AccessDropActions";
import OrderCreatorNew from "@/components/OrderCreatorNew";

const TriageConsult = () => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    user: null as any,
  });

  useMount(() => {
    fetchUser();
  });

  // useEffect(() => {
  //   if (!state.user?.id) return;
  //   handleReload();
  // }, [state.user?.id]);

  const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchUser = async () => {
    const user = await UserApi.getUserInfo();
    setState({ user });
  };

  const handleJieZhen = async (item: any) => {
    const data = { id: item.id, diagnosisState: 20 };
    await MallApi.putAppUserTriageStateMianzhen({ data });
    message.success("接诊成功");
    handleReload();
  };

  return (
    <WeTable
      autoLoad={false}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      request={(params) => MallApi.getAppUserTriage({ params })}
      params={{ doctorId: state.user?.id }}
      search={[
        <Form.Item label="分诊时间" name={`CreateDate`} initialValue={[dayjs().startOf("day"), dayjs().endOf("day")]}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="面诊时间" name={`DiagnosisReceptionTime`}>
          <DatePicker.RangePicker style={{ width: "100%" }} presets={DatePresetRanges} />
        </Form.Item>,
        <Form.Item label="会员" name={`shopVipUserId`}>
          <VipPickerInput />
        </Form.Item>,
        <Form.Item label="面诊状态" name={`diagnosisState`}>
          <Select
            options={DiagnosisState.map((n) => ({ label: n.name, value: n.id }))}
            optionFilterProp="label"
            placeholder="请选择面诊状态"
            allowClear
            showSearch
          />
        </Form.Item>,
        <Form.Item label="就诊类型" name={`triageTypeId`}>
          <DictPicker type="triageType" placeholder="请选择就诊类型" />
        </Form.Item>,
        <Form.Item label="成交状态" name={`transactionState`}>
          <Select
            options={[
              { label: "未成交", value: 0 },
              { label: "成交", value: 1 },
            ]}
            optionFilterProp="label"
            placeholder="请选择成交状态"
            allowClear
            showSearch
          />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "vipUser",
          render: (c) => (
            c ? <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
              : "--"
          ),
        },
        { title: "所属客服", dataIndex: "adviserName" },
        { title: "所属医生", dataIndex: "doctorName" },
        { title: "所属治疗师", dataIndex: "nurseName" },
        { title: "就诊类型", dataIndex: "triageTypeName" },
        { title: "预约号", dataIndex: "reservationSerialNo" },
        { title: "面诊时间", dataIndex: "diagnosisReceptionTime", render: (c) => fdate(c) },
        {
          title: "面诊备注",
          dataIndex: "diagnosisContent",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
        {
          title: "分诊时间",
          dataIndex: "createDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : ""),
        },
        {
          title: "分诊备注",
          dataIndex: "content",
          render: (c) => (
            <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
              {c}
            </Typography.Text>
          ),
        },
        { title: "成交状态", dataIndex: "transactionState", render: (c) => (c ? "成交" : "未成交") },
        { fixed: "right", title: "面诊状态", dataIndex: "diagnosisState", render: (c) => renderTag(DiagnosisState, c) },
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <Popconfirm title={`接诊当前用户？ - ${item.vipUser?.name}`} onConfirm={() => handleJieZhen(item)}>
                <Typography.Link disabled={!(item.diagnosisState == 10)}>接诊</Typography.Link>
              </Popconfirm>

              <OrderCreatorNew userId={item.shopVipUserId} params={{ triageId: item.id }}>
                <Typography.Link>开单</Typography.Link>
              </OrderCreatorNew>

              <ConsultLogEdit
                title={`意向推荐`}
                data={{ id: item.consultId, shopVipUserId: item.shopVipUserId, triageId: item.id }}
                onOk={handleReload}
              >
                <Typography.Link>意向推荐</Typography.Link>
              </ConsultLogEdit>

              <ModalEdit
                title={`新增治疗单`}
                data={{ shopVipUserId: item.shopVipUserId }}
                onOk={handleReload}
                exData={{ triageId: item.id }}
              >
                <a>治疗单</a>
              </ModalEdit>

              <EndModal data={item} onOk={handleReload}>
                <Typography.Link disabled={!(item.diagnosisState == 20)}>面诊结束</Typography.Link>
              </EndModal>

              <AccessDropActions userId={item.shopVipUserId} onOk={handleReload} />
            </Space>
          ),
        },
      ]}
    />
  );
};

export default TriageConsult;
