import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallCollectActivityApi from "./api";
import MallCollectActivityEdit from "./edit";
import { Form, Input, Popconfirm, Space, message, Card, Select, Button } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, } from "@/utils/Tools";
import dayjs from "dayjs";
import { useMount, useSetState } from "react-use";
import UserApi from "@/services/UserApi";
import { StateMap } from "./types";
import { SignRule } from "../CollectCardLog/rule";
import { PaperClipOutlined, SnippetsOutlined } from "@ant-design/icons";
import MallCollectCardTypePage from "../CollectCardType/index.page";
import MallCollectCardPrizePage from "../CollectCardPrize/index.page";
import MallCollectCardLogPage from "../CollectCardLog/index.page";
import MallPrizeLogPage from "../PrizeLog/index.page";
import MallCollectExchangeProductPage from "../CollectExchangeProduct/index.page";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallCollectActivityPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    collectCardSeries: [] as any[],
    collectCardSeriesId: "",
  });


  useMount(() => {
    fetchSeries();
  });

  const fetchSeries = async () => {
    const params = { typeEnCode: "CollectActivitySeries", pageSize: 9999 };
    const res = await UserApi.getDictDataByCode({ params });
    let list: any[] = res || [];
    list = list.map((n) => ({ tab: n.name, key: n.id }));
    setState({ collectCardSeries: list, collectCardSeriesId: list[0]?.key || "" });
  };


  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallCollectActivityApi.delMallCollectActivity({ data: { ids: item.id } });
    message.success("删除集卡活动成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={state.collectCardSeries}
        activeTabKey={state.collectCardSeriesId}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(collectCardSeriesId) => setState({ collectCardSeriesId })}
        tabBarExtraContent={
          <SignRule onOk={handleReload}>
            <Button type="primary" icon={<PaperClipOutlined />}>集卡规则</Button>
          </SignRule>
        }
      />
      <WeTable
        autoLoad={false}
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ collectCardSeriesId: state.collectCardSeriesId }}
        request={(p) => MallCollectActivityApi.getMallCollectActivity({ params: p })}
        title={
          <Space>
            <MallCollectActivityEdit title={"新增集卡活动"} collectCardSeriesId={state.collectCardSeriesId} onOk={handleReload}>
              <WeTable.AddBtn >新增活动</WeTable.AddBtn>
            </MallCollectActivityEdit>
            <MallCollectCardTypePage title="集卡类型管理" collectCardSeriesId={state.collectCardSeriesId}>
              <Button type="primary" icon={<SnippetsOutlined />}>集卡类型管理</Button>
            </MallCollectCardTypePage>
          </Space>
        }
        search={[
          <Form.Item label="活动标题" name={`title`}>
            <Input placeholder="请输入活动标题" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "活动标题", dataIndex: "title", render: (c) => c ? c : "--" },
          { title: "开始时间", dataIndex: "startDate", render: (c) => c ? fdate(c) : "--" },
          { title: "结束时间", dataIndex: "endDate", render: (c) => c ? fdate(c) : "--" },
          { title: "状态", dataIndex: "state", render: (c) => <>{StateMap.find((item) => item.value === c)?.label || "--"}</> },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallCollectCardPrizePage title="奖项管理" activityId={item.id} collectCardSeriesId={state.collectCardSeriesId}>
                    <a>奖项管理</a>
                  </MallCollectCardPrizePage>
                  <MallPrizeLogPage title="抽奖记录" activityId={item.id}>
                    <a>抽奖记录</a>
                  </MallPrizeLogPage>
                  <MallCollectCardLogPage title="用户卡片管理" activityId={item.id} collectCardSeriesId={state.collectCardSeriesId}>
                    <a>用户卡片</a>
                  </MallCollectCardLogPage>
                  <MallCollectExchangeProductPage title="兑换项目管理" activityId={item.id} collectCardSeriesId={state.collectCardSeriesId}>
                    <a>兑换项目</a>
                  </MallCollectExchangeProductPage>
                  <MallCollectActivityEdit title={`编辑集卡活动`} data={item} collectCardSeriesId={state.collectCardSeriesId} onOk={handleReload}>
                    <a>编辑活动</a>
                  </MallCollectActivityEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除活动</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallCollectActivityPage;
