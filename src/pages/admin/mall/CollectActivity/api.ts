import { makeApi } from "@/utils/Api";

const MallCollectActivityApi = {
    getMallCollectActivity: makeApi("get", `/api/mallCollectActivity`),
    getMallCollectActivityInfo: makeApi("get", `/api/mallCollectActivity`, true),
    addMallCollectActivity: makeApi("post", `/api/mallCollectActivity`),
    putMallCollectActivity: makeApi("put", `/api/mallCollectActivity`),
    delMallCollectActivity: makeApi("delete", `/api/mallCollectActivity`),
};

export default MallCollectActivityApi;
