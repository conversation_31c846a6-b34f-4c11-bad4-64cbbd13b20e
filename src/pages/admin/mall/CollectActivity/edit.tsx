import WeModal from "@/components/WeModal/WeModal";
import MallCollectActivityApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";
import { StateMap } from "./types";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";

const layout = { row: 10, col: 24 };

const MallCollectActivityEdit = (props: { title: any; children: any; onOk?: Function; data?: any; collectCardSeriesId: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const data = await MallCollectActivityApi.getMallCollectActivityInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      data.Date = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.collectCardSeriesId = props.collectCardSeriesId;
    data.image = ImageUpload.deserializer(data.image);

    const Date = formatDateRange(data.Date);
    data.startDate = Date.start + " 00:00:00";
    data.endDate = Date.end + " 23:59:59";
    delete data.Date;

    if (!data.id) {
      await MallCollectActivityApi.addMallCollectActivity({ data });
      message.success("添加集卡活动成功");
    }
    if (data.id) {
      await MallCollectActivityApi.putMallCollectActivity({ data });
      message.success("修改集卡活动成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`title`} label="活动标题" rules={[{ required: true, message: "请输入活动标题" }]}>
              <Input placeholder="请输入活动标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item label="活动时间" name={`Date`} rules={[{ required: true, message: "请选择日期范围" }]} initialValue={[]}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`image`} label="图片" rules={[{ required: false, message: "请上传图片" }]} valuePropName="fileList">
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col> */}
          <Col span={layout.col}>
            <Form.Item name={`content`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input.TextArea placeholder="请输入说明" rows={4} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallCollectActivityEdit;
