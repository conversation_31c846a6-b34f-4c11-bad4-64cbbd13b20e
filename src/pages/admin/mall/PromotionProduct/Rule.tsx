import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, message, Row } from "antd";
import { Api } from "./Api";
import RichText from "@/components/RichText";
import { Switch01 } from "@/components/Units/Switch01";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    await Api.putRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getRule();
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="推广规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`promotionMallOpenTag`} label="开启推广">
              <Switch01 />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`promotionMallRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
