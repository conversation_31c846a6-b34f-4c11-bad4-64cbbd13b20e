import WeModal from "@/components/WeModal/WeModal";
import WeTable from "@/components/WeTable";
import { Api } from "./Api";
import { DatePresetRanges, formatDate } from "@/utils/Tools";
import { DatePicker, Form } from "antd";

export const FxMoney = (props: { children: any; uid: any }) => {
  return (
    <WeModal trigger={props.children} title="推广佣金" footer={false} width={1000}>
      <WeTable
        request={(p) => Api.getFxMoney({ params: { ...p, source: 3, shopVipUserId: props.uid } })}
        searchNum={2}
        search={[
          <Form.Item label="选择日期" name="CreateDate">
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "日期", dataIndex: "createDate", render: (c) => formatDate(c) },
          { title: "类型", dataIndex: "incomeExpenseType", render: (c) => (c == 1 ? "收入" : "支出") },
          { title: "佣金", dataIndex: "money" },
          { title: "备注", dataIndex: "moneyTypeName" },
        ]}
      />
    </WeModal>
  );
};
