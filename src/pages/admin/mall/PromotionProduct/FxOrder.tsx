import WeModal from "@/components/WeModal/WeModal";
import WeTable from "@/components/WeTable";
import { Api } from "./Api";
import { DatePresetRanges, formatDate } from "@/utils/Tools";
import { DatePicker, Form } from "antd";

export const FxOrder = (props: { children: any; uid: any }) => {
  return (
    <WeModal trigger={props.children} title="推广佣金" footer={false} width={1000}>
      <WeTable
        tableProps={{ scroll: { x: "max-content" } }}
        request={(p) => Api.getFxOrder({ params: { ...p, promotionUserId: props.uid } })}
        searchNum={2}
        search={[
          <Form.Item label="选择日期" name="PayDate">
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { fixed: true, title: "会员", dataIndex: "vipUser", render: (c) => c?.name },
          { title: "门店", dataIndex: "shop", render: (c) => c?.name },
          { title: "项目", dataIndex: "productName" },
          { title: "支付金额", dataIndex: "payMoney" },
          { title: "订单号", dataIndex: "serialNo" },
          { title: "支付时间", dataIndex: "payDate", render: (c) => formatDate(c) },
          {
            title: "佣金状态",
            dataIndex: "commissionState",
            render: (c) => (
              <>
                {c == 10 && "未入账"}
                {c == 20 && "已入账"}
              </>
            ),
          },
          { title: "预估佣金", dataIndex: "commission" },
        ]}
      />
    </WeModal>
  );
};
