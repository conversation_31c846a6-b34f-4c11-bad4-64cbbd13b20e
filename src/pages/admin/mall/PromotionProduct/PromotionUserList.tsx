import WeTable, { WeTableRef } from "@/components/WeTable";
import { Drawer, Form, Input, Select, Space } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useMount, useSetState } from "react-use";
import { Api } from "./Api";
import dayjs from "dayjs";
import { WechatOutlined } from "@ant-design/icons";
import UserManager from "@/components/UserManager";
import { SexType } from "../member/types";
import { FxMoney } from "./FxMoney";
import { FxOrder } from "./FxOrder";

const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const PromotionUserList = (props: { children: any }) => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
    prole: [] as any[],
  });

  useMount(() => {
    fetchPromotionRole();
  });

  useEffect(() => {
    if (!state.open) return;
    handleReload();
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchPromotionRole = async () => {
    const res = await Api.getPromotionRole();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.roleName, value: item.roleId }));
    setState({ prole: list });
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} onClose={() => setState({ open: false })} width={"80%"} title="推广团队">
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          request={(p) => Api.getPromotionUser({ params: p })}
          search={[
            <Form.Item label="推广角色" name={`promotionRoleId`}>
              <Select
                options={state.prole}
                optionFilterProp="label"
                placeholder="请选择推广角色"
                allowClear
                showSearch
              />
            </Form.Item>,
            <Form.Item label="会员" name={`searchKey`}>
              <Input placeholder="会员号/姓名/手机号" />
            </Form.Item>,
          ]}
          columns={[
            {
              fixed: "left",
              title: "会员姓名",
              dataIndex: "name",
              render: (c, item) => (
                <div className="flex items-center gap-1">
                  <WechatOutlined
                    className="text-([16px] #2aae67) data-[disabled=true]:invisible"
                    data-disabled={!item.wxsRegisterTag}
                  />
                  {c ? (
                    <UserManager userId={item.id}>
                      <a>{c}</a>
                    </UserManager>
                  ) : (
                    "--"
                  )}
                </div>
              ),
            },
            // { title: "建档门店", dataIndex: "shopName" },
            { title: "会员号", dataIndex: "vipCard" },
            { title: "手机", dataIndex: "mobile" },
            { title: "顾客来源", dataIndex: "sourceName", render: (c) => (c ? c : "--") },
            { title: "渠道类型", dataIndex: "channelName", render: (c) => (c ? c : "--") },
            {
              title: "性别",
              dataIndex: "sex",
              render: (c) => (c ? SexType.find((n) => n.id == c)?.name : "--"),
            },
            {
              title: "生日",
              dataIndex: "birthday",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "--"),
            },
            { title: "会员等级", dataIndex: "vipLevelName" },
            {
              title: "有效期",
              dataIndex: "expireDate",
              render: (c, item) => (item.foreverTag == 1 ? "永久有效" : c ? dayjs(c).format("YYYY-MM-DD") : "--"),
            },
            { title: "钱包余额", dataIndex: "balance" },
            // { title: "积分", dataIndex: "integral" },
            { title: "M币", dataIndex: "coin" },
            { title: "最近消费", dataIndex: "lastConsumeMoney" },
            { title: "累计消费", dataIndex: "totalConsumeMoney" },
            {
              title: "最近到院",
              dataIndex: "lastServiceTime",
              render: (c) => (c ? dayjs(c).format("YYYY-MM-DD") : "--"),
            },
            { title: "所属客服", dataIndex: "adviserName", render: (c) => (c ? c : "--") },
            // { title: "所属医生", dataIndex: "doctorName", render: (c) => c ? c : "--", },
            { title: "所属开发", dataIndex: "developerName", render: (c) => (c ? c : "--") },
            {
              title: "推荐人",
              dataIndex: "promotionUser",
              render: (c) => c?.name ?? "--",
            },

            {
              title: "建档时间",
              dataIndex: "createDate",
              width: 160,
              render: (c) => fdate(c),
            },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <FxMoney uid={item.appUserId}>
                      <a>推广佣金</a>
                    </FxMoney>
                    <FxOrder uid={item.appUserId}>
                      <a>推广订单</a>
                    </FxOrder>
                  </Space>
                );
              },
            },
            // {
            //   fixed: "right",
            //   title: "操作",
            //   render: (item: any) => {
            //     return (
            //       <Space>
            //         <UserInfoEdit title={`编辑会员 - ${item.name}`} data={item} onOk={handleReload}>
            //           <a>编辑</a>
            //         </UserInfoEdit>
            //         {/* <Popconfirm title={`确定要删除会员 - ${item.name}吗？`} onConfirm={() => handleDel(item)}>
            //           <a>删除</a>
            //         </Popconfirm> */}
            //       </Space>
            //     );
            //   },
            // },
          ]}
        />
      </Drawer>
    </>
  );
};
