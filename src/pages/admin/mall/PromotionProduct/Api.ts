import { makeApi } from "@/utils/Api";

export const Api = {
  getMarketProduct: makeApi("get", `/api/mallMarketProduct`),
  getMarketProductInfo: makeApi("get", `/api/mallMarketProduct`, true),
  addMarketProduct: makeApi("post", `/api/mallMarketProduct`),
  putMarketProduct: makeApi("put", `/api/mallMarketProduct`),
  delMarketProduct: makeApi("delete", `/api/mallMarketProduct`),
  batchModifySaleIn: makeApi("post", `/api/mallMarketProduct/batch_modify_sale_in`),

  getRule: makeApi("get", `/api/mallMarketProduct/query_rule`),
  putRule: makeApi("post", `/api/mallMarketProduct/save_rule`),

  getPromotionRole: makeApi("get", `/api/promotionCenter/query_promotion_role_list`),
  getPromotionUser: makeApi("get", `/api/promotionCenter/query_promotion_user_list`),

  getFxMoney: makeApi("get", `/api/appUserBalanceLog`),
  getFxOrder: makeApi("get", `/api/promotionCenter/query_promotion_commission_order_list`),
};
