import UserManager from "@/components/UserManager";
import WeTable, { WeTableRef } from "@/components/WeTable";
import MallApi from "@/services/MallApi";
import { renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { EvaluationState } from "./types";
import { DatePicker, Form, Input, Radio, Space, Typography } from "antd";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import { useRef } from "react";
import { DatePresetRanges } from "@/utils/Tools";
import ModalDetail from "../serviceOrder/ModalDetail";
import { useSetState } from "react-use";
import { StarOutlined, TwitchOutlined } from "@ant-design/icons";

const ServiceEvaluation = (props: { userId?: any; doctorId?: any; goodsKeeper?: any }) => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    total: {} as any,
  });

  const fetchList = (params: any) => {
    return MallApi.getServiceOrderEvaluation({ params });
  };

  const fetchTotal = async (params: any) => {
    const res = await MallApi.getServiceOrderEvaluationTotal({ params });
    setState({ total: res });
  };

  return (
    <WeTable
      size={props.userId ? 10 : undefined}
      ref={tableRef}
      tableProps={{ scroll: { x: "max-content" } }}
      params={{ queryType: 2 }}
      request={(params) => {
        fetchTotal(params);
        return fetchList(params);
      }}
      extra={
        <div className="mt-5px flex">
          <div className="text-(14px #000/80) fw-bold">
            <TwitchOutlined style={{ color: "green" }} /> 评论数量：{state.total?.successCount} / {state.total?.totalCount}
          </div>
          <div className="ml-20px text-(14px #000/80) fw-bold">
            <StarOutlined style={{ color: "red" }} /> 门店评分：{state.total?.shopAverageScore}
          </div>
          <div className="ml-20px text-(14px #000/80) fw-bold">
            <StarOutlined style={{ color: "blue" }} /> 医生评分：{state.total?.doctorAverageScore}
          </div>
        </div>
      }
      // title={
      //   <>
      //     <Rule>
      //       <Button type="primary" icon={<PaperClipOutlined />} >设置评价奖励</Button>
      //     </Rule>
      //   </>
      // }
      search={[
        <Form.Item label="治疗日期" name={`CreateDate`}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
        !props.userId && (
          <Form.Item label="会员" name={`shopVipUserId`}>
            <VipPickerInput />
          </Form.Item>
        ),
        <Form.Item label="评价状态" name={`evaluationState`}>
          <Radio.Group options={EvaluationState.map(item => ({ label: item.name, value: item.id }))} />
        </Form.Item>,
        <Form.Item label="评价日期" name={`EvaluationDate`}>
          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
        </Form.Item>,
        <Form.Item label="治疗单号" name={`serialNo`}>
          <Input placeholder="请输入治疗单号" />
        </Form.Item>,
        <Form.Item label="项目名称" name={`productName`}>
          <Input placeholder="请输入项目名称" />
        </Form.Item>,
      ]}
      columns={[
        {
          fixed: "left",
          title: "会员姓名",
          dataIndex: "vipUser",
          hide: props.userId,
          render: (c) => (
            c ? <UserManager userId={c?.id}>
              <a>{c?.name}</a>
            </UserManager>
              : "--"
          ),
        },
        {
          title: "治疗门店",
          dataIndex: "shop",
          hide: !props.userId,
          render: (c) => c?.name ?? "--",
        },
        { title: "医生", dataIndex: "doctorName" },
        { title: "治疗师", dataIndex: "nurseName" },
        { title: "治疗单号", dataIndex: "serialNo" },
        {
          title: "治疗日期",
          dataIndex: "createDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
        },
        {
          title: "治疗项目",
          dataIndex: "itemList",
          render: (c) => {
            return (
              <div className="w-400px">
                {c?.map((sub: any) => {
                  return (
                    <div className="flex items-center">
                      <div className="flex-1 min-w-0 line-clamp-1" title={sub.productName + " " + (sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName ? " - " + sub.productSpecificationName : "") + " * " + sub.serviceNum}>
                        <div className="line-clamp-1">
                          {sub?.productName}
                          {sub?.productSpecificationName && sub?.productName !== sub?.productSpecificationName && (
                            <span className="text-gray-400 ml-2px">
                              {" - " + sub.productSpecificationName}
                            </span>
                          )}
                          &nbsp;* {sub.serviceNum}
                        </div>
                      </div>
                      {/* <div className="w-10 text-right">{sub.serviceNum}</div> */}
                    </div>
                  );
                })}
              </div>
            );
          },
        },
        { title: "划扣金额", dataIndex: "deductionMoney" },
        {
          title: "评价状态",
          dataIndex: "evaluationState",
          render: (c) => renderTag(EvaluationState, c),
        },
        {
          title: "评价日期",
          dataIndex: "evaluationDate",
          render: (c) => (c ? dayjs(c).format("YYYY-MM-DD HH:mm:ss") : "--"),
        },
        {
          title: "门店评分",
          dataIndex: "evaluationScore",
          render: (c) => <>{c ? c : "-- "}</>,
        },
        {
          title: "医生评分",
          render: (c) => <>{c?.doctorCommentData?.score ? c?.doctorCommentData?.score : "-- "}</>,
        },
        {
          title: "评价内容",
          dataIndex: "evaluationDesc",
          render: (c) => (
            <>
              {c ? (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c}
                </Typography.Text>
              ) : (
                "--"
              )}
            </>
          ),
        },
        /* {
          title: "医生评价",
          render: (c) => (
            <>
              {c?.doctorCommentData?.content ? (
                <Typography.Text style={{ width: 150 }} ellipsis={{ tooltip: true }}>
                  {c?.doctorCommentData?.content}
                </Typography.Text>
              ) : (
                "--"
              )}
            </>
          ),
        }, */
        {
          fixed: "right",
          title: "操作",
          render: (item) => (
            <Space>
              <ModalDetail data={item}>
                <Typography.Link>详情</Typography.Link>
              </ModalDetail>
            </Space>
          ),
        },
      ]}
    />
  );
};

export default ServiceEvaluation;
