import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, message, Row } from "antd";
import Mall<PERSON><PERSON> from "@/services/MallApi";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    await MallApi.putServiceOrderRule({ data });
    message.success("保存成功");
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await MallApi.getServiceOrderRule();
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="设置评价奖励" width={400} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`evaluationGiveCoin`} label="奖励M币">
              <Input placeholder="请输入奖励 " addonAfter="M币" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
