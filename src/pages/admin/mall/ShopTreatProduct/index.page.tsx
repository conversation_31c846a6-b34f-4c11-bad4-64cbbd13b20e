import { <PERSON><PERSON>, Card, Col, Dropdown, Form, Input, Modal, Pagination, Popconfirm, Row, Select, Space, Table, Tooltip, Tree, Typography, message } from "antd";
import { useMount, useSetState } from "react-use";
import {
  ApartmentOutlined,
  ClearOutlined,
  DeleteOutlined,
  DownOutlined,
  FormOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  UpOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
} from "@ant-design/icons";
import Style from "../productcategory/index.module.scss";
import EditType from "../productcategory/EditType";
import { useState } from "react";
import MallApi from "@/services/MallApi";
import SelectShop from "@/components/Units/SelectShop";
import { SaleInType } from "./types";
import { renderTag } from "@/utils/Tools";
import AsyncSwitch from "@/components/AsyncSwitch";
import { ProductModelType } from "@/components/GoodsPicker/types";
import { Api } from "./Api";
import { EditModal } from "./editv2/EditModal";
import { useTable } from "@/components/WeTablev2/useTable";
import MallCommissionConfigApi from "../../operation/CommissionConfig/api";

const SelectShopComp = (props: any) => {
  const value = (props.value || "").split(",").filter((n: any) => n);
  return <SelectShop mode="multiple" value={value} onChange={(e) => props.onChange((e || []).join(","))} />;
};

//线下治疗项目
const property = 1;

const Product1Page = () => {
  const [fold, setFold] = useState(true);
  const [state, setState] = useSetState({
    keys: [] as any[],

    types: [] as any[],
    typeId: null as any,
    exdata: {} as any,

    editOpen: false,
    editId: "" as any,
    editStep: 0,
    commissionConfigList: [] as any[],
  });

  const table = useTable({
    params: { shopCategoryId: state.typeId, property },
    onFetch: (p) => {
      setState({ keys: [] });
      return Api.getProductList({ params: p });
    },
  });

  useMount(() => {
    fetchTypes();
    fetchCommissionConfig();
  });

  const fetchCommissionConfig = async () => {
    const res = await MallCommissionConfigApi.selectMallCommissionConfig();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ commissionConfigList: list });
  };

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  const fetchTypes = async () => {
    const params = { pageSize: 9999, property };
    const res = await MallApi.getShopProductTypeForSelect({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await MallApi.delShopProductType({ data });
    message.success("删除分类成功");
    fetchTypes();
  };

  const handleDelItem = async (item: any) => {
    const data = { ids: item.id };
    await Api.delProduct({ data });
    message.success("删除项目成功");
    handleReload();
  };

  return (
    <div>
      <Row wrap={false} gutter={10}>
        <Col flex={`300px`} style={{ minWidth: 0 }}>
          <Card
            classNames={{ body: "!p-2" }}
            title={`项目分类`}
            style={{ height: "100%" }}
            extra={
              <EditType title={`添加项目分类`} property={property} onOk={() => fetchTypes()} tree={state.types}>
                <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
              </EditType>
            }
          >
            <Tree.DirectoryTree
              className={Style.tree}
              treeData={state.types}
              showIcon={false}
              selectedKeys={[state.typeId]}
              onSelect={(e) => {
                const key = e[0];
                if (state.typeId === key) {
                  setState({ typeId: "" });
                } else {
                  setState({ typeId: key });
                }
              }}
              titleRender={(item) => {
                return (
                  <div className={Style.row} key={item.key}>
                    <div className={Style.title}>{item.title}</div>
                    <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                      <EditType title={`编辑分类`} property={property} onOk={() => fetchTypes()} tree={state.types} data={item}>
                        <Tooltip title="编辑分类">
                          <FormOutlined className={Style.ico} />
                        </Tooltip>
                      </EditType>

                      <EditType title={`添加下级`} property={property} onOk={() => fetchTypes()} tree={state.types} data={{ parentId: item.id }}>
                        <Tooltip title="添加下级">
                          <ApartmentOutlined className={Style.ico} />
                        </Tooltip>
                      </EditType>

                      <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                        <Tooltip title="删除分类">
                          <DeleteOutlined className={Style.ico} />
                        </Tooltip>
                      </Popconfirm>
                    </div>
                  </div>
                );
              }}
            />
          </Card>
        </Col>
        <Col flex={"auto"}>
          <Form form={table.form} onValuesChange={table.onFormChange}>
            <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
              <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
                <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
                  <Form.Item label="名称" name={`name`}>
                    <Input placeholder="请输入名称" />
                  </Form.Item>
                  <Form.Item label="适用门店" name={`shopIds`}>
                    <SelectShopComp />
                  </Form.Item>
                  <Form.Item label="上下架" name={`saleIn`}>
                    <Select options={SaleInType} fieldNames={{ label: "name", value: "id" }} placeholder="请选择上下架" allowClear />
                  </Form.Item>
                  <Form.Item label="模式" name={`productModel`}>
                    <Select options={ProductModelType} fieldNames={{ label: "name", value: "id" }} placeholder="请选择模式" allowClear />
                  </Form.Item>
                  {/* <Form.Item label="推荐标识" name={`recommendTag`}>
                    <Select
                      options={[
                        { label: "开启", value: 1 },
                        { label: "关闭", value: 0 },
                      ]}
                      placeholder="请选择推荐标识"
                      allowClear
                    />
                  </Form.Item> */}
                  <Form.Item label="M币赠送" name={`integralTag`}>
                    <Select
                      options={[
                        { label: "开启", value: 1 },
                        { label: "关闭", value: 0 },
                      ]}
                      placeholder="请选择M币赠送"
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item label="会员折扣" name={`preferentialTag`}>
                    <Select
                      options={[
                        { label: "开启", value: 1 },
                        { label: "关闭", value: 0 },
                      ]}
                      placeholder="请选择会员折扣"
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item label="佣金方案" name={`commissionConfigId`}>
                    <Select options={state.commissionConfigList} allowClear showSearch optionFilterProp="label" placeholder="请选择佣金方案" />
                  </Form.Item>
                </div>

                <div className="flex gap-2 pl-3">
                  <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
                  <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                    搜索
                  </Button>
                  <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
                </div>
              </div>
            </Card>

            <Card
              classNames={{ body: "!p-0" }}
              title={
                <Space>
                  <Button type="primary" icon={<PlusOutlined />} onClick={() => setState({ editOpen: true, editId: "", editStep: 0 })}>
                    新增项目
                  </Button>
                </Space>
              }
            >
              <Table
                rowKey={"id"}
                dataSource={table.state.list}
                columns={[
                  { fixed: "left", title: "名称", dataIndex: "name" },
                  { title: "编号", dataIndex: "serialNo" },
                  { title: "模式", dataIndex: "productModel", render: (c) => renderTag(ProductModelType, c) },
                  {
                    title: "分类",
                    dataIndex: "shopCategoryName",
                    width: 300,
                    render: (c) => (
                      <Typography.Text style={{ width: 300 }} ellipsis={{ tooltip: true }}>
                        {c}
                      </Typography.Text>
                    ),
                  },
                  { title: "划线价", dataIndex: "oldPrice", render: (c) => <del>&yen;{c}</del> },
                  { title: "售价", dataIndex: "salePrice", render: (c) => <span>&yen;{c}</span> },
                  { title: "销量", dataIndex: "soldCount" },
                  { title: "排序", dataIndex: "sort" },
                  {
                    title: "上下架",
                    // dataIndex: "saleIn",
                    render: (c) => (
                      <AsyncSwitch
                        onClick={async () => {
                          await Api.batchModifySaleIn({ data: { operType: 1, ids: c.id, saleIn: Number(!c.saleIn) } });
                          handleReload();
                        }}
                        value={!!c.saleIn}
                      />
                    ),
                  },
                  {
                    fixed: "right",
                    title: "操作",
                    render: (item) => (
                      <Space>
                        <a onClick={() => setState({ editOpen: true, editId: item.id, editStep: 0 })}>编辑商品</a>
                        <a onClick={() => setState({ editOpen: true, editId: item.id, editStep: 1 })}>编辑规格</a>
                        <Popconfirm title={`确定删除 ${item.name} 吗？`} onConfirm={() => handleDelItem(item)}>
                          <a>删除</a>
                        </Popconfirm>
                      </Space>
                    ),
                  },
                ]}
                pagination={false}
                rowSelection={{
                  selectedRowKeys: state.keys,
                  onChange: (selectedRowKeys) => {
                    setState({ keys: selectedRowKeys });
                  },
                }}
              />
              <div className="p-4 flex items-center">
                <Space>
                  <Button
                    onClick={() => {
                      const len = state.keys.length;
                      if (len == table.state.list.length) {
                        setState({ keys: [] });
                      } else {
                        setState({ keys: table.state.list.map((n) => n.id) });
                      }
                    }}
                  >
                    全选
                  </Button>
                  <Dropdown
                    menu={{
                      items: [
                        { label: "批量上架", key: "up", icon: <VerticalAlignTopOutlined /> },
                        { label: "批量下架", key: "down", icon: <VerticalAlignBottomOutlined /> },
                        { label: "批量删除", key: "del", icon: <DeleteOutlined />, danger: true },
                      ],
                      onClick: (e) => {
                        if (!state.keys.length) return message.error("请先进行选择");

                        if (e.key == "up") {
                          Modal.confirm({
                            title: "确定要批量上架这些商品吗？",
                            onOk: async () => {
                              const data = { saleIn: 1, ids: state.keys.join(",") };
                              await Api.batchModifySaleIn({ data });
                              message.success("批量上架成功");
                              handleReload();
                            },
                          });
                        }

                        if (e.key == "down") {
                          Modal.confirm({
                            title: "确定要批量下架这些商品吗？",
                            onOk: async () => {
                              const data = { saleIn: 0, ids: state.keys.join(",") };
                              await Api.batchModifySaleIn({ data });
                              message.success("批量下架成功");
                              handleReload();
                            },
                          });
                        }

                        if (e.key == "del") {
                          Modal.confirm({
                            title: "确定要批量删除这些商品吗？",
                            onOk: async () => {
                              const data = { ids: state.keys.join(",") };
                              await Api.delProduct({ data });
                              message.success("批量删除成功");
                              handleReload();
                            },
                          });
                        }
                      },
                    }}
                  >
                    <Button>
                      批量操作
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                  <div className="text-#666 text-xs">
                    {table.state.list.length}条数据(已选{state.keys.length}条)
                  </div>
                </Space>
                <Pagination
                  className="ml-a"
                  {...{
                    current: table.state.page,
                    pageSize: table.state.size,
                    total: table.state.total,
                    showSizeChanger: true,
                    showTotal: (total) => <div>共 {total} 条数据</div>,
                    pageSizeOptions: [5, 10, 20, 50, 100],
                    onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
                  }}
                />
              </div>
            </Card>
          </Form>
        </Col>
      </Row>

      <EditModal
        open={state.editOpen}
        pid={state.editId}
        step={state.editStep}
        onClose={() => {
          setState({ editOpen: false });
          handleReload();
        }}
      />
    </div>
  );
};

export default Product1Page;
