import { Card, Drawer, Steps } from "antd";
import { useSetState } from "react-use";
import { Step0 } from "./step0";
import { useEffect } from "react";
import { Step1 } from "./step1";

export const EditModal = (props: { open?: boolean; step?: any; pid?: any; onClose?: any }) => {
  const [state, setState] = useSetState({
    // open: false,
    step: 0,
    pid: "",
    key: 0,
  });

  useEffect(() => {
    if (props.open) {
      setState({ step: props.step, pid: props.pid, key: state.key + 1 });
    }
  }, [props.open]);

  const onClose = () => props.onClose?.();

  return (
    <>
      <Drawer
        title={`配置项目`}
        open={props.open}
        onClose={onClose}
        keyboard={false}
        width={"70%"}
        maskClosable={false}
      >
        <Card>
          <div>
            <div className="w-[600px] m-a mb-10">
              <Steps
                current={state.step}
                items={[
                  { title: "商品信息", description: <span>基础信息</span> },
                  { title: "规格信息", description: <span>规格配置</span> },
                ]}
              />
            </div>

            <div className="max-w-1200px mx-a">
              {state.step == 0 && (
                <Step0 key={state.key} pid={state.pid} goNext={(pid) => setState({ step: 1, pid })} onClose={onClose} />
              )}
              {state.step == 1 && (
                <Step1 key={state.key} pid={state.pid} goPrev={() => setState({ step: 0 })} onClose={onClose} />
              )}
            </div>
          </div>
        </Card>
      </Drawer>
    </>
  );
};
