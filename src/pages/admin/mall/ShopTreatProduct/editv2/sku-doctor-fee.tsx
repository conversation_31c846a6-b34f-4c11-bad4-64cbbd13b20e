import WeModal from "@/components/WeModal/WeModal";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { Button, Form, InputNumber, Table, Tooltip } from "antd";
import { Api } from "../Api";
import ShopEmployeePicker from "@/components/ShopEmployeePicker";

const PlainVlaue = (p: any) => p.value;

export const SkuDoctorFee = (props: any) => {
  const [form] = Form.useForm();

  const onOpen = async () => {
    form.resetFields();
    const res = await Api.getSkuInfo(props.skuId);
    form.setFieldsValue(res);
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.id = props.skuId;
    const res = await Api.putSku({ data });
    props.onOk?.(res);
  };

  return (
    <WeModal trigger={props.children} title={
      <>
        医生服务费(选填)
        <Tooltip title="若有医生需要收取额外的服务费，请在此填写，不收取可不填">
          <QuestionCircleOutlined
            style={{
              cursor: "pointer",
              marginLeft: 2,
            }}
          />
        </Tooltip>
      </>
    } width={800} onOpen={onOpen} onOk={onOk}>
      <Form form={form} labelCol={{ className: "min-w-80px" }}>
        <div className="pb-8">
          <Form.List name="serviceFeeList" initialValue={[]}>
            {(fields, action) => {
              return (
                <div>
                  <Table
                    className="[&_.ant-form-item]:m-0"
                    size="small"
                    locale={{ emptyText: "暂无数据" }}
                    pagination={false}
                    dataSource={fields}
                    columns={[
                      {
                        title: "门店",
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "serviceShopName"]}>
                              <PlainVlaue />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "医生",
                        width: 250,
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "serviceUserName"]}>
                              <PlainVlaue />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "服务费",
                        width: 250,
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "serviceFee"]} rules={[{ required: true }]} help={false}>
                              <InputNumber min={0} addonAfter="元" placeholder="请输入" className="w-full" />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "操作",
                        width: "100px",
                        render: (c) => <a onClick={() => action.remove(c.name)}>删除</a>,
                      },
                    ]}
                  />
                  <ShopEmployeePicker
                    params={{ queryType: 9, positionCode: "Doctor,Therapist" }}
                    onSelect={(rows) => {
                      const list: any[] = form.getFieldValue("serviceFeeList");

                      rows.forEach((item) => {
                        if (list.every((n) => !(n.serviceShopId === item.shopId && n.serviceUserId === item.user.id)))
                          list.push({
                            serviceShopId: item.shopId,
                            serviceShopName: item.shopName,
                            serviceUserId: item.user.id,
                            serviceUserName: item.user.name,
                            serviceFee: undefined,
                          });
                      });

                      form.setFieldValue("serviceFeeList", list);
                    }}
                  >
                    <Button block type="dashed" icon={<PlusOutlined />} className="mt-4">
                      添加医生
                    </Button>
                  </ShopEmployeePicker>
                </div>
              );
            }}
          </Form.List>
        </div>
      </Form>
    </WeModal>
  );
};
