import { CloseCircleOutlined, PlusCircleOutlined, PlusOutlined } from "@ant-design/icons";
import { Button, Form, Input, InputNumber, Space, Table } from "antd";
import { useMount, useSetState } from "react-use";
import { Api } from "../Api";
import React from "react";
import { SkuEdit } from "./sku-edit";
import { SkuShop } from "./sku-shop";
import { SkuMate } from "./sku-mate";
import AsyncSwitch from "@/components/AsyncSwitch";
import { useDebounceFn } from "@/utils/Tools";

export const Step1 = (props: { pid?: string; goPrev?: any; onClose?: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    list: [] as any[],
    info: null as any,
    isPackage: false,
  });

  useMount(() => {
    getDetail();
    getSku();
  });

  const getDetail = async () => {
    const res = await Api.getProduct(props.pid);
    form.setFieldsValue(res);
    setState({ info: res, isPackage: res.productModel == 2 });
  };

  const getSku = async () => {
    const params = { productId: props.pid };
    const res = await Api.getSku({ params });
    setState({ list: res?.list || [] });
  };

  const onUpdateSku = async () => {
    const data = await form.validateFields();
    data.id = props.pid;

    await Api.putPorduct({ data });

    await getSku();
  };

  const __putSku__ = useDebounceFn(async (data: any) => {
    await Api.putSku({ data });
    getSku();
  }, 500);

  return (
    <div>
      <Form form={form}>
        <Form.Item label="规格配置">
          <Form.List name="attributeList" initialValue={[]}>
            {(lv1, lv1act) => {
              return (
                <div>
                  <Table
                    bordered
                    className="[&_.ant-table-tbody_.ant-table-cell:first-child]:bg-#f5f5f5"
                    size="small"
                    locale={{ emptyText: "暂无数据" }}
                    pagination={false}
                    dataSource={lv1}
                    columns={[
                      {
                        title: "规格名",
                        width: 200,
                        render: (c) => {
                          return (
                            <div className="flex items-center">
                              <Form.Item noStyle name={[c.name, "id"]} hidden>
                                <Input />
                              </Form.Item>
                              <Form.Item noStyle name={[c.name, "name"]} rules={[{ required: true }]} help={false}>
                                <Input className="w-full" placeholder="请输入" addonAfter={<CloseCircleOutlined className="text-(red 16px) cursor-pointer" onClick={() => lv1act.remove(c.name)} />} />
                              </Form.Item>
                            </div>
                          );
                        },
                      },
                      {
                        title: "规格值",
                        render: (c) => {
                          return (
                            <Form.List name={[c.name, "attributeValueList"]} initialValue={[]}>
                              {(lv2, lv2act) => {
                                return (
                                  <div className="flex items-center gap-4 flex-wrap">
                                    {lv2.map((item, idx) => (
                                      <React.Fragment key={idx}>
                                        <Form.Item noStyle name={[item.name, "id"]} hidden>
                                          <Input />
                                        </Form.Item>
                                        <Form.Item noStyle key={item.name} name={[item.name, "attributeValue"]} rules={[{ required: true }]} help={false}>
                                          <Input
                                            className="w-160px"
                                            placeholder="请输入"
                                            addonAfter={<CloseCircleOutlined className="text-(red 16px) cursor-pointer" onClick={() => lv2act.remove(item.name)} />}
                                          />
                                        </Form.Item>
                                      </React.Fragment>
                                    ))}
                                    <PlusCircleOutlined className="text-#999 text-20px cursor-pointer" onClick={() => lv2act.add({ attributeValue: "" })} />
                                  </div>
                                );
                              }}
                            </Form.List>
                          );
                        },
                      },
                    ]}
                  />
                  <div className="flex gap-4 items-center mt-4">
                    <Button icon={<PlusOutlined />} type="dashed" className="w-200px" onClick={() => lv1act.add({ name: "", attributeValueList: [{ attributeValue: "" }] })}>
                      添加规格
                    </Button>
                    <Button type="primary" onClick={onUpdateSku}>
                      保存配置
                    </Button>
                  </div>
                </div>
              );
            }}
          </Form.List>
        </Form.Item>
      </Form>

      <Form.Item label="规格列表">
        <Table
          rowKey="id"
          size="small"
          locale={{ emptyText: "暂无数据" }}
          pagination={false}
          dataSource={state.list}
          columns={[
            { title: "规格", width: 200, dataIndex: "name" },
            {
              title: "划线价",
              width: 150,
              dataIndex: "oldPrice",
              render: (c, item) => (
                <InputNumber
                  className="w-full"
                  value={c}
                  onChange={async (v) => {
                    const data = { id: item.id, oldPrice: v.toFixed(2) };
                    await __putSku__(data);
                  }}
                />
              ),
            },
            {
              title: "售价",
              width: 150,
              dataIndex: "salePrice",
              render: (c, item) => (
                <InputNumber
                  className="w-full"
                  value={c}
                  onChange={async (v) => {
                    const data = { id: item.id, salePrice: v.toFixed(2) };
                    await __putSku__(data);
                  }}
                />
              ),
            },
            {
              title: "次数/数量",
              width: 150,
              dataIndex: "includeNum",
              hidden: state.isPackage,
              render: (c, item) =>
                state.info?.productModel === 1 ? (
                  <InputNumber
                    className="w-full"
                    value={c}
                    onChange={async (v) => {
                      // await Api.putSku({ data: { id: item.id, includeNum: v } });
                      // getSku();
                      const data = { id: item.id, includeNum: v };
                      await __putSku__(data);
                    }}
                  />
                ) : (
                  "--"
                ),
            },
            {
              title: "上下架",
              // dataIndex: "saleIn",
              render: (c) => (
                <AsyncSwitch
                  onClick={async () => {
                    await Api.batchModifySkuSaleIn({ data: { ids: c.id, saleIn: Number(!c.saleIn) } });
                    getSku();
                  }}
                  value={!!c.saleIn}
                />
              ),
            },
            {
              title: "操作",
              // width: 100,
              render: (c) => (
                <Space>
                  <SkuEdit skuId={c.id} productModel={state.info?.productModel} title={c.name} onOk={getSku}>
                    <a>编辑规格</a>
                  </SkuEdit>

                  <SkuShop skuId={c.id} onOk={getSku}>
                    <a>门店售价</a>
                  </SkuShop>

                  <SkuMate skuId={c.id} onOk={getSku}>
                    <a>关联物资</a>
                  </SkuMate>
                </Space>
              ),
            },
          ]}
        />
      </Form.Item>

      <div className="flex items-center justify-center gap-4">
        <Button type="default" onClick={props.onClose}>
          返回列表
        </Button>
        <Button type="primary" onClick={props.goPrev}>
          上一步
        </Button>
        <Button type="primary" onClick={props.onClose}>
          保存信息
        </Button>
      </div>
    </div>
  );
};
