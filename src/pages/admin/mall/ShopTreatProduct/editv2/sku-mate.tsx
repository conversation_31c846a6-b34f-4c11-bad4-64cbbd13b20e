import WeModal from "@/components/WeModal/WeModal";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import { Button, Form, InputNumber, Table, Tooltip } from "antd";
import { Api } from "../Api";
import MatterPicker from "@/components/MatterPicker";

const PlainVlaue = (p: any) => p.value;

export const SkuMate = (props: any) => {
  const [form] = Form.useForm();

  const onOpen = async () => {
    form.resetFields();
    const res = await Api.getSkuInfo(props.skuId);
    form.setFieldsValue(res);
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.id = props.skuId;
    const res = await Api.putSku({ data });
    props.onOk?.(res);
  };

  return (
    <WeModal trigger={props.children} title={
      <>
        关联物资(选填)
        <Tooltip title="填写关联的耗材,项目治疗时可进行自动扣减物资库存">
          <QuestionCircleOutlined
            style={{
              cursor: "pointer",
              marginLeft: 2,
            }}
          />
        </Tooltip>
      </>
    } width={800} onOpen={onOpen} onOk={onOk}>
      <Form form={form} labelCol={{ className: "min-w-80px" }}>
        <div className="pb-8">
          <Form.List name="goodsList" initialValue={[]}>
            {(fields, action) => {
              return (
                <div>
                  <Table
                    className="[&_.ant-form-item]:m-0"
                    size="small"
                    locale={{ emptyText: "暂无数据" }}
                    pagination={false}
                    dataSource={fields}
                    columns={[
                      {
                        title: "物资名",
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "goodsName"]}>
                              <PlainVlaue />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "数量",
                        width: 250,
                        render: (c) => {
                          return (
                            <Form.Item name={[c.name, "num"]} rules={[{ required: true }]} help={false}>
                              <InputNumber
                                min={1}
                                addonAfter={
                                  <Form.Item noStyle name={[c.name, "unit"]}>
                                    <PlainVlaue />
                                  </Form.Item>
                                }
                                placeholder="请输入"
                                className="w-full"
                              />
                            </Form.Item>
                          );
                        },
                      },
                      {
                        title: "操作",
                        width: "100px",
                        render: (c) => <a onClick={() => action.remove(c.name)}>删除</a>,
                      },
                    ]}
                  />

                  <MatterPicker
                    onSelect={(rows) => {
                      const list: any[] = form.getFieldValue("goodsList");
                      rows.forEach((item) => {
                        if (list.every((n) => n.goodsId !== item.id))
                          list.push({
                            goodsId: item.id,
                            goodsName: item.name,
                            unit: item.unit,
                            num: 1,
                          });
                      });

                      form.setFieldValue("goodsList", list);
                    }}
                  >
                    <Button block type="dashed" icon={<PlusOutlined />} className="mt-4">
                      添加物资
                    </Button>
                  </MatterPicker>
                </div>
              );
            }}
          </Form.List>
        </div>
      </Form>
    </WeModal>
  );
};
