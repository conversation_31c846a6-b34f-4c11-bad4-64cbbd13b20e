import WeModal from "@/components/WeModal/WeModal";
import { Button, Divider, Form, Input, InputNumber, Table } from "antd";
import { SwitchContent } from "./unit";
import { PlusOutlined } from "@ant-design/icons";
import { Api } from "../Api";
import { GoodsSkuPicker } from "@/components/GoodsSkuPicker";

const PlainVlaue = (p: any) => p.value;

export const SkuEdit = (props: { children: any; title: any; skuId: any; productModel?: any; onOk?: any }) => {
  const [form] = Form.useForm();
  const isPackage = props.productModel == 2;

  const onOpen = async () => {
    form.resetFields();
    const res = await Api.getSkuInfo(props.skuId);
    form.setFieldsValue(res);
  };

  const onOk = async () => {
    const data = await form.validateFields();
    data.id = props.skuId;
    const res = await Api.putSku({ data });
    props.onOk?.(res);
  };

  return (
    <WeModal trigger={props.children} title={`编辑规格 - [ ${props.title} ]`} width={1000} onOk={onOk} onOpen={onOpen}>
      <div className="h-4"></div>
      <Form form={form} labelCol={{ className: "min-w-80px" }}>
        <div className="grid cols-3 gap-x-4">
          <div className="col-span-3 grid cols-3 gap-x-4">
            <Form.Item label="划线价" name="oldPrice" rules={[{ required: true, message: "请输入" }]}>
              <Input placeholder="请输入" addonAfter="元" />
            </Form.Item>

            <Form.Item label="售价" name="salePrice" rules={[{ required: true, message: "请输入" }]}>
              <Input placeholder="请输入" addonAfter="元" />
            </Form.Item>

            {/* <Form.Item label="成本" name="costFee" rules={[{ required: true, message: "请输入" }]}>
              <Input placeholder="请输入" addonAfter="元" />
            </Form.Item> */}

            {!isPackage && (
              <Form.Item label="次数/数量" name="includeNum" rules={[{ required: true, message: "请输入" }]}>
                <Input placeholder="请输入" addonAfter="次/个" />
              </Form.Item>
            )}
          </div>

          <Form.Item label="库存限制" className="col-span-1">
            <SwitchContent name="inventoryLimit">
              <Form.Item noStyle name="inventoryCount">
                <InputNumber className="w-full" placeholder="请输入库存数量" />
              </Form.Item>
            </SwitchContent>
          </Form.Item>
        </div>

        {isPackage && (
          <div className="pb-8">
            <Divider orientation="left">套餐包含项目(必填)</Divider>
            <Form.List name="includeProductList" initialValue={[]}>
              {(fields, action) => {
                return (
                  <div>
                    <Table
                      className="[&_.ant-form-item]:m-0"
                      size="small"
                      locale={{ emptyText: "暂无数据" }}
                      pagination={false}
                      dataSource={fields}
                      columns={[
                        {
                          title: "项目",
                          render: (c) => {
                            return (
                              <Form.Item name={[c.name, "productName"]}>
                                <PlainVlaue />
                              </Form.Item>
                            );
                          },
                        },
                        {
                          title: "规格",
                          width: 250,
                          render: (c) => {
                            return (
                              <Form.Item name={[c.name, "productSpecificationName"]}>
                                <PlainVlaue />
                              </Form.Item>
                            );
                          },
                        },
                        {
                          title: "价格",
                          width: 150,
                          render: (c) => {
                            return (
                              <Form.Item name={[c.name, "salePrice"]} rules={[{ required: true }]} help={false}>
                                <InputNumber min={0} placeholder="请输入" className="w-full" />
                              </Form.Item>
                            );
                          },
                        },
                        {
                          title: "次数/数量",
                          width: 150,
                          render: (c) => {
                            return (
                              <Form.Item name={[c.name, "includeNum"]} rules={[{ required: true }]} help={false}>
                                <InputNumber min={0} placeholder="请输入" className="w-full" />
                              </Form.Item>
                            );
                          },
                        },
                        {
                          title: "操作",
                          width: "100px",
                          render: (c) => <a onClick={() => action.remove(c.name)}>删除</a>,
                        },
                      ]}
                    />
                    <GoodsSkuPicker
                      hideProductModel={true}
                      params={{ productModel: 1, property: 1, isAll: 1 }}
                      onOk={(rows) => {
                        const list: any[] = form.getFieldValue("includeProductList") || [];

                        rows.forEach((item) => {
                          if (list.every((n) => !(n.productSpecificationId === item.productSpecificationId))) {
                            list.push({
                              productId: item.productId,
                              productName: item.productName,
                              productSpecificationId: item.productSpecificationId,
                              productSpecificationName: item.productSpecificationName,
                              salePrice: item.salePrice,
                              includeNum: 1,
                            });
                          }
                        });

                        form.setFieldValue("includeProductList", list);
                      }}
                    >
                      <Button block type="dashed" icon={<PlusOutlined />} className="mt-4">
                        添加项目
                      </Button>
                    </GoodsSkuPicker>
                  </div>
                );
              }}
            </Form.List>
          </div>
        )}
      </Form>
    </WeModal>
  );
};
