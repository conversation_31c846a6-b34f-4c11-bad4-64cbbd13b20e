import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallRfmConfigItemApi from "./api";
import MallRfmConfigItemEdit from "./edit";
import { Form, Input, Space } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallRfmConfigItemPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };


  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallRfmConfigItemApi.getMallRfmConfigItem({ params: p })}
        title={
          <Space>
            <MallRfmConfigItemEdit title={"新增RFM配置"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallRfmConfigItemEdit>
          </Space>
        }
        search={[
          <Form.Item label="公司id" name={`companyId`}>
            <Input placeholder="请输入公司id" />
          </Form.Item>,
          <Form.Item label="配置id" name={`configId`}>
            <Input placeholder="请输入配置id" />
          </Form.Item>,
          <Form.Item label="最近消费分数" name={`recencyScore`}>
            <Input placeholder="请输入最近消费分数" />
          </Form.Item>,
          <Form.Item label="消费频次分数" name={`frequencyScore`}>
            <Input placeholder="请输入消费频次分数" />
          </Form.Item>,
          <Form.Item label="消费总额分数" name={`monetaryScore`}>
            <Input placeholder="请输入消费总额分数" />
          </Form.Item>,
          <Form.Item label="标签id" name={`labelId`}>
            <Input placeholder="请输入标签id" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "公司id", dataIndex: "companyId", render: (c) => c ? c : "--" },
          { title: "配置id", dataIndex: "configId", render: (c) => c ? c : "--" },
          { title: "最近消费分数", dataIndex: "recencyScore" },
          { title: "消费频次分数", dataIndex: "frequencyScore" },
          { title: "消费总额分数", dataIndex: "monetaryScore" },
          { title: "标签id", dataIndex: "labelId", render: (c) => c ? c : "--" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallRfmConfigItemEdit title={`编辑RFM配置`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallRfmConfigItemEdit>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallRfmConfigItemPage;
