import WeModal from "@/components/WeModal/WeModal";
import MallRfmConfigItemApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";

const layout = { row: 10, col: 24 };

const MallRfmConfigItemEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?:any;}) => {

  const [form] = Form.useForm();

  const handleOpen = async() => {
    form.resetFields();
    if (props.data) {
      const data = await MallRfmConfigItemApi.getMallRfmConfigItemInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    if (!data.id)  {
      await MallRfmConfigItemApi.addMallRfmConfigItem({ data });
      message.success("添加RFM配置成功");
    }
    if (data.id) {
      await MallRfmConfigItemApi.putMallRfmConfigItem({ data });
      message.success("修改RFM配置成功");
    }
    props.onOk?.();
  };

return (
  <WeModal trigger={props.children} title={props.title} width={800} onOpen={handleOpen} okButtonProps ={{hidden:props.hideSubmit}} onOk={handleSubmit}>
    <Form form={form} labelCol={{ flex: "100px" }}>
      <Form.Item name={`id`} hidden>
        <Input />
      </Form.Item>
      <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`companyId`} label="公司id" rules={[{ required: false, message: "请输入公司id" }]}>
              <Input placeholder="请输入公司id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`configId`} label="配置id" rules={[{ required: false, message: "请输入配置id" }]}>
              <Input placeholder="请输入配置id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`recencyScore`} label="最近消费分数" rules={[{ required: false, message: "请输入最近消费分数" }]} initialValue={0}>
             <InputNumber min={0} placeholder="请输入最近消费分数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`frequencyScore`} label="消费频次分数" rules={[{ required: false, message: "请输入消费频次分数" }]} initialValue={0}>
             <InputNumber min={0} placeholder="请输入消费频次分数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`monetaryScore`} label="消费总额分数" rules={[{ required: false, message: "请输入消费总额分数" }]} initialValue={0}>
             <InputNumber min={0} placeholder="请输入消费总额分数" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`labelId`} label="标签id" rules={[{ required: false, message: "请输入标签id" }]}>
              <Input placeholder="请输入标签id" />
            </Form.Item>
          </Col>
      </Row>
    </Form>
  </WeModal>
  );
};

export default MallRfmConfigItemEdit;
