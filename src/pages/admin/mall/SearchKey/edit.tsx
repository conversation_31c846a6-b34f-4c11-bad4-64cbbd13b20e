import WeModal from "@/components/WeModal/WeModal";
import PlatformSearchKeywordApi from "./api";
import { Col, Form, Input, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import { RecommendMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformSearchKeywordEdit = (props: { title: any; children: any; onOk?: Function; data?: any; source: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformSearchKeywordApi.getPlatformSearchKeywordInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.source = props.source;
    if (!data.id) {
      await PlatformSearchKeywordApi.addPlatformSearchKeyword({ data });
      message.success("添加检索关键词成功");
    }
    if (data.id) {
      await PlatformSearchKeywordApi.putPlatformSearchKeyword({ data });
      message.success("修改检索关键词成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`searchKey`} label="关键字描述" rules={[{ required: false, message: "请输入关键字描述" }]}>
              <Input placeholder="请输入关键字描述" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`recommendTag`} label="推荐标识" rules={[{ required: false, message: "请选择推荐标识" }]} initialValue={0}>
              <Select placeholder="请选择推荐标识" options={RecommendMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformSearchKeywordEdit;
