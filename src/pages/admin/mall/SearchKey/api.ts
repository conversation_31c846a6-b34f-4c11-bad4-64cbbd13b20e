import { makeApi } from "@/utils/Api";

const PlatformSearchKeywordApi = {
    getPlatformSearchKeyword: makeApi("get", `/api/platformSearchKeyword`),
    getPlatformSearchKeywordInfo: makeApi("get", `/api/platformSearchKeyword`, true),
    addPlatformSearchKeyword: makeApi("post", `/api/platformSearchKeyword`),
    putPlatformSearchKeyword: makeApi("put", `/api/platformSearchKeyword`),
    delPlatformSearchKeyword: makeApi("delete", `/api/platformSearchKeyword`),
};

export default PlatformSearchKeywordApi;
