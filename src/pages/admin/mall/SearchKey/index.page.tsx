import WeTable, { WeTableRef } from "@/components/WeTable";
import { useEffect, useRef } from "react";
import PlatformSearchKeywordApi from "./api";
import PlatformSearchKeywordEdit from "./edit";
import { Card, Form, Input, Popconfirm, Select, Space, message } from "antd";
import { RecommendMap } from "./types";
import { useMount, useSetState } from "react-use";
import AsyncSwitch from "@/components/AsyncSwitch";

const tabs = [
  { key: "10", tab: '商品', },
];

const PlatformSearchKeywordPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    source: "",
  });

  useMount(() => {
    setState({ source: tabs[0].key })
  });

  useEffect(() => {
    if (!state.source) return
  }, [state.source])

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformSearchKeywordApi.delPlatformSearchKeyword({ data: { ids: item.id } });
    message.success("删除检索关键词成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={tabs}
        activeTabKey={state.source}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(source) => setState({ source: source })}
      />
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ source: state.source }}
        request={(p) => PlatformSearchKeywordApi.getPlatformSearchKeyword({ params: p })}
        title={
          <PlatformSearchKeywordEdit title={"新增检索关键词"} source={state.source} onOk={handleReload}>
            <WeTable.AddBtn />
          </PlatformSearchKeywordEdit>
        }
        search={[
          <Form.Item label="关键字描述" name={`searchKey`}>
            <Input placeholder="请输入关键字描述" />
          </Form.Item>,
          <Form.Item label="推荐标识" name={`recommendTag`}>
            <Select placeholder="请选择推荐标识" options={RecommendMap} allowClear />
          </Form.Item>,
        ]}
        columns={[
          { title: "关键字描述", dataIndex: "searchKey", render: (c) => c ? c : "--" },
          { title: "检索次数", dataIndex: "searchTimes", render: (c) => c ? c : "--", sorter: true, },
          //{ title: "推荐标识", dataIndex: "recommendTag", render: (c) => RecommendMap.find((i) => i.value === c)?.label },
          {
            title: "推荐标识",
            render: (c) => (
              <AsyncSwitch
                onClick={async () => {
                  await PlatformSearchKeywordApi.putPlatformSearchKeyword({ data: { id: c.id, recommendTag: Number(!c.recommendTag) } });
                  handleReload();
                }}
                value={!!c.recommendTag}
              />
            ),
          },

          { title: "排序", dataIndex: "sort" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <PlatformSearchKeywordEdit title={`编辑检索关键词`} data={item} source={state.source} onOk={handleReload}>
                    <a>编辑</a>
                  </PlatformSearchKeywordEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformSearchKeywordPage;
