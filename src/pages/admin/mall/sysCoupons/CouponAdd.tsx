import WeModal from "@/components/WeModal/WeModal";
import { <PERSON><PERSON>, Col, DatePicker, Divider, Form, Input, InputNumber, Radio, Row, Select, Space, Switch, Table, message } from "antd";
import { CouponLifeType, CouponObtainStyle, CouponType, PlatformType } from "./type";
import SelectShop from "@/components/Units/SelectShop";
import GoodsPicker from "@/components/GoodsPicker";
import { useSetState } from "react-use";
import { PlusOutlined } from "@ant-design/icons";
import { formatDateRange } from "@/utils/Tools";
import MallApi from "@/services/MallApi";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { ProductModelType, ProductPropertyType } from "@/components/GoodsPicker/types";

const layout = { row: 10, col: 12 };

const Group = (props: any) => {
  return (
    <Col span={24}>
      <Row gutter={layout.row}>{props.children}</Row>
    </Col>
  );
};

const CouponAdd = (props: { title: any; children: any; onOk?: Function; cid?: any; obs?: any; shopOnly?: boolean }) => {
  const [form] = Form.useForm();
  const obtainStyle = Form.useWatch("obtainStyle", form);
  const [state, setState] = useSetState({
    goods: [] as any[],
  });

  const fetchDetail = async (id: any) => {
    let res: any = null;
    if (props.shopOnly) {
      res = await MallApi.getShopCoupon(id);
    } else {
      res = await MallApi.getCoupons(id);
    }
    // console.log(res);

    const goods = res.limitProductList || [];
    setState({ goods });

    const fdate = (n: any) => (n ? dayjs(n) : undefined);
    res.shopIds = res.limitShopList?.map((n: any) => n.id) || [];
    res.saleDate = [fdate(res.saleStartDate), fdate(res.saleEndDate)];
    res.effectiveDate = [fdate(res.effectiveStartDate), fdate(res.effectiveEndDate)];

    res.exchangeIntegral = ((res?.exchangeIntegral || 0) / 100)?.toFixed(2);

    form.setFieldsValue(res);
  };

  const handleOpen = () => {
    form.resetFields();
    setState({ goods: [] });

    if (props.obs) {
      form.setFieldValue("obtainStyle", +props.obs);
    }

    if (props.cid) {
      fetchDetail(props.cid);
    }
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.exchangeIntegral = (data?.exchangeIntegral || 0) * 100;
    if ((data.couponType === 2 || props.shopOnly) && !data.shopLimit && !data.productLimit) {
      message.error("商家券类型下, 门店限制和商品限制至少选择一项");
      return false;
    }

    if (data.productLimit && !state.goods.length) {
      message.error("请选择商品");
      return false;
    }

    data.saleIn = Number(data.saleIn);
    data.shopLimit = Number(data.shopLimit);
    data.productLimit = Number(data.productLimit);
    data.saleTimeLimit = Number(data.saleTimeLimit);
    data.inventoryLimit = Number(data.inventoryLimit);
    data.eachObtainLimit = Number(data.eachObtainLimit);

    const saleDate = formatDateRange(data.saleDate);
    data.saleStartDate = saleDate.start;
    data.saleEndDate = saleDate.end;
    delete data.saleDate;

    const effectiveDate = formatDateRange(data.effectiveDate);
    data.effectiveStartDate = effectiveDate.start;
    data.effectiveEndDate = effectiveDate.end;
    delete data.effectiveDate;

    data.productIds = state.goods.map((n) => n.id).join(",");
    data.shopIds = data.shopIds?.join(",") || "";
    // console.log(data);

    if (props.shopOnly) {
      if (data.id) {
        await MallApi.putShopCoupon({ data });
        message.success("修改优惠券成功");
      } else {
        await MallApi.addShopCoupon({ data });
        message.success("添加优惠券成功");
      }
    } else {
      if (data.id) {
        await MallApi.putCoupons({ data });
        message.success("修改优惠券成功");
      } else {
        await MallApi.addCoupons({ data });
        message.success("添加优惠券成功");
      }
    }

    props.onOk?.();
  };

  return (
    <WeModal title={props.title} trigger={props.children} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item hidden name={`id`}>
          <Input />
        </Form.Item>
        <div style={{ height: 10 }}></div>
        <Row gutter={layout.row}>
          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="名称" name={`name`} rules={[{ required: true, message: "请输入优惠券名称" }]}>
                <Input placeholder="请输入优惠券名称" />
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col}>
              <Form.Item label="优惠类型" name={"type"} initialValue={10} rules={[{ required: true, message: "请选择优惠类型" }]}>
                <Radio.Group options={CouponType.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
            </Col>
            <Form.Item noStyle dependencies={["type"]}>
              {(form) => {
                const type = form.getFieldValue("type");
                return (
                  <>
                    {type === 10 && (
                      <Col span={layout.col}>
                        <Form.Item label="优惠金额" name={`preferentialMoney`} rules={[{ required: true, message: "请输入优惠金额" }]}>
                          <InputNumber placeholder="请输入优惠金额" addonAfter="元" min={0} style={{ width: "100%" }} />
                        </Form.Item>
                      </Col>
                    )}
                    {type === 20 && (
                      <Col span={layout.col}>
                        <Form.Item label="优惠后折扣" name={`discount`} rules={[{ required: true, message: "请输入优惠后折扣" }]} tooltip="0-10,数值越小,优惠力度越大">
                          <InputNumber placeholder="请输入优惠后折扣" min={0.1} max={9.9} step={0.1} addonAfter="折" style={{ width: "100%" }} />
                        </Form.Item>
                      </Col>
                    )}
                  </>
                );
              }}
            </Form.Item>
          </Group>

          <Group>
            <Col span={layout.col}>
              <Form.Item
                label="最低消费"
                //tooltip="0为不限制"
                name={`minConsumeMoney`}
                rules={[{ required: true, message: "请输入最低消费" }]}
              >
                <InputNumber placeholder="请输入最低消费，0为无门槛" addonAfter="元" min={0} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col}>
              <Form.Item label="有效期" name={`effectiveStyle`} initialValue={10} rules={[{ required: true, message: "请选择有效期" }]}>
                <Select placeholder="请选择有效期" options={CouponLifeType} fieldNames={{ label: "name", value: "id" }} allowClear />
              </Form.Item>
            </Col>
            <Form.Item noStyle dependencies={["effectiveStyle"]}>
              {(form) => {
                const style = form.getFieldValue("effectiveStyle");

                return (
                  <>
                    {style === 10 && (
                      <>
                        <Col span={6}>
                          <Form.Item name={`effectiveDeferredDays`} initialValue={0} rules={[{ required: true, message: "请输入领取后生效时间" }]}>
                            <InputNumber min={0} addonBefore="领取后" addonAfter="天生效" />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item name={`effectiveDays`} initialValue={365} rules={[{ required: true, message: "请输入有效期" }]}>
                            <InputNumber min={0} addonBefore="有效期" addonAfter="天" />
                          </Form.Item>
                        </Col>
                      </>
                    )}
                    {style === 20 && (
                      <Col span={layout.col}>
                        <Form.Item name={`effectiveDate`} rules={[{ required: true, message: "请选择有效期" }]}>
                          <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
                        </Form.Item>
                      </Col>
                    )}
                  </>
                );
              }}
            </Form.Item>
          </Group>

          <Group>
            <Col span={layout.col} hidden>
              <Form.Item label="领取方式" name={`obtainStyle`} rules={[{ required: true, message: "请选择领取方式" }]} initialValue={20}>
                <Radio.Group options={CouponObtainStyle.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
            </Col>
            {obtainStyle == 30 && (
              <Col span={layout.col}>
                <Form.Item label="所需M币" name={`exchangeIntegral`} rules={[{ required: true, message: "请输入兑换所需M币" }]}>
                  <InputNumber placeholder="请输入兑换所需M币" min={0} style={{ width: "100%" }} />
                </Form.Item>
              </Col>
            )}
          </Group>

          <Group>
            <Col span={layout.col}>
              <Form.Item label="上下架" name={`saleIn`} valuePropName="checked" initialValue={true}>
                <Switch checkedChildren="上架" unCheckedChildren="下架" />
              </Form.Item>
            </Col>
            <Col span={layout.col}>
              <Form.Item label="排序" name={`sort`}>
                <InputNumber style={{ width: "100%" }} placeholder="请输入排序，范围1~999" />
              </Form.Item>
            </Col>
          </Group>

          <Divider orientation="left">约束限制</Divider>
          {!props.shopOnly && (
            <Group>
              <Col span={layout.col}>
                <Form.Item label="类型" name={`couponType`} rules={[{ required: true, message: "请选择类型" }]} initialValue={2}>
                  <Radio.Group options={PlatformType.map((n) => ({ label: n.name, value: n.id }))} />
                </Form.Item>
              </Col>
            </Group>
          )}

          <Form.Item dependencies={["couponType"]} noStyle>
            {(form) => {
              const couponType = form.getFieldValue("couponType");
              return (
                (props.shopOnly || couponType == 2) && (
                  <>
                    <Group>
                      <Col span={layout.col * 2}>
                        <Form.Item label="门店限制">
                          <div style={{ display: "flex" }}>
                            <div style={{ height: 32, display: "flex", alignItems: "center", marginRight: 20 }}>
                              <Form.Item noStyle name={`shopLimit`} valuePropName="checked" initialValue={false}>
                                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                              </Form.Item>
                            </div>
                            <Form.Item noStyle dependencies={["shopLimit"]}>
                              {(form) => {
                                const value = form.getFieldValue("shopLimit");
                                return (
                                  !!value && (
                                    <div style={{ flex: 1 }}>
                                      <Form.Item noStyle name={`shopIds`} rules={[{ required: true, message: "请选择门店" }]}>
                                        <SelectShop params={{ isAll: 1 }} mode="tags" />
                                      </Form.Item>
                                    </div>
                                  )
                                );
                              }}
                            </Form.Item>
                          </div>
                        </Form.Item>
                      </Col>
                    </Group>

                    <Group>
                      <Col span={layout.col * 2}>
                        <Form.Item label="限用商品">
                          <div style={{ display: "flex" }}>
                            <div style={{ height: 32, display: "flex", alignItems: "center", marginRight: 20 }}>
                              <Form.Item noStyle name={`productLimit`} valuePropName="checked" initialValue={false}>
                                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                              </Form.Item>
                            </div>
                            <Form.Item noStyle dependencies={["productLimit"]}>
                              {(form) => {
                                const value = form.getFieldValue("productLimit");
                                return (
                                  !!value && (
                                    <>
                                      <div style={{ flex: 1 }}>
                                        <Table
                                          rowKey={`id`}
                                          size="small"
                                          dataSource={state.goods}
                                          pagination={false}
                                          locale={{ emptyText: "暂无商品" }}
                                          columns={[
                                            // { title: "编号", dataIndex: "serialNo" },
                                            { title: "名称", dataIndex: "name" },
                                            {
                                              title: "属性",
                                              dataIndex: "property",
                                              render: (c) => ProductPropertyType.find((n) => n.id == c)?.name,
                                            },
                                            {
                                              title: "模式",
                                              dataIndex: "productModel",
                                              render: (c) => ProductModelType.find((n) => n.id == c)?.name,
                                            },
                                            //{ title: "分类", dataIndex: "shopCategoryName" },
                                            {
                                              title: "操作",
                                              render: (item) => (
                                                <Space>
                                                  <a
                                                    onClick={() => {
                                                      const goods = state.goods.filter((crt) => crt.id !== item.id);
                                                      setState({ goods });
                                                    }}
                                                  >
                                                    删除
                                                  </a>
                                                </Space>
                                              ),
                                            },
                                          ]}
                                        />
                                        <div style={{ height: 20 }} />
                                        <GoodsPicker
                                          params={{ isAll: 1, saleInAll: 1 }}
                                          onSelect={(rows) => {
                                            const newList: any[] = rows.filter((item) => {
                                              return state.goods.every((n) => n.id !== item.id);
                                            });
                                            const fulllist = [...state.goods, ...newList];
                                            setState({ goods: fulllist });
                                          }}
                                        >
                                          <Button block type="dashed" icon={<PlusOutlined />}>
                                            添加商品
                                          </Button>
                                        </GoodsPicker>
                                      </div>
                                    </>
                                  )
                                );
                              }}
                            </Form.Item>
                          </div>
                        </Form.Item>
                      </Col>
                    </Group>
                  </>
                )
              );
            }}
          </Form.Item>

          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="展示时限">
                <div style={{ display: "flex" }}>
                  <div style={{ height: 32, display: "flex", alignItems: "center", marginRight: 20 }}>
                    <Form.Item noStyle name={`saleTimeLimit`} valuePropName="checked" initialValue={false}>
                      <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                    </Form.Item>
                  </div>

                  <Form.Item noStyle dependencies={["saleTimeLimit"]}>
                    {(form) => {
                      const value = form.getFieldValue("saleTimeLimit");
                      return (
                        !!value && (
                          <div style={{ flex: 1 }}>
                            <Form.Item noStyle name={`saleDate`} rules={[{ required: true, message: "请选择日期范围" }]} initialValue={[]}>
                              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
                            </Form.Item>
                          </div>
                        )
                      );
                    }}
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="库存限制">
                <div style={{ display: "flex" }}>
                  <div style={{ height: 32, display: "flex", alignItems: "center", marginRight: 20 }}>
                    <Form.Item noStyle name={`inventoryLimit`} valuePropName="checked" initialValue={false}>
                      <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                    </Form.Item>
                  </div>

                  <Form.Item noStyle dependencies={["inventoryLimit"]}>
                    {(form) => {
                      const value = form.getFieldValue("inventoryLimit");
                      return (
                        !!value && (
                          <div style={{ flex: 1 }}>
                            <Form.Item noStyle name={`inventoryCount`} rules={[{ required: true, message: "请输入库存数量" }]}>
                              <InputNumber placeholder="请输入库存数量" min={0} style={{ width: "100%" }} addonAfter="张" />
                            </Form.Item>
                          </div>
                        )
                      );
                    }}
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
          </Group>

          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="获得限制">
                <div style={{ display: "flex" }}>
                  <div style={{ height: 32, display: "flex", alignItems: "center", marginRight: 20 }}>
                    <Form.Item noStyle name={`eachObtainLimit`} valuePropName="checked" initialValue={false}>
                      <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                    </Form.Item>
                  </div>
                  <Form.Item noStyle dependencies={["eachObtainLimit"]}>
                    {(form) => {
                      const value = form.getFieldValue("eachObtainLimit");
                      return (
                        !!value && (
                          <div style={{ flex: 1 }}>
                            <Form.Item noStyle name={`eachObtainCount`} rules={[{ required: true, message: "请输入获得数量" }]}>
                              <InputNumber placeholder="请输入数量" min={1} style={{ width: "100%" }} addonBefore="每人限制" addonAfter="张" />
                            </Form.Item>
                          </div>
                        )
                      );
                    }}
                  </Form.Item>
                </div>
              </Form.Item>
            </Col>
          </Group>

          <Divider orientation="left">扩展信息</Divider>
          <Group>
            <Col span={layout.col * 2}>
              <Form.Item label="使用说明" name={`buyNotices`}>
                <Input.TextArea autoSize={{ minRows: 4 }} maxLength={200} showCount placeholder="请输入使用说明" />
              </Form.Item>
            </Col>
          </Group>
        </Row>
      </Form>
    </WeModal>
  );
};

export default CouponAdd;
