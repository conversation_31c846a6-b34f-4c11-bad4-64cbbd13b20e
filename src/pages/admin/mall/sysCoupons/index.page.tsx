import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import CouponAdd from "./CouponAdd";
import { useRef } from "react";
import { Card, Form, Input, Popconfirm, Select, Space, Tag, message } from "antd";
import { CouponType, CouponObtainStyle, SaleInType } from "./type";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const CouponListPage = (props: { shopOnly?: boolean }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({
    obtainStyle: CouponObtainStyle[0].id + "",
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDelete = async (item: any) => {
    const data = { ids: item.id };

    if (props.shopOnly) {
      await MallApi.delShopCoupon({ data });
    } else {
      await MallApi.delCoupons({ data });
    }

    message.success("删除优惠券成功");
    handleReload();
  };

  return (
    <div>
      <Card
        className="mb-10px"
        classNames={{ body: "hidden" }}
        activeTabKey={state.obtainStyle}
        tabList={CouponObtainStyle.map((n) => ({ key: n.id + "", label: n.name }))}
        onTabChange={(e) => setState({ obtainStyle: e })}
      />
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        request={(params) => {
          return props.shopOnly ? MallApi.getShopCouponList({ params }) : MallApi.getCouponsList({ params });
        }}
        params={{ obtainStyle: state.obtainStyle }}
        title={
          <CouponAdd title={`新增优惠券`} onOk={handleReload} obs={state.obtainStyle} shopOnly={props.shopOnly}>
            <WeTable.AddBtn />
          </CouponAdd>
        }
        search={[
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="优惠类型" name={`type`}>
            <Select options={CouponType.map((item) => ({ label: item.name, value: item.id }))} placeholder="请选择优惠类型" allowClear />
          </Form.Item>,
          <Form.Item label="上下架" name={`saleIn`}>
            <Select options={SaleInType.map((item) => ({ label: item.name, value: item.id }))} placeholder="请选择上下架" allowClear />
          </Form.Item>,
        ]}
        columns={[
          { fixed: "left", title: "名称", dataIndex: "name" },
          {
            title: "优惠类型",
            dataIndex: "type",
            render: (c) => {
              const crt = CouponType.find((n) => n.id == c);
              return <Tag color={crt?.color}>{crt?.name}</Tag>;
            },
          },
          {
            title: "优惠.折扣",
            render: (c) => {
              return (
                <div>
                  {c.type == 10 && `${c.preferentialMoney} 元`} {c.type == 20 && `${c.discount} 折`}
                </div>
              );
            },
          },
          {
            title: "最低消费",
            dataIndex: "minConsumeMoney",
            render: (c) => <>{c ? `${c} 元` : "无门槛"}</>,
          },
          {
            title: "所需M币",
            hide: state.obtainStyle != "30",
            dataIndex: "exchangeIntegral",
            render: (c) => {
              c = ((c || 0) / 100)?.toFixed(2);
              return c ? `${c} M币` : "无";
            },
          },
          {
            title: "有效期",
            render: (c) => (
              <>
                {c.effectiveStyle == 10 && `领取后${c.effectiveDeferredDays}天生效 有效期${c.effectiveDays}天`}
                {c.effectiveStyle == 20 && `从 ${dayjs(c.effectiveStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.effectiveEndDate).format("YYYY-MM-DD")}`}
              </>
            ),
          },
          {
            title: "展示时限",
            render: (c) => {
              return (
                <>
                  {!c.saleTimeLimit && "永久"}
                  {!!c.saleTimeLimit && `从 ${dayjs(c.saleStartDate).format("YYYY-MM-DD")} 到 ${dayjs(c.saleEndDate).format("YYYY-MM-DD")}`}
                </>
              );
            },
          },
          { title: "库存", render: (c) => (c.inventoryLimit ? c.inventoryCount + "张" : "不限") },
          { title: "累计使用", dataIndex: "totalUseCount" },
          {
            fixed: "right",
            title: "上下架",
            dataIndex: "saleIn",
            render: (c) => (c ? <Tag color="green">上架</Tag> : <Tag color="gray">下架</Tag>),
          },
          {
            fixed: "right",
            title: "操作",
            render: (item: any) => {
              return (
                <Space>
                  <CouponAdd title={`编辑优惠券 - ${item.name}`} onOk={handleReload} cid={item.id} obs={state.obtainStyle} shopOnly={props.shopOnly}>
                    <a>编辑</a>
                  </CouponAdd>
                  <Popconfirm title={`确定要删除 - ${item.name} 吗？`} onConfirm={() => handleDelete(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default CouponListPage;
