import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import Configs from "@/utils/Configs";
import { FileImageOutlined } from "@ant-design/icons";
import { Col, Form, Input, InputNumber, message, Progress, Row, Switch } from "antd";
import axios from "axios";
import { useSetState } from "react-use";
import Compressor from "compressorjs";

const layout = { row: 10, col: 12 };

const Group = (props: any) => (
  <Col span={24}>
    <Row gutter={layout.row}>{props.children}</Row>
  </Col>
);

export default function Edit(props: { children: any; onOk: any; title: any; data?: any }) {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    img: "",
    rate: "0",
  });

  const handleOpen = () => {
    form.resetFields();
    setState({ img: "" });

    if (props.data) {
      const data = { ...props.data };
      console.log(data);
      data.online = !!data.online;
      setState({ img: data.image });
      form.setFieldsValue(props.data);
    }
  };

  const beforeUpload: any = (file: any) => {
    if (file?.type == "image/gif") return file;
    return new Promise((resolve) => {
      new Compressor(file, {
        maxWidth: 1920,
        convertSize: 1024 * 200,
        success: (res) => resolve(res),
        error: () => resolve(file),
      });
    });
  };

  const handleSubmit = async () => {
    const data = await form.validateFields();
    data.image = state.img;
    data.online = Number(data.online);

    if (!data.image) {
      message.error("请上传图片");
      return false;
    }

    if (data.id) {
      await MallApi.putPoster({ data });
    } else {
      await MallApi.addPoster({ data });
    }

    props.onOk();
  };

  return (
    <WeModal title={props.title} width={600} trigger={props.children} onOk={handleSubmit} onOpen={handleOpen}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Form.Item name={`id`} hidden>
          <Input></Input>
        </Form.Item>
        <Row gutter={layout.row}>
          <Group>
            <Col span={16}>
              <Form.Item label="名称" name={`name`} rules={[{ required: true }]}>
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={12}>
              <Form.Item label="排序" name={`sort`}>
                <InputNumber min={0} step={1} precision={0} placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="启用" name={`online`} valuePropName="checked" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>
          </Group>
          <Group>
            <Col span={24}>
              <Form.Item label="图片" required>
                <div className="w-240px">
                  <div className="pos-relative flex flex-col items-center justify-center w-240px h-360px b-(2px dashed #eee) bg-#fff rounded-10px overflow-hidden">
                    <input
                      className="z-10 pos-absolute top-0 left-0 size-full opacity-0 cursor-pointer"
                      type="file"
                      accept="image/*"
                      onInput={async (e) => {
                        const _file = e.currentTarget.files?.[0];
                        e.currentTarget.value = "";
                        if (!_file) return;

                        const file = await beforeUpload(_file);

                        const res = await axios.postForm(
                          Configs.uploadHost,
                          { file },
                          {
                            onUploadProgress: (e) => {
                              const rate = ((e.progress || 0) * 100).toFixed(2);
                              setState({ rate });
                            },
                          }
                        );
                        const url = res?.data?.data?.path ?? "";
                        setState({ img: url });
                      }}
                    />
                    {state.img ? (
                      <img className="size-full object-cover" src={state.img} alt="" />
                    ) : (
                      <div className="flex flex-col items-center justify-center">
                        <FileImageOutlined className="text-(50px #999)" />
                        <div className="mt-10px text-(14px #999)">上传海报</div>
                      </div>
                    )}
                  </div>
                  <Progress percent={+state.rate} size="small" />
                </div>
              </Form.Item>
            </Col>
          </Group>
        </Row>
      </Form>
    </WeModal>
  );
}
