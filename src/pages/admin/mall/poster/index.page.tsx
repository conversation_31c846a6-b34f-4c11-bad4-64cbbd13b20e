import Mall<PERSON><PERSON> from "@/services/MallApi";
import {
  <PERSON><PERSON>,
  Card,
  Divider,
  Empty,
  Form,
  Image,
  Input,
  message,
  Pagination,
  Popconfirm,
  Select,
  Space,
  Spin,
} from "antd";
import { useSetState } from "react-use";
import Edit from "./Edit";
import AsyncSwitch from "@/components/AsyncSwitch";
import { ClearOutlined, PlusOutlined, SearchOutlined } from "@ant-design/icons";
import { useEffect } from "react";

export default function Poster() {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    list: [] as any[],
    size: 12,
    page: 1,
    total: 0,
    loading: false,

    params: {} as any,
    update: 0,
  });

  const onSearch = async () => {
    const data = await form.validateFields();
    setState({ params: data, update: state.update + 1 });
  };

  const onReset = async () => {
    form.resetFields();
    onSearch();
  };

  useEffect(() => {
    getList(1);
  }, [state.update]);

  const getList = async (page = 1) => {
    setState({ loading: true });
    const res = await MallApi.getPoster({
      params: {
        pageSize: state.size,
        pageNum: page,

        ...state.params,
      },
    }).finally(() => setState({ loading: false }));
    const list = res?.list || [];
    setState({ list, size: res?.pageSize, page: res?.pageNum, total: res?.total });
  };

  const handleReload = () => {
    getList(state.page);
  };

  return (
    <div>
      <Card classNames={{ body: "!p-10px" }}>
        <Form layout="inline" form={form}>
          <Form.Item label="名称" name="name" >
            <Input placeholder="请输入名称" allowClear style={{ width: 200 }} />
          </Form.Item>
          <Form.Item label="状态" name="online" >
            <Select
              placeholder="请选择状态"
              style={{ width: 200 }}
              allowClear
              options={[
                { label: "启用", value: "1" },
                { label: "禁用", value: "0" },
              ]}
            />
          </Form.Item>
          <Form.Item className="ml-10px">
            <Space>
              <Button icon={<SearchOutlined />} type="primary" onClick={onSearch}>
                搜索
              </Button>
              <Button icon={<ClearOutlined />} onClick={onReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
          <div className="ml-auto">
            <Edit title={`新增海报`} onOk={handleReload}>
              <Button icon={<PlusOutlined />} type="primary">
                新增海报
              </Button>
            </Edit>
          </div>
        </Form>
      </Card>
      <div className="h-10px"></div>
      <Spin spinning={state.loading}>
        <Card classNames={{ body: "!p-10px" }}>
          {!state.total && (
            <div className="flex items-center justify-center h-500px">
              <Empty></Empty>
            </div>
          )}
          <div className="grid cols-6 gap-20px">
            {state.list.map((item) => (
              <div key={item.id}>
                <Card
                  hoverable
                  bordered
                  cover={
                    <div className="w-full h-400px flex overflow-hidden">
                      <Image className="flex-1" src={item.image + `?x-oss-process=image/resize,w_750,limit_0`} />
                    </div>
                  }
                >
                  <Card.Meta
                    title={item.name}
                    description={
                      <>
                        <div className="flex items-center">
                          <span>启用：</span>
                          <AsyncSwitch
                            size="small"
                            onClick={async () => {
                              await MallApi.putPoster({ data: { id: item.id, online: Number(!item.online) } });
                              handleReload();
                            }}
                            checked={!!item.online}
                          />

                          <div className="ml-auto"></div>
                          <Edit title={`编辑`} data={item} onOk={handleReload}>
                            <a>编辑</a>
                          </Edit>
                          <Divider type="vertical" />
                          <Popconfirm
                            title="确定要删除这个海报吗?"
                            onConfirm={async () => {
                              await MallApi.delPoster({ data: { ids: item.id } });
                              message.success("删除成功");
                              handleReload();
                            }}
                          >
                            <a className="c-red">删除</a>
                          </Popconfirm>
                        </div>
                      </>
                    }
                  ></Card.Meta>
                </Card>
              </div>
            ))}
          </div>
        </Card>
      </Spin>
      <div className="mt-10px flex justify-end">
        <Card classNames={{ body: "!p-10px" }}>
          <div className="flex justify-end">
            <Pagination pageSize={state.size} total={state.total} onChange={(e) => getList(e)} />
          </div>
        </Card>
      </div>
    </div>
  );
}
