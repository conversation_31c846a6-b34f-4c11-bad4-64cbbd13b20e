import WeModal from "@/components/WeModal/WeModal";
import MallShopVipCommissionConfigApi from "./api";
import { Checkbox, Col, Form, Input, Radio, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import { useMount, useSetState } from "react-use";
import MallCommissionConfigApi from "../../operation/CommissionConfig/api";
import { CommissionModeMap, StateMap } from "./types";

const layout = { row: 10, col: 24 };

const MallShopVipCommissionConfigEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; vipLevelId?: any, type?: any }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    commissionConfigList: [] as any[],
  });


  useMount(() => {
    fetchCommissionConfig();
  });

  const fetchCommissionConfig = async () => {
    const res = await MallCommissionConfigApi.selectMallCommissionConfig();
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ commissionConfigList: list });
  };


  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await MallShopVipCommissionConfigApi.getMallShopVipCommissionConfigInfo(props.data.id);
      if (data.rewardCommissionRate > 0 && data.rewardCashCommissionRate == data.rewardCommissionRate) {
        data.rewardCommissionTag = 1;
      }
      if (data.repurchaseCommissionRate > 0 && data.repurchaseCashCommissionRate == data.repurchaseCommissionRate) {
        data.repurchaseCommissionTag = 1;
      }
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.vipLevelId = props.vipLevelId;
    data.type = props.type;

    if (Number(data.rewardCommissionTag) == 1) {
      data.rewardCashCommissionRate = data.rewardCommissionRate;
    } else {
      data.rewardCashCommissionRate = 0;
    }
    if (Number(data.repurchaseCommissionTag) == 1) {
      data.repurchaseCashCommissionRate = data.repurchaseCommissionRate;
    } else {
      data.repurchaseCashCommissionRate = 0;
    }

    if (!data.id) {
      await MallShopVipCommissionConfigApi.addMallShopVipCommissionConfig({ data });
      message.success("添加佣金方案成功");
    }
    if (data.id) {
      await MallShopVipCommissionConfigApi.putMallShopVipCommissionConfig({ data });
      message.success("修改佣金方案成功");
    }
    props.onOk?.();
  };


  const commissionMode = Form.useWatch("commissionMode", form);
  const isEdit = !!props.data?.id;

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`commissionMode`} label="佣金模式" rules={[{ required: false, message: "请选择佣金模式" }]} initialValue={1}>
              <Radio.Group options={CommissionModeMap} disabled={isEdit} />
            </Form.Item>
          </Col>
          {commissionMode == 2 && (
            <Col span={layout.col}>
              <Form.Item label="佣金方案" name={`commissionConfigId`}>
                <Select options={state.commissionConfigList}
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  placeholder="请选择佣金方案"
                  disabled={isEdit}
                  style={{ width: 200 }
                  }
                />
              </Form.Item>
            </Col>
          )}
          <Col span={18}>
            <Form.Item name={`rewardCommissionRate`} label="奖励M币" rules={[{ required: false, message: "请输入奖励M币百分比" }]} initialValue={0}>
              <InputNumber
                placeholder="请输入奖励M币"
                style={{ width: "100%" }}
                min={0}
                max={100}
                step={0.1}
                addonAfter="百分比"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name={`rewardCommissionTag`} initialValue={false} valuePropName="checked">
              <Checkbox value={1}>
                可提现
              </Checkbox>
            </Form.Item>
          </Col>
          <Col span={18}>
            <Form.Item name={`repurchaseCommissionRate`} label="复购奖励M币" rules={[{ required: false, message: "请输入复购奖励M币百分比" }]} initialValue={0}>
              <InputNumber
                placeholder="请输入复购奖励M币"
                style={{ width: "100%" }}
                min={0}
                max={100}
                step={0.1}
                addonAfter="百分比"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name={`repurchaseCommissionTag`} initialValue={false} valuePropName="checked">
              <Checkbox value={1}>
                可提现
              </Checkbox>
            </Form.Item>
          </Col>
          <Col span={layout.col/2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col/2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallShopVipCommissionConfigEdit;
