import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallShopVipCommissionConfigApi from "./api";
import MallShopVipCommissionConfigEdit from "./edit";
import { Card, Drawer, Popconfirm, Space, message } from "antd";
import { useMount, useSetState } from "react-use";
import { CommissionModeMap, StateMap, TypeMap } from "./types";

const tabs = [
  { key: "10", tab: "自己消费" },
  { key: "20", tab: "直推消费" },
  { key: "30", tab: "间推消费" },
];

const MallShopVipCommissionConfigPage = (props: { children: any; title: any, vipLevelId?: any }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
    type: "",
  });

  useMount(() => {
    setState({ type: tabs[0].key })
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);


  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallShopVipCommissionConfigApi.delMallShopVipCommissionConfig({ data: { ids: item.id } });
    message.success("删除佣金方案成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <Card
          tabList={tabs}
          activeTabKey={state.type}
          bodyStyle={{ display: "none" }}
          style={{ marginBottom: 10 }}
          onTabChange={(type) => setState({ type })}
        />

        <WeTable
          autoLoad={true}
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ vipLevelId: props.vipLevelId, type: state.type, }}
          request={(p) => MallShopVipCommissionConfigApi.getMallShopVipCommissionConfig({ params: p })}
          title={
            <MallShopVipCommissionConfigEdit title={"新增佣金方案"} onOk={handleReload} vipLevelId={props.vipLevelId} type={state.type}>
              <WeTable.AddBtn />
            </MallShopVipCommissionConfigEdit>
          }
          columns={[
            { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((i) => i.value === c)?.label },
            { title: "佣金模式", dataIndex: "commissionMode", render: (c) => CommissionModeMap.find((i) => i.value === c)?.label },
            { title: "佣金方案", dataIndex: "commissionConfigName", render: (c, item) => item.commissionMode === 2 ? c : "--" },
            {
              title: "奖励M币%",
              dataIndex: "rewardCommissionRate",
              render: (c, item) => {
                let cash = item.rewardCashCommissionRate > 0 && item.rewardCashCommissionRate == c ? "(可提现)" : ""
                return `${c}% ${cash}`
              }
            },
            //{ title: "M币可提现", dataIndex: "rewardCashCommissionRate", render: (c, item) => item.rewardCommissionRate > 0 && item.rewardCommissionRate == c ? "✔" : "✖" },
            {
              title: "复购奖励M币%",
              dataIndex: "repurchaseCommissionRate",
              render: (c, item) => {
                let cash = item.repurchaseCashCommissionRate > 0 && item.repurchaseCashCommissionRate == c ? "(可提现)" : ""
                return `${c}% ${cash}`
              }
            },
            //{ title: "复购奖励M币可提现", dataIndex: "repurchaseCashCommissionRate", render: (c, item) => item.repurchaseCommissionRate > 0 && item.repurchaseCommissionRate == c ? "✔" : "✖" },
            { title: "排序", dataIndex: "sort" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((i) => i.value === c)?.label },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallShopVipCommissionConfigEdit title={`编辑佣金方案`} data={item} vipLevelId={props.vipLevelId} type={state.type} onOk={handleReload}>
                      <a>编辑</a>
                    </MallShopVipCommissionConfigEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallShopVipCommissionConfigPage;
