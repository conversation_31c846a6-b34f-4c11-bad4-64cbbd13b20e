import { makeApi } from "@/utils/Api";

const MallShopVipCommissionConfigApi = {
    getMallShopVipCommissionConfig: makeApi("get", `/api/mallShopVipCommissionConfig`),
    getMallShopVipCommissionConfigInfo: makeApi("get", `/api/mallShopVipCommissionConfig`, true),
    addMallShopVipCommissionConfig: makeApi("post", `/api/mallShopVipCommissionConfig`),
    putMallShopVipCommissionConfig: makeApi("put", `/api/mallShopVipCommissionConfig`),
    delMallShopVipCommissionConfig: makeApi("delete", `/api/mallShopVipCommissionConfig`),
};

export default MallShopVipCommissionConfigApi;
