import { makeApi } from "@/utils/Api";

const MallRfmLabelApi = {
    getMallRfmLabel: makeApi("get", `/api/mallRfmLabel`),
    getMallRfmLabelInfo: makeApi("get", `/api/mallRfmLabel`, true),
    addMallRfmLabel: makeApi("post", `/api/mallRfmLabel`),
    putMallRfmLabel: makeApi("put", `/api/mallRfmLabel`),
    selectMallRfmLabel: makeApi("get", `/api/mallRfmLabel/select_rfm_list`),
};

export default MallRfmLabelApi;
