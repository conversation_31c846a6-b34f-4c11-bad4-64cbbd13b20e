import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallRfmLabelApi from "./api";
import MallRfmLabelEdit from "./edit";
import { Form, Input, Space, } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, } from "@/utils/Tools";
import dayjs from "dayjs";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallRfmLabelPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };


  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => MallRfmLabelApi.getMallRfmLabel({ params: p })}
        title={
          <Space>
            <MallRfmLabelEdit title={"新增RFM配置标签"} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallRfmLabelEdit>
          </Space>
        }
        search={[
          <Form.Item label="公司id" name={`companyId`}>
            <Input placeholder="请输入公司id" />
          </Form.Item>,
          <Form.Item label="名称" name={`name`}>
            <Input placeholder="请输入名称" />
          </Form.Item>,
          <Form.Item label="运营策略" name={`strategyContent`}>
            <Input placeholder="请输入运营策略" />
          </Form.Item>,
          <Form.Item label="rfm数据" name={`rfmData`}>
            <Input placeholder="请输入rfm数据" />
          </Form.Item>,
          <Form.Item label="排序" name={`sort`}>
            <Input placeholder="请输入排序" />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "公司id", dataIndex: "companyId", render: (c) => c ? c : "--" },
          { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
          { title: "运营策略", dataIndex: "strategyContent", render: (c) => c ? c : "--" },
          { title: "rfm数据", dataIndex: "rfmData", render: (c) => c ? c : "--" },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <MallRfmLabelEdit title={`编辑RFM配置标签`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </MallRfmLabelEdit>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MallRfmLabelPage;
