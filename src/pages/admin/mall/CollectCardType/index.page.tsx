import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallCollectCardTypeApi from "./api";
import MallCollectCardTypeEdit from "./edit";
import { Drawer, Form, Image, Input, Popconfirm, Space, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useSetState } from "react-use";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallCollectCardTypePage = (props: { children: any; title: any, collectCardSeriesId: any; }) => {
  const tableRef = useRef<WeTableRef>(null);


  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallCollectCardTypeApi.delMallCollectCardType({ data: { ids: item.id } });
    message.success("删除集卡类型成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ collectCardSeriesId: props.collectCardSeriesId }} //默认参数
          request={(p) => MallCollectCardTypeApi.getMallCollectCardType({ params: p })}
          title={
            <MallCollectCardTypeEdit title={"新增集卡类型"} collectCardSeriesId={props.collectCardSeriesId} onOk={handleReload}>
              <WeTable.AddBtn />
            </MallCollectCardTypeEdit>
          }
          search={[
            <Form.Item label="名称" name={`name`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
            <Form.Item label="创建日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "图片", dataIndex: "image", render: (c) => (c ? <Image width={50} src={c} /> : "--") },
            { title: "名称", dataIndex: "name", render: (c) => (c ? c : "--") },
            { title: "说明", dataIndex: "content", render: (c) => (c ? c : "--") },
            { title: "排序", dataIndex: "sort" },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallCollectCardTypeEdit title={`编辑集卡类型`} data={item} collectCardSeriesId={props.collectCardSeriesId} onOk={handleReload}>
                      <a>编辑</a>
                    </MallCollectCardTypeEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallCollectCardTypePage;
