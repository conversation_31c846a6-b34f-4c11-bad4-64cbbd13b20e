import WeModal from "@/components/WeModal/WeModal";
import MallCollectCardTypeApi from "./api";
import { Col, Form, Input, Row, message } from "antd";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";

const layout = { row: 10, col: 24 };

const MallCollectCardTypeEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  collectCardSeriesId: any;
  hideSubmit?: any;
}) => {
  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await MallCollectCardTypeApi.getMallCollectCardTypeInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.collectCardSeriesId = props.collectCardSeriesId;
    data.image = ImageUpload.deserializer(data.image);
    if (!data.id) {
      await MallCollectCardTypeApi.addMallCollectCardType({ data });
      message.success("添加集卡类型成功");
    }
    if (data.id) {
      await MallCollectCardTypeApi.putMallCollectCardType({ data });
      message.success("修改集卡类型成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal
      trigger={props.children}
      title={props.title}
      width={600}
      onOpen={handleOpen}
      okButtonProps={{ hidden: props.hideSubmit }}
      onOk={handleSubmit}
    >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="名称" rules={[{ required: false, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`image`}
              label="图片"
              rules={[{ required: false, message: "请上传图片" }]}
              valuePropName="fileList"
            >
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`content`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input placeholder="请输入说明" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`sort`}
              label="排序"
              rules={[{ required: false, message: "请输入排序" }]}
              initialValue={999}
              tooltip="数字越小越靠前"
            >
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallCollectCardTypeEdit;
