import { makeApi } from "@/utils/Api";

const MallCollectCardTypeApi = {
    getMallCollectCardType: makeApi("get", `/api/mallCollectCardType`),
    getMallCollectCardTypeInfo: makeApi("get", `/api/mallCollectCardType`, true),
    addMallCollectCardType: makeApi("post", `/api/mallCollectCardType`),
    putMallCollectCardType: makeApi("put", `/api/mallCollectCardType`),
    delMallCollectCardType: makeApi("delete", `/api/mallCollectCardType`),
};

export default MallCollectCardTypeApi;
