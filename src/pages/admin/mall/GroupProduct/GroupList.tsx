import WeTable, { WeTableRef } from "@/components/WeTable";
import { Drawer, Dropdown, Form, Input, InputNumber, message, Popconfirm, Select, Space, Typography } from "antd";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";
import { Api } from "./Api";
import dayjs from "dayjs";
import { GroupState } from "./types";
import UserManager from "@/components/UserManager";
import VipPicker from "@/components/Units/VipPicker";


const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const GroupList = (props: { children: any, marketProductId?: string }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({ open: false });

  useEffect(() => {
    if (!state.open) return;
    handleReload();
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} onClose={() => setState({ open: false })} width={"80%"} title="开团记录">
        <WeTable
          ref={tableRef}
          params={{ marketProductId: props.marketProductId }}
          request={(p) => Api.getLogs({ params: p })}
          search={[
            <Form.Item label="拼团状态" name="state">
              <Select
                options={GroupState}
                fieldNames={{ label: "name", value: "id" }}
                placeholder="请选择"
                allowClear
              />
            </Form.Item>,
            <Form.Item label="团长" name={`publisherId`}>
              <VipPicker placeholder="会员号/姓名/手机号" />
            </Form.Item>,
            <Form.Item label="商品名称" name="productName">
              <Input placeholder="请输入商品名称" />
            </Form.Item>,
            <Form.Item label="拼团号" name="serialNo">
              <Input placeholder="请输入拼团号" />
            </Form.Item>,
            <Form.Item label="成团人数" name="groupNum">
              <InputNumber className="w-full" placeholder="请输入成团人数" />
            </Form.Item>,
          ]}
          columns={[
            { title: "拼团号", dataIndex: "serialNo" },
            { title: "商品名称", dataIndex: "productName" },
            { title: "商品单价", dataIndex: "productPrice" },
            {
              title: "团长",
              dataIndex: "publisher",
              render: (c) => (
                c ? <UserManager userId={c?.id}>
                  <a>{c?.name}</a>
                </UserManager>
                  : "--"
              ),
            },
            { title: "成团人数", dataIndex: "groupNum" },
            { title: "已团人数", dataIndex: "joinNum" },
            {
              title: "拼团状态",
              dataIndex: "state",
              render: (c) => {
                const s = GroupState.find((n) => c == n.id);
                return <span style={{ color: s?.color }}>{s?.name}</span>;
              }
            },
            //{ title: "开团时间", dataIndex: "createDate", render: (c) => fdate(c) },
            { title: "开始时间", dataIndex: "startDate", render: (c) => fdate(c) },
            { title: "截止时间", dataIndex: "endDate", render: (c) => fdate(c) },
            {
              title: "操作",
              render: (c) => (
                <>
                  <Space>
                    <Dropdown
                      menu={{
                        items: [
                          {
                            key: "1",
                            disabled: c.state !== 10,
                            label: (
                              <Popconfirm
                                title="确定要进行参团操作吗？"
                                onConfirm={async () => {
                                  await Api.virtualJoinGroup({ data: { groupId: c.id } });
                                  message.success("参团成功");
                                  handleReload();
                                }}
                              >
                                <a>虚拟参团</a>
                              </Popconfirm>
                            ),
                          },
                        ],
                      }}
                    >
                      <Typography.Link>更多</Typography.Link>
                    </Dropdown>
                  </Space>
                </>
              ),
            },
          ]}
        />
      </Drawer>
    </>
  );
};
