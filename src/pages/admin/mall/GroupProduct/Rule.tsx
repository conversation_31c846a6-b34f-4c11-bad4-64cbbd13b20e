import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, InputNumber, message, Row } from "antd";
import { Api } from "./Api";
import RichText from "@/components/RichText";
import { Switch01 } from "@/components/Units/Switch01";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.collectMallOpenTag = Number(data.collectMallOpenTag);
    await Api.putRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getRule();
    data.collectMallOpenTag = !!data.collectMallOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="团购规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`groupMallOpenTag`} label="开启团购">
              <Switch01 />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`groupMallOpenLimitNum`} label="开团限制数量">
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`groupMallJoinLimitNum`} label="参团限制数量">
              <InputNumber />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`groupMallRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
