import WeModal from "@/components/WeModal/WeModal";
import AppUserSignLogApi from "./api";
import { Col, Form, Input, Row, } from "antd";
import { InputNumber } from "antd";

const layout = { row: 10, col: 24 };

const AppUserSignLogEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await AppUserSignLogApi.getAppUserSignLogInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };


  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} >
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`appUserId`} label="app用户id" rules={[{ required: false, message: "请输入app用户id" }]}>
              <Input placeholder="请输入app用户id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`signId`} label="签到id" rules={[{ required: false, message: "请输入签到id" }]}>
              <Input placeholder="请输入签到id" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`signNo`} label="签到序号" rules={[{ required: false, message: "请输入签到序号" }]} initialValue={0}>
              <InputNumber min={0} placeholder="请输入签到序号" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`integral`} label="获得积分" rules={[{ required: false, message: "请输入获得积分" }]} initialValue={0}>
              <InputNumber min={0} placeholder="请输入获得积分" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppUserSignLogEdit;
