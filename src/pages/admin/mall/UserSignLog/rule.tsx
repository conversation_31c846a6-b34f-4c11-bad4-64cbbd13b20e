import RichText from "@/components/RichText";
import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, message, Row, Switch } from "antd";
import Api from "./api";

const layout = { row: 10, col: 24 };

export const SignRule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.signOpenTag = Number(data.signOpenTag);
    await Api.putSignRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await Api.getSignRule();
    data.signOpenTag = !!data.signOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="签到规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`signOpenTag`} label="是否开启签到" valuePropName="checked">
              <Switch></Switch>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`signRuleContent`} label="规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
