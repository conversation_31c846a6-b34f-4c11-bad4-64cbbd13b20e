import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppUserSignLogApi from "./api";
import { Button, Form, Input } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";
import UserManager from "@/components/UserManager";
import AppUserSignConfigPage from "../UserSignConfig/index.page";
import { SignRule } from "./rule";
import { FormOutlined, PaperClipOutlined } from "@ant-design/icons";

const fdate = (n: any, f = "YYYY-MM-DD HH:mm:ss") => (n ? dayjs(n).format(f) : "");

const AppUserSignLogPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}} //默认参数
        request={(p) => AppUserSignLogApi.getAppUserSignLog({ params: p })}
        title={
          <>
            <AppUserSignConfigPage onClose={handleReload}>
              <Button type="primary" icon={<FormOutlined />}>签到配置</Button>
            </AppUserSignConfigPage>
            <SignRule onOk={handleReload}>
              <Button type="primary" icon={<PaperClipOutlined />}>签到规则</Button>
            </SignRule>
          </>
        }
        search={[
          <Form.Item label="会员" name={`appUserId`}>
            <VipPickerInput />
          </Form.Item>,
          <Form.Item label="连续签到次数" name={`signNo`}>
            <Input placeholder="请输入连续签到次数" />
          </Form.Item>,
          <Form.Item label="签到日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          {
            fixed: "left",
            title: "会员姓名",
            dataIndex: "vipUser",
            render: (c) => (
              c ? <UserManager userId={c?.id}>
                <a>{c?.name}</a>
              </UserManager>
                : "--"
            ),
          },
          { title: "连续签到次数", dataIndex: "signNo" },
          { title: "获得M币", dataIndex: "coin" },
          { title: "签到日期", dataIndex: "createDate", render: (c) => fdate(c) },
        ]}
      />
    </div>
  );
};

export default AppUserSignLogPage;
