import WeModal from "@/components/WeModal/WeModal";
import PlatformBlacklistApi from "./api";
import { Col, Form, Input, Radio, Row, message } from "antd";
import { StateMap } from "./types";

const layout = { row: 10, col: 24 };

const PlatformBlacklistEdit = (props: { title: any; children: any; onOk?: Function; data?: any; source: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const data = await PlatformBlacklistApi.getPlatformBlacklistInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.source = props.source;
    if (!data.id) {
      await PlatformBlacklistApi.addPlatformBlacklist({ data });
      message.success("添加黑名单成功");
    }
    if (data.id) {
      await PlatformBlacklistApi.putPlatformBlacklist({ data });
      message.success("修改黑名单成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={600} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`keyId`} label="手机号" rules={[{ required: false, message: "请输入手机号" }]}>
              <Input placeholder="请输入手机号" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`keyDesc`} label="描述" rules={[{ required: false, message: "请输入描述" }]}>
              <Input placeholder="请输入描述" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default PlatformBlacklistEdit;
