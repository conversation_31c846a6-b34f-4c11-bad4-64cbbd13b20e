import { makeApi } from "@/utils/Api";

const PlatformBlacklistApi = {
    getPlatformBlacklist: makeApi("get", `/api/platformBlacklist`),
    getPlatformBlacklistInfo: makeApi("get", `/api/platformBlacklist`, true),
    addPlatformBlacklist: makeApi("post", `/api/platformBlacklist`),
    putPlatformBlacklist: makeApi("put", `/api/platformBlacklist`),
    delPlatformBlacklist: makeApi("delete", `/api/platformBlacklist`),
};

export default PlatformBlacklistApi;
