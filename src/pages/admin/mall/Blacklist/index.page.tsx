import WeTable, { WeTableRef } from "@/components/WeTable";
import { useEffect, useRef } from "react";
import PlatformBlacklistApi from "./api";
import PlatformBlacklistEdit from "./edit";
import { Card, Form, Input, Popconfirm, Select, Space, message } from "antd";
import { useMount, useSetState } from "react-use";
import { SourceMap, StateMap } from "./types";


const tabs = [
  { key: "10", tab: '禁止提现名单', },
  { key: "20", tab: '老带新白名单', },
];

const PlatformBlacklistPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    source: "",
  });

  useMount(() => {
    setState({ source: tabs[0].key })
  });

  useEffect(() => {
    if (!state.source) return
  }, [state.source])

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await PlatformBlacklistApi.delPlatformBlacklist({ data: { ids: item.id } });
    message.success("删除黑名单成功");
    handleReload();
  };

  return (
    <div>
      <Card
        tabList={tabs}
        activeTabKey={state.source}
        bodyStyle={{ display: "none" }}
        style={{ marginBottom: 10 }}
        onTabChange={(source) => setState({ source: source })}
      />
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{ source: state.source }}
        request={(p) => PlatformBlacklistApi.getPlatformBlacklist({ params: p })}
        title={
          <PlatformBlacklistEdit title={"新增名单"} source={state.source} onOk={handleReload}>
            <WeTable.AddBtn />
          </PlatformBlacklistEdit>
        }
        search={[
          <Form.Item label="手机号" name={`keyId`}>
            <Input placeholder="请输入手机号" />
          </Form.Item>,
          <Form.Item label="当前状态" name={`state`}>
            <Select placeholder="请选择当前状态" options={StateMap} allowClear />
          </Form.Item>,
        ]}
        columns={[
          { title: "来源", dataIndex: "source", render: (c) => c ? SourceMap.find((item) => item.value === c)?.label : "--" },
          { title: "手机号", dataIndex: "keyId", render: (c) => c ? c : "--" },
          { title: "描述", dataIndex: "keyDesc", render: (c) => c ? c : "--" },
          { title: "当前状态", dataIndex: "state", render: (c) => c ? StateMap.find((item) => item.value === c)?.label : "--" },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <PlatformBlacklistEdit title={`编辑名单`} data={item} source={state.source} onOk={handleReload}>
                    <a>编辑</a>
                  </PlatformBlacklistEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default PlatformBlacklistPage;
