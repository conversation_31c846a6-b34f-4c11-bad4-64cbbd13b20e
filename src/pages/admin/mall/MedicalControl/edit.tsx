import WeModal from "@/components/WeModal/WeModal";
import MallMedicalControlApi from "./api";
import { Card, Col, Form, Input, Radio, Row, Select, Tooltip, message } from "antd";
import { InputNumber } from "antd";
import { useSetState } from "react-use";
import MallMedicalControlCategoryApi from "../MedicalControlCategory/api";
import { ControlTypeMap, OperationModeMap, StateMap } from "./types";

const layout = { row: 10, col: 24 };

// 提取通用的卡片样式
const tooltipCardStyle = {
  color: '#000',
  fontSize: '14px',
  lineHeight: '1.8',
  fontWeight: 'normal' as const,
};

const tooltipItemStyle = {
  padding: '2px 0'
};

const tooltipLinkStyle = {
  color: '#1890ff',
  fontWeight: 'bold' as const,
  cursor: 'pointer'
};

const MallMedicalControlEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    categorys: [] as any[],
  });

  const handleOpen = async () => {
    form.resetFields();
    fetchCategorys();
    if (props.data) {
      const data = await MallMedicalControlApi.getMallMedicalControlInfo(props.data.id);
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    // if (data.fontColor && typeof data.fontColor.toHexString === 'function') {
    //   data.fontColor = data.fontColor.toHexString();
    // }
    // if (data.backgroundColor && typeof data.backgroundColor.toHexString === 'function') {
    //   data.backgroundColor = data.backgroundColor.toHexString();
    // }

    if (!data.id) {
      await MallMedicalControlApi.addMallMedicalControl({ data });
      message.success("添加病历控件成功");
    }
    if (data.id) {
      await MallMedicalControlApi.putMallMedicalControl({ data });
      message.success("修改病历控件成功");
    }
    props.onOk?.();
  };

  const fetchCategorys = async () => {
    const params = { pageSize: 9999 };
    const res = await MallMedicalControlCategoryApi.getMallMedicalControlCategory({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({
      label: item?.name,
      value: item?.id,
    }));
    setState({ categorys: list });
  };

  const controlType = Form.useWatch("controlType", form);


  const handleControlTypeChange = (e: any) => {
    const newControlType = e.target.value;
    if (newControlType) {
      // 重置正则表达式字段
      form.resetFields(['regularExpression']);

      // 重置选项列表字段
      form.resetFields(['optionsJson']);
    }
  };

  // 提取文本控件的正则表达式提示内容
  const renderTextRegexTooltip = () => (
    <Card title="正则表达式" size="small" className="w-full" style={tooltipCardStyle}>
      <div style={tooltipCardStyle}>
        <div style={tooltipItemStyle}>
          文本：<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '\\S');
            }}
          >\S</span>
        </div>
        <div style={tooltipItemStyle}>
          整数：<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '^[0-9]*$');
            }}
          >^[0-9]*$</span>
        </div>
        <div style={tooltipItemStyle}>
          小数：<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '^-?\\d+\\.\\d+$');
            }}
          >^-?\d+\.\d+$</span>
        </div>
        <div style={tooltipItemStyle}>
          身份证：<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '^\\d{15}|\\d{18}$');
            }}
          >^\d{15}|\d{18}$</span>
        </div>
      </div>
    </Card>
  );

  // 提取日期控件的输入格式提示内容
  const renderDateRegexTooltip = () => (
    <Card title="输入格式说明" size="small" className="w-full">
      <div style={tooltipCardStyle}>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{yy}');
            }}
          >{"{yy}"}</span> 年 年的后两位
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{yyyy}');
            }}
          >{"{yyyy}"}</span> 年 四位年
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{M}');
            }}
          >{"{M}"}</span> 月 1到12月
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{MM}');
            }}
          >{"{MM}"}</span> 月 01到12月
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{d}');
            }}
          >{"{d}"}</span> 日 1到31
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{h}');
            }}
          >{"{h}"}</span>或<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{hh}');
            }}
          >{"{hh}"}</span> 小时 00到23时
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{m}');
            }}
          >{"{m}"}</span>或<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{mm}');
            }}
          >{"{mm}"}</span> 分钟 00到59分
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{s}');
            }}
          >{"{s}"}</span> 秒 0到59秒
        </div>
        <div style={tooltipItemStyle}>
          <span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{ss}');
            }}
          >{"{ss}"}</span> 秒 00到59秒
        </div>
        <div style={tooltipItemStyle}>
          如：<span
            style={tooltipLinkStyle}
            onClick={(e) => {
              e.stopPropagation();
              form.setFieldValue('regularExpression', '{yyyy}-{MM}-{dd} {hh}:{mm}:{ss}');
            }}
          >{"{yyyy}-{MM}-{dd} {hh}:{mm}:{ss}"}</span>
        </div>
        <div>转换后：2017-01-01 00:00:00</div>
      </div>
    </Card>
  );

  // 渲染正则表达式/输入格式字段
  const renderRegexField = () => {
    if (controlType === "ctrl_text") {
      return (
        <Tooltip
          color="white"
          placement="top"
          trigger="click"
          overlayInnerStyle={{ width: '300px' }}
          getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          title={renderTextRegexTooltip()}
        >
          <Form.Item
            name={`regularExpression`}
            label="正则表达式"
            rules={[{ required: false, message: "请输入正则表达式" }]}
          >
            <Input placeholder="请输入正则表达式" />
          </Form.Item>
        </Tooltip>
      );
    } else if (controlType === "ctrl_date") {
      return (
        <Tooltip
          color="white"
          placement="top"
          trigger="click"
          overlayInnerStyle={{ width: '300px' }}
          getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          title={renderDateRegexTooltip()}
        >
          <Form.Item
            name={`regularExpression`}
            label="时间格式"
            rules={[{ required: true, message: "请输入时间格式" }]}
          >
            <Input placeholder="请输入时间格式" />
          </Form.Item>
        </Tooltip>
      );
    }
    return null;
  };

  // 渲染选项列表字段
  const renderOptionsField = () => {
    if (controlType === "ctrl_radio" || controlType === "ctrl_checkbox" || controlType === "ctrl_select") {
      return (
        <Form.Item label="选项列表" required >
          <Form.List name={['optionsJson']}>
            {(fields, { add, remove }) => (
              <div style={{ border: '1px solid #d9d9d9', borderRadius: '4px', padding: '16px', marginBottom: '8px' }}>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8}>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, "label"]}
                        rules={[{ required: true, message: '请输入选项标签' }]}
                      >
                        <Input placeholder="选项标签" />
                      </Form.Item>
                    </Col>
                    <Col span={10}>
                      <Form.Item
                        {...restField}
                        name={[name, "value"]}
                        rules={[{ required: true, message: '请输入选项值' }]}
                      >
                        <Input placeholder="选项值" />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <a onClick={() => remove(name)}>删除</a>
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <a onClick={() => add()}>+ 添加选项</a>
                </Form.Item>
              </div>
            )}
          </Form.List>
        </Form.Item>
      );
    }
    return null;
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item label="控件分类" name={`categoryId`} rules={[{ required: true, message: "请选择控件分类" }]}>
              <Radio.Group options={state.categorys} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`controlType`} label="控件类型" rules={[{ required: true, message: "请选择控件类型" }]}>
              <Radio.Group options={ControlTypeMap} onChange={handleControlTypeChange} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`name`} label="控件名称" rules={[{ required: true, message: "请输入控件名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`code`} label="控件编码" rules={[{ required: true, message: "请输入控件编码" }]}>
              <Input placeholder="请输入编码" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`description`} label="内容描述" rules={[{ required: true, message: "请输入内容描述" }]}>
              <Input placeholder="请输入内容描述" />
            </Form.Item>
          </Col>
          {/*   
          <Col span={layout.col / 2}>
            <Form.Item name={`isRequired`} label="是否必填" rules={[{ required: false, message: "请选择是否必填" }]} initialValue={1}>
              <Radio.Group options={IsRequiredMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`prohibitedDelete`} label="不许删除" rules={[{ required: false, message: "请选择不许删除" }]} initialValue={1}>
              <Radio.Group options={ProhibitedDeleteMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`placeholder`} label="输入提示" rules={[{ required: false, message: "请输入输入提示文字" }]}>
              <Input placeholder="请输入输入提示文字" />
            </Form.Item>
          </Col> 
          */}
          <Col span={layout.col / 2}>
            <Form.Item name={`operationMode`} label="操作模式" rules={[{ required: true, message: "请输入操作模式" }]} initialValue={1}>
              <Select placeholder="请选择操作模式" options={OperationModeMap} allowClear />
            </Form.Item>
          </Col>

          <Col span={layout.col / 2}>
            {renderRegexField()}
          </Col>

          {/* <Col span={layout.col / 2}>
            <Form.Item name={`fontColor`} label="字体颜色" rules={[{ required: false, message: "请输入字体颜色" }]}>
              <ColorPicker showText />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`backgroundColor`} label="背景颜色" rules={[{ required: false, message: "请输入背景颜色" }]}>
              <ColorPicker showText />
            </Form.Item>
          </Col> */}
          <Col span={layout.col / 2}>
            <Form.Item name={`state`} label="状态" rules={[{ required: false, message: "请选择状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col / 2}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            {renderOptionsField()}
          </Col>

        </Row>
      </Form>
    </WeModal >
  );
};

export default MallMedicalControlEdit;