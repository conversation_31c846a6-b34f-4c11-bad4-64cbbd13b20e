//状态 1.启用 0.禁用
export const StateMap = [
  { value: 1, label: "启用" },
  { value: 0, label: "禁用" },
];

//控件类型 ctrl_label | ctrl_text | ctrl_textarea | ctrl_date | ctrl_radio | ctrl_checkbox | ctrl_select
export const ControlTypeMap = [
  { value: "ctrl_label", label: "标签控件" },
  { value: "ctrl_text", label: "单行文本" },
  { value: "ctrl_textarea", label: "多行文本" },
  { value: "ctrl_date", label: "日期控件" },
  { value: "ctrl_select", label: "下拉选择" },
  { value: "ctrl_radio", label: "单选框" },
  { value: "ctrl_checkbox", label: "复选框" },
]

//是否必填 1.是 0.否
export const IsRequiredMap = [
  { value: 1, label: "是" },
  { value: 0, label: "否" },
];

//不许删除 1.是 0.否
export const ProhibitedDeleteMap = [
  { value: 1, label: "是" },
  { value: 0, label: "否" },
];

//操作模式 1.编辑模式 2.只读模式
export const OperationModeMap = [
  { value: 1, label: "编辑模式" },
  { value: 2, label: "只读模式" },
];  