import Style from "./index.module.scss";
import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import MallMedicalControlApi from "./api";
import MallMedicalControlEdit from "./edit";
import { Card, Col, Form, Input, Popconfirm, Radio, Row, Select, Space, Tooltip, Tree, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { useMount, useSetState } from "react-use";
import MallMedicalControlCategoryApi from "../MedicalControlCategory/api";
import MallMedicalControlCategoryEdit from "../MedicalControlCategory/edit";
import { DeleteOutlined, FormOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { ControlTypeMap, OperationModeMap, StateMap } from "./types";
const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const MallMedicalControlPage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    categorys: [] as any[],
    categoryId: null as any,
  });

  useMount(() => {
    fetchCategorys();
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const fetchCategorys = async () => {
    const params = { pageSize: 9999 };
    const res = await MallMedicalControlCategoryApi.getMallMedicalControlCategory({ params });
    const types = genTree(res?.list || [], (item) => ({ key: item.id, title: item.name, ...item }));
    setState({ categorys: types });
  };

  const genTree = (list: any[], format: (item: any) => any) => {
    const arr: any[] = [];
    const map: any = {};

    list = list.sort((a, b) => a.sort - b.sort);

    list.forEach((item) => {
      map[item.id] = format(item);
    });

    list.forEach((rect) => {
      const item = map[rect.id];
      const parent = map[item?.parentId];

      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(item);
      } else {
        arr.push(item);
      }
    });

    return arr;
  };

  const handleDelType = async (item: any) => {
    const data = { ids: item.id };
    await MallMedicalControlCategoryApi.delMallMedicalControlCategory({ data });
    message.success("删除分类成功");
    fetchCategorys();
  };

  const handleDelItem = async (item: any) => {
    const data = { ids: item.id };
    await MallMedicalControlApi.delMallMedicalControl({ data });
    message.success("删除组件成功");
    handleReload();
  };


  return (
    <Row wrap={false} gutter={10}>
      <Col flex={`300px`} style={{ minWidth: 0 }}>
        <Card
          bodyStyle={{ padding: 10 }}
          title={`控件分类`}
          style={{ height: "100%" }}
          extra={
            <MallMedicalControlCategoryEdit title={`添加分类`} onOk={() => fetchCategorys()} >
              <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
            </MallMedicalControlCategoryEdit>
          }
        >
          <Tree.DirectoryTree
            className={Style.tree}
            treeData={state.categorys}
            showIcon={false}
            selectedKeys={[state.categoryId]}
            onSelect={(e) => {
              const key = e[0];
              if (state.categoryId === key) {
                setState({ categoryId: "" });
              } else {
                setState({ categoryId: key });
              }
            }}
            titleRender={(item) => {
              return (
                <div className={Style.row} key={item.key}>
                  <div className={Style.title}>{item.title}</div>
                  <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                    <MallMedicalControlCategoryEdit title={`编辑分类`} onOk={() => fetchCategorys()} data={item}>
                      <Tooltip title="编辑分类">
                        <FormOutlined className={Style.ico} />
                      </Tooltip>
                    </MallMedicalControlCategoryEdit>

                    <Popconfirm title={`确定删除分类 - ${item.title}？`} onConfirm={() => handleDelType(item)}>
                      <Tooltip title="删除分类">
                        <DeleteOutlined className={Style.ico} />
                      </Tooltip>
                    </Popconfirm>
                  </div>
                </div>
              );
            }}
          />
        </Card>
      </Col>
      <Col flex={"auto"}>
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ categoryId: state.categoryId }}
          request={(p) => MallMedicalControlApi.getMallMedicalControl({ params: p })}
          title={
            <Space>
              <MallMedicalControlEdit title={"新增病历控件"} onOk={handleReload}>
                <WeTable.AddBtn />
              </MallMedicalControlEdit>
            </Space>
          }
          search={[
            <Form.Item label="控件编码" name={`code`}>
              <Input placeholder="请输入控件编码" />
            </Form.Item>,
            <Form.Item label="控件名称" name={`name`}>
              <Input placeholder="请输入控件名称" />
            </Form.Item>,
            <Form.Item label="控件类型" name={`controlType`}>
              <Select placeholder="请选择控件类型" options={ControlTypeMap} allowClear >
              </Select>
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Radio.Group
                options={StateMap}
              />
            </Form.Item>,
            <Form.Item label="创建日期" name={`CreateDate`}>
              <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
            </Form.Item>,
          ]}
          columns={[
            { title: "控件类型", dataIndex: "controlType", render: (c) => ControlTypeMap.find((item) => item.value === c)?.label || "--" },
            { title: "编码", dataIndex: "code", render: (c) => c ? c : "--" },
            { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
            { title: "描述", dataIndex: "description", render: (c) => c ? c : "--" },
            //{ title: "是否必填", dataIndex: "isRequired", render: (c) => IsRequiredMap.find((item) => item.value === c)?.label || "--" },
            //{ title: "不许删除", dataIndex: "prohibitedDelete", render: (c) => ProhibitedDeleteMap.find((item) => item.value === c)?.label || "--" },
            //{ title: "输入提示", dataIndex: "placeholder", render: (c) => c ? c : "--" },
            //{ title: "正则表达式", dataIndex: "regularExpression", render: (c) => c ? c : "--" },
            { title: "操作模式", dataIndex: "operationMode", render: (c) => OperationModeMap.find((item) => item.value === c)?.label || "--" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label || "--" },
            { title: "排序", dataIndex: "sort" },
            { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallMedicalControlEdit title={`编辑病历控件`} data={item} onOk={handleReload}>
                      <a>编辑</a>
                    </MallMedicalControlEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDelItem(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Col>
    </Row>




  );
};

export default MallMedicalControlPage;
