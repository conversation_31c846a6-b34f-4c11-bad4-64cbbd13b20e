import WeModal from "@/components/WeModal/WeModal";
import AppNoticeApi from "./api";
import { Col, Form, Input, Radio, Row, Select, message } from "antd";
import { DatePicker } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { JumpTypeMap, StateMap, TypeMap } from "./types";
import { SwitchContent } from "./unit";
import { DatePresetRanges, formatDateRange } from "@/utils/Tools";

const layout = { row: 10, col: 12 };

const AppNoticeEdit = (props: { title: any; children: any; onOk?: Function; data?: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const jumpType = Form.useWatch("jumpType", form);

  const handleOpen = async () => {
    form.resetFields();
    if (props.data) {
      const fdate = (n: any) => (n ? dayjs(n) : undefined);
      const data = await AppNoticeApi.getAppNoticeInfo(props.data.id);
      //data.state = !!data.state;
      data.limitDate = [fdate(data.startDate), fdate(data.endDate)];
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.startDate = dayjs(data.startDate).format("YYYY-MM-DD HH:mm:ss");
    data.endDate = dayjs(data.endDate).format("YYYY-MM-DD HH:mm:ss");
    //data.state = Number(data.state);
    data.timeLimit = Number(data.timeLimit);
    const limitDate = formatDateRange(data.limitDate, "YYYY-MM-DD HH:mm:ss");
    data.startDate = limitDate.start;
    data.endDate = limitDate.end;
    delete data.saleDate;
    if (!data.id) {
      await AppNoticeApi.addAppNotice({ data });
      message.success("添加公告成功");
    }
    if (data.id) {
      await AppNoticeApi.putAppNotice({ data });
      message.success("修改公告成功");
    }
    props.onOk?.();
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`type`} label="类型" rules={[{ required: false, message: "请选择类型" }]} initialValue={1}>
              <Radio.Group options={TypeMap} />
            </Form.Item>
          </Col>
          <Col />
          <Col span={layout.col * 2}>
            <Form.Item name={`title`} label="标题" rules={[{ required: false, message: "请输入标题" }]}>
              <Input placeholder="请输入标题" />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2}>
            <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
              <Input.TextArea placeholder="请输入内容" rows={4} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 2} >
            <Form.Item label="展示时间" className="w-600px">
              <SwitchContent name="timeLimit">
                <Form.Item
                  noStyle
                  name="date"
                  initialValue={[]}
                  rules={[{ required: true, message: "请选择限时时间" }]}
                >
                  <DatePicker.RangePicker presets={DatePresetRanges} className="w-full" showTime />
                </Form.Item>
              </SwitchContent>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              label="跳转类型"
              name="jumpType"
              rules={[{ required: true, message: "请选择跳转类型" }]}
              initialValue={0}
            >
              <Select options={JumpTypeMap} placeholder="请选择跳转类型" />
            </Form.Item>
          </Col>
          <Col span={layout.col} />
          {jumpType == 1 && (
            <Col span={layout.col * 2}>
              <Form.Item label="URL地址" name="jumpKey" rules={[{ required: true, message: "请输入URL地址" }]}>
                <Input placeholder="请输入URL地址" />
              </Form.Item>
            </Col>
          )}
          <Col span={layout.col}>
            <Form.Item
              label="状态"
              name="state"
              rules={[{ required: true, message: "请选择状态" }]}
              initialValue={1}
            >
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppNoticeEdit;
