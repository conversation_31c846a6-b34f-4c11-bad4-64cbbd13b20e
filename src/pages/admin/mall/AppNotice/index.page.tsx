import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import AppNoticeApi from "./api";
import AppNoticeEdit from "./edit";
import { Form, Input, Popconfirm, Select, Space, message } from "antd";
import { Typography } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges } from "@/utils/Tools";
import dayjs from "dayjs";
import { JumpTypeMap, StateMap, TypeMap } from "./types";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppNoticePage = () => {

  const tableRef = useRef<WeTableRef>(null);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await AppNoticeApi.delAppNotice({ data: { ids: item.id } });
    message.success("删除公告成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        params={{}}//默认参数
        request={(p) => AppNoticeApi.getAppNotice({ params: p })}
        title={
          <AppNoticeEdit title={"新增公告"} onOk={handleReload}>
            <WeTable.AddBtn />
          </AppNoticeEdit>
        }
        search={[
          <Form.Item label="类型" name={`type`}>
            <Select options={TypeMap} placeholder="请选择类型" allowClear />
          </Form.Item>,
          <Form.Item label="标题" name={`title`}>
            <Input placeholder="请输入标题" />
          </Form.Item>,
          <Form.Item label="内容" name={`content`}>
            <Input placeholder="请输入内容" />
          </Form.Item>,
          <Form.Item label="状态" name={`state`}>
            <Select options={StateMap} placeholder="请选择状态" allowClear />
          </Form.Item>,
          <Form.Item label="创建日期" name={`CreateDate`}>
            <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
          </Form.Item>,
        ]}
        columns={[
          { title: "类型", dataIndex: "type", render: (c) => TypeMap.find((item) => item.value === c)?.label },
          {
            title: "标题",
            dataIndex: "title",
            width: 500,
            render: (c) => (
              <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
                {c ? c : "--"}
              </Typography.Text>
            ),
          },
          {
            title: "展示时间",
            width: 300,
            render: (c) => {
              return (
                <>
                  {!c.timeLimit && "永久"}
                  {!!c.timeLimit &&
                    `${dayjs(c.startDate).format("YYYY-MM-DD HH:mm")} 至 ${dayjs(c.endDate).format("YYYY-MM-DD HH:mm")}`}
                </>
              );
            },
          },
          { title: "跳转类型", dataIndex: "jumpType", render: (c) => JumpTypeMap.find((item) => item.value === c)?.label },
          { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
          { title: "排序", dataIndex: "sort" },
          { title: "创建日期", dataIndex: "createDate", render: (c) => fdate(c) },
          {
            fixed: "right",
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <AppNoticeEdit title={`编辑公告`} data={item} onOk={handleReload}>
                    <a>编辑</a>
                  </AppNoticeEdit>
                  <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default AppNoticePage;
