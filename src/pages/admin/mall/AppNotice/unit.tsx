import { Switch01 } from "@/components/Units/Switch01";
import { Form, SwitchProps } from "antd";

export const SwitchOC = (props: SwitchProps) => {
  return <Switch01 className="w-60px" unCheckedChildren="关闭" checkedChildren="开启" {...props} />;
};

export const SwitchContent = (props: { name: string; children?: any }) => {
  const fname = props.name;
  const form = Form.useFormInstance();
  const val = Form.useWatch(fname, form);

  return (
    <div className="flex gap-4 items-center">
      <Form.Item noStyle name={fname}>
        <SwitchOC />
      </Form.Item>
      {!!val && <div className="w-full">{props.children}</div>}
    </div>
  );
};
