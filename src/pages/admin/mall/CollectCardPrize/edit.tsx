import WeModal from "@/components/WeModal/WeModal";
import MallCollectCardPrizeApi from "./api";
import { Col, Form, Input, Radio, Row, Select, message } from "antd";
import { InputNumber } from "antd";
import ImageUpload from "@/components/ImageUpload";
import { PropertyMap, StateMap } from "./types";
import { useSetState } from "react-use";
import MallApi from "@/services/MallApi";
import CardTypePicker from "../card/CardTypePicker";

const layout = { row: 10, col: 8 };

const MallCollectCardPrizeEdit = (props: { title: any; children: any; onOk?: Function; data?: any; activityId: any; collectCardSeriesId: any; hideSubmit?: any; }) => {

  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    CollectCardTypes: [] as any[],
    MallCardTypes: [] as any[],
  });


  const fetchCollectCardTypeList = async () => {
    const res = await MallApi.getCollectCard({ params: { collectCardSeriesId: props.collectCardSeriesId, pageSize: 999 } });
    let list = res?.list || [];
    list = list.map((item: any) => ({ label: item.name, value: item.id }));
    setState({ CollectCardTypes: list || [] });
  };

  const handleOpen = async () => {
    form.resetFields();
    fetchCollectCardTypeList();
    if (props.data) {
      const data = await MallCollectCardPrizeApi.getMallCollectCardPrizeInfo(props.data.id);
      data.image = ImageUpload.serializer(data.image);
      if (data.property == 10) {
        data.value = ((data?.value || 0) / 100)?.toFixed(2);; // 如果是M币，将值转换为原始值
      }

      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.activityId = props.activityId;
    data.image = ImageUpload.deserializer(data.image);
    if (property == 10) {
      data.value = (data?.value || 0) * 100;
    }
    if (!data.id) {
      await MallCollectCardPrizeApi.addMallCollectCardPrize({ data });
      message.success("添加集卡奖品成功");
    }
    if (data.id) {
      await MallCollectCardPrizeApi.putMallCollectCardPrize({ data });
      message.success("修改集卡奖品成功");
    }
    props.onOk?.();
  };

  const property = Form.useWatch("property", form);

  return (
    <WeModal trigger={props.children} title={props.title} width={900} onOpen={handleOpen} okButtonProps={{ hidden: props.hideSubmit }} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "110px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Col span={layout.col * 3}>
            <Form.Item label="属性" name="property" rules={[{ required: true, message: "请选择属性" }]}>
              <Radio.Group
                onChange={() => {
                  form.setFieldsValue({ keyData: "" });
                }}
                options={PropertyMap}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name={`name`} label="名称" rules={[{ required: true, message: "请输入名称" }]}>
              <Input placeholder="请输入名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            {property == 10 && (
              <Form.Item name={`value`} label="M币" rules={[{ required: true, message: "请输入价值" }]} initialValue={0}>
                <InputNumber min={0} placeholder="请输入价值" style={{ width: "100%" }} addonAfter="M币" />
              </Form.Item>
            )}
            {property == 20 && (
              <Form.Item name={`keyData`} label="奖品卡" rules={[{ required: true, message: "请选择卡项" }]} >
                <CardTypePicker params={{ cardProperty: 2 }} />
              </Form.Item>
            )}
            {property == 30 && (
              <Form.Item name="keyData" label="集卡" rules={[{ required: true, message: "请选择集卡" }]}>
                <Select options={state.CollectCardTypes} placeholder="请选择集卡" />
              </Form.Item>
            )}
          </Col>

          <Col span={layout.col}>
            <Form.Item name={`winRate`} label="中奖几率" rules={[{ required: true, message: "请输入中奖几率" }]}
              tooltip="此奖项中奖值占所有奖项中奖值的比例。"
              initialValue={0}>
              <InputNumber placeholder="请输入中奖几率" style={{ width: "100%" }} step="1" max={100} min={0} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`provideTimes`} label="从N次加强" rules={[{ required: false, message: "请输入从N次加强" }]}
              tooltip="当累计抽奖次数达到此项设定时。若从未抽中，则往后每一次中奖概率进行加强，直至中奖为止。（0表示不加强）"
              initialValue={0}>
              <InputNumber placeholder="请输入从第几次开始概率加强(0表示不加强)" style={{ width: "100%" }} max={100} min={0} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`provideEachRate`} label="每次加强几率" rules={[{ required: false, message: "请输入每次加强几率" }]}
              tooltip="加强后，在现有的中奖几率上每次增加指定中奖记录（0表示不加强）"
              initialValue={0}>
              <InputNumber placeholder="请输入每次加强几率(0表示不加强)" style={{ width: "100%" }} max={100} min={0} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`state`} label="状态" rules={[{ required: true, message: "请输入状态" }]} initialValue={1}>
              <Radio.Group options={StateMap} />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="排序" rules={[{ required: false, message: "请输入排序" }]} initialValue={999} tooltip="数字越小越靠前">
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          <Col span={layout.col * 3}>
            <Form.Item name={`image`} label="图片" rules={[{ required: true, message: "请上传图片" }]} valuePropName="fileList">
              <ImageUpload maxCount={1} />
            </Form.Item>
          </Col>

          <Col span={layout.col * 3}>
            <Form.Item name={`content`} label="说明" rules={[{ required: false, message: "请输入说明" }]}>
              <Input.TextArea placeholder="请输入说明" rows={3} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default MallCollectCardPrizeEdit;
