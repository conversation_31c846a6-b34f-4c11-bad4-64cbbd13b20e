import WeTable, { WeTableRef } from "@/components/WeTable";
import { cloneElement, useEffect, useRef } from "react";
import MallCollectCardPrizeApi from "./api";
import MallCollectCardPrizeEdit from "./edit";
import { Drawer, Form, Image, Input, Popconfirm, Select, Space, message } from "antd";
import { PropertyMap, StateMap } from "./types";
import { useSetState } from "react-use";
const MallCollectCardPrizePage = (props: { children: any; title: any; activityId: any; collectCardSeriesId: any; }) => {

  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    open: false,
  });

  useEffect(() => {
    if (state.open) {
      handleReload();
    }
  }, [state.open]);

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallCollectCardPrizeApi.delMallCollectCardPrize({ data: { ids: item.id } });
    message.success("删除集卡奖品成功");
    handleReload();
  };

  return (
    <div>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer
        open={state.open}
        width={"70%"}
        onClose={() => setState({ open: false })}
        title={props.title}
        keyboard={false}
      >
        <WeTable
          ref={tableRef}
          tableProps={{ scroll: { x: "max-content" } }}
          params={{ activityId: props.activityId }} //默认参数
          request={(p) => MallCollectCardPrizeApi.getMallCollectCardPrize({ params: p })}

          title={
            <Space>
              <MallCollectCardPrizeEdit title={"新增集卡奖品"} onOk={handleReload} activityId={props.activityId} collectCardSeriesId={props.collectCardSeriesId}>
                <WeTable.AddBtn />
              </MallCollectCardPrizeEdit>
            </Space>
          }
          search={[
            <Form.Item label="属性" name={`property`}>
              <Select options={PropertyMap} placeholder="请选择属性" allowClear />
            </Form.Item>,
            <Form.Item label="名称" name={`name`}>
              <Input placeholder="请输入名称" />
            </Form.Item>,
            <Form.Item label="状态" name={`state`}>
              <Select options={StateMap} allowClear placeholder="请选择状态" />
            </Form.Item>,
          ]}
          columns={[
            {
              title: "图片",
              dataIndex: "image",
              render: (c) => {
                return <Image src={c?.length ? c : ""} style={{ maxWidth: 50, maxHeight: 50 }} />;
              },
            },
            { title: "名称", dataIndex: "name", render: (c) => c ? c : "--" },
            { title: "属性", dataIndex: "property", render: (c) => PropertyMap.find((item) => item.value === c)?.label },
            { title: "中奖几率", dataIndex: "winRate" },
            { title: "状态", dataIndex: "state", render: (c) => StateMap.find((item) => item.value === c)?.label },
            { title: "排序", dataIndex: "sort" },
            {
              fixed: "right",
              title: "操作",
              render: (item) => {
                return (
                  <Space>
                    <MallCollectCardPrizeEdit title={`编辑集卡奖品`} data={item} activityId={props.activityId} collectCardSeriesId={props.collectCardSeriesId} onOk={handleReload}>
                      <a>编辑</a>
                    </MallCollectCardPrizeEdit>
                    <Popconfirm title={`确定要删除此条信息？`} onConfirm={() => handleDel(item)}>
                      <a>删除</a>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
        />
      </Drawer>
    </div>
  );
};

export default MallCollectCardPrizePage;
