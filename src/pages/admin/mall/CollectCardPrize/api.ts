import { makeApi } from "@/utils/Api";

const MallCollectCardPrizeApi = {
    getMallCollectCardPrize: makeApi("get", `/api/mallCollectCardPrize`),
    getMallCollectCardPrizeInfo: makeApi("get", `/api/mallCollectCardPrize`, true),
    addMallCollectCardPrize: makeApi("post", `/api/mallCollectCardPrize`),
    putMallCollectCardPrize: makeApi("put", `/api/mallCollectCardPrize`),
    delMallCollectCardPrize: makeApi("delete", `/api/mallCollectCardPrize`),
};

export default MallCollectCardPrizeApi;
