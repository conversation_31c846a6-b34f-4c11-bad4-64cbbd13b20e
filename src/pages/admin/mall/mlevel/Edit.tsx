import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { Checkbox, Col, Divider, Form, Input, InputNumber, Row,  message } from "antd";
import { useMount, useSetState } from "react-use";

const layout = { row: 10, col: 12 };

const LevelEdit = (props: { title: any; children: any; onOk?: Function; data?: any; groupType?: any }) => {
  const [form] = Form.useForm();

  const [state, setState] = useSetState({
    foreverTag: false,
    vipLv: [] as any[],
  });

  useMount(() => {
    fetchVipLv();
  });

  const fetchVipLv = async () => {
    const params = { pageSize: 9999 };
    const res = await MallApi.getMLevelForSelect({ params });
    let list: any[] = res?.list || [];
    list = list.map((item) => ({ label: item.name, value: item.id }));
    setState({ vipLv: list });
  };

  const handleOpen = () => {
    form.resetFields();

    if (props.data) {
      const data = { ...props.data };

      /* if (data.consumeRewardCashIntegralRadix > 0 && data.consumeRewardCashIntegralRadix == data.consumeRewardIntegralRadix) {
        data.consumeRewardIntegralCashTag = 1;
      }
      if (data.promotionRewardCashIntegralRadix > 0 && data.promotionRewardCashIntegralRadix == data.promotionRewardIntegralRadix) {
        data.promotionRewardIntegralCashTag = 1;
      }
      if (data.indirectPromotionRewardCashIntegralRadix > 0 && data.indirectPromotionRewardCashIntegralRadix == data.indirectPromotionRewardIntegralRadix) {
        data.indirectPromotionRewardIntegralCashTag = 1;
      } */

      form.setFieldsValue(data);

      setState({ foreverTag: data.foreverTag ? true : false });
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.foreverTag = Number(data.foreverTag);

    /* if (Number(data.consumeRewardIntegralCashTag) == 1) {
      data.consumeRewardCashIntegralRadix = data.consumeRewardIntegralRadix;
    } else {
      data.consumeRewardCashIntegralRadix = 0;
    }
    if (Number(data.promotionRewardIntegralCashTag) == 1) {
      data.promotionRewardCashIntegralRadix = data.promotionRewardIntegralRadix;
    } else {
      data.promotionRewardCashIntegralRadix = 0;
    }
    if (Number(data.indirectPromotionRewardIntegralCashTag) == 1) {
      data.indirectPromotionRewardCashIntegralRadix = data.indirectPromotionRewardIntegralRadix;
    } else {
      data.indirectPromotionRewardCashIntegralRadix = 0;
    } */
    data.groupType = props.groupType;

    if (data.id) {
      await MallApi.putMLevel({ data });
      message.success("修改会员等级成功");
    } else {
      await MallApi.addMLevel({ data });
      message.success("添加会员等级成功");
    }

    props.onOk?.();
  };

  //const commissionMode = Form.useWatch("commissionMode", form);

  const checkChange = (e: any) => {
    if (e.target.checked) {
      form.setFieldsValue({ effectiveDays: 0 });
      setState({ foreverTag: true });
    } else {
      form.setFieldsValue({ effectiveDays: undefined });
      setState({ foreverTag: false });
    }
  };

  // useEffect(() => {
  //   setState({ foreverTag: state.foreverTag ? true : false });
  // }, [state.foreverTag]);

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "130px" }}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>

        <Row gutter={layout.row}>
          <Divider orientation="left">基本信息</Divider>
          <Col span={layout.col}>
            <Form.Item name={`name`} label="等级名称" rules={[{ required: true, message: "请输入等级名称" }]}>
              <Input placeholder="请输入等级名称" />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`code`} label="等级编码" rules={[{ required: true, message: "请输入等级编码" }]}>
              <Input placeholder="请输入等级编码" />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`groupType`} label="体系分组" rules={[{ required: true, message: "请选择体系分组" }]}>
              <Select placeholder="请选择体系分组" options={GroupTypes} />
            </Form.Item>
          </Col> */}
          <Col span={layout.col}>
            <Form.Item name={`sort`} label="会员等级" rules={[{ required: true, message: "请输入会员等级" }]}>
              <InputNumber style={{ width: "100%" }} min={0} placeholder="请输入会员等级" />
            </Form.Item>
          </Col>
          <Col span={10}>
            <Form.Item name={`effectiveDays`} label="有效期" rules={[{ required: true, message: "请输入有效期" }]}>
              <InputNumber
                placeholder="请输入天数"
                disabled={state.foreverTag}
                style={{ width: "100%" }}
                min={0}
                addonAfter="天"
              />
            </Form.Item>
          </Col>
          <Col span={2}>
            <Form.Item name={`foreverTag`} initialValue={false} valuePropName="checked">
              <Checkbox value={1} onChange={checkChange}>
                永久
              </Checkbox>
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item name={`cardPrice`} label="售卡金额" initialValue={0}>
              <Input addonAfter="元" placeholder="请输入售卡金额" />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`rechargeMoney`} label="储值金额" initialValue={0}>
              <Input addonAfter="元" placeholder="请输入储值金额" />
            </Form.Item>
          </Col> */}

          <Divider orientation="left">权益信息</Divider>
          <Col span={24}>
            <Form.Item name={`equityDesc`} label="会员权益描述">
              <Input.TextArea autoSize={{ minRows: 2 }} maxLength={200} showCount placeholder="请输入会员权益描述" />
            </Form.Item>
          </Col>
          <Divider orientation="left">消费权益</Divider>
          <Col span={layout.col}>
            <Form.Item
              name={`discountRadix`}
              label="消费-享受折扣"
              rules={[{ required: true, message: "请输入消费享受折扣" }]}
              tooltip="会员消费时享受订单总金额的折扣,范围0~10，10表示不打折，数值越小，优惠力度越大"
            >
              <InputNumber
                placeholder="请输入消费享受折扣,范围0~10"
                style={{ width: "100%" }}
                min={0}
                max={10}
                step={0.1}
                addonAfter="折"
              />
            </Form.Item>
          </Col>
          <Col span={layout.col}>
            <Form.Item
              name={`integralRadix`}
              label="消费-M币抵现"
              rules={[{ required: true, message: "请输入消费时M币抵现百分比" }]}
              tooltip="会员消费时可用M币百分比，范围0~100（0表示不能使用M币，100表示可使用M币抵扣全部消费金额），数字越大优惠越大。"
            >
              <InputNumber
                placeholder="请输入消费时M币抵现"
                style={{ width: "100%" }}
                min={0}
                max={100}
                step={0.1}
                addonAfter="百分比"
              />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item
              name={`consumeRewardIntegralRadix`}
              label="消费-赠送M币"
              rules={[{ required: true, message: "请输入消费后赠送M币百分比" }]}
              tooltip="会员消费后根据订单总金额赠送M币，范围0~100（0表示不赠送，100表示按照总金额100%赠送M币），数字越大赠送越多。"
            >
              <InputNumber
                placeholder="请输入消费后赠送M币"
                style={{ width: "100%" }}
                min={0}
                max={100}
                step={0.1}
                addonAfter="百分比"
              />
            </Form.Item>
          </Col>
          <Form.Item name={`consumeRewardIntegralCashTag`} initialValue={false} valuePropName="checked">
            <Checkbox value={1}>
              可提现
            </Checkbox>
          </Form.Item>
          <Divider orientation="left">推荐权益</Divider>
          <Col span={layout.col * 2}>
            <Form.Item
              name={`commissionMode`}
              label="佣金模式"
              rules={[{ required: true, message: "请选择" }]}
              tooltip="按总金额：佣金根据订单总金额结算。按总佣金：佣金根据商品佣金比例结算。"
              initialValue={1}
            >
              <Radio.Group
                options={[
                  { label: "按订单金额", value: 1 },
                  { label: "按商品佣金", value: 2 },
                ]}
                optionType="button"
                buttonStyle="solid"
                defaultValue={1}
              />
            </Form.Item>
          </Col>

          {commissionMode === 1 && (
            <>
              <Col span={layout.col}>
                <Form.Item
                  name={`promotionRewardIntegralRadix`}
                  label="直推-奖励M币"
                  rules={[{ required: true, message: "请输入直推会员消费后奖励M币百分比" }]}
                  tooltip="直推的会员在消费后根据其订单总金额奖励M币，范围0~100（0表示不奖励M币，100表示按照总金额100%奖励M币），数字越大获得越多。"
                >
                  <InputNumber
                    placeholder="根据订单支付金额奖励M币"
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    step={0.1}
                    addonAfter="百分比"
                  />
                </Form.Item>
              </Col>
              <Form.Item name={`promotionRewardIntegralCashTag`} initialValue={false} valuePropName="checked">
                <Checkbox value={1}>
                  可提现
                </Checkbox>
              </Form.Item>
              <Col span={layout.col}>
                <Form.Item
                  name={`indirectPromotionRewardIntegralRadix`}
                  label="间推-奖励M币"
                  rules={[{ required: true, message: "请输入间推会员消费后奖励M币百分比" }]}
                  tooltip="间推的会员在消费后根据其订单总金额奖励M币，范围0~1（0表示不奖励M币，1表示按照订单总金额100%奖励M币），数字越大赠送越多。"
                >
                  <InputNumber
                    placeholder="根据订单支付金额奖励M币"
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    step={0.1}
                    addonAfter="百分比"
                  />
                </Form.Item>
              </Col>
              <Form.Item name={`indirectPromotionRewardIntegralCashTag`} initialValue={false} valuePropName="checked">
                <Checkbox value={1}>
                  可提现
                </Checkbox>
              </Form.Item>
            </>
          )}
          {commissionMode === 2 && (
            <>
              <Col span={layout.col}>
                <Form.Item
                  name={`promotionRewardIntegralRadix`}
                  label="直推-奖励M币"
                  rules={[{ required: true, message: "请输入直推会员消费后奖励M币百分比" }]}
                  tooltip="直推的会员在消费后根据其订单商品总佣金奖励M币，范围0~100（0表示不奖励M币，100表示按照订单商品总佣金100%奖励M币），数字越大获得越多。"
                >
                  <InputNumber
                    placeholder="根据订单总佣金奖励M币"
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    step={0.1}
                    addonAfter="百分比"
                  />
                </Form.Item>
              </Col>
              <Form.Item name={`promotionRewardIntegralCashTag`} initialValue={false} valuePropName="checked">
                <Checkbox value={1}>
                  可提现
                </Checkbox>
              </Form.Item>
              <Col span={layout.col}>
                <Form.Item
                  name={`indirectPromotionRewardIntegralRadix`}
                  label="间推-奖励M币"
                  rules={[{ required: true, message: "请输入间推会员消费后奖励M币百分比" }]}
                  tooltip="间推的会员在消费后根据其订单商品总佣金奖励M币，范围0~1（0表示不奖励M币，1表示按照订单商品总佣金100%奖励M币），数字越大赠送越多。"
                >
                  <InputNumber
                    placeholder="根据订单总佣金奖励M币"
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    step={0.1}
                    addonAfter="百分比"
                  />
                </Form.Item>
              </Col>
              <Form.Item name={`indirectPromotionRewardIntegralCashTag`} initialValue={false} valuePropName="checked">
                <Checkbox value={1}>
                  可提现
                </Checkbox>
              </Form.Item>
            </>
          )} */}

        </Row>
      </Form>
    </WeModal >
  );
};

export default LevelEdit;
