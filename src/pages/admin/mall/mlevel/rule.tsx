import RichText from "@/components/RichText";
import WeModal from "@/components/WeModal/WeModal";
import Mall<PERSON>pi from "@/services/MallApi";
import { Col, Form, message, Row } from "antd";

const layout = { row: 10, col: 24 };

export const LevelRule = (props: { children: any; onOk: any }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    data.signOpenTag = Number(data.signOpenTag);
    await MallApi.putMLevelRule({ data });
    message.success("保存成功");
    props.onOk();
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await MallApi.gutMLevelRule();
    data.signOpenTag = !!data.signOpenTag;
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="邀请规则" width={1000} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "120px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`newbieInviteRuleContent`} label="老带新规则说明">
              <RichText></RichText>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
