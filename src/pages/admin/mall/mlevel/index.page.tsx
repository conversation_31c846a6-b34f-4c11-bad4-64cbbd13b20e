import WeTable, { WeTableRef } from "@/components/WeTable";
import { useRef } from "react";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import LevelEdit from "./Edit";
import { Button, Card, Popconfirm, Space, Tooltip, Typography, message } from "antd";
import { PaperClipOutlined, QuestionCircleOutlined } from "@ant-design/icons";
import MallShopVipUpgradeConfigPage from "../mlevelconfig/index.page";
import { GroupTypes } from "./types";
import MallShopVipCommissionConfigPage from "../mlevelcommission/index.page";
import { LevelRule } from "./rule";
import { useSetState } from "react-use";

const MemberPage = () => {
  const tableRef = useRef<WeTableRef>(null);

  const [state, setState] = useSetState({
    groupType: GroupTypes[0].value + "",
  });

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleDel = async (item: any) => {
    await MallApi.delMLevel({ data: { ids: item.id } });
    message.success("删除会员等级成功");
    handleReload();
  };

  return (
    <div>
      <Card
        className="mb-10px"
        classNames={{ body: "hidden" }}
        activeTabKey={state.groupType}
        tabList={GroupTypes.map((n) => ({ key: n.value + "", label: n.label }))}
        onTabChange={(e) => setState({ groupType: e })}
        tabBarExtraContent={
          <LevelRule onOk={handleReload}>
            <Button type="primary" icon={<PaperClipOutlined />}>邀请规则</Button>
          </LevelRule>
        }
      />
      <WeTable
        ref={tableRef}
        params={{ groupType: state.groupType }}
        request={(p) => MallApi.getMLevel({ params: p })}
        title={
          <>
            <LevelEdit title={"新增会员等级"} groupType={state.groupType} onOk={handleReload}>
              <WeTable.AddBtn />
            </LevelEdit>
          </>
        }
        /*  search={[
           <Form.Item label="等级名称" name={`name`}>
             <Input placeholder="请输入等级名称" />
           </Form.Item>,
         ]} */
        columns={[
          { title: "等级名称", dataIndex: "name" },
          { title: "等级编码", dataIndex: "code" },
          // { title: "体系分组", dataIndex: "groupType", render: (c) => GroupTypes.find((t) => t.value === c)?.label },
          { title: "会员等级", dataIndex: "sort" },
          {
            title: "有效期", dataIndex: "effectiveDays", render: (c, item) => (
              item.foreverTag === 1 ? "永久有效" : c + "天"
            )
          },
          { title: "售卡金额", dataIndex: "cardPrice" },
          {
            title: (
              <>
                消费享折扣
                <Tooltip title="会员消费时享受订单总金额的折扣,范围0~10，10表示不打折，数值越小，优惠力度越大">
                  <QuestionCircleOutlined style={{ cursor: "pointer", marginLeft: 4 }} />
                </Tooltip>
              </>
            ),
            dataIndex: "discountRadix",
            render: (c) => {
              return c == 10 ? "无优惠" : c + "折"
            }
          },
          {
            title: "注册默认",
            dataIndex: "defaultTag",
            render: (c, item) => (
              <div>
                {c === 1 ? <b>当前默认</b> :
                  <>
                    [<Typography.Link
                      onClick={async () => {
                        await MallApi.defaultMLevel({ data: { id: item.id } });
                        handleReload();
                      }}
                    >
                      设为默认
                    </Typography.Link>
                    ]
                  </>
                }
              </div>
            ),
          },
          {
            title: "操作",
            render: (item) => {
              return (
                <Space>
                  <LevelEdit title={`编辑会员等级 - ${item.name}`} data={item} groupType={state.groupType} onOk={handleReload}>
                    <a>编辑</a>
                  </LevelEdit>
                  <MallShopVipUpgradeConfigPage title={`升级/降级/延期规则 - ${item.name}`} vipLevelId={item.id}>
                    <a>升|降|延规则</a>
                  </MallShopVipUpgradeConfigPage>
                  <MallShopVipCommissionConfigPage title={`佣金设置 - ${item.name}`} vipLevelId={item.id}>
                    <a>佣金设置</a>
                  </MallShopVipCommissionConfigPage>
                  <Popconfirm title={`确定要删除 - ${item.name} 吗？`} onConfirm={() => handleDel(item)}>
                    <a>删除</a>
                  </Popconfirm>
                </Space>
              );
            },
          },
        ]}
      />
    </div>
  );
};

export default MemberPage;
