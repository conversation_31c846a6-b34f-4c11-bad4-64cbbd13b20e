import WeTable, { WeTableRef } from "@/components/WeTable";
import { DatePicker, Drawer, Form, Input, message, Popconfirm, Select, Typography } from "antd";
import AppInformationArticleApi from "./api";
import dayjs from "dayjs";
import { cloneElement, useEffect, useRef } from "react";
import { useSetState } from "react-use";

const fdate = (n: any) => (n ? dayjs(n).format("YYYY-MM-DD HH:mm:ss") : "");

export const Comment = (props: { params?: any; children?: any }) => {
  const tableRef = useRef<WeTableRef>(null);
  const [state, setState] = useSetState({ open: false });

  const toggle = async (item: any) => {
    const data = { id: item.id, showState: Number(!item.showState) };
    await AppInformationArticleApi.putComment({ data });
    tableRef.current?.reload();
    message.success("操作成功");
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  useEffect(() => {
    if (state.open) {
      tableRef.current?.reset();
    }
  }, [state.open]);

  return (
    <>
      {cloneElement(props.children, { onClick: () => setState({ open: true }) })}
      <Drawer open={state.open} width={1200} title="评论列表" onClose={() => setState({ open: false })}>
        <WeTable
          size={10}
          ref={tableRef}
          request={(p) => AppInformationArticleApi.getComment({ params: p })}
          params={props.params}
          search={[
            <Form.Item label="已读状态" name={`reviewState`}>
              <Select
                placeholder="请选择"
                options={[
                  { label: "未读", value: 1 },
                  { label: "已读", value: 2 },
                ]}
                allowClear
              />
            </Form.Item>,
            <Form.Item label="发布人" name={`appUserData`}>
              <Input placeholder="请输入" />
            </Form.Item>,
            <Form.Item label="内容" name={`content`}>
              <Input placeholder="请输入" />
            </Form.Item>,
            <Form.Item label="发布时间" name={`CreateDate`}>
              <DatePicker.RangePicker />
            </Form.Item>,
          ]}
          columns={[
            { title: "发布人", render: (c) => c?.appUser?.name },
            { title: "回复给", render: (c) => c?.replyCommentsUser?.name || "--" },
            {
              title: "内容",
              dataIndex: "content",
              render: (c) => (
                <>
                  <div className="w-200px" style={{ wordBreak: "break-all" }}>
                    {c}
                  </div>
                </>
              ),
            },
            {
              title: (
                <div className="flex items-center">
                  <div>已读</div>
                  <Popconfirm
                    title="确认？"
                    onConfirm={async () => {
                      await AppInformationArticleApi.makeArticleRead({ data: { reviewAll: 1, type: props.params.type } });
                      handleReload();
                    }}
                  >
                    <a className="text-s ml-1">全部已读</a>
                  </Popconfirm>
                </div>
              ),
              dataIndex: "reviewState",
              render: (c, item) => (
                <div>
                  {c === 2 ? "已读" :
                    <>
                      未读
                      [<Typography.Link
                        onClick={async () => {
                          await AppInformationArticleApi.makeCommentRead({ data: { id: item.id } });
                          handleReload();
                        }}
                      >
                        已读
                      </Typography.Link>
                      ]
                    </>
                  }
                </div>
              ),
            },
            {
              title: "显示/隐藏",
              dataIndex: "showState",
              render: (c, item) => (c ?
                <>
                  显示中
                  [
                  <Popconfirm title="确定要隐藏这条评论？" onConfirm={() => toggle(item)}>
                    <Typography.Link>隐藏</Typography.Link>
                  </Popconfirm>
                  ]
                </>
                :
                <>
                  已隐藏
                  [
                  <Popconfirm title="确定要显示这条评论？" onConfirm={() => toggle(item)}>
                    <Typography.Link>显示</Typography.Link>
                  </Popconfirm>
                  ]
                </>)
            },
            { title: "时间", dataIndex: "createDate", render: (c) => fdate(c) },
            // {
            //   title: "操作",
            //   render: (item) => {
            //     return (
            //       <Space>
            //         <Popconfirm title="确定要隐藏这条评论？" onConfirm={() => toggle(item)}>
            //           <Typography.Link disabled={!item.showState}>隐藏</Typography.Link>
            //         </Popconfirm>
            //         <Popconfirm title="确定要显示这条评论？" onConfirm={() => toggle(item)}>
            //           <Typography.Link disabled={!!item.showState}>显示</Typography.Link>
            //         </Popconfirm>
            //       </Space>
            //     );
            //   },
            // },
          ]}
        />
      </Drawer>
    </>
  );
};
