import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, message, Row } from "antd";
import AppInformationArticleApi from "./api";
import { Switch01 } from "@/components/Units/Switch01";

const layout = { row: 10, col: 24 };

export const Rule2 = (props: { children: any; }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    await AppInformationArticleApi.putArticleRule({ data });
    message.success("保存成功");
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await AppInformationArticleApi.getArticleRule();
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="默认规则" width={400} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`blockedTag`} label="极简模式" tooltip="开启后。小程序只会展示默认用户的图文类文章，适用于小程序过审操作">
              <Switch01 />
            </Form.Item>
          </Col>
          {/* <Col span={layout.col}>
            <Form.Item name={`blockedVersion`} label="屏蔽版本" tooltip="开启后。小程序会对应版本只会展示固定版本的图文类文章，版本号为3段">
              <Input placeholder="请输入屏蔽版本1.0.0" />
            </Form.Item>
          </Col> */}
        </Row>
      </Form>
    </WeModal>
  );
};
