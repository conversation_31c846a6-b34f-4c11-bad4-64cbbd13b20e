import WeModal from "@/components/WeModal/WeModal";
import { Col, Form, Input, message, Row } from "antd";
import AppInformationArticleApi from "./api";

const layout = { row: 10, col: 24 };

export const Rule = (props: { children: any; }) => {
  const [form] = Form.useForm();

  const onOk = async () => {
    const data = await form.validateFields();
    await AppInformationArticleApi.putArticleRule({ data });
    message.success("保存成功");
  };

  const onOpen = async () => {
    form.resetFields();
    const data = await AppInformationArticleApi.getArticleRule();
    form.setFieldsValue(data);
  };

  return (
    <WeModal trigger={props.children} title="默认用户设置" width={400} onOk={onOk} onOpen={onOpen}>
      <Form form={form} labelCol={{ flex: "100px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`defaultUserMobile`} label="默认手机号">
              <Input placeholder="请输入默认用户手机号 " />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};
