import WeModal from "@/components/WeModal/WeModal";
import AppInformationArticleApi from "./api";
import { Col, Form, Radio, Row, Select, message } from "antd";
import { CopyTypeMap } from "./types";
import { useSetState } from "react-use";
import { useEffect } from "react";

const layout = { row: 10, col: 24 };

const AppInformationArticleCopy = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  audit?: boolean;
}) => {
  const [form] = Form.useForm();
  const [state, setState] = useSetState({
    type: "",
    cates: [] as any[],
  });

  useEffect(() => {
    setState({ cates: [] });
    form.resetFields(["categoryId"]);
    if (state.type) {
      fetchCates();
    }
  }, [state.type]);

  const handleOpen = async () => {
    setState({ cates: [] });
    form.resetFields();
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.id = props.data.id;

    await AppInformationArticleApi.copyAppInformationArticle({ data });
    message.success("复制文章成功");
    props.onOk?.();
  };

  const fetchCates = async () => {
    const res = await AppInformationArticleApi.getCate({ params: { type: state.type, pageSize: 9999 } });
    const list = res?.list || [];
    setState({ cates: list });
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={500} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "80px" }}>
        <Row gutter={layout.row}>
          <Col span={layout.col}>
            <Form.Item name={`type`} label="复制到" rules={[{ required: true, message: "请选择复制类型" }]}>
              <Radio.Group
                options={CopyTypeMap}
                optionType="button"
                buttonStyle="solid"
                onChange={(e) => setState({ type: e.target.value })}
              />
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item name={`categoryId`} label="选择分类" rules={[{ required: true, message: "请选择分类" }]}>
              <Select
                options={state.cates}
                fieldNames={{ label: "name", value: "id" }}
                allowClear
                placeholder="请选择分类"
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppInformationArticleCopy;
