import { makeApi } from "@/utils/Api";

const AppInformationArticleApi = {
  getAppInformationArticle: makeApi("get", `/api/appInformationArticle`),
  getAppInformationArticleInfo: makeApi("get", `/api/appInformationArticle`, true),
  addAppInformationArticle: makeApi("post", `/api/appInformationArticle`),
  copyAppInformationArticle: makeApi("post", `/api/appInformationArticle/copy`),
  auditAppInformationArticle: makeApi("post", `/api/appInformationArticle/audit`),
  putAppInformationArticle: makeApi("put", `/api/appInformationArticle`),
  delAppInformationArticle: makeApi("delete", `/api/appInformationArticle`),
  makeArticleRead: makeApi("post", `/api/appInformationArticle/review`),
  getArticleRule: makeApi("get", `/api/appInformationArticle/query_rule`),
  putArticleRule: makeApi("post", `/api/appInformationArticle/save_rule`),

  putState: makeApi("post", `/api/appInformationArticle/batch_modify_state`),

  getCate: makeApi("get", `/api/appInformationCategory`),
  addCate: makeApi("post", `/api/appInformationCategory`),
  putCate: makeApi("put", `/api/appInformationCategory`),
  delCate: makeApi("delete", `/api/appInformationCategory`),

  getComment: makeApi("get", `/api/appInformationArticleComment`),
  putComment: makeApi("put", `/api/appInformationArticleComment`),
  getUnreadComment: makeApi("get", `/api/appInformationArticleComment/query_review_state_total`),
  makeCommentRead: makeApi("post", `/api/appInformationArticleComment/review`),
};

export default AppInformationArticleApi;
