import { useState } from "react";
import AppInformationArticleApi from "./api";
import AppInformationArticleEdit from "./edit";
import { Badge, Button, Card, Dropdown, Form, Input, Modal, Pagination, Popconfirm, Select, Space, Table, Tooltip, Tree, Typography, message } from "antd";
import { DatePicker } from "antd";
import { DatePresetRanges, renderTag } from "@/utils/Tools";
import dayjs from "dayjs";
import { ModeMap, SourceMap, StateMap } from "./types";
import EditCategory from "./EditCategory";
import { useMount, useSetState } from "react-use";
import Style from "./index.module.scss";
import {
  ClearOutlined,
  DeleteOutlined,
  DownOutlined,
  FormOutlined,
  MessageOutlined,
  PaperClipOutlined,
  PlusCircleOutlined,
  PlusOutlined,
  SearchOutlined,
  SettingOutlined,
  SwapOutlined,
  UpOutlined,
} from "@ant-design/icons";
import { Comment } from "./Commemt";
import AsyncSwitch from "@/components/AsyncSwitch";
import AppInformationArticleCopy from "./copy";
import { useTable } from "@/components/WeTablev2/useTable";
import { Rule2 } from "./Rule2";

const fdate = (n: any, f = "YYYY-MM-DD") => (n ? dayjs(n).format(f) : "");

const AppInformationArticlePage = (props: { type?: any }) => {
  const [fold, setFold] = useState(true);
  const [state, setState] = useSetState({
    keys: [] as any[],

    cates: [] as any[],
    cateId: null as any,
    unread: 0,
  });

  const table = useTable({
    params: { type: props.type, categoryId: state.cateId },
    onFetch: (p) => {
      setState({ keys: [] });
      return getAppInformationArticleList(p);
    },
  });

  useMount(() => {
    fetchCategory();
    getUnreadComment();
  });

  const handleReload = () => {
    setState({ keys: [] });
    table.onRefresh();
  };

  const fetchCategory = async () => {
    const res = await AppInformationArticleApi.getCate({ params: { type: props.type, pageSize: 9999 } });
    const list = res?.list || [];
    setState({ cates: list });
  };

  const delCate = async (item: any) => {
    const data = { ids: item.id };
    await AppInformationArticleApi.delCate({ data });
    message.success("删除分类成功");
    fetchCategory();
  };

  const handleDel = async (item: any) => {
    await AppInformationArticleApi.delAppInformationArticle({ data: { ids: item.id } });
    message.success("删除文章成功");
    handleReload();
  };
  const handleModifyState = async (item: any, state: number) => {
    await AppInformationArticleApi.auditAppInformationArticle({ data: { id: item.id, auditState: state } });
    handleReload();
  };

  const getUnreadComment = async () => {
    const res = await AppInformationArticleApi.getUnreadComment({ params: { type: props.type } });
    setState({ unread: res.state1Num });
  };

  const getAppInformationArticleList = async (p: any) => {
    p = { ...p };
    p.state = p.state ?? "10,20,21";
    return await AppInformationArticleApi.getAppInformationArticle({ params: p });
  };

  return (
    <div>
      <div className="flex">
        <div className="w-240px mr-20px flex-shrink-0">
          <Card
            styles={{ body: { padding: 10 } }}
            title={`分类`}
            style={{ height: "100%" }}
            extra={
              <EditCategory title={`添加分类`} type={props.type} onOk={() => fetchCategory()}>
                <PlusCircleOutlined style={{ fontSize: 16, color: "#333" }} />
              </EditCategory>
            }
          >
            <Tree.DirectoryTree
              className={Style.tree}
              treeData={state.cates}
              fieldNames={{ title: "name", key: "id" }}
              showIcon={false}
              selectedKeys={[state.cateId]}
              onSelect={(e) => {
                const key = e[0];
                if (state.cateId === key) {
                  setState({ cateId: "" });
                } else {
                  setState({ cateId: key });
                }
              }}
              titleRender={(item) => {
                return (
                  <div className={Style.row} key={item.id}>
                    <div className={Style.title}>{item.name}</div>
                    <div className={Style.extra} onClick={(e) => e.stopPropagation()}>
                      <EditCategory title={`编辑分类`} onOk={() => fetchCategory()} type={props.type} data={item}>
                        <Tooltip title="编辑分类">
                          <FormOutlined className={Style.ico} />
                        </Tooltip>
                      </EditCategory>

                      <Popconfirm title={`确定删除分类 - ${item.name}？`} onConfirm={() => delCate(item)}>
                        <Tooltip title="删除分类">
                          <DeleteOutlined className={Style.ico} />
                        </Tooltip>
                      </Popconfirm>
                    </div>
                  </div>
                );
              }}
            />
          </Card>
        </div>

        <div className="flex-1 min-w-0 ![&_.ant-card-head-title]:overflow-visible">
          <Form form={table.form} onValuesChange={table.onFormChange}>
            <Card className="mb-2" size="small" classNames={{ body: "p-0" }}>
              <div className="flex [&_.ant-form-item]:mb-0 [&_.ant-form-item-label]:min-w-20">
                <div className="flex-1 grid cols-3 gap-3 overflow-hidden data-[on=true]:h-8" data-on={fold}>
                  {["10", "20", "30", "50"].includes(props.type) && (
                    <Form.Item label="已读状态" name={`reviewState`}>
                      <Select
                        placeholder="请选择"
                        options={[
                          { label: "未读", value: 1 },
                          { label: "已读", value: 2 },
                        ]}
                        allowClear
                      />
                    </Form.Item>
                  )}
                  {["10", "20", "40", "50"].includes(props.type) && (
                    <Form.Item label="标题" name={`title`}>
                      <Input placeholder="请输入标题" />
                    </Form.Item>
                  )}
                  <Form.Item label="状态" name={`state`}>
                    <Select placeholder="请选择" options={StateMap.map((n) => ({ label: n.name, value: n.id }))} allowClear />
                  </Form.Item>
                  {["10", "30", "50"].includes(props.type) && (
                    <Form.Item label="来源" name={`source`}>
                      <Select placeholder="请选择" options={SourceMap.map((n) => ({ label: n.name, value: n.id }))} allowClear />
                    </Form.Item>
                  )}
                  {["10", "30", "50"].includes(props.type) && (
                    <Form.Item label="用户手机" name={`appUserMobile`}>
                      <Input placeholder="请输入用户手机号" />
                    </Form.Item>
                  )}
                  {["10", "20", "30", "50"].includes(props.type) && (
                    <Form.Item label="内容" name={`content`}>
                      <Input placeholder="请输入内容" />
                    </Form.Item>
                  )}
                  {["10", "20", "50"].includes(props.type) && (
                    <Form.Item label="模式" name={`model`}>
                      <Select placeholder="请选择" options={ModeMap.map((n) => ({ label: n.name, value: n.id }))} allowClear />
                    </Form.Item>
                  )}
                  <Form.Item label="推荐标识" name={`recommendTag`}>
                    <Select
                      options={[
                        { label: "开启", value: 1 },
                        { label: "关闭", value: 0 },
                      ]}
                      placeholder="请选择推荐标识"
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item label="创建日期" name={`CreateDate`}>
                    <DatePicker.RangePicker presets={DatePresetRanges} style={{ width: "100%" }} />
                  </Form.Item>
                </div>
                <div className="flex gap-2 pl-3">
                  <Button type="dashed" icon={fold ? <DownOutlined /> : <UpOutlined />} onClick={() => setFold(!fold)} />
                  <Button type="primary" icon={<SearchOutlined />} onClick={table.onSubmit}>
                    搜索
                  </Button>
                  <Button type="default" icon={<ClearOutlined />} onClick={table.onReset} />
                </div>
              </div>
            </Card>

            <Card
              classNames={{ body: "!p-0" }}
              title={
                <Space>
                  <AppInformationArticleEdit title={"新增文章"} type={props.type} onOk={handleReload}>
                    <Button type="primary" icon={<PlusOutlined />}>
                      新增文章
                    </Button>
                  </AppInformationArticleEdit>
                  <Rule2>
                    <Button type="primary" icon={<PaperClipOutlined />}>
                      规则设置
                    </Button>
                  </Rule2>
                  {["10", "20", "30", "50"].includes(props.type) && (
                    <Comment params={{ type: props.type }}>
                      <Badge dot={!!state.unread}>
                        <Button type="primary" icon={<MessageOutlined />}>
                          查看全部评论
                        </Button>
                      </Badge>
                    </Comment>
                  )}
                </Space>
              }
            >
              <Table
                rowKey={"id"}
                dataSource={table.state.list}
                scroll={{ x: "max-content" }}
                columns={[
                  {
                    title: "标题",
                    dataIndex: "title",
                    hidden: !["10", "20", "40", "50"].includes(props.type),
                    width: 400,
                    render: (c) => (c ? c : "--"),
                  },
                  {
                    title: "内容",
                    dataIndex: "content",
                    hidden: !["30"].includes(props.type),
                    width: 400,
                    render: (c) => (
                      <Typography.Text style={{ width: 500 }} ellipsis={{ tooltip: true }}>
                        {c}
                      </Typography.Text>
                    ),
                  },
                  {
                    title: "分类",
                    dataIndex: "categoryName",
                    render: (c) => c ?? "--",
                  },

                  {
                    title: "用户",
                    dataIndex: "appUser",
                    hidden: !["10", "30", "50"].includes(props.type),
                    render: (c) => c?.name ?? "--",
                  },
                  {
                    title: "评分",
                    dataIndex: "evaluationStars",
                    hidden: !["30"].includes(props.type),
                    render: (c) => (c ? c + "星" : "--"),
                  },
                  {
                    title: "浏览",
                    hidden: !["10", "20", "30", "50"].includes(props.type),
                    sorter: true,
                    dataIndex: "viewsNum",
                    width: 80,
                  },
                  {
                    title: "点赞",
                    hidden: !["10", "20", "30", "50"].includes(props.type),
                    sorter: true,
                    dataIndex: "likesNum",
                    width: 80,
                  },
                  {
                    title: "分享",
                    hidden: !["10", "20", "30", "50"].includes(props.type),
                    sorter: true,
                    dataIndex: "sharesNum",
                    width: 80,
                  },
                  {
                    title: "评论",
                    hidden: !["10", "20", "30", "50"].includes(props.type),
                    sorter: true,
                    dataIndex: "commentsNum",
                    width: 80,
                  },
                  {
                    title: (
                      <div className="flex items-center">
                        <div>已读</div>
                        <Popconfirm
                          title="确认？"
                          onConfirm={async () => {
                            await AppInformationArticleApi.makeArticleRead({ data: { reviewAll: 1, type: props.type } });
                            handleReload();
                          }}
                        >
                          <a className="text-s ml-1">全部已读</a>
                        </Popconfirm>
                      </div>
                    ),
                    dataIndex: "reviewState",
                    hidden: !["10", "20", "30", "50"].includes(props.type),
                    render: (c, item) => (
                      <div>
                        {c === 2 ? (
                          "已读"
                        ) : (
                          <>
                            未读 [
                            <Typography.Link
                              onClick={async () => {
                                await AppInformationArticleApi.makeArticleRead({ data: { id: item.id } });
                                handleReload();
                              }}
                            >
                              已读
                            </Typography.Link>
                            ]
                          </>
                        )}
                      </div>
                    ),
                  },
                  {
                    title: "状态",
                    dataIndex: "state",
                    render: (c, item) => (
                      <div>
                        {renderTag(StateMap, c)}
                        {c === 20 ? (
                          <Tooltip title={`切换成异常状态`}>
                            <Typography.Link onClick={() => handleModifyState(item, 21)}>
                              <SwapOutlined />
                            </Typography.Link>
                          </Tooltip>
                        ) : (
                          ""
                        )}
                        {c === 21 ? (
                          <Tooltip title={`切换成正常状态`}>
                            <Typography.Link onClick={() => handleModifyState(item, 20)}>
                              <SwapOutlined />
                            </Typography.Link>
                          </Tooltip>
                        ) : (
                          ""
                        )}
                      </div>
                    ),
                  },
                  {
                    title: "推荐",
                    render: (c) => (
                      <AsyncSwitch
                        onClick={async () => {
                          await AppInformationArticleApi.putAppInformationArticle({ data: { id: c.id, recommendTag: Number(!c.recommendTag) } });
                          handleReload();
                        }}
                        value={!!c.recommendTag}
                      />
                    ),
                  },
                  { title: "排序", dataIndex: "sort" },
                  { title: "创建日期", dataIndex: "createDate", sorter: true, render: (c) => fdate(c) },
                  {
                    fixed: "right",
                    title: "操作",
                    render: (item) => {
                      return (
                        <Space>
                          <AppInformationArticleEdit title={`编辑文章`} type={props.type} data={item} onOk={handleReload}>
                            <a>编辑</a>
                          </AppInformationArticleEdit>
                          {["10", "20", "30", "50"].includes(props.type) && (
                            <Comment params={{ articleId: item.id }}>
                              <a>查看评论</a>
                            </Comment>
                          )}
                          {["10", "30"].includes(props.type) && (
                            <AppInformationArticleEdit title={`审核文章`} type={props.type} data={item} audit={true} onOk={handleReload}>
                              <Typography.Link disabled={![10].includes(item.state)}>审核</Typography.Link>
                            </AppInformationArticleEdit>
                          )}

                          <Dropdown
                            menu={{
                              items: [
                                {
                                  key: "1",
                                  label: (
                                    <AppInformationArticleCopy title={`复制文章`} data={item} onOk={handleReload}>
                                      <a>复制文章</a>
                                    </AppInformationArticleCopy>
                                  ),
                                },
                                {
                                  key: "2",
                                  label: (
                                    <Popconfirm title="确定要删除这条数据吗？" onConfirm={() => handleDel(item)}>
                                      <a>删除文章</a>
                                    </Popconfirm>
                                  ),
                                },
                              ],
                            }}
                          >
                            <Typography.Link>更多</Typography.Link>
                          </Dropdown>
                        </Space>
                      );
                    },
                  },
                ]}
                pagination={false}
                rowSelection={{
                  selectedRowKeys: state.keys,
                  onChange: (selectedRowKeys) => {
                    setState({ keys: selectedRowKeys });
                  },
                }}
              />
              <div className="p-4 flex items-center">
                <Space>
                  <Button
                    onClick={() => {
                      const len = state.keys.length;
                      if (len == table.state.list.length) {
                        setState({ keys: [] });
                      } else {
                        setState({ keys: table.state.list.map((n) => n.id) });
                      }
                    }}
                  >
                    全选
                  </Button>
                  <Dropdown
                    menu={{
                      items: [
                        { label: "批量正常", key: "s1", icon: <SettingOutlined /> },
                        { label: "批量异常", key: "s2", icon: <SettingOutlined /> },
                      ],
                      onClick: (e) => {
                        if (!state.keys.length) return message.error("请先进行选择");

                        if (e.key == "s1") {
                          Modal.confirm({
                            title: "确定要批量正常这些商品吗？",
                            onOk: async () => {
                              const data = { state: 20, ids: state.keys.join(",") };
                              await AppInformationArticleApi.putState({ data });
                              message.success("批量正常成功");
                              handleReload();
                            },
                          });
                        }

                        if (e.key == "s2") {
                          Modal.confirm({
                            title: "确定要批量异常这些商品吗？",
                            onOk: async () => {
                              const data = { state: 21, ids: state.keys.join(",") };
                              await AppInformationArticleApi.putState({ data });
                              message.success("批量异常成功");
                              handleReload();
                            },
                          });
                        }
                      },
                    }}
                  >
                    <Button>
                      批量操作
                      <DownOutlined />
                    </Button>
                  </Dropdown>
                  <div className="text-#666 text-xs">
                    {table.state.list.length}条数据(已选{state.keys.length}条)
                  </div>
                </Space>
                <Pagination
                  className="ml-a"
                  {...{
                    current: table.state.page,
                    pageSize: table.state.size,
                    total: table.state.total,
                    showSizeChanger: true,
                    showTotal: (total) => <div>共 {total} 条数据</div>,
                    pageSizeOptions: [5, 10, 20, 50, 100],
                    onChange: (page, size) => table.onTableChange({ current: page, pageSize: size }),
                  }}
                />
              </div>
            </Card>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default AppInformationArticlePage;
