import WeModal from "@/components/WeModal/WeModal";
import AppInformationArticleApi from "./api";
import { Button, Col, Divider, Form, Input, Radio, Row, Select, Space, Switch, Table, message } from "antd";
import dayjs from "dayjs";
import { InputNumber } from "antd";
import { Mode1Map, ModeMap } from "./types";
import { FileImageOutlined, PlusOutlined, UserOutlined } from "@ant-design/icons";
import { useSetState } from "react-use";
import { UploadV2 } from "@/components/ImageUpload/UploadV2";
import GoodsPicker from "@/components/GoodsPicker";
import { ProductModelType, ProductPropertyType } from "@/components/GoodsPicker/types";
import AppUerPicker from "@/components/Units/AppUerPicker";
import { Rule } from "./Rule";

const layout = { row: 10, col: 12 };

const AppInformationArticleEdit = (props: {
  title: any;
  children: any;
  onOk?: Function;
  data?: any;
  type?: any;
  audit?: boolean;
}) => {
  const [form] = Form.useForm();
  const mode = Form.useWatch("model", form);
  const [state, setState] = useSetState({
    cates: [] as any[],
    goods: [] as any[],
  });

  const handleOpen = async () => {
    setState({ goods: [] });
    form.resetFields();
    fetchCates();
    if (props.data) {
      const data = await AppInformationArticleApi.getAppInformationArticleInfo(props.data.id);
      data.images = UploadV2.str2arr(data.images);
      data.videoCoverImage = UploadV2.str2arr(data.videoCoverImage);
      data.videoUrl = UploadV2.str2arr(data.videoUrl);
      data.auditDate = data.auditDate ? dayjs(data.auditDate) : "";
      setState({ goods: data.productDataList });
      form.setFieldsValue(data);
    }
  };

  const handleSubmit = async () => {
    const { ...data } = await form.validateFields();
    data.images = UploadV2.arr2str(data.images);
    data.videoCoverImage = UploadV2.arr2str(data.videoCoverImage);
    data.videoUrl = UploadV2.arr2str(data.videoUrl);
    data.auditDate = dayjs(data.auditDate).format("YYYY-MM-DD HH:mm:ss");
    data.type = props.type;
    data.recommendTag = Number(data.recommendTag);
    data.relateProductIds = state.goods?.map((n) => n.id).join(",");

    if (!data.id) {
      await AppInformationArticleApi.addAppInformationArticle({ data });
      message.success("添加文章成功");
    }
    if (data.id) {
      if (!props.audit) {
        await AppInformationArticleApi.putAppInformationArticle({ data });
        message.success("修改文章成功");
      }
      if (props.audit) {
        await AppInformationArticleApi.auditAppInformationArticle({ data });
        message.success("审核文章成功");
      }
    }
    props.onOk?.();
  };

  const fetchCates = async () => {
    const res = await AppInformationArticleApi.getCate({ params: { type: props.type, pageSize: 9999 } });
    const list = res?.list || [];
    setState({ cates: list });
  };

  const isEdit = !!props.data?.id;

  // 监听表单字段变化
  const handleValuesChange = (changedValues: any) => {
    // 仅在 videoUrl 发生变化且非编辑模式时处理
    if (changedValues.videoUrl && !isEdit) {
      const videoUrlArr = changedValues.videoUrl;

      // 确保 videoUrlArr 是数组且不为空
      if (Array.isArray(videoUrlArr) && videoUrlArr.length > 0) {
        const videoUrl = videoUrlArr[0]?.url;

        // 检查 videoUrl 是否有效
        if (videoUrl) {
          const videoCoverImage = videoUrl.replace(/\.[^/.]+$/, ".png");

          // 更新表单字段
          form.setFieldsValue({ videoCoverImage: UploadV2.str2arr(videoCoverImage) });
        }
      }
    }
  };

  return (
    <WeModal trigger={props.children} title={props.title} width={1000} onOpen={handleOpen} onOk={handleSubmit}>
      <Form form={form} labelCol={{ flex: "120px" }} onValuesChange={handleValuesChange}>
        <Form.Item name={`id`} hidden>
          <Input />
        </Form.Item>
        <Row gutter={layout.row}>
          <Divider orientation="left">基础信息</Divider>
          {["10", "20", "50"].includes(props.type) && (
            <Col span={layout.col}>
              <Form.Item
                name={`model`}
                label="模式"
                rules={[{ required: true, message: "请选择模式" }]}
                initialValue={1}
              >
                <Radio.Group options={ModeMap.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
            </Col>
          )}
          {["30", "40"].includes(props.type) && (
            <Col span={layout.col}>
              <Form.Item
                name={`model`}
                label="模式"
                rules={[{ required: true, message: "请选择模式" }]}
                initialValue={1}
              >
                <Radio.Group options={Mode1Map.map((n) => ({ label: n.name, value: n.id }))} />
              </Form.Item>
            </Col>
          )}
          <Col span={layout.col}>
            <Form.Item name={`categoryId`} label="分类" rules={[{ required: true, message: "请选择分类" }]}>
              <Select
                options={state.cates}
                fieldNames={{ label: "name", value: "id" }}
                allowClear
                placeholder="请选择分类"
              />
            </Form.Item>
          </Col>
          {["10", "20", "40", "50"].includes(props.type) && (
            <Col span={layout.col * 2}>
              <Form.Item name={`title`} label="标题" rules={[{ required: true, message: "请输入标题" }]}>
                <Input placeholder="请输入标题" />
              </Form.Item>
            </Col>
          )}

          {mode == 1 && (
            <Col span={layout.col * 2}>
              <Form.Item name={`images`} label="图片" rules={[{ required: false, message: "请上传图片" }]}>
                <UploadV2 maxCount={5} />
              </Form.Item>
            </Col>
          )}

          {mode == 2 && (
            <Col span={layout.col * 2}>
              <Form.Item label="视频" rules={[{ required: false, message: "请上传视频封面" }]}>
                <div className="flex">

                  <div className="mr-10px">
                    <Form.Item noStyle name={`videoUrl`}>
                      <UploadV2 mode="video" />
                    </Form.Item>
                  </div>

                  <div className="mr-10px">
                    <Form.Item noStyle name={`videoCoverImage`}>
                      <UploadV2
                        children={
                          <div>
                            <FileImageOutlined className="text-30px text-#666" />
                            <div className="text-14px mt-5px text-#666">视频封面</div>
                          </div>
                        }
                      />
                    </Form.Item>
                  </div>

                </div>
              </Form.Item>
            </Col>
          )}

          <Col span={layout.col * 2}>
            <Form.Item name={`jumpKey`} label="跳转URL">
              <Input placeholder="请输入跳转URL" />
            </Form.Item>
          </Col>

          {["30"].includes(props.type) && (
            <Col span={layout.col}>
              <Form.Item name={`evaluationStars`} label="评分" rules={[{ required: true, message: "请选择" }]}>
                <Select
                  options={[
                    { label: "1星", value: 1 },
                    { label: "2星", value: 2 },
                    { label: "3星", value: 3 },
                    { label: "4星", value: 4 },
                    { label: "5星", value: 5 },
                  ]}
                  allowClear
                />
              </Form.Item>
            </Col>
          )}
          {/* {["20"].includes(props.type) && (
            <Col span={layout.col * 2}>
              <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
                <RichText></RichText>
              </Form.Item>
            </Col>
          )} */}
          {["10", "20", "30", "50"].includes(props.type) && (
            <Col span={layout.col * 2}>
              <Form.Item name={`content`} label="内容" rules={[{ required: false, message: "请输入内容" }]}>
                <Input.TextArea rows={10} placeholder="请输入内容" />
              </Form.Item>
            </Col>
          )}

          <Col span={layout.col}>
            <Form.Item label="推荐标识">
              <Form.Item noStyle name={`recommendTag`} valuePropName="checked" initialValue={false}>
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Form.Item>
          </Col>

          <Col span={layout.col}>
            <Form.Item
              name={`sort`}
              label="排序"
              rules={[{ required: false, message: "请输入排序" }]}
              initialValue={999}
              tooltip="数字越小越靠前"
            >
              <InputNumber min={0} placeholder="请输入排序,数字越小越靠前" style={{ width: "100%" }} />
            </Form.Item>
          </Col>
          {!isEdit && (
            <>
              <Col span={14}>
                <Form.Item
                  name={`appUserId`}
                  label="发布用户"
                  rules={[{ required: false, message: "请选择发布用户" }]}
                  tooltip="不选择则为系统默认用户"
                >
                  <AppUerPicker placeholder="会员号/姓名/手机号，不填则为系统默认用户" params={{ dataType: "1,2" }} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Rule>
                  <Button type="primary" icon={<UserOutlined />}>设置默认用户</Button>
                </Rule>
              </Col>
            </>
          )}

          {["10", "20", "50"].includes(props.type) && (
            <Col span={23}>
              <Form.Item label="关联商品" rules={[{ required: false, message: "请输入关联商品" }]}>
                <div>
                  <Table
                    rowKey={`id`}
                    size="small"
                    dataSource={state.goods}
                    pagination={false}
                    locale={{ emptyText: "暂无商品" }}
                    columns={[
                      { title: "名称", dataIndex: "name" },
                      {
                        title: "属性",
                        dataIndex: "property",
                        render: (c) => ProductPropertyType.find((n) => n.id == c)?.name,
                      },
                      {
                        title: "模式",
                        dataIndex: "productModel",
                        render: (c) => ProductModelType.find((n) => n.id == c)?.name,
                      },
                      //{ title: "分类", dataIndex: "shopCategoryName" },

                      {
                        title: "操作",
                        render: (item) => (
                          <Space>
                            <a
                              onClick={() => {
                                const goods = state.goods.filter((crt) => crt.id !== item.id);
                                setState({ goods });
                              }}
                            >
                              删除
                            </a>
                          </Space>
                        ),
                      },
                    ]}
                  />
                  <div className="h-10px"></div>
                  <GoodsPicker
                    property={2}
                    params={{ isAll: 1 }}
                    onSelect={(rows) => {
                      const newList: any[] = rows.filter((item) => {
                        return state.goods.every((n) => n.id !== item.id);
                      });
                      const fulllist = [...state.goods, ...newList];
                      setState({ goods: fulllist });
                    }}
                  >
                    <Button block type="dashed" icon={<PlusOutlined />}>
                      添加商品
                    </Button>
                  </GoodsPicker>
                </div>
              </Form.Item>
            </Col>
          )}
          {props.audit && (
            <>
              <Divider orientation="left">审核操作</Divider>

              <Col span={layout.col * 2}>
                <Form.Item
                  name={`auditState`}
                  label="审核结果"
                  rules={[{ required: false, message: "请输入审核结果" }]}
                >
                  <Radio.Group
                    options={[
                      { label: <div className="text-blue-4">通过</div>, value: 20 },
                      { label: <div className="text-red-4">拒绝</div>, value: 21 },
                    ]}
                  />
                </Form.Item>
              </Col>
              <Col span={layout.col * 2}>
                <Form.Item
                  name={`auditContent`}
                  label="审核备注"
                  rules={[{ required: false, message: "请输入审核备注" }]}
                >
                  <Input.TextArea autoSize={{ minRows: 3 }} placeholder="请输入审核备注" />
                </Form.Item>
              </Col>
            </>
          )}
        </Row>
      </Form>
    </WeModal>
  );
};

export default AppInformationArticleEdit;
