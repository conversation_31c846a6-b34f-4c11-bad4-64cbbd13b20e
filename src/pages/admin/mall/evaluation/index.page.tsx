import { useRef } from "react";
import { Form, Image, Input, Select, Switch, message } from "antd";
import WeTable, { WeTableRef } from "@/components/WeTable";
import Mall<PERSON><PERSON> from "@/services/MallApi";
import { formatDate } from "@/utils/Tools";
import UserManager from "@/components/UserManager";
import VipPickerInput from "@/components/VipPlusPicker/PickMemberInput";

const CompList = () => {
  const tableRef = useRef<WeTableRef>(null);


  const fetchOrderList = (params: any) => {
    params = { ...params, evaluationState: 2, queryType: 9 };
    return MallApi.getOrderListNew({ params });
  };

  const handleReload = () => {
    tableRef.current?.reload();
  };

  const handleModifyEvaluationShow = async (item: any, value: any) => {
    const data = { orderId: item.id, evaluationShowState: value ? 1 : 0 };
    await Mall<PERSON><PERSON>.modifyOrderEvaluationShow({ data });
    message.success("修改成功");
    handleReload();
  };

  return (
    <div>
      <WeTable
        ref={tableRef}
        tableProps={{ scroll: { x: "max-content" } }}
        request={(params) => {
          return fetchOrderList(params);
        }}

        search={[
          <Form.Item label="会员" name={`shopVipUserId`}>
            <VipPickerInput />
          </Form.Item>,
          <Form.Item label="显示状态" name={`evaluationShowState`}>
            <Select
              options={[
                { label: "显示", value: 1 },
                { label: "隐藏", value: 0 },
              ]}
              placeholder="请选择显示状态"
              allowClear
            />
          </Form.Item>,
          <Form.Item label="单号" name={`serialNo`}>
            <Input placeholder="请输入账单号" />
          </Form.Item>,
        ]}
        columns={[
          {
            fixed: "left",
            title: "会员姓名",
            dataIndex: "vipUser",
            render: (c) => (
              c ? <UserManager userId={c?.id}>
                <a>{c?.name}</a>
              </UserManager>
                : "--"
            ),
          },
          { title: "单号", dataIndex: "serialNo", render: (c) => c ? c : "--" },
          { title: "产品信息", dataIndex: "productName", render: (c) => c ? c : "--" },
          { title: "下单时间", dataIndex: "createDate", render: (c) => formatDate(c) },
          { title: "评论时间", dataIndex: "evaluationDate", render: (c) => formatDate(c) },
          { title: "评论星级", dataIndex: "evaluationLevel" },
          {
            title: "评论图片",
            dataIndex: "evaluationImage",
            render: (c) => {
              if (c) {
                return (
                  <div style={{ display: "flex" }}>
                    {c.split(",").map((img: string) => (
                      <Image src={img} style={{ width: "50px", height: "50px", objectFit: "cover" }} />
                    ))}
                  </div>
                );
              }
            },
          },
          { title: "评论说明", dataIndex: "evaluationDesc", },
          {
            title: "评论显示",
            render: (item) => {
              return (
                <Switch
                  checkedChildren="显示"
                  unCheckedChildren="隐藏"
                  checked={item.evaluationShowState === 1}
                  onChange={(checked) => { handleModifyEvaluationShow(item, checked) }}
                />
              );
            },
          },
          // {
          //   fixed: "right",
          //   title: "操作",
          //   render: (item) => {
          //     return (
          //       <Space>
          //         <OrderDetail data={item}>
          //           <a>详细</a>
          //         </OrderDetail>
          //       </Space>
          //     );
          //   },
          // },
        ]}
      />
    </div>
  );
};

export default CompList;
